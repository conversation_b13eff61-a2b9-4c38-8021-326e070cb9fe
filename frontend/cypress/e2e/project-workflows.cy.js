/**
 * Test E2E per i workflow completi dei progetti
 * Testa l'integrazione frontend-backend end-to-end
 */

describe('Project Management Workflows', () => {
  beforeEach(() => {
    // Login come admin
    cy.login('admin', 'password')
    
    // Intercept API calls
    cy.intercept('GET', '/api/projects*', { fixture: 'projects.json' }).as('getProjects')
    cy.intercept('POST', '/api/projects', { fixture: 'project-created.json' }).as('createProject')
    cy.intercept('GET', '/api/clients*', { fixture: 'clients.json' }).as('getClients')
    cy.intercept('GET', '/api/personnel/users*', { fixture: 'users.json' }).as('getUsers')
  })

  describe('Project Creation Workflow', () => {
    it('should create a complete project with team and tasks', () => {
      // 1. Navigate to projects
      cy.visit('/app/projects')
      cy.wait('@getProjects')
      
      // 2. Click create project
      cy.get('[data-testid="create-project-button"]').click()
      
      // 3. Fill project form
      cy.get('[data-testid="project-name"]').type('E2E Test Project')
      cy.get('[data-testid="project-description"]').type('Complete end-to-end test project')
      cy.get('[data-testid="project-budget"]').type('50000')
      cy.get('[data-testid="project-client"]').select('Test Client')
      cy.get('[data-testid="project-start-date"]').type('2025-01-01')
      cy.get('[data-testid="project-end-date"]').type('2025-06-30')
      
      // 4. Save project
      cy.get('[data-testid="save-button"]').click()
      cy.wait('@createProject')
      
      // 5. Verify success message
      cy.get('[data-testid="success-message"]').should('contain', 'Project created successfully')
      
      // 6. Navigate to project details
      cy.url().should('include', '/projects/')
      
      // 7. Add team members
      cy.get('[data-testid="team-tab"]').click()
      cy.get('[data-testid="add-member-button"]').click()
      
      cy.get('[data-testid="user-select"]').select('John Doe')
      cy.get('[data-testid="role-input"]').type('Project Manager')
      cy.get('[data-testid="allocation-input"]').clear().type('50')
      cy.get('[data-testid="save-member-button"]').click()
      
      // 8. Verify team member added
      cy.get('[data-testid="team-member-list"]').should('contain', 'John Doe')
      cy.get('[data-testid="team-member-list"]').should('contain', 'Project Manager')
      
      // 9. Add tasks
      cy.get('[data-testid="tasks-tab"]').click()
      cy.get('[data-testid="add-task-button"]').click()
      
      cy.get('[data-testid="task-title"]').type('Setup Development Environment')
      cy.get('[data-testid="task-description"]').type('Configure tools and environment')
      cy.get('[data-testid="task-assignee"]').select('John Doe')
      cy.get('[data-testid="task-estimated-hours"]').type('16')
      cy.get('[data-testid="task-priority"]').select('High')
      cy.get('[data-testid="save-task-button"]').click()
      
      // 10. Verify task added
      cy.get('[data-testid="task-list"]').should('contain', 'Setup Development Environment')
      cy.get('[data-testid="task-list"]').should('contain', '16 hours')
    })

    it('should handle project creation validation errors', () => {
      cy.visit('/app/projects/new')
      
      // Try to save without required fields
      cy.get('[data-testid="save-button"]').click()
      
      // Verify validation errors
      cy.get('[data-testid="name-error"]').should('contain', 'Project name is required')
      cy.get('[data-testid="budget-error"]').should('contain', 'Budget is required')
      
      // Fill invalid budget
      cy.get('[data-testid="project-budget"]').type('-1000')
      cy.get('[data-testid="save-button"]').click()
      
      cy.get('[data-testid="budget-error"]').should('contain', 'Budget must be positive')
    })
  })

  describe('Project Team Management', () => {
    beforeEach(() => {
      cy.visit('/app/projects/1') // Existing project
      cy.get('[data-testid="team-tab"]').click()
    })

    it('should manage team members lifecycle', () => {
      // Add member
      cy.get('[data-testid="add-member-button"]').click()
      cy.get('[data-testid="user-select"]').select('Jane Smith')
      cy.get('[data-testid="role-input"]').type('Senior Developer')
      cy.get('[data-testid="allocation-input"]').type('100')
      cy.get('[data-testid="save-member-button"]').click()
      
      // Verify member added
      cy.get('[data-testid="team-member-list"]').should('contain', 'Jane Smith')
      
      // Edit member
      cy.get('[data-testid="edit-member-2"]').click()
      cy.get('[data-testid="role-input"]').clear().type('Lead Developer')
      cy.get('[data-testid="allocation-input"]').clear().type('75')
      cy.get('[data-testid="save-member-button"]').click()
      
      // Verify changes
      cy.get('[data-testid="team-member-list"]').should('contain', 'Lead Developer')
      cy.get('[data-testid="team-member-list"]').should('contain', '75%')
      
      // Remove member
      cy.get('[data-testid="remove-member-2"]').click()
      cy.get('[data-testid="confirm-remove"]').click()
      
      // Verify member removed
      cy.get('[data-testid="team-member-list"]').should('not.contain', 'Jane Smith')
    })

    it('should show team analytics', () => {
      // Verify team statistics are displayed
      cy.get('[data-testid="total-hours"]').should('be.visible')
      cy.get('[data-testid="average-hours"]').should('be.visible')
      cy.get('[data-testid="active-members"]').should('be.visible')
      
      // Verify allocation chart
      cy.get('[data-testid="allocation-chart"]').should('be.visible')
    })
  })

  describe('Project Status Workflow', () => {
    it('should transition project through statuses', () => {
      cy.visit('/app/projects/1')
      
      // Start with planning status
      cy.get('[data-testid="project-status"]').should('contain', 'Planning')
      
      // Change to active
      cy.get('[data-testid="status-dropdown"]').click()
      cy.get('[data-testid="status-active"]').click()
      cy.get('[data-testid="confirm-status-change"]').click()
      
      // Verify status changed
      cy.get('[data-testid="project-status"]').should('contain', 'Active')
      cy.get('[data-testid="success-message"]').should('contain', 'Project status updated')
      
      // Change to completed
      cy.get('[data-testid="status-dropdown"]').click()
      cy.get('[data-testid="status-completed"]').click()
      cy.get('[data-testid="confirm-status-change"]').click()
      
      // Verify completion
      cy.get('[data-testid="project-status"]').should('contain', 'Completed')
      cy.get('[data-testid="completion-date"]').should('be.visible')
    })
  })

  describe('Project KPI Management', () => {
    beforeEach(() => {
      cy.visit('/app/projects/1')
      cy.get('[data-testid="kpi-tab"]').click()
    })

    it('should manage project KPIs', () => {
      // Add KPI
      cy.get('[data-testid="add-kpi-button"]').click()
      cy.get('[data-testid="kpi-name"]').type('Budget Utilization')
      cy.get('[data-testid="kpi-target"]').type('90')
      cy.get('[data-testid="kpi-unit"]').select('Percentage')
      cy.get('[data-testid="kpi-category"]').select('Budget')
      cy.get('[data-testid="save-kpi-button"]').click()
      
      // Verify KPI added
      cy.get('[data-testid="kpi-list"]').should('contain', 'Budget Utilization')
      cy.get('[data-testid="kpi-list"]').should('contain', '90%')
      
      // Update KPI value
      cy.get('[data-testid="update-kpi-1"]').click()
      cy.get('[data-testid="current-value"]').type('75')
      cy.get('[data-testid="save-kpi-value"]').click()
      
      // Verify progress bar
      cy.get('[data-testid="kpi-progress"]').should('have.attr', 'value', '83.33') // 75/90*100
    })
  })

  describe('Project Expenses Tracking', () => {
    beforeEach(() => {
      cy.visit('/app/projects/1')
      cy.get('[data-testid="expenses-tab"]').click()
    })

    it('should track project expenses', () => {
      // Add expense
      cy.get('[data-testid="add-expense-button"]').click()
      cy.get('[data-testid="expense-description"]').type('Software License')
      cy.get('[data-testid="expense-amount"]').type('299.99')
      cy.get('[data-testid="expense-category"]').select('Software')
      cy.get('[data-testid="expense-date"]').type('2025-01-15')
      cy.get('[data-testid="save-expense-button"]').click()
      
      // Verify expense added
      cy.get('[data-testid="expense-list"]').should('contain', 'Software License')
      cy.get('[data-testid="expense-list"]').should('contain', '€299.99')
      
      // Verify budget impact
      cy.get('[data-testid="total-expenses"]').should('contain', '€299.99')
      cy.get('[data-testid="remaining-budget"]').should('contain', '€49,700.01')
      
      // Approve expense
      cy.get('[data-testid="approve-expense-1"]').click()
      cy.get('[data-testid="expense-status-1"]').should('contain', 'Approved')
    })
  })

  describe('Project Timesheet Integration', () => {
    beforeEach(() => {
      cy.visit('/app/projects/1')
      cy.get('[data-testid="timesheet-tab"]').click()
    })

    it('should display project timesheet data', () => {
      // Verify timesheet grid
      cy.get('[data-testid="timesheet-grid"]').should('be.visible')
      
      // Verify team member hours
      cy.get('[data-testid="member-hours"]').should('contain', 'John Doe')
      cy.get('[data-testid="total-project-hours"]').should('be.visible')
      
      // Filter by date range
      cy.get('[data-testid="date-from"]').type('2025-01-01')
      cy.get('[data-testid="date-to"]').type('2025-01-31')
      cy.get('[data-testid="filter-timesheet"]').click()
      
      // Verify filtered results
      cy.get('[data-testid="filtered-hours"]').should('be.visible')
    })
  })
})

// Custom commands for E2E tests
Cypress.Commands.add('login', (username, password) => {
  cy.session([username, password], () => {
    cy.visit('/login')
    cy.get('[data-testid="username"]').type(username)
    cy.get('[data-testid="password"]').type(password)
    cy.get('[data-testid="login-button"]').click()
    cy.url().should('include', '/app/dashboard')
  })
})
