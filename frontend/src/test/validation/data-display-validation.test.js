/**
 * Tests for validating that frontend components correctly display API data
 * and handle various data states and error conditions
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { nextTick } from 'vue'
import Projects from '@/views/projects/Projects.vue'
import ProjectDetail from '@/views/projects/ProjectDetail.vue'
import Dashboard from '@/views/Dashboard.vue'
import { useProjectsStore } from '@/stores/projects'
import { useDashboardStore } from '@/stores/dashboard'
import projectsFixture from '../fixtures/projects.json'

describe('Data Display Validation', () => {
  let pinia

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    global.fetch = vi.fn()
    vi.clearAllMocks()
  })

  describe('API Data Rendering', () => {
    it('should correctly display project data from API', async () => {
      // Mock API response with fixture data
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: projectsFixture
        })
      })

      const wrapper = mount(Projects, {
        global: { plugins: [pinia] }
      })

      // Wait for API call and component update
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 0))

      const projectsStore = useProjectsStore()
      
      // Verify store received correct data
      expect(projectsStore.projects).toHaveLength(3)
      expect(projectsStore.projects[0].name).toBe('E-Commerce Platform')
      expect(projectsStore.projects[0].budget).toBe(75000)

      // Verify UI displays the data correctly
      expect(wrapper.text()).toContain('E-Commerce Platform')
      expect(wrapper.text()).toContain('€75,000')
      expect(wrapper.text()).toContain('TechCorp Solutions')
      expect(wrapper.text()).toContain('Active')

      // Verify project status badges
      const statusBadges = wrapper.findAll('[data-testid="project-status"]')
      expect(statusBadges[0].text()).toContain('Active')
      expect(statusBadges[1].text()).toContain('Planning')

      // Verify budget formatting
      expect(wrapper.text()).toContain('€75,000') // First project
      expect(wrapper.text()).toContain('€50,000') // Second project
      expect(wrapper.text()).toContain('€100,000') // Third project
    })

    it('should display detailed project information correctly', async () => {
      const projectData = projectsFixture.projects[0]
      
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: projectData
        })
      })

      const wrapper = mount(ProjectDetail, {
        global: { 
          plugins: [pinia],
          mocks: {
            $route: { params: { id: '1' } }
          }
        }
      })

      await nextTick()

      // Verify project header information
      expect(wrapper.find('[data-testid="project-title"]').text()).toBe('E-Commerce Platform')
      expect(wrapper.find('[data-testid="project-description"]').text()).toContain('Modern e-commerce platform')
      expect(wrapper.find('[data-testid="project-budget"]').text()).toContain('€75,000')
      expect(wrapper.find('[data-testid="project-status"]').text()).toContain('Active')

      // Verify client information
      expect(wrapper.find('[data-testid="client-name"]').text()).toBe('TechCorp Solutions')
      expect(wrapper.find('[data-testid="client-industry"]').text()).toBe('Technology')

      // Verify project dates
      expect(wrapper.find('[data-testid="start-date"]').text()).toContain('2025-01-01')
      expect(wrapper.find('[data-testid="end-date"]').text()).toContain('2025-08-31')

      // Verify team members display
      const teamMembers = wrapper.findAll('[data-testid="team-member"]')
      expect(teamMembers).toHaveLength(3)
      expect(teamMembers[0].text()).toContain('Alice Johnson')
      expect(teamMembers[0].text()).toContain('Project Manager')
      expect(teamMembers[0].text()).toContain('50%')

      // Verify tasks display
      const tasks = wrapper.findAll('[data-testid="task-item"]')
      expect(tasks).toHaveLength(4)
      expect(tasks[0].text()).toContain('Setup Development Environment')
      expect(tasks[0].text()).toContain('Completed')
      expect(tasks[2].text()).toContain('In Progress')

      // Verify KPIs display
      const kpis = wrapper.findAll('[data-testid="kpi-item"]')
      expect(kpis).toHaveLength(2)
      expect(kpis[0].text()).toContain('Budget Utilization')
      expect(kpis[0].text()).toContain('20%')
      expect(kpis[1].text()).toContain('Task Completion Rate')
      expect(kpis[1].text()).toContain('100%')
    })

    it('should display dashboard statistics correctly', async () => {
      const dashboardData = {
        totalProjects: 15,
        activeProjects: 8,
        completedTasks: 142,
        pendingTasks: 23,
        totalHours: 1250,
        thisWeekHours: 45
      }

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: dashboardData
        })
      })

      const wrapper = mount(Dashboard, {
        global: { plugins: [pinia] }
      })

      await nextTick()

      const dashboardStore = useDashboardStore()
      
      // Verify store data
      expect(dashboardStore.stats.totalProjects).toBe(15)
      expect(dashboardStore.stats.activeProjects).toBe(8)

      // Verify UI displays statistics
      expect(wrapper.find('[data-testid="total-projects"]').text()).toContain('15')
      expect(wrapper.find('[data-testid="active-projects"]').text()).toContain('8')
      expect(wrapper.find('[data-testid="completed-tasks"]').text()).toContain('142')
      expect(wrapper.find('[data-testid="pending-tasks"]').text()).toContain('23')
      expect(wrapper.find('[data-testid="total-hours"]').text()).toContain('1,250')
      expect(wrapper.find('[data-testid="week-hours"]').text()).toContain('45')
    })
  })

  describe('Data Formatting and Presentation', () => {
    it('should format currency values correctly', async () => {
      const projectData = {
        id: 1,
        name: 'Test Project',
        budget: 123456.78,
        expenses: 12345.67
      }

      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { projects: [projectData] }
        })
      })

      const wrapper = mount(Projects, {
        global: { plugins: [pinia] }
      })

      await nextTick()

      // Should format large numbers with commas and currency symbol
      expect(wrapper.text()).toContain('€123,456.78')
      expect(wrapper.text()).toContain('€12,345.67')
    })

    it('should format dates correctly', async () => {
      const projectData = {
        id: 1,
        name: 'Test Project',
        start_date: '2025-01-15',
        end_date: '2025-12-31',
        created_at: '2025-01-01T10:30:00Z'
      }

      const wrapper = mount(ProjectDetail, {
        global: { 
          plugins: [pinia],
          mocks: {
            $route: { params: { id: '1' } }
          }
        }
      })

      // Set project data directly for this test
      wrapper.vm.project = projectData
      await nextTick()

      // Should format dates in readable format
      expect(wrapper.text()).toContain('Jan 15, 2025') // start_date
      expect(wrapper.text()).toContain('Dec 31, 2025') // end_date
      expect(wrapper.text()).toContain('Jan 1, 2025') // created_at
    })

    it('should handle percentage calculations correctly', async () => {
      const projectData = {
        id: 1,
        name: 'Test Project',
        budget: 10000,
        expenses: 2500,
        team_members: [
          { allocation_percentage: 50 },
          { allocation_percentage: 75 },
          { allocation_percentage: 100 }
        ],
        kpis: [
          { target_value: 90, current_value: 67.5 }
        ]
      }

      const wrapper = mount(ProjectDetail, {
        global: { 
          plugins: [pinia],
          mocks: {
            $route: { params: { id: '1' } }
          }
        }
      })

      wrapper.vm.project = projectData
      await nextTick()

      // Budget utilization: 2500/10000 = 25%
      expect(wrapper.text()).toContain('25%')
      
      // Team allocation percentages
      expect(wrapper.text()).toContain('50%')
      expect(wrapper.text()).toContain('75%')
      expect(wrapper.text()).toContain('100%')
      
      // KPI progress: 67.5/90 = 75%
      expect(wrapper.text()).toContain('75%')
    })
  })

  describe('Error State Handling', () => {
    it('should display error messages from API', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: () => Promise.resolve({
          success: false,
          error: 'Internal server error occurred'
        })
      })

      const wrapper = mount(Projects, {
        global: { plugins: [pinia] }
      })

      await nextTick()

      const projectsStore = useProjectsStore()
      expect(projectsStore.error).toBeTruthy()
      expect(wrapper.find('[data-testid="error-message"]').text()).toContain('Internal server error occurred')
    })

    it('should display validation errors correctly', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          success: false,
          errors: {
            name: ['Project name is required'],
            budget: ['Budget must be a positive number'],
            end_date: ['End date must be after start date']
          }
        })
      })

      const wrapper = mount(ProjectDetail, {
        global: { 
          plugins: [pinia],
          mocks: {
            $route: { params: { id: 'new' } }
          }
        }
      })

      // Trigger form submission
      await wrapper.find('[data-testid="save-button"]').trigger('click')
      await nextTick()

      // Should display all validation errors
      expect(wrapper.find('[data-testid="name-error"]').text()).toContain('Project name is required')
      expect(wrapper.find('[data-testid="budget-error"]').text()).toContain('Budget must be a positive number')
      expect(wrapper.find('[data-testid="end-date-error"]').text()).toContain('End date must be after start date')
    })

    it('should handle network errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network connection failed'))

      const wrapper = mount(Projects, {
        global: { plugins: [pinia] }
      })

      await nextTick()

      const projectsStore = useProjectsStore()
      expect(projectsStore.error).toBeTruthy()
      expect(wrapper.find('[data-testid="error-message"]').text()).toContain('Network connection failed')
      expect(wrapper.find('[data-testid="retry-button"]').exists()).toBe(true)
    })
  })

  describe('Loading States', () => {
    it('should display loading indicators during API calls', async () => {
      // Mock a delayed response
      fetch.mockImplementationOnce(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, data: { projects: [] } })
          }), 100)
        )
      )

      const wrapper = mount(Projects, {
        global: { plugins: [pinia] }
      })

      // Should show loading state immediately
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="projects-list"]').exists()).toBe(false)

      // Wait for API call to complete
      await new Promise(resolve => setTimeout(resolve, 150))
      await nextTick()

      // Should hide loading state and show content
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(false)
      expect(wrapper.find('[data-testid="projects-list"]').exists()).toBe(true)
    })
  })

  describe('Empty States', () => {
    it('should display empty state when no data available', async () => {
      fetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { projects: [], pagination: { total: 0 } }
        })
      })

      const wrapper = mount(Projects, {
        global: { plugins: [pinia] }
      })

      await nextTick()

      expect(wrapper.find('[data-testid="empty-state"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="empty-state"]').text()).toContain('No projects found')
      expect(wrapper.find('[data-testid="create-first-project"]').exists()).toBe(true)
    })
  })
})
