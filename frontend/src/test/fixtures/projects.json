{"projects": [{"id": 1, "name": "E-Commerce Platform", "description": "Modern e-commerce platform with Vue.js frontend", "status": "active", "budget": 75000, "expenses": [{"id": 1, "description": "Development Tools License", "amount": 299.99, "category": "software", "expense_date": "2025-01-05", "status": "approved", "user": {"id": 1, "full_name": "<PERSON>"}}, {"id": 2, "description": "Cloud Hosting Setup", "amount": 150.0, "category": "infrastructure", "expense_date": "2025-01-10", "status": "approved", "user": {"id": 2, "full_name": "<PERSON>"}}], "start_date": "2025-01-01", "end_date": "2025-08-31", "project_type": "service", "is_billable": true, "client": {"id": 1, "name": "TechCorp Solutions", "email": "<EMAIL>", "industry": "Technology"}, "contract": {"id": 1, "contract_number": "CNT-2025-001", "value": 75000}, "team_members": [{"id": 1, "full_name": "<PERSON>", "email": "<EMAIL>", "role": "Project Manager", "allocation_percentage": 50, "hours_worked": 120, "daily_rate": 400}, {"id": 2, "full_name": "<PERSON>", "email": "<EMAIL>", "role": "Senior Developer", "allocation_percentage": 100, "hours_worked": 240, "daily_rate": 350}, {"id": 3, "full_name": "<PERSON>", "email": "<EMAIL>", "role": "UI/UX Designer", "allocation_percentage": 75, "hours_worked": 180, "daily_rate": 300}], "tasks": [{"id": 1, "title": "Setup Development Environment", "description": "Configure development tools and CI/CD pipeline", "status": "completed", "priority": "high", "estimated_hours": 16, "actual_hours": 18, "assigned_to": 2, "due_date": "2025-01-15"}, {"id": 2, "title": "Design User Interface", "description": "Create wireframes and UI mockups", "status": "completed", "priority": "high", "estimated_hours": 40, "actual_hours": 38, "assigned_to": 3, "due_date": "2025-02-01"}, {"id": 3, "title": "Implement Authentication", "description": "User login, registration, and security features", "status": "in_progress", "priority": "high", "estimated_hours": 32, "actual_hours": 20, "assigned_to": 2, "due_date": "2025-02-15"}, {"id": 4, "title": "Product Catalog", "description": "Product listing, search, and filtering", "status": "todo", "priority": "medium", "estimated_hours": 48, "actual_hours": 0, "assigned_to": 2, "due_date": "2025-03-15"}], "kpis": [{"id": 1, "name": "Budget Utilization", "description": "Percentage of budget used", "unit": "percentage", "target_value": 90, "current_value": 20, "category": "budget"}, {"id": 2, "name": "Task Completion Rate", "description": "Percentage of tasks completed on time", "unit": "percentage", "target_value": 95, "current_value": 100, "category": "productivity"}]}, {"id": 2, "name": "Mobile App Development", "description": "Cross-platform mobile application", "status": "planning", "budget": 50000, "expenses": [], "start_date": "2025-03-01", "end_date": "2025-10-31", "project_type": "product", "is_billable": true, "client": {"id": 2, "name": "StartupXYZ", "email": "<EMAIL>", "industry": "Fintech"}, "team_members": [], "tasks": [], "kpis": []}, {"id": 3, "name": "AI Research Project", "description": "Machine learning research and development", "status": "active", "budget": 100000, "expenses": [{"id": 3, "description": "GPU Computing Resources", "amount": 5000.0, "category": "infrastructure", "expense_date": "2024-11-01", "status": "approved", "user": {"id": 5, "full_name": "<PERSON>"}}, {"id": 4, "description": "Conference Registration", "amount": 1200.0, "category": "travel", "expense_date": "2025-01-15", "status": "pending", "user": {"id": 4, "full_name": "Dr. <PERSON>"}}], "start_date": "2024-09-01", "end_date": "2025-08-31", "project_type": "rd", "is_billable": false, "funding_source": "public_funding", "client": {"id": 3, "name": "Research Institute", "email": "<EMAIL>", "industry": "Research"}, "team_members": [{"id": 4, "full_name": "Dr. <PERSON>", "email": "<EMAIL>", "role": "Research Lead", "allocation_percentage": 80, "hours_worked": 640, "daily_rate": 500}, {"id": 5, "full_name": "<PERSON>", "email": "<EMAIL>", "role": "Data Scientist", "allocation_percentage": 100, "hours_worked": 800, "daily_rate": 400}], "tasks": [{"id": 5, "title": "Literature Review", "description": "Review existing research and methodologies", "status": "completed", "priority": "high", "estimated_hours": 80, "actual_hours": 85, "assigned_to": 4, "due_date": "2024-10-31"}, {"id": 6, "title": "Data Collection", "description": "Gather and prepare training datasets", "status": "completed", "priority": "high", "estimated_hours": 120, "actual_hours": 115, "assigned_to": 5, "due_date": "2024-12-15"}, {"id": 7, "title": "Model Development", "description": "Develop and train ML models", "status": "in_progress", "priority": "high", "estimated_hours": 200, "actual_hours": 150, "assigned_to": 5, "due_date": "2025-04-30"}], "kpis": [{"id": 3, "name": "Research Milestones", "description": "Number of research milestones achieved", "unit": "count", "target_value": 8, "current_value": 5, "category": "research"}]}], "pagination": {"page": 1, "per_page": 10, "total": 3, "pages": 1}}