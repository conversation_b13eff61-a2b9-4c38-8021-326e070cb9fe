import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mountComponent, mockApiResponse, mockProject, mockClient, fillForm } from '../utils/test-helpers.js'
import Projects from '@/views/projects/Projects.vue'
import ProjectEdit from '@/views/projects/ProjectEdit.vue'
import ProjectTeam from '@/views/projects/components/ProjectTeam.vue'
import { useProjectsStore } from '@/stores/projects'
import { useAuthStore } from '@/stores/auth'

// Mock stores
vi.mock('@/stores/projects', () => ({
  useProjectsStore: vi.fn()
}))

vi.mock('@/stores/auth', () => ({
  useAuthStore: vi.fn()
}))

// Mock API
global.fetch = vi.fn()

describe('Projects Components', () => {
  let mockProjectsStore
  let mockAuthStore

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Setup mock projects store
    mockProjectsStore = {
      projects: [
        mockProject,
        {
          ...mockProject,
          id: 2,
          name: 'Another Project',
          status: 'completed'
        }
      ],
      currentProject: null,
      loading: false,
      error: null,
      fetchProjects: vi.fn(),
      createProject: vi.fn(),
      updateProject: vi.fn(),
      deleteProject: vi.fn(),
      getProject: vi.fn()
    }
    
    // Setup mock auth store
    mockAuthStore = {
      hasPermission: vi.fn(() => true),
      user: { id: 1, role: 'admin' }
    }
    
    useProjectsStore.mockReturnValue(mockProjectsStore)
    useAuthStore.mockReturnValue(mockAuthStore)
    
    fetch.mockClear()
  })

  describe('Projects List Component', () => {
    it('should render projects list', () => {
      const wrapper = mountComponent(Projects)
      
      expect(wrapper.text()).toContain('Test Project')
      expect(wrapper.text()).toContain('Another Project')
    })

    it('should show project status badges', () => {
      const wrapper = mountComponent(Projects)
      
      expect(wrapper.find('[data-testid="status-active"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="status-completed"]').exists()).toBe(true)
    })

    it('should filter projects by status', async () => {
      const wrapper = mountComponent(Projects)
      
      // Select "Active" filter
      const statusFilter = wrapper.find('[data-testid="status-filter"]')
      await statusFilter.setValue('active')
      
      // Should only show active projects
      expect(wrapper.text()).toContain('Test Project')
      expect(wrapper.text()).not.toContain('Another Project')
    })

    it('should search projects by name', async () => {
      const wrapper = mountComponent(Projects)
      
      const searchInput = wrapper.find('[data-testid="search-input"]')
      await searchInput.setValue('Another')
      
      // Should only show matching projects
      expect(wrapper.text()).toContain('Another Project')
      expect(wrapper.text()).not.toContain('Test Project')
    })

    it('should show create button for authorized users', () => {
      const wrapper = mountComponent(Projects)
      
      expect(wrapper.find('[data-testid="create-project-button"]').exists()).toBe(true)
    })

    it('should hide create button for unauthorized users', () => {
      mockAuthStore.hasPermission.mockReturnValue(false)
      
      const wrapper = mountComponent(Projects)
      
      expect(wrapper.find('[data-testid="create-project-button"]').exists()).toBe(false)
    })

    it('should call fetchProjects on mount', () => {
      mountComponent(Projects)
      
      expect(mockProjectsStore.fetchProjects).toHaveBeenCalled()
    })

    it('should show loading state', () => {
      mockProjectsStore.loading = true
      
      const wrapper = mountComponent(Projects)
      
      expect(wrapper.find('[data-testid="loading-spinner"]').exists()).toBe(true)
    })

    it('should show error state', () => {
      mockProjectsStore.error = 'Failed to load projects'
      
      const wrapper = mountComponent(Projects)
      
      expect(wrapper.text()).toContain('Failed to load projects')
    })
  })

  describe('Project Edit Component', () => {
    beforeEach(() => {
      mockProjectsStore.currentProject = mockProject
    })

    it('should render project form', () => {
      const wrapper = mountComponent(ProjectEdit)
      
      expect(wrapper.find('[data-testid="project-name"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="project-description"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="project-budget"]').exists()).toBe(true)
    })

    it('should populate form with existing project data', () => {
      const wrapper = mountComponent(ProjectEdit)
      
      const nameInput = wrapper.find('[data-testid="project-name"]')
      expect(nameInput.element.value).toBe(mockProject.name)
    })

    it('should validate required fields', async () => {
      const wrapper = mountComponent(ProjectEdit)
      
      // Clear required field
      await wrapper.find('[data-testid="project-name"]').setValue('')
      
      // Try to submit
      await wrapper.find('[data-testid="save-button"]').trigger('click')
      
      expect(wrapper.text()).toContain('Project name is required')
    })

    it('should save project on form submission', async () => {
      mockProjectsStore.updateProject.mockResolvedValue({ success: true })
      
      const wrapper = mountComponent(ProjectEdit)
      
      // Fill form
      await fillForm(wrapper, {
        'project-name': 'Updated Project Name',
        'project-description': 'Updated description',
        'project-budget': '15000'
      })
      
      // Submit form
      await wrapper.find('[data-testid="save-button"]').trigger('click')
      
      expect(mockProjectsStore.updateProject).toHaveBeenCalledWith(
        mockProject.id,
        expect.objectContaining({
          name: 'Updated Project Name',
          description: 'Updated description',
          budget: 15000
        })
      )
    })

    it('should show success message after save', async () => {
      mockProjectsStore.updateProject.mockResolvedValue({ success: true })
      
      const wrapper = mountComponent(ProjectEdit)
      
      await wrapper.find('[data-testid="save-button"]').trigger('click')
      
      expect(wrapper.text()).toContain('Project saved successfully')
    })

    it('should show error message on save failure', async () => {
      mockProjectsStore.updateProject.mockRejectedValue(new Error('Save failed'))
      
      const wrapper = mountComponent(ProjectEdit)
      
      await wrapper.find('[data-testid="save-button"]').trigger('click')
      
      expect(wrapper.text()).toContain('Failed to save project')
    })

    it('should navigate back on cancel', async () => {
      const mockRouter = { push: vi.fn() }
      
      const wrapper = mountComponent(ProjectEdit, {
        global: {
          mocks: {
            $router: mockRouter
          }
        }
      })
      
      await wrapper.find('[data-testid="cancel-button"]').trigger('click')
      
      expect(mockRouter.push).toHaveBeenCalledWith('/app/projects')
    })
  })

  describe('Project Team Component', () => {
    const mockTeamProject = {
      ...mockProject,
      team_members: [
        {
          id: 1,
          full_name: 'John Doe',
          email: '<EMAIL>',
          role: 'Project Manager',
          allocation_percentage: 50,
          hours_worked: 40
        },
        {
          id: 2,
          full_name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'Developer',
          allocation_percentage: 100,
          hours_worked: 80
        }
      ]
    }

    it('should display team members', () => {
      const wrapper = mountComponent(ProjectTeam, {
        props: { project: mockTeamProject }
      })

      expect(wrapper.text()).toContain('John Doe')
      expect(wrapper.text()).toContain('Jane Smith')
      expect(wrapper.text()).toContain('Project Manager')
      expect(wrapper.text()).toContain('Developer')
    })

    it('should show team statistics', () => {
      const wrapper = mountComponent(ProjectTeam, {
        props: { project: mockTeamProject }
      })

      // Total hours worked: 40 + 80 = 120
      expect(wrapper.text()).toContain('120')
      // Average hours per member: 120 / 2 = 60
      expect(wrapper.text()).toContain('60')
      // Active members: 2 (both have hours > 0)
      expect(wrapper.text()).toContain('2')
    })

    it('should add team member', async () => {
      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))

      const wrapper = mountComponent(ProjectTeam, {
        props: { project: mockTeamProject }
      })

      // Open add member modal
      await wrapper.find('[data-testid="add-member-button"]').trigger('click')

      // Fill form
      await fillForm(wrapper, {
        'user-select': '3',
        'role-input': 'QA Tester',
        'allocation-input': '75'
      })

      // Submit
      await wrapper.find('[data-testid="save-member-button"]').trigger('click')

      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockTeamProject.id}/team`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': expect.any(String)
        },
        body: JSON.stringify({
          user_id: '3',
          role: 'QA Tester',
          allocation_percentage: 75
        })
      })
    })

    it('should remove team member', async () => {
      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))

      const wrapper = mountComponent(ProjectTeam, {
        props: { project: mockTeamProject }
      })

      // Click remove button for first member
      await wrapper.find('[data-testid="remove-member-1"]').trigger('click')

      // Confirm removal
      await wrapper.find('[data-testid="confirm-remove"]').trigger('click')

      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockTeamProject.id}/team/1`, {
        method: 'DELETE'
      })
    })
  })

  describe('Project Workflow Integration', () => {
    it('should handle complete project creation workflow', async () => {
      // Mock successful API calls
      fetch
        .mockResolvedValueOnce(mockApiResponse({ // Create project
          success: true,
          data: { ...mockProject, id: 1 }
        }))
        .mockResolvedValueOnce(mockApiResponse({ // Add team member
          success: true
        }))
        .mockResolvedValueOnce(mockApiResponse({ // Create task
          success: true,
          data: { id: 1, title: 'Setup', status: 'todo' }
        }))

      const wrapper = mountComponent(ProjectEdit)

      // Fill project form
      await fillForm(wrapper, {
        'project-name': 'Integration Test Project',
        'project-description': 'Full workflow test',
        'project-budget': '25000',
        'project-client': '1'
      })

      // Save project
      await wrapper.find('[data-testid="save-button"]').trigger('click')

      // Verify project creation API call
      expect(fetch).toHaveBeenCalledWith('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: 'Integration Test Project',
          description: 'Full workflow test',
          budget: 25000,
          client_id: 1
        })
      })
    })

    it('should handle project status transitions', async () => {
      const activeProject = { ...mockProject, status: 'planning' }

      fetch.mockResolvedValueOnce(mockApiResponse({
        success: true,
        data: { ...activeProject, status: 'active' }
      }))

      const wrapper = mountComponent(ProjectEdit, {
        props: { project: activeProject }
      })

      // Change status to active
      await wrapper.find('[data-testid="status-select"]').setValue('active')
      await wrapper.find('[data-testid="save-button"]').trigger('click')

      expect(fetch).toHaveBeenCalledWith(`/api/projects/${activeProject.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: 'active'
        })
      })
    })
  })

  describe('Projects Store', () => {
    it('should fetch projects successfully', async () => {
      const mockProjects = [mockProject]

      fetch.mockResolvedValueOnce(mockApiResponse({
        success: true,
        data: mockProjects
      }))

      const { useProjectsStore } = await import('@/stores/projects')
      const store = useProjectsStore()

      await store.fetchProjects()

      expect(store.projects).toEqual(mockProjects)
      expect(store.loading).toBe(false)
    })

    it('should create project successfully', async () => {
      const newProject = { name: 'New Project', description: 'Test' }

      fetch.mockResolvedValueOnce(mockApiResponse({
        success: true,
        data: { ...newProject, id: 3 }
      }))

      const { useProjectsStore } = await import('@/stores/projects')
      const store = useProjectsStore()

      const result = await store.createProject(newProject)

      expect(result.success).toBe(true)
      expect(fetch).toHaveBeenCalledWith('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newProject)
      })
    })

    it('should update project successfully', async () => {
      const updates = { name: 'Updated Name' }

      fetch.mockResolvedValueOnce(mockApiResponse({
        success: true,
        data: { ...mockProject, ...updates }
      }))

      const { useProjectsStore } = await import('@/stores/projects')
      const store = useProjectsStore()

      const result = await store.updateProject(mockProject.id, updates)

      expect(result.success).toBe(true)
      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updates)
      })
    })

    it('should delete project successfully', async () => {
      fetch.mockResolvedValueOnce(mockApiResponse({ success: true }))

      const { useProjectsStore } = await import('@/stores/projects')
      const store = useProjectsStore()

      const result = await store.deleteProject(mockProject.id)

      expect(result.success).toBe(true)
      expect(fetch).toHaveBeenCalledWith(`/api/projects/${mockProject.id}`, {
        method: 'DELETE'
      })
    })
  })
})
