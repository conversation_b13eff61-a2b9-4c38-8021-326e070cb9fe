/**
 * Mock Service Worker (MSW) handlers for API mocking
 * Provides realistic API responses for testing
 */

import { rest } from 'msw'

// Mock data
const mockProjects = [
  {
    id: 1,
    name: 'Project Alpha',
    description: 'First test project',
    status: 'active',
    budget: 50000,
    start_date: '2025-01-01',
    end_date: '2025-06-30',
    client: { id: 1, name: 'Acme Corp' },
    team_members: [
      { id: 1, full_name: '<PERSON>', role: 'Project Manager', allocation_percentage: 50 },
      { id: 2, full_name: '<PERSON>', role: 'Developer', allocation_percentage: 100 }
    ],
    tasks: [
      { id: 1, title: 'Setup Environment', status: 'completed', estimated_hours: 8 },
      { id: 2, title: 'Develop Features', status: 'in_progress', estimated_hours: 40 }
    ]
  },
  {
    id: 2,
    name: 'Project Beta',
    description: 'Second test project',
    status: 'planning',
    budget: 30000,
    start_date: '2025-02-01',
    end_date: '2025-08-31',
    client: { id: 2, name: 'Beta Industries' },
    team_members: [],
    tasks: []
  }
]

const mockUsers = [
  { id: 1, full_name: '<PERSON>', email: '<EMAIL>', role: 'manager' },
  { id: 2, full_name: '<PERSON>', email: '<EMAIL>', role: 'employee' },
  { id: 3, full_name: 'Bob Wilson', email: '<EMAIL>', role: 'employee' }
]

const mockClients = [
  { id: 1, name: 'Acme Corp', email: '<EMAIL>', industry: 'Technology' },
  { id: 2, name: 'Beta Industries', email: '<EMAIL>', industry: 'Manufacturing' }
]

// API Handlers
export const handlers = [
  // Authentication
  rest.post('/api/auth/login', (req, res, ctx) => {
    const { username, password } = req.body
    
    if (username === 'admin' && password === 'password') {
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          data: {
            user: { id: 1, username: 'admin', role: 'admin', full_name: 'Admin User' },
            token: 'mock-jwt-token'
          }
        })
      )
    }
    
    return res(
      ctx.status(401),
      ctx.json({
        success: false,
        error: 'Invalid credentials'
      })
    )
  }),

  rest.post('/api/auth/logout', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ success: true })
    )
  }),

  rest.get('/api/auth/me', (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization')
    
    if (authHeader && authHeader.includes('mock-jwt-token')) {
      return res(
        ctx.status(200),
        ctx.json({
          success: true,
          data: { id: 1, username: 'admin', role: 'admin', full_name: 'Admin User' }
        })
      )
    }
    
    return res(
      ctx.status(401),
      ctx.json({ success: false, error: 'Unauthorized' })
    )
  }),

  // Projects
  rest.get('/api/projects', (req, res, ctx) => {
    const page = req.url.searchParams.get('page') || 1
    const search = req.url.searchParams.get('search')
    const status = req.url.searchParams.get('status')
    
    let filteredProjects = [...mockProjects]
    
    if (search) {
      filteredProjects = filteredProjects.filter(p => 
        p.name.toLowerCase().includes(search.toLowerCase())
      )
    }
    
    if (status) {
      filteredProjects = filteredProjects.filter(p => p.status === status)
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          projects: filteredProjects,
          pagination: {
            page: parseInt(page),
            per_page: 10,
            total: filteredProjects.length,
            pages: Math.ceil(filteredProjects.length / 10)
          }
        }
      })
    )
  }),

  rest.get('/api/projects/:id', (req, res, ctx) => {
    const { id } = req.params
    const project = mockProjects.find(p => p.id === parseInt(id))
    
    if (!project) {
      return res(
        ctx.status(404),
        ctx.json({ success: false, error: 'Project not found' })
      )
    }
    
    return res(
      ctx.status(200),
      ctx.json({ success: true, data: project })
    )
  }),

  rest.post('/api/projects', (req, res, ctx) => {
    const projectData = req.body
    
    // Validate required fields
    if (!projectData.name || !projectData.budget) {
      return res(
        ctx.status(400),
        ctx.json({
          success: false,
          errors: {
            name: !projectData.name ? ['Project name is required'] : [],
            budget: !projectData.budget ? ['Budget is required'] : []
          }
        })
      )
    }
    
    const newProject = {
      id: mockProjects.length + 1,
      ...projectData,
      team_members: [],
      tasks: [],
      created_at: new Date().toISOString()
    }
    
    mockProjects.push(newProject)
    
    return res(
      ctx.status(201),
      ctx.json({ success: true, data: newProject })
    )
  }),

  rest.put('/api/projects/:id', (req, res, ctx) => {
    const { id } = req.params
    const updates = req.body
    const projectIndex = mockProjects.findIndex(p => p.id === parseInt(id))
    
    if (projectIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ success: false, error: 'Project not found' })
      )
    }
    
    mockProjects[projectIndex] = { ...mockProjects[projectIndex], ...updates }
    
    return res(
      ctx.status(200),
      ctx.json({ success: true, data: mockProjects[projectIndex] })
    )
  }),

  rest.delete('/api/projects/:id', (req, res, ctx) => {
    const { id } = req.params
    const projectIndex = mockProjects.findIndex(p => p.id === parseInt(id))
    
    if (projectIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ success: false, error: 'Project not found' })
      )
    }
    
    mockProjects.splice(projectIndex, 1)
    
    return res(
      ctx.status(200),
      ctx.json({ success: true })
    )
  }),

  // Project Team Management
  rest.post('/api/projects/:id/team', (req, res, ctx) => {
    const { id } = req.params
    const { user_id, role, allocation_percentage } = req.body
    const project = mockProjects.find(p => p.id === parseInt(id))
    
    if (!project) {
      return res(
        ctx.status(404),
        ctx.json({ success: false, error: 'Project not found' })
      )
    }
    
    const user = mockUsers.find(u => u.id === parseInt(user_id))
    if (!user) {
      return res(
        ctx.status(404),
        ctx.json({ success: false, error: 'User not found' })
      )
    }
    
    const teamMember = {
      id: user.id,
      full_name: user.full_name,
      email: user.email,
      role,
      allocation_percentage: parseInt(allocation_percentage),
      hours_worked: 0
    }
    
    project.team_members.push(teamMember)
    
    return res(
      ctx.status(200),
      ctx.json({ success: true, data: teamMember })
    )
  }),

  rest.delete('/api/projects/:projectId/team/:userId', (req, res, ctx) => {
    const { projectId, userId } = req.params
    const project = mockProjects.find(p => p.id === parseInt(projectId))
    
    if (!project) {
      return res(
        ctx.status(404),
        ctx.json({ success: false, error: 'Project not found' })
      )
    }
    
    const memberIndex = project.team_members.findIndex(m => m.id === parseInt(userId))
    if (memberIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ success: false, error: 'Team member not found' })
      )
    }
    
    project.team_members.splice(memberIndex, 1)
    
    return res(
      ctx.status(200),
      ctx.json({ success: true })
    )
  }),

  // Users
  rest.get('/api/personnel/users', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ success: true, data: mockUsers })
    )
  }),

  // Clients
  rest.get('/api/clients', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ success: true, data: mockClients })
    )
  }),

  // Dashboard
  rest.get('/api/dashboard/stats', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          totalProjects: mockProjects.length,
          activeProjects: mockProjects.filter(p => p.status === 'active').length,
          completedTasks: 45,
          pendingTasks: 12,
          totalHours: 320,
          thisWeekHours: 40
        }
      })
    )
  }),

  // Error simulation
  rest.get('/api/error-test', (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({ success: false, error: 'Internal server error' })
    )
  }),

  // Network delay simulation
  rest.get('/api/slow-endpoint', (req, res, ctx) => {
    return res(
      ctx.delay(2000), // 2 second delay
      ctx.status(200),
      ctx.json({ success: true, data: 'Slow response' })
    )
  })
]
