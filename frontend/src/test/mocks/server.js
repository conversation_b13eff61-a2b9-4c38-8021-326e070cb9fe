/**
 * Mock Service Worker server setup for testing
 * Intercepts API calls and provides mock responses
 */

import { setupServer } from 'msw/node'
import { handlers } from './api-handlers.js'

// Setup MSW server with our handlers
export const server = setupServer(...handlers)

// Start server before all tests
beforeAll(() => {
  server.listen({
    onUnhandledRequest: 'warn' // Warn about unhandled requests
  })
})

// Reset handlers after each test
afterEach(() => {
  server.resetHandlers()
})

// Close server after all tests
afterAll(() => {
  server.close()
})

// Export utilities for test-specific mocking
export const mockApi = {
  // Mock successful responses
  mockSuccess: (endpoint, data, status = 200) => {
    server.use(
      rest.get(endpoint, (req, res, ctx) => {
        return res(
          ctx.status(status),
          ctx.json({ success: true, data })
        )
      })
    )
  },

  // Mock error responses
  mockError: (endpoint, error, status = 500) => {
    server.use(
      rest.get(endpoint, (req, res, ctx) => {
        return res(
          ctx.status(status),
          ctx.json({ success: false, error })
        )
      })
    )
  },

  // Mock network delays
  mockDelay: (endpoint, delay = 1000) => {
    server.use(
      rest.get(endpoint, (req, res, ctx) => {
        return res(
          ctx.delay(delay),
          ctx.status(200),
          ctx.json({ success: true, data: 'Delayed response' })
        )
      })
    )
  },

  // Mock authentication states
  mockAuth: {
    authenticated: () => {
      server.use(
        rest.get('/api/auth/me', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              data: { id: 1, username: 'testuser', role: 'admin' }
            })
          )
        })
      )
    },

    unauthenticated: () => {
      server.use(
        rest.get('/api/auth/me', (req, res, ctx) => {
          return res(
            ctx.status(401),
            ctx.json({ success: false, error: 'Unauthorized' })
          )
        })
      )
    }
  },

  // Mock specific project scenarios
  mockProjects: {
    empty: () => {
      server.use(
        rest.get('/api/projects', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              data: { projects: [], pagination: { total: 0 } }
            })
          )
        })
      )
    },

    withData: (projects) => {
      server.use(
        rest.get('/api/projects', (req, res, ctx) => {
          return res(
            ctx.status(200),
            ctx.json({
              success: true,
              data: { projects, pagination: { total: projects.length } }
            })
          )
        })
      )
    },

    createError: (errors) => {
      server.use(
        rest.post('/api/projects', (req, res, ctx) => {
          return res(
            ctx.status(400),
            ctx.json({ success: false, errors })
          )
        })
      )
    }
  }
}
