[{"debug": "Calling MCP tool: closeAllDiffTabs", "timestamp": "2025-06-19T07:36:22.052Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"CLOSED_0_DIFF_TABS\"}]}", "timestamp": "2025-06-19T07:36:22.069Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:36:26.472Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:36:26.478Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:36:31.609Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:36:31.617Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:36:45.105Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:36:45.124Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:01.496Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:01.513Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: closeAllDiffTabs", "timestamp": "2025-06-19T07:41:01.534Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"CLOSED_0_DIFF_TABS\"}]}", "timestamp": "2025-06-19T07:41:01.579Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:16.546Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:16.558Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:20.219Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:20.229Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:23.769Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:23.778Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:26.881Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:26.891Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:31.221Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:31.229Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:34.743Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:34.754Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:38.426Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:38.437Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:41.819Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:41.829Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:44.935Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:44.946Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:48.146Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:48.160Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:51.942Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:51.951Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:41:55.156Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:41:55.164Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:00.243Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:00.259Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:06.030Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:06.038Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:09.917Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:09.925Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:13.997Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:14.009Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:18.158Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:18.168Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:20.397Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:20.408Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:24.021Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:24.033Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:28.866Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:28.876Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:42:43.593Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:42:43.604Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:43:27.114Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:43:27.117Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-19T07:43:32.262Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/funding_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-19T07:43:32.270Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}, {"error": "transport closed", "timestamp": "2025-06-19T07:51:55.746Z", "sessionId": "9ed370d9-da7a-499d-8107-da68b8cd9619", "cwd": "/home/<USER>/workspace"}]