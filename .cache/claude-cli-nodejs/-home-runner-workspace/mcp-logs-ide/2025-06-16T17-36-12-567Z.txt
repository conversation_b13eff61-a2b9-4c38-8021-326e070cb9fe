[{"debug": "Calling MCP tool: closeAllDiffTabs", "timestamp": "2025-06-16T19:03:06.862Z", "sessionId": "b43bd1a7-1552-488a-9fc8-6b791c9f0d04", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"CLOSED_0_DIFF_TABS\"}]}", "timestamp": "2025-06-16T19:03:06.893Z", "sessionId": "b43bd1a7-1552-488a-9fc8-6b791c9f0d04", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-16T19:03:11.735Z", "sessionId": "b43bd1a7-1552-488a-9fc8-6b791c9f0d04", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/personnel/PersonnelOrgChart.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/layout/SidebarNavigation.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/design-system/examples/ViewModeToggleExample.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/router/index.js\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/icons/IconLibrary.js\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/layout/SidebarIcon.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/personnel/DepartmentView.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetDashboard.vue\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"'watch' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 191,\\n            \\\"character\\\": 35\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 191,\\n            \\\"character\\\": 40\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'clearError' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 421,\\n            \\\"character\\\": 6\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 421,\\n            \\\"character\\\": 16\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'row' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 31\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 34\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'day' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 36\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 39\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetApprovals.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetEntry.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetAnalytics.vue\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"'timesheetStore' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 254,\\n            \\\"character\\\": 6\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 254,\\n            \\\"character\\\": 20\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/business_intelligence_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/components_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-16T19:03:11.742Z", "sessionId": "b43bd1a7-1552-488a-9fc8-6b791c9f0d04", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-16T19:03:16.086Z", "sessionId": "b43bd1a7-1552-488a-9fc8-6b791c9f0d04", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/personnel/PersonnelOrgChart.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/layout/SidebarNavigation.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/design-system/examples/ViewModeToggleExample.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/router/index.js\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/icons/IconLibrary.js\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/layout/SidebarIcon.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/personnel/DepartmentView.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetDashboard.vue\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"'watch' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 191,\\n            \\\"character\\\": 35\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 191,\\n            \\\"character\\\": 40\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'clearError' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 421,\\n            \\\"character\\\": 6\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 421,\\n            \\\"character\\\": 16\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'row' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 31\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 34\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'day' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 36\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 39\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetApprovals.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetEntry.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetAnalytics.vue\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"'timesheetStore' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 254,\\n            \\\"character\\\": 6\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 254,\\n            \\\"character\\\": 20\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/business_intelligence_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/components_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-16T19:03:16.096Z", "sessionId": "b43bd1a7-1552-488a-9fc8-6b791c9f0d04", "cwd": "/home/<USER>/workspace"}, {"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-16T19:03:21.010Z", "sessionId": "b43bd1a7-1552-488a-9fc8-6b791c9f0d04", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/vue.volar-2.2.10/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/personnel/PersonnelOrgChart.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/layout/SidebarNavigation.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/design-system/examples/ViewModeToggleExample.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/router/index.js\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/icons/IconLibrary.js\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/components/layout/SidebarIcon.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/personnel/DepartmentView.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetDashboard.vue\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"'watch' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 191,\\n            \\\"character\\\": 35\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 191,\\n            \\\"character\\\": 40\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'clearError' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 421,\\n            \\\"character\\\": 6\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 421,\\n            \\\"character\\\": 16\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'row' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 31\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 34\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      },\\n      {\\n        \\\"message\\\": \\\"'day' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 36\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 110,\\n            \\\"character\\\": 39\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetApprovals.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetEntry.vue\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/frontend/src/views/timesheet/TimesheetAnalytics.vue\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"'timesheetStore' is declared but its value is never read.\\\",\\n        \\\"severity\\\": \\\"Hint\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 254,\\n            \\\"character\\\": 6\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 254,\\n            \\\"character\\\": 20\\n          }\\n        },\\n        \\\"source\\\": \\\"ts-plugin\\\",\\n        \\\"code\\\": \\\"6133\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/business_intelligence_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/docs/components_plan.md\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-16T19:03:21.017Z", "sessionId": "b43bd1a7-1552-488a-9fc8-6b791c9f0d04", "cwd": "/home/<USER>/workspace"}]