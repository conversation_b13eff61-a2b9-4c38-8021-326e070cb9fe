{"name": "@pkgjs/parseargs", "dist-tags": {"latest": "0.11.0"}, "versions": {"0.1.0": {"name": "@pkgjs/parseargs", "version": "0.1.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-dEqu3wEMuysm8VQ4dMzJ6jv24owX87Bd/7Yc1cKAfq5E+hvAF52hKPTNEu4aO5pDifkLdnXN5kKsOhOa52oo6Q==", "shasum": "795ff322608f75eee90f9458b71634939915a9e3", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.1.0.tgz", "fileCount": 6, "unpackedSize": 37695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7FpqCRA9TVsSAnZWagAAdCwQAI3IVTMmfI1FQd2O/cz9\nDvx7uBo7aVSLkIlCzBizZ/ummBRSjh9rbVPIeRVuGsr02juqIKxVqmr5DTA+\ndAecKrG6X/rneh4aUAaKLltaH99EyDzpDfk0eiscJR4NGsMyuW9vAu30uCSH\no69I7DaowlLK65pFP0ku+hlW6wEJN7ix4KleJzr5glSkjfTuXB3+XMOFZoaP\nVzwVR/iVVjBvGSpuKhQyMnIL1ZCtpIp4NTFiU08f/0uCImSlLE1efSRF0qgL\nvobWwFXphiZtRcY71f6g+Sozj5uqEw775UBZZGiXf1X8B9PpovKU0hlJjU+5\nYB0egLRDc+Dy124f8NAAGdVP49LQzP064G8p2oRtTKUwaVW6Bb6Md51f554C\n+/vpZrnNS2QIzLWMbNDdhGde12CjCIM7MAc/begIU7Cl9WZHse8poUnrWMze\nrufk4/J0XzXfbk1ItGbF8lWW5WjEkH9V8lZKMQvhJd6w5uZtzOmd2uX/xwKM\nk8xc/W+iswpHJl/8adE9guVbWawKdjtSjlLV95MS4ryUwQ8jPLKL5ZIX7N84\naRRKveXuSXraPzLu1Abn3OFrzwdDjWRyL7Kd0Al+bf9jsfXKOM+S9VmPpLFd\nHD99293dPpDKb9QRgfCOz7r9Xq11nwgm2SLet48mukkPd2laIuOfRjV1u5D7\nyx69\r\n=nH1C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDYQbe+24liKg3slwJQZ6/d60uAr5Swdq7dQKipU14WNwIhAK6LIeLW+G9bqlRxOWuOHnfwy9UghaIy/u+7xrgekQXy"}]}}, "0.1.1": {"name": "@pkgjs/parseargs", "version": "0.1.1", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-eobYeKw1OXPHDnKBHk9k3p4YhDsku+tAl6LLUZL6QqvYIcip2SkyyrXJx2OW4uK8eTp10pGVDPcXlg1wEmub3g==", "shasum": "ad971d9d6c0d1f6194f109769105555ce6ef315d", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.1.1.tgz", "fileCount": 6, "unpackedSize": 38282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+ygtCRA9TVsSAnZWagAAcFEP/1tCpAsIFDWL7pyXUYKC\n7w+06VouSJ+1gdTQ1eMxZQt+dhCLBaabudNb1h+ZE6FI+46ZJTD3RXJy5flt\nI/jkuFARPW1yz2rj+j4puEGqkH+/T0QFUmx06t4n6elIS9hTAkNABNsKOls+\nJYepoVI82Bu0hkvkc2EWI6VS+16RluTePGxraqXetKXtmo9dC7R7pemx3+aI\nnQPaoWaP2cEeE6hGVOOpooEyh4yTgkyz9GHQZAyUEnJ5FJVUw2gXzqAXB52y\n6ZAnbiIK25sh2uZtgyYkJ5R4ymmlvSwGd9P57tCYStAojs+7Rpj+pFtmJtjJ\nBE518tyXJ0wHUR4e3f4UKKAZEe1cwUSdgyrFAgCzEOlI5MRnF4HuIQk3v9LR\nKLK+2qzhutzZPNMsIlDlwxhDXq4WDIgD/sUnoq7BMbZv8cM6EReZ6U6dbrI/\njCG4DxIiL+Oj413bTvuZEYnzVJpxODLMIBAfegujPZ7tQaHoaZqMKZClsNeb\nxCglVFvohbpPJbWF8WW7IqZQoBsIOOd6283D6/mtS1PsfRQn6mz9wHSfva3S\na9GXSFCd8b6ge/28M6Kxeji4zn6K9q0X/Ryupr80dtf8chwam46odXhiCTg4\ng+DbUe6CJ4yujuc0qySuOuFhf57CkVqn0HcMgOMeZ/fy7dDQooJoicYvDawX\ncEKX\r\n=bvi+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDREeqZFhgvnYmSJgZgBg+LavI+iE1PvhI7gSQPCXF3SQIhAO5UydB+R127aFI/GQ+ya4xk8CAFUSIozFzQHiXrfk9b"}]}}, "0.2.0": {"name": "@pkgjs/parseargs", "version": "0.2.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-ruLzuerLGUM+2MDjXMDis71Id9lGAFJfhNMCTc0v8jiATnHtNGLx2lQbLIMnEMxP0sapDkkRH2z4dD48E0F1ww==", "shasum": "88beb3ad8fc52fe20c29f25a1d8721795dc2d3de", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.2.0.tgz", "fileCount": 8, "unpackedSize": 41035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/pG9CRA9TVsSAnZWagAA6yYP/RThBvc+Hm6qaghjSsiP\nugDC4PebUA9U4h+YELOSwhczsQ4PMi4zxbLZsR+Y3dgvSDl/xpp41NsS6X4Y\nzKRHPhrdSsgpOTmMVzybLzTg0jTA7CN7eedvtBhDP4TT1Sslyqy1miGhuI3D\nsTKS7jwi1U6zJ0WIBP3BX2rcU9hgL7l854y6diweSa4NMOmU8IkeescM6+dH\nn0O49CGUhFRYQ6VcrQdEPGIgN6Nc9ylxbAaeHTbnooj6piNC0RrVZgfZjSsH\ncQqbmjuqjEr2H/LZDog6Fk1FlXHyqntG5zdNbk5nWBjNSQZFUTT4phmm5myM\nHL3jctW+yGLmI4JI6zPbFnyf7YXV453xiq8hdvXOnxKZtWtWvqmr95qnyuEo\niaXlFG++06VOJ39P7S5bxgGukopSjnYGeLCCFLEQfdV5KfGswn8BNC3j5ZTx\ncwpAav2jLlx/pUt2HCB9fJuXL0CsfJSQU9RxJu0Bllhtil96RJ1+G4505ZNg\n23Oh6idi2yKosOq03rTDYusPNHt3JjsHOAeqFkD7mt5fqgOLSWmE6pFvGPRg\ngTIcVKXbyaR3u4TBzNL/QfSDI6cqxdgLjylpD7WM+7O8AFhx9H11RPQcNbPN\nH4L9NgcHeo1qpgrTi9BupeuqgFVGBKlJTde9dtu17jdoNxCz1ZkbySIvyoSa\nD3fy\r\n=dAZR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICXArIsFEG6O0sqEtySMzf8oHpTJQSNAzcX3oabn24MPAiA/dHTQbWQ5Al7r8btEnJvUGk0ZpBEwrXaSVxU7SEdDxw=="}]}}, "0.3.0": {"name": "@pkgjs/parseargs", "version": "0.3.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-eOnWMgXvloKFqNsg677z7DIzylIzo2DSPkW13gia046BpfcN8Z0o1Xg+y/tG0HskQbkMkcDY0hkyIZMoeG2qaw==", "shasum": "cebe1e8df3b24e8eaf91227d5a693551dbca26bb", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.3.0.tgz", "fileCount": 8, "unpackedSize": 41511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/9RMCRA9TVsSAnZWagAAafwQAJvO7Y4FAasdybDx3/RE\nIS9HfvtXI4rgY1KW7YMlkdqpbABTApw/F4J7hMJWsGYZQoEuGPW15MwBGcV9\nmv1GV4adFLW3SxelAANo2O4QLWybrjvCPDfHGgKx6tYB3o+7zIqgkdS6pwjH\n9ixVyihi6o0BL37IQbcUiTILobQPdF1e3YFe+IvcBECKsjYB5w38fUbtDbKe\nmQsZJgyx0LIATTEyJ/e5TshW78/iC7zkLO9+NbAaBw/r0wFVQKwlU1uWhN32\nSUrPlRlIZTHSUAO4aU6uBkSruofpMfdpquoAlGGOxjgnC6+gw+n7SXiCM91I\nN3ZUZV4f+83dD5NMsQWT3qTz5E3S6me27e6WBsp/3Y0N5mwYF6f9439/nzRO\nVSFzGuiSOuBXcfJyczK33zjv9tX6/1WTcfXuJzRunLWfrnVDpNACGr/dBIYn\nrAT0SGedOcrVllPr3rZDeRecoNgA167/ljg8IwvFbeCgHqvf5APET54IXpzN\n71R/NAjw7hjH2YiWESH0KMYy7i/NiP7boW3m+gmVRG4dWXCLYZ4IKsMfh6qM\nd4KIN7gp9OhjS7rUTiM19otQOx/InXAFo3qxwH/PcBUnuYualEpOGQXedCNq\nOym8OenB0Fi3A8ukgEbHhBje3+lZ47vQqHLOq2oXQAjsy02iwY6RKwWBI4/e\nhZdG\r\n=clDR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZ8DBsQlTqX6K++P5J5X0OjsDnp/30TyGgRqwp4eNurwIgT2yFBXY+oNmmlpC/ecLMwYBfO3IaZHtAYpVaV1S1GV4="}]}}, "0.4.0": {"name": "@pkgjs/parseargs", "version": "0.4.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-H34NX3+TNjxPG9FwWZVSfi22McfsQSI+PDiM8aU3BhPYZLfkUC9eYurYC7UIKF7bHbVKMpJj6TvdvFH9FareQw==", "shasum": "10b7d0be31201b6608a7c40dd99c481eaa2211df", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.4.0.tgz", "fileCount": 10, "unpackedSize": 50009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiP/o+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr73RAAjOxPgDElXWQCzYpu4iTA4xpqPh3yADmNF2thkUHRmKaEvauZ\r\nn2awfNZunrDVHxs+Xu/shFfBSnfWvqpWkWKrSAbl6apXgPtS8gnKIsScMaw4\r\nfsaLf2Z+BWe7w9GFVUVqZv/S6D9uddTn7GqX0LXxHkXdgucXmU1HX4uJk6aE\r\nGZVBB3zL0LdpibUIyCs1zuTpe4dQgv7WJBcOujxAC9hmyFoJFsgjglztEys0\r\nJiE9qdBVJzD1NL89XJ2soYpEbmq6gQbfaUrzICtZ7g4wkaupsoXdHYIMlUvW\r\nJ4RamI4iu5Inrxjp5UXVgGE/M5ciFdsWjo3PODaSe5c2++05B5VHml+9r6qk\r\ngrPOW6uoMxVTgmHfEr8DBEyyYbehqRfco1JCyRj5hqD068+h3sYnmlHTYWUh\r\nq0bYZ3euNE8uRqx+/w/TZom++m+dDDIGvf77+DJPIuOiXq/4kdXdKEiosIPb\r\nh6ZB2Zoi1k63IpS18evne6SkYDgYQtSHbN5o4ALjVICoNbvxz0gjiYB9+Q72\r\n5lcbkmWsx9DkKty+dm4Ef+2gr2dx4qoiSN8PrB+mw9Di0QK+CEhCWbOCiRvn\r\n2kJvgL/3Vnf+lPmohFosjWQ3x5bRKMztRl8sONQFdEKgnFh7wczRSJufS/4l\r\nz4uEwn9cRa+yQIchEKoqlWNKeXc+64zXxY4=\r\n=3M5k\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBqB2/YFFRXDmpOs5CB4yYvDw/73oWnAhXo8FvqHLO08AiEAuomW2xglNIRdH2N2H0jw3VkwfLIF/+XwqrwTXRFy9I0="}]}}, "0.5.0": {"name": "@pkgjs/parseargs", "version": "0.5.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-ZwPebZgPrVRqgm1fMBwKSVIfmNFNL4dF23tYsSxiYy4syLFgVAhezDwEcR8BPSob3OUwEKihR4vnPHG326ldig==", "shasum": "37a08fc189cb278f1c2e8a6d037e1f9bdf033836", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.5.0.tgz", "fileCount": 10, "unpackedSize": 50763, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEsttus0QaIzMxnGf0IMdXmHAIXro9c2+SvhNc0qYEH6AiEA9raG0KL8prD/S0Y+FqOYD5qIODIKKX4VKy32zLEYaow="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUuzYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiwA//UYNOYt783bGqCH9CTmYKrJbpwNeg5mTilU7rLoW8KZYmJajy\r\nIl24w4pTv0Bycn82x66llR58bq9cqAqOYltvNkZ9wOitOfIuBIAk9yAPhwul\r\ngC7/Nn6rqsht2CpfOrjbNbo1S2JBvvZHYXe4hWZdTAZDZJMpvK42zXtF2OIV\r\neFJHuoP+cr+B+JMsFgLPHNASY1qDben5EfGnwp4lenPEGNB+39GCIORavcvr\r\n/ecog7iYkhRGHExC425j2N8TmSQwaxCEuvbyZoqrcLyrC3RDD4CDkJvK8dCY\r\nnOh+QOhR/6tPW3kr/JOXXyZ7nbw6MhnkFHe/fkunuQIbL4VU5pXzBs8K5OUK\r\nF8zvofKc12wlDMNGfZddups2DdXf9hZZQ6i9O/0jSmr1N72JVdhCgDk4gR3d\r\ntZQSVfogpzjshVtY3Xv9+vCI/t2s+BNEaNBK8ME20c3H2xTa2qEAEkMfZw4a\r\nevSvSfO8fvS/Z0E9RGcDrod9RVe9RCIEJ3rVubn6mcqbKI3GbcE7Rz4d5DQV\r\nQI6JaXF98AzduUmHvDKHXdDAnbu6Kn5sXNyOeOshy7hMas6KYFQ2VknWTiz4\r\niOvSsb6Km+rRZ0M5ECODqJ+WRypcaQBPqFPyHm3O7JaIN7EGOXi5Ux8KsRup\r\nE4H6Sw3fUdvYXQAxqEs0OMj7FUoU/iw5X1E=\r\n=BXaz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.0": {"name": "@pkgjs/parseargs", "version": "0.6.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-fgNrEyVzD2W1yRxmTY3REfAJFkKXVtTSH6Xd4P7iUCxytzfg0ASja4ypdLU8lI47F/eT+cYvpbdareriMHuqUA==", "shasum": "c48f03ec8cbaa879bc5d5b0e9f27e27400c25a20", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.6.0.tgz", "fileCount": 10, "unpackedSize": 51365, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHew1bfF4S0VdMMp2EOdheePSRsBTs7sNbhSo3j3SRjyAiAJSqF3gBcPtYbpWqAuZkrSWXKneFH3tDNXw1lh0fCN1Q=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVCILACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjVA//S3SwhHEYtiUBHKOX8t4OE/Nsy5WvaIULoO7eanwmWQSJBdbr\r\nsKVGLAbLANtSrpb9SzzqA8gOuGFvjTilTFQX2p4oOmKfy/qIovxOYx+s4yLP\r\ndUQB4Okhl4jqU1RGAEZiaoIYCUbm7P6IGTXaCy0TWfZ8zSjXae2KHXoE5qj8\r\nhwywYtoqs5NxIj+VoR+2ivyPgyxnKEfm80xZnX3ym9JLZ+3Dgkz7zKbhj5ea\r\nlPVe+uACcSbDBJ19XCm0YGN2oV09TYJpUiITllRpIjPeUmjAuLDfYJsvX8j7\r\nSbAUbSfUYo+/YbtAp0aOe8FHEEyKo8WqiYUzdOGxcVFDSoGLQraj83TOLD/S\r\nvaXdw6snEsBtsJpJl0corYm4rIDKrXtCE6Y91Ezc2jXBZh8oBtmfzqwSlKbQ\r\nsgk/LsnMm9CWRaYSPtcP8Lba9m/QNW+i4rtOESJ23TjCGU2ydk3EmB0pThk5\r\ngDyvtDbc1PA8AIJvITOm6lFMbtIkEQ7j6ndGGPN9xEbijPwYfa5hTPPj3PrJ\r\nmLBASjGpUwobVAVjYxVnnnMFr8tSDvYI7RrLAqnt6lpMUOw9MiZAPnk970fn\r\nDTmsnusEi/sAX0zRmJo7gvr+YyTqgXjDF6qCVoix7kgC84ju/i4zWK7OIF3l\r\ngfZxs7a48BGUUiGdc7mrLcStxCAiLx94fJ4=\r\n=hzgN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.0": {"name": "@pkgjs/parseargs", "version": "0.7.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-pTjN5oIX/yBCsfXa+mRKqp43Z134nTEO2BpawElQRLjjP/RpUc/O7hb7uvWaS+v2952PDzP82f8YQEKIbeR+pw==", "shasum": "f1ce7f40167afdeca139d959dbf6310f66753d10", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.7.0.tgz", "fileCount": 10, "unpackedSize": 53318, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYdJHBc8wNN5VcD5W/Yw1XGH2L08i+OXAErr56P4NdqgIgZFdbQ3og3CQcTL2k0fjs8odn6FtIp88On8tOPOQe268="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWBNDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvuQ//eRaEv7ReVIKy7OwHWNoBorlWAvm/7oAODtb2MptjZwHMe6z6\r\nq/WHaO4ZyxZ4SC3CpoxxOsTRqXokqBvZ+wGrGzq8oCDe9LA16FqvTGnOV9qJ\r\nHNViWQdsG4/iPXZ2l+xtGBQcec2+WtXoy0dq6FN4Wvn7upIr9viPrJYHsn94\r\nMt3z7coer/B0ONH6mnDSUBWTHY+AfWtF52PwSfFWUqtvVr/28EkybchJwVRH\r\n2TQNnQ7hsJZiGFf19cy0swsbrJJEZaKns79gd0WMp2uEHxb9wKLQvKng2q7H\r\nvn6VgZnXGpVf8srjX+twoSLW5eFm9InRCa4dwA5fbGVvKWnNrlkjml2CcvCS\r\nPcoq/gG5nqgkGU/PStO6QGuDtalsXbXdEpsREUabFTV/mhIRJSx4cbgfYSzs\r\na9L4a8tk3Hzf4n7ezAU5xVkJ0EhHDCNrwqzZLn3ax0D2YMrnWx+SuDM0GIPY\r\nQ0h3mOx92wyEFJY4/A+UXSlkWRSZLaH5W8bQsILxrPTXC5SOvWpGigYixZPb\r\nfdBkSrhizMxRlqFsfAoj1Ma6tis9X/czYMDKF9zOunOGXbE5H1EB9uAmB+ks\r\nYE/7F2ai4Sg3kIUWSzJkC7Bl2KXhzpmvW2wzd+0BqTFVzug8RHNcdNw8a2Ae\r\nnGSZ8q2bi5YZQpn/qYAouDezWv9lJ/IqnmA=\r\n=eaKc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.1": {"name": "@pkgjs/parseargs", "version": "0.7.1", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-GlhsKlO648ZqVtyr/h1guGyt4zpBL6W1Fe4j9tbEbFw/ZJxAaANOdOVerVnaPUkauo83O0mlSBQ2Ghn17c9spQ==", "shasum": "8be8f4628f04bdbc438332cd53c407f90f9faa61", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.7.1.tgz", "fileCount": 10, "unpackedSize": 55721, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCJWtokIGto2zreqJjliEvRuLibghNx9gy9wu77zCLFSQIhAOTdLw7IhK5Ev3D/xxhUTvfE6s5wtkpenJWI9YJZ3LcU"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWff0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3chAAlV4WJYmYtBBptZwE604qjiybJqPrlSEL6D1m4n1gxdeJXLrh\r\nHgb5BsNC6OyetkPE1S+6RO3Wvs60CwBsOG0HwWo31v/r90y6vSdq+cVftGmX\r\nic9FufDzI7ta9DkK/VciAXrUzA2MCmOnrX4Z642UsRFpXfjfhMIX99/BcIHx\r\n1/vvKJmfdHXfnqf91ntHwiuKHt8h7c2CMEU6g+ii/m1uhFMAdJ8egvZ815kn\r\nRZ9g+71y+q/XWHlq05dbdhku/sPzxFk+9xFmFldZQn8vsoCkz8qP2R0XzlaC\r\nEtaipRJTr1ghljhK+hN1wD5gx6cbgWXqVBFtiGvwTe2etHJVZNE6dC8eA1mS\r\n1LttKHrUiWMGTEQhV7CRStVavNa5sX2JJwDujWzFWk6Kf0TqXrzkIxQ/51a5\r\n7/78aeLcn1Sc7N5AvU8XTqKd3FkCACz7G9KRyh2Skvv5K922Nbqok9DvyNca\r\nryeV4oItKllqggEA0HqvAkpcLXYTi1xYRSBfTNNLsAW71Zv34C9EXXuS86FU\r\nO0p2kjpYQLmmvbGobKPgRpO6BNpT5FwyWQ5o4NFoaotSi1poVRmnc8CFlSaU\r\nKYYP9GujMB4zfUlBGD91J+fBhPEX9q4c0VDs1e13B/vz9Ga5hbl7tAVmlWXq\r\nX0z5vJa4+PMfbUtQo8jJuU2VT5rAU7rDPRs=\r\n=untp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.0": {"name": "@pkgjs/parseargs", "version": "0.8.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-ExBgNM5Hg3l6jZMui5Iy59OavbNVHAMXTWLBz9Es6JnzOwZZc1wArVp5GnP6fN23T6rTox5/kXR1EgtJygs+AA==", "shasum": "b9f09a048832b620d3567f382e526c202e5eb79c", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.8.0.tgz", "fileCount": 10, "unpackedSize": 61431, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQaDgWg1tKucmi47NR2wwwJB09Cu64G6/Ppij8XlWMDQIgbtsBt3IQElEpkhv465m3T3Qn7rhryY84F4w3etG84Jc="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigbOkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpv6w//YPE3chsjrI7k/FUn9hf/q/LPTOWfVXIXVfdtbBY9NBvM/8p2\r\nFWTVPbjCAMn6zaS8+Mn40iV+JonQEYlsSpXB9LSxMb5dmVt0ytGp7PGDL0iq\r\nGCNrcnULM1MOrlgqGLjQT8JUDqht1gp932BCimidMNFO4CFOMGnFL62O8Iso\r\nyzBBodUVeonQRHDAGtc02w/ZpbF72MX4EMHLZUfH+6KpS3Z9gQ68u74SVfwl\r\nQY/9wIhYL8Y7kc35wiCoX9uTJfuwsd1XJyDC8riGwakxwXF/Tk8wZcDcVYo1\r\nT62h829ER3xRvN81A2eCJwwPuFPy473Qwx6LbOPPwPNNb4q4/zNvBTQfLPMx\r\nZK2rbp5DoNk3DuYHebrnqAC/pWv9mRLbhiZkIHluRmYsRH9Quv7L1BQif+3o\r\nVDd3ZeGBSMXTq7KCixdL1li/8A+oiC7Z54niUCd6H0bXozTUGUSHcOAfKyXE\r\nPb0ll5cHLhkITmdSStgGiVvm0P4DDnXwh0NU/ofiOJlRKDt2YSEeAkspYGv7\r\njJ17V9rh6ADZPggRbFekI9HALemkT0+G7j1fDuC4xjYqrWLaBrVoy/RBxHVV\r\nmYn4e4yDpYdoJCzZ2CFMWLh091WrPWhyb0+H1ZFu8JBRUAtsXmyURU6zW471\r\nh3XPQ9zPL9NAFE7wFaMYYNG4zpdjlx6gIF8=\r\n=8x/w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.9.0": {"name": "@pkgjs/parseargs", "version": "0.9.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-jUn59xyDdWEOZCRxH+ISzq1nVLLLLbyqX69v5VoiHx3kp22FbkXgj7wrwtkDwjZmQj3rhB3gcWQIC13JH6pd7w==", "shasum": "788e038966e668f91b160b3c4f8ca71a47408374", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.9.0.tgz", "fileCount": 10, "unpackedSize": 60832, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG2IcN926g3MJv/a0aKjcy9h6Ndg4ErtD1PrD4YaCWi7AiEAr00eoTSlhWdgOMt0yG+TBc+iV59LHkG9MFRdFKglF2U="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijSuJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQzA//RlQXkJ8e7lvNIO8QvDMQaghp7KzzWmBGo5gcUTveYrTDZO1V\r\nGbEC9p05LvuId7C4xWYmOKGP8izlJ3EDNZoe/XXz0vX13hvRGbhqFIjXs4ZI\r\n5pILTNdsPu0s8Hr7JqSlALWNjFtjXzETH2kdErZUUumpRZIgC2rNaJvhZrFF\r\nhreRtoXGBrk7mfpyjTts9MyxoKWtJqMUC3oPyZIXrefq3kTPmf0UWHBXMCEh\r\nMeXTbWpK6JcTfd57LnYlhx7ZtFGvPntxq1xnNGk6p7I95eGuvqhgQEWvrapA\r\nUpPWfgCxWh4WLuPhpKtrsJ7y58vRczPWBn52xzFuTuUbQxcRJAr3wZzHHfLY\r\ntLdqlnitYUF6vf04wb3PCN/k7ioNCYghBk+gWG7gwFmmtqv+MFiQl/epndgB\r\ntxayo0aBGRtvqWGOXwLh9CmoeD2+RFXKfK1wy9H0c2isO4QAALRQlXniB6gl\r\n3mBR7KterneRfwxAtg2C215GwbDZxLRetLmIYyOztkzE4NlDfuL+pXpbXrAX\r\nfK/MSnzs09XEJOOSdYIpctIrwYdR8oE1IX4gpYUOjh4DQg7WcuZoO75HHIxI\r\niaBG2ySGF2jkr4FzFcm8jAnKrVu5BjJIFhDFfmRzACKU/ijwE307ZceLMDGH\r\nV3LGd9R1CObV9brz7vnXr3uEsJlnstBSepo=\r\n=hfMg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.9.1": {"name": "@pkgjs/parseargs", "version": "0.9.1", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-7CTaA2hsAZ7QgtR6ZB78fyBu3U2kNMwZg/YeeLK8aQMsn73qXIQfbaRJf1ccusTCJJEBRwAIzH6O91jdOyzIDg==", "shasum": "062325409e76aaf71950b7bdfb4f03da1b30afb8", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.9.1.tgz", "fileCount": 11, "unpackedSize": 61038, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeuzTdWJFWOKmzdwEeA+nBIZlWFCR/YIQKbNDrHkk6ogIgRPXBmEGIFCD20vBv2S4Y92diqbD4LvrEouCU1MXeZms="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisMNtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUYA//U+rsqYO8eVU92R5ljpqtkF3tn86eSodHRO9JWi8kIPkW0U6Y\r\n23kSoI37kzhfocGKZ6TMrxDdTYusKXHfyYSwwDs6Ag0rvJXNqN4iTZ3PvDJ9\r\nLZ5hVOu1o/xKzRtRcqh01Rci1fCqmlcLejvLKlJlnA1yQH5qlLrVCW51ZKL3\r\nvpXxs3cduiQzjezkGQ1MIdGRMvigHmLh+3dFHfQcMemoEWgILANRQjaBScac\r\n16MEuM3P68ByboDXv52Va7I21IH9DzFsKHsijFD9h4JeMWXUuWuEhzq2RBv8\r\nxUWwYHxpHdq+hLkaWMo35g4A3x7/wcH7NERysIGSgRZAkIrCQXzJgB/wkCXZ\r\n+eWqxPMt9ZUvsYfZ/9ZPSeiCFLaUH0aALMgVOSawv4i7CksDnvWpldLyhXlK\r\nXcIhpt2NTuYDTc4ORIozpUicf9nJQlxTf6xH4oIqLYBxFdM4YLrGagxW7/9F\r\n/so8nvwaW47KQUELO+toZzCT+1hNAYbk2nXPuKIJ3rfgNIR37MbZJVy+g5Ym\r\nAYIlihJNZO6BQ9hkwmjlFtK/ogI4uIluiz6DHAyrWMvOZ4hWoJC5SvY1O3TJ\r\nYL83NgC38HSFPT6+eMqx56BIXZi1BpCa7JlaMDeN8FK0Z3NowfnYXeU/7xGs\r\n6O3J0fLX8b+cHAeB2J7Zg7j0CTmYMqCS+lE=\r\n=yBtg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "0.10.0": {"name": "@pkgjs/parseargs", "version": "0.10.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-gcLFrTC3mgIWeDk+eJySEz9unk8XNKi0i2lyppi36qOrW54wBEtOVdGle71ssm5E+vauNwyGKQuWJ2psgTYRVQ==", "shasum": "02605e33f8bc4d76efae9b660edc67902f9a0396", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.10.0.tgz", "fileCount": 13, "unpackedSize": 66409, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZZ33vhm43/dEnVCRnNlD/f519FyXBv/9CurTtcx2mHAIhAO7lHKqt5VSCtBtQvtNfFaVTmLKH0dGvw85ICS/NCwyo"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4fyiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjkQ//ccrmhpFeJLryxPKVWlhUeTufq9oVUkpV469pSux1ikGY5H+M\r\nIYzc24uZ01LbRbDsoFiZVYMphVkkNN/SR5dzr0wLbZ3fGFHuUt7zDJaJ1IuO\r\nJQ2pEsI3VMqJYOjq12P3tc6RgRKZ8h/0Y4qt2nzw//Sr5OSR6SNFu+acbvUb\r\nid0Tb/8Gib1/eUMwjpWERKanOvbkqjao8clReGQ7adjfCEYelP2PArEb7N3V\r\nqa7Z1C4a06kxxkdQF8oIj0WOsgVP5DIa5jlPFQe6IElBSbHl8MPxFMF1tbc3\r\nmM3hdiAuEVJa9h0XdKZ99BdMD+zvPSBIwtapkLpJJ7cv+ZSLehqI6U+MuBCN\r\ngFf/xObVyRFen6s4vGFDUmo1iBYSZK861Dyv+nZ6M0Gdua6yZBUTDROtgsa4\r\nFFaQlD9JJHCAYQHmBseA8Qh+35LO9VJnY/64NTKnqKkJzilB3binyDA2GQYX\r\n//J29mJcB4bpPUiQxRHyPMD2xY+AiLgX4PY4mh1Y7ud5K7UzyRmyAdW45zi6\r\ncc+RfRnu4SipeZderK7nxxhqMI1XXTYNWb7+/viVVG0jqnHHcgdPNQ9IwUWN\r\nxbJpCfkzGo+YU5ZptND9/8ofkVfidyCDY9x7iU6woXYrcDbFe00RC5kZprbj\r\nvxMB1hf1PzJRdm4ejVcWKnxl43RFjbbG8rw=\r\n=wuuf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "0.11.0": {"name": "@pkgjs/parseargs", "version": "0.11.0", "devDependencies": {"c8": "^7.10.0", "eslint": "^8.2.0", "eslint-plugin-node-core": "github:iansu/eslint-plugin-node-core", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==", "shasum": "a77ea742fab25775145434eb1d2328cf5013ac33", "tarball": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "fileCount": 17, "unpackedSize": 74173, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDvG/+saUhufCRZDSAMyqIZHb0x4c7N/gUvaev0PjFqHQIhAP3fAOwBeiw8NWaXAwTHTxmCZ5hxLTuFeTKtFBuLZrUz"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRCnEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxPw//dpGdahKANuMATGuciM2D4V7hb2Xqh0j+fxQ6/f2YX9aVpciE\r\nthUd8xPNqjUY0XmJsVLT+6fyLMDuZQp5rujRujWSaYqP/wg8bf17iwKdPPEH\r\n9u8q3cBy8nZZ1/JGRoz5Tc4oH6BKIy35Grl7W2eyFBORHHfzGmqQ4xVXM67H\r\nA0kuU6fZmMzZ1wajInzTdRBFDtjFEzuFhd/86Izk1kXhPOosj6xPn2QgBkD0\r\n9q7+Hfw6sY4GJ4ZWAHBORZ4iMATPUWN5w0YcFUozkFFtSlBCDAUsjCHJMsII\r\naWSgySSTIsXLDxqTTeG27umBfJ22TxxC4zh8wNM0rbAXMFmJv7Sa3w0U27aY\r\nN6P+4B77iMeBYi9rpz4QdQiQrymC4DhH3nA1UQSmAXbxYlqVNLTs4vnLgq2l\r\nV9qCxnJoPfuE4KWgVlpnluoII3RtYdzwRLAtKdeGpyMMmCvi8TyrWxLbH2Xs\r\n3Sg96U0LEtriD6RBwat870K2nfmDj3jTtLqZQPmcuXq7DLv7K5UmLDimQXDq\r\nwLq86PmPPsiPevflOjTrXUGhZh0NwCB5VyXOHDuLsAtmz2lqm8psdhya/XKA\r\nBquEVg30l7bP/Y1+fTTQWXzmJDcCeSDH7Xnez2wGYaYxJWeaUGOMmzjz9CKE\r\n6e0EV7qjDa8wUCxk84OCu8C0zsqpHeSp+XY=\r\n=vsby\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}}, "modified": "2023-07-12T19:06:07.630Z", "cachedAt": 1750172042335}