{"name": "@humanwhocodes/module-importer", "dist-tags": {"latest": "1.0.1"}, "versions": {"1.0.0": {"name": "@humanwhocodes/module-importer", "version": "1.0.0", "devDependencies": {"@types/node": "^18.7.6", "c8": "7.12.0", "chai": "4.3.6", "eslint": "8.21.0", "lint-staged": "13.0.3", "mocha": "9.2.2", "rollup": "2.77.2", "typescript": "4.7.4", "yorkie": "2.0.0"}, "dist": {"integrity": "sha512-LXSM+yMY+nTKUTM7Wws2mYXeob5T0oM4SVarpuLvIsYN0mGg86rM+Rwky900Ug9BfGLkjhRuPL22x+h51tHTZQ==", "shasum": "6ef4759409cad2de4c9c183d6265e177bf0b41a5", "tarball": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.0.tgz", "fileCount": 7, "unpackedSize": 21289, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtTE5/bcI94sar7HCXuK4alDrTVx9sQeBJdQIzoi0blAIhAIAxVzEGpNSMiW0V5xXiVDjBDQgpNpiaCWkNsHlNKqqA"}], "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/UBiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNuBAApP9+AdsrCs+792S/NKRiBrQ6RZPXiuiZGYTCLiE/I6/f74vU\r\nnwqkJXQSXI8G38mSAJjetyQjcNLuUIEmNEOuOVCLT18YfMB9McCxWR6+ogKX\r\nFIegCK8UDdtK6Y8jcoGVNPf3cgp6dBo48FcF382JHyEfHlMxhWeBeA3067wt\r\nKWC2KK/xVljzicKbG3Cr/ZQKoGVV00R9MWAJjHrrcVtTmQPfm+piV0i5jjVp\r\nk8OG3PRAooc8Y7hGlPHNtBmcY8zwXK/9tv+84/89fjBGa3CXLyuItLLvmMv1\r\n0+vlpUN/mvLwe+qwbASawiH0DTfzwEGf5xmPRjoXQEXZYvbtrJUrW5Fv+R6Y\r\n7fSBf0k59jGYpoOlynr2gu8awmQOmOF1iddtYQUFnkOISCbkoVFb3dPoenPx\r\ns/I+C3/gVBCEdMtTTUqahrKkklKSIWcsai1MMpRS9FncKdh85I4ua3TpFeBS\r\nakLHAbg4ZpuCZyCfEiN+dOCvK9NVdgR/4hjL11KG/+8O2vZJZfCgAnF8jTRd\r\nb0Sgk043PYdpYiWBKjDbqOEy4DNMq6v861mHR3rSneUVC4mP8mFPQpZxCz+Y\r\nkcDUCqfjO8AFxLmJb8cdBXQ28CW2auxmcmflRx5JjlFMzxAL8/hz3HDMrV9/\r\nutaIITRH/Sw5TYWWWiHDzDH9pYe3OjNDt5w=\r\n=z+XP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "1.0.1": {"name": "@humanwhocodes/module-importer", "version": "1.0.1", "devDependencies": {"@types/node": "^18.7.6", "c8": "7.12.0", "chai": "4.3.6", "eslint": "8.22.0", "lint-staged": "13.0.3", "mocha": "9.2.2", "rollup": "2.78.0", "typescript": "4.7.4", "yorkie": "2.0.0"}, "dist": {"integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==", "shasum": "af5b2691a22b44be847b0ca81641c5fb6ad0172c", "tarball": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "fileCount": 10, "unpackedSize": 21177, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAwEl7WYJX3Nf8MCSAGIsWc7dmTYhXbUbFMj+7gt82dVAiAH2f6o/LCDJ1RKpoHNaY6KdMNRscLLX4fNckGCLO/zow=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/n0nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0Bw//TMMh4MKPGZsdB9qsox+zBU4F/23x4OR7Nk8ms16Pf7rmo3Jp\r\nmFCWE+2AQQEMowkrG1LcgJZCYxO0qgbd0szro/9sJHOXRP8dWXCd8/Ken5EC\r\nKK4Z2hAaTz017pjmWAxRCxWs3OdRSH3QmXOuS7s3SbwBv5mEWXUHwdFVlkMU\r\nK1/oTvIdjPSp9ETvL6ACCb7/U/wowjSyDClTqA26Hoa8ClHUy8IpcGQSRkNP\r\ntnbWArgGch4ko/uw+B67FZIHaGhykqIQhE7stkrNDexPSlIl/Zf6iy7UF+Fo\r\nThsCnIG153/A0fQcaYTiyknM5JdSr8bjuDDhFzwP8boLO+XTL3v1Ofuaib0l\r\noKc5uC2vSklWNffyMVFUQJScvD5dndU9w5OXs/79yDOj36bfeJiLyfpBLoA2\r\n2407ayxENXDdxzXjPY6gUoVsN4EOERO+H0R3TspG91ESanXyIus63ASYD2Yr\r\n37h/hS+jZy41jRPTNzYKUgYLJoyoShQSLsx7HsRG/AP/qmDLz1xLhVlfMsuU\r\nq+oMsxgweuBGu56FELCOUkBj10WA+5z7LvfBc95v7Zv4L/QFeFXwe7fZ3D9X\r\nW2PhYmW6xaUTrpYS+UFfPt34ux9MWdFgs/gerFwIX5kd8AElMwaMJEw3Qn6h\r\n/l9IShNEAZxgO93N0kseJGP+QHPVVYCmkoo=\r\n=YWcW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}}, "modified": "2023-06-22T16:31:11.465Z", "cachedAt": 1750172037982}