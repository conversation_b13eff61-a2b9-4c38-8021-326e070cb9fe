{"name": "@humanwhocodes/config-array", "dist-tags": {"latest": "0.13.0"}, "versions": {"0.1.0": {"name": "@humanwhocodes/config-array", "version": "0.1.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.1.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3"}, "dist": {"shasum": "77f5c252be2fafc73f97bacf8c8389e171df3e33", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.1.0.tgz", "fileCount": 4, "integrity": "sha512-l4vgcmmismTGHIRXO4eIjfZnZmJCKZ6DQNni34LYmkGmLceofFs8i08dEr/zvRoUd1qk2BQqXulFFUTjL2PNDQ==", "signatures": [{"sig": "MEUCIC0pyBOKoUTbQ2b5aiqEOC7aF9hiM+n/5qBWnEaOCNjNAiEA6MMGZ3AsXIzFO6DNr4HyH77mW8EVK/wqjFNqXJbl68Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3DE2CRA9TVsSAnZWagAATwkP/i76AeCtBji8W3o/IcuH\n07Sk5dFvCMc8RandZAKpy0RBKa++XWN8kQNKL8dqgU55svaTUxBbcyyHpQI4\ntIy6MQxTypGV77pb0UOLgXoIReXN1T8QtvyRyn2zPttZwhQ4gX2EsUaFHtY2\nID7E2JzuuVDtB81w511tsnNXj+dxcsp92OR2/dBFce0cCEfoQVlMMSC7Bped\nNxOHMcX+6mITDPkhcHxp9hkM3Sr0BgMQ0KbKeEerySCf8Zod9Ky8cCkOaY0X\nuiQRrJODDeBGlymCom0KFFbOgTBTB8xd8xBNk2jhGAKahd+EvNQ7X5xsVA2Y\nD/KRj4OD62G9F2vz6Av3vym8eIOiWk1z8GCztzeJdNa+hzhuEh9JHSK4YD4m\nmSU8ZViB18i46MkU5mpSOOeB/NoIdGRNy5buW2cNT6YMAAomoyLony7etndD\n5Bm+jdWZOqs5jRfET9qnMjTI4Dw75BXdEbpy139Ekd/h69wkfcZ4XH8Ht9+z\nT7L/mz0YgCjgToi0h/Eqerp4AdZEQjz9uYxlGABR7TXKg8pWy5Inv+C/nbge\n+PdY1h7nAj8h7FSadLZIUUF/mlyGxAaPByJu9kB8BzARnWmly+Yfn5+YKcpx\nhX9Yvo71tYB6N1M/5hl6zK0WmIpLJxO4AXInekFz/2z9DGS9F0Mict61RzP3\njqN4\r\n=mkv0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.2.0": {"name": "@humanwhocodes/config-array", "version": "0.2.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.1.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3"}, "dist": {"shasum": "40e90c7133f6af2f47504af1cf5b5d406549b60d", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.2.0.tgz", "fileCount": 4, "integrity": "sha512-vfwTTBF/rVlFX4rWlqhYKqz4FaLs4MyVsA2Rf/LG6TP5zMFwyBaatvT+V3OgZTmptnGgmMs/Dh/omklRls21tA==", "signatures": [{"sig": "MEUCIFu+GI+Bhbv/DPysQuqYy1yOIFrp3Zjybwfwj0MEOTwNAiEAz1xI4Cr+3jJZLYaKeTPIRclemur4o8omd3xxNA4vDLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezBQnCRA9TVsSAnZWagAAK30P/R7XoCqTLmViBh+KIRD2\nxEa1++WtCt4jOE71IXgOXJPQskLqjLU3wh9y0e5kbLxA+0EpAJ7hi+FDZYFU\nHRgyDX/kCvx5kYaiELcOlyUkKe2fr8likYw/VnV573+1spQNc99dXzvwORXL\noG9YBGWpgJEncRyQss1lQGgIy/utKfTqzlv+qxDaAxj5OqCeyo+O9B+hqaDl\nWlBb9lkDb3XM0tJVuFA7FL1FGQiws2DqSuDmWn+dLnxGCZVfRbhlcngt6jEs\nB5evbgQp59kGVXEiOZ6aLbFhGM1J2lrN0Dol0p20bSMEZ1kiEWIJqg9DSBnp\nD44YNgIzpX22nLbQbD5YeBPHvX/rfx4WpQmBQAdka6cX3JZFrf5iLcjgtX8L\ncRwFIl0SUzsBggLbelJCqykuXc8DeR05cr3IPMvdqMRKxK5ZQZc2YKAi9H1h\nVYI3NPYgEXM3pySCR26xqu78625vdcCMO3lAPuXgqVf/wRkUSOfSc8IG8qt4\n+qitQaDv2/w3n/H63DEJ24ME07rtOTl0cjYMz1/XbG+3quUDdMGeJmtXs+7g\nh6jGMLd1IUTWna/V8lGS5U+un+mFJYkqTVqFN9cIPBixOgelGI699CS+pVcK\nrC3QlP5E9Hk7cQV5zju4VWsO8jexmfHVAMSstuQRHPW10eWuWEB0HTDX+hZl\n7twJ\r\n=lZkb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.2.1": {"name": "@humanwhocodes/config-array", "version": "0.2.1", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.1.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3"}, "dist": {"shasum": "956d82f4a1ca301496cb55b68d29ba0adade4d5b", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.2.1.tgz", "fileCount": 4, "integrity": "sha512-bdFHabuMzqNUl4DmJ8oKY27E2IK1Mmt4/YKGBprYwU4nz4w6KslMyf7kTvr4aM4Y6u8CnxMf3pmsqoo/x7Ga4A==", "signatures": [{"sig": "MEQCIDkjn9ABVxIpVWQy5RgNoQCLVmvdR/xAYSlYSBDV9iAdAiAlL3OPgiFG7e5UHGyuXv5CPr9zqSXpkwgIWYrxkp4pVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0UMZCRA9TVsSAnZWagAAD+4P/122PkOiur/Ei03vGcjW\ntM6lQB9meB/ghCH1wapD7lA8QrG2gwuxJBLisPOWPAVbJt5U7feIrnrxEmEP\n6sgN5R2Gos0uc/aqabZ7s1VXToHiEW2M5Z6ZCssVx6LceICxKr0eWKOVX6gY\nFg8QbvXO/5SVO20oSFDV0RjVGNSh9/+rLN4KuMxE4qfFC7g/jTn12N42EziY\nO8X8v/OWn5RCTMR+8mc4FMOEupyenSjQ2uRmxpVWzzvuvTNBvwszNfN75Ery\nC5VSfGGTW7mXEoOoO6JWgbTmMyUj1bqSgXJAd4gGd35NIdEu452/KIq5DV7Z\nd5IzQlJlY4Yv/+wbd06ZcTrVVEFk80htSeUF7GDe6RnwS5j4Ysh7o8XePXkA\nsMZxP3OFw1RlMYYmfUlLWZwXn9rewFudlIlnmBcRsG3Rte/3Wh36GnQYM6MN\nOlB7HvG3KVWtOFw1etU/Q3XzYum55qtyHHWo/AgCepyHfH8cwQPYd/N1kRpU\nN5zLS/EBOdSDh2wRTnS/6hCMBVwkJUSKe7e4kAcStfaNVYOkKVaBGRy95uK1\njJvd3/lgr/23UPoxGs0PlMJ6Uu3BQ3BbMTbx9pdrSNpXAuJZW0YiR8eq3b0x\nJhLK/M203cQ0/z1RogoDOxv8U19h0H0mUEGIVRd2qx7xFAx/TM1wqFw0QKax\nT5H0\r\n=2bpW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.2.2": {"name": "@humanwhocodes/config-array", "version": "0.2.2", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.1.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3"}, "dist": {"shasum": "f3e6ddfdfac2e8c623bb9e32ba7d14e18998186c", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.2.2.tgz", "fileCount": 4, "integrity": "sha512-rTJvRRAy0YcGp0ARr6/0C2y/XRLjfeckZTL/SffMtW5BT4OyRfkWrgRHtX00ygKi3vwPAnoea4cIvNowQChTdQ==", "signatures": [{"sig": "MEYCIQCBcyZwpTSMQ+qexjQGNRZNIyTxlLl6YLWolVlyFR8EvwIhAMeVeYY5ZvJZ1GTQlv8MVN3yL1ko7rbnfH83vzEyHKbz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34092, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2AYXCRA9TVsSAnZWagAAuE0P/jB5JnVFYPs9PtZlSYYQ\n8u9Y4TISzRgWAoi6cvOkB6rDYy6CY9abpMG3kVQJJcaWCiETWRPmLAPW/Ys1\njWWDyxo+lv/Fb7B2UQ1BmspK2msBoNs3kjNb3BFg7L0uxAdsEaIcSnQH0G9D\noY+FQ8QoV4Y2homq4yNfaTL8cstBfPlBgNdBAbxWy3HGnWoUMa8qFuxaXHcs\nJ3kj6IJXI0ekQmy7YgqDEuOe9PuVlXycKDuuduzrNMlgRPmbVTo5vS2ubJ43\njfW4tGjNnKmC/+EKC2E58cgDQdK50d5Q0JjDgHoSHq3u/Mza2DN/u9Zhl1yA\nqJb7T2bf5yADh+m2+nY5KZjcFpOQD0AgOVPlFonAM/3HMszGOAiT87dk0HBv\nG45+Wck7JkPFtI58EwWZFgxDfFR/qvzXl9SQa0Ks+LJX0oOID0WnXLIhHvme\neVUpq87kIYqh3pjngX++NnlgbtDhbsVd8frOftpAOmMlZ6IF3A1ZYGX3wvJp\n5OLGjQScE6RoyitF3Cm3wSHFqZJ6epI0kAugN3AxGETigkMrSMSNrmsnDphl\n+t3ls0M7pdLbkntDZfOkFZX2yezeRtfvodnsmYSC2w1zxwYRzJ54CZMuPv4u\nB9/QC6h/msuDtzDonKalA3QGiwwEXl2S1tZ7hjirN0OBoosSYMoVNXT19Dir\nFWy3\r\n=AEHp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.3.0": {"name": "@humanwhocodes/config-array", "version": "0.3.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.1.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "67a84386619bdac9a9159f4d745044e2341ffa38", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.3.0.tgz", "fileCount": 4, "integrity": "sha512-a+TWMEAllXYiYUmsYZSugalmxGQ0budLrRIF/IiBauK5XLYgIyNIYmBCkQdxA4aBv/+lrxOpeYQvjWViPEvneA==", "signatures": [{"sig": "MEUCIQCVd9uGBh1FQou4GBj6BGZtub7qTF/tciXDO7/lA3qzFAIgNeLZip/GZ3ucAdpmrPKjWipHoERdW+xYyTRUEQ5p/Go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU5gBCRA9TVsSAnZWagAASuoQAJ5wdIlWTNN//0J+MioB\njchycyPoxPpiSFhiGjaR7mFdtQ0hdcCNRnf9ylyqCvgqlX+27V/62j7FJDHh\nfLib8tcSvkv3Dr+3V+QfhQ+st3T7wlTjQLsRYUfqmad8dCBw38F70jiz0ZS6\nbh0T0TWXnlShdXH6ta7d4GVu1AbSx4U7uhycc+E0t4Fq6enLlBEKWUoo7SYT\nIpc2opoeksjkU0OebNqyRwObLSFJ1j8ISsphoUw+qDjnydFQvp50Yu5RX4zQ\nPG1YPVYi84zoJlR+NohzpDP8DgpdGBze0+K8vDGwR78pW+8V6qD9eftT6mp7\ndgXVFmNHsqdHe0iFNTXv95CDwbTnUqKvccjZX8LR1zh5crH4h7OhrWVS8Ic1\n/UB+6P+p7AKIo0i/MS54KB2hxiaG5RHRCVqdi2hAoCJa6F04yu/qfUsNJ8U7\neqwS+Qsc6jLyCApMlN+9GtCz/iDF7lsu287pg/jHks5VgsuTHbksDP9c5Hts\nAeVpnAO6ykKlcHHm1SY1lXYVQnB+wbiSy4DErt53B3RmgZ2lE9KOZqp88h4Q\nxsQG7rjrRvgyJYqOfDuSZrB95uDgfTjaj5RDprv1OziXwp6s06Y+CpocanPf\nPQfRkUyk1UbI7/vROb6NilLz+ecU9OVSglkloZBZ7lBlKLq0IMuhiHBMsPlp\nrpgn\r\n=lbBY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.3.1": {"name": "@humanwhocodes/config-array", "version": "0.3.1", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.1.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "3b593f413db510bdde3b9f1dd8b42b8171748987", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.3.1.tgz", "fileCount": 4, "integrity": "sha512-qcXXKyUUCfzg5g1BvNY08AwOLifQkRB0leDZ1NCufOv2yvanziwdg164YGBYlcMdyfl13Vac4Vau7jysH6zyjg==", "signatures": [{"sig": "MEQCIHXgeHkSKqUUpHslbApgljw6X6PnY6dUUWPWaSaz3VSiAiBzZIi3TpBBXoJ/JuVzNH/6+EbWGy0Im0xB0jXCa0rvKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVVbTCRA9TVsSAnZWagAAt4IQAImqvhuyEybZrG09fSnb\nzKA0iD2BOvlIeA83vC17jqhBqg/mz3y3+yTrcgxa4eWPOQ/oNsbZtmKMmNrP\n1QGBG98Ul6yfUvYC2N1GBvrDV08vEJELiV932P842lDiVZXg4Rr/UgWTV4JN\nfV2hx625NA7oaz26zE3Eo7eK63Htkm54YnKxxSn2mYhwj0xoMwCIGbzhe2ou\nI9ciw5otLBwYsz3rBQ5YcOwXK/WLrlf4rq3LylwgSdZCOeDtGVvYMhKjA0SU\nv6boo8mBzQbrr6ZRPhCmEQk48PUiUu64BhMCJUayi6vNxzJ8L1JHThIsRjN6\nTNIkrlSlkEzZub5xG45jBAKoSRGd42Ve1NxCtf8rWSAgQs/GUFDQ69ty9nuf\nnKb4R4fUdMXUIiNfrxqbSVy7qxr9t0CxKO62gdqS4kGUFybHMF0KEv+lA+Pf\nOaE56uS+FWovgXE5hUgDpH4JIEx37wjwQTBnQezRKv2Tm/a4slWnjvGwG70i\nwqcYI8lB2DWOjx83k0c6rC89r58JgKKJZbqBq1Bbt+AsHcdL6Mwbe3zlClCj\nNC2cStWT4pj5glQCAwY8ZPAlxl2teqIhvJO6nSVNxecBPrJzxZhGtrrX8YWK\noHY3VYlApz1WeuKdYukFQ/aDWlcLRb17xkuL5LIBWwOTs/7828Flzt5cb3Vn\nKRdT\r\n=8wX0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.3.2": {"name": "@humanwhocodes/config-array", "version": "0.3.2", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.0"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "9a3344debcab1d2ac8931bd94ee8d12bae6ad694", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.3.2.tgz", "fileCount": 4, "integrity": "sha512-5rnSWurNGQMVlQVefYJ1zLXO4ArMxUaVy00D/r0AeKOASpl6rmWecE4bdPHhMW+GKDwxHI/BAT/lnBhefe499Q==", "signatures": [{"sig": "MEQCIA48FPu7v5bgqgh+QZgrZu6wrSh4C4wZTap2oMavcIWxAiB91Fgp5YYMGrxqt1RSeO6F7tsZ7dZsQWSlFHdRTpWVBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVVhACRA9TVsSAnZWagAAZCQP/ieTzT2NOggpvwfe4zG/\nhf87Lax//cico3bp9PsuRJ6H6wD4rsaT5MWhja6Y/kXCSHW39DWVvdUsu5aA\noGVajkng+dCUE/T2Hez+MO5Odi2yHKiCSpp4dkMgwHrLbogD7jt49F2ICMGV\n8BmvyojX3iwr31kyz1+igRcNlrOC//68eSHlUcr+7jbANdG3yBGK2iSS2dlc\n+yX7sLdabo4JpCTeGEJbjsOC51jrzKIP7mmCkr4h/kMRj3awFXALrtkAhRZ6\nzhfCU8OTqP0LM0pBIWijFpI5Y6/3Ec7VBd3qOy7VU2w13DQwBW7r3r+yBBle\nWgSx1vtlJlrT5rrJ5qoQhfAjyiUeB0Mm6mo0T349nsBXKNPuRT9jQVJ8yqc0\nT9n6vcmmakdx+hd0PTuKJVpRK114IFLzobj4TYa20ImawBS50jdzJJJATS7i\n+zgcyp7D9CHrPW1GE73apgohWQzVtZur5RH1QjFDgV/n75yIjjKoqDCVuZDI\n8DymosAWZvOYeMn/oQFIFcpfrqjZIvVssnkBcAwad37ILqOs/yhHnipwQuDJ\novga6DrJ1fid7wBLGah/NUNrJJeBDuGGM5QvjxNSzItAKgU5S6b3qeae2q2m\n683lIRLl7r+iK00DSsNN1NDdpmjOGsXnsTFlcejXCMQUsHloJh296zfcNGVI\n0q3b\r\n=vLD+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.4.0": {"name": "@humanwhocodes/config-array", "version": "0.4.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.0"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "85ad1ddf3aa36150fa74ef5874bb29f304cffa2b", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.4.0.tgz", "fileCount": 4, "integrity": "sha512-upiIKs0pgRAJ9eQvmsFTfQWiFuUoaN1cviDgid0Lsu1fcNSr2iX7wUnZLlljE8QmCVK2xSXE47uvQ2meA5W1gg==", "signatures": [{"sig": "MEUCIBv7JXiAJt4OHEVQlOyHjK7kEqQE0JrzpiK7vtcZyQ9+AiEApJuWSb7NgiIr9TI3kHFvQCj0N/TW0YT6yi2IMN+i5Mg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJga1U5CRA9TVsSAnZWagAAx40P/3suvlBZsM8im5doCXfL\n6vjB5KKLzk1RiHwmS3AtVlkGwNYmvoi/3DNZFwiOX1UgI4RLQ4ccQMoinJ7C\n9P3HOPr36DNFLSM6FR8a/bIebANOdLW4DHcjcMjAmNs6rm/PDBzP6EMJGfet\n4ZaFdqNRLA/AmFeSlntQ3JMlULokO+H40OrJ/Zpz1n22yu7yO4vHbpqpEW6f\n/eK6O77JFZSIlo2mfT9eu0aXE6NH5+liqYCGtDm/F4cQ+d2/c5lydTUqhW5M\nJBLi0+f1FqU64hTp60mUclvUwyKeMgHCdWPvR7roZITRT5Ihu7JVh4whqdxF\nE7GXzF0WA5nhYJ46dZsJWrlhyYPLey1ntgOQjiaehReTNQPhFb6JRCU0LS8a\nPbJkoF2fRpOZBKSA8bcFFSAVtPeaDFRCs53DQywl3Ib5D5z8d0zEm/yxWhiM\nToC2j7wyrFo4gZz0MQRHynG0VRbyIFTMuAK0Jxyc/Tnv2QP0U/+McSOV537s\nlukmrYIFm3kQnw1DQ9bIy78MlzS/S78GQWerXGI1yoIeqgQrtDOtub8s1JuL\nvYz0n1wmfi01fWYRYjUjaQR10a1CTow1+Uf3usOHkR2HkhCCN3dQmGXMIxVp\nrOR/yqeOLG/q+hhY9VQbRW6P9emO3rAvERnvMJ8RW4+sEIbLYzPjqSkUZxFf\njikK\r\n=90LH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.5.0": {"name": "@humanwhocodes/config-array", "version": "0.5.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.0"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "1407967d4c6eecd7388f83acf1eaf4d0c6e58ef9", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.5.0.tgz", "fileCount": 4, "integrity": "sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg==", "signatures": [{"sig": "MEUCIHAG5hMlQqLD2WyBR2pDyfYo5fshxHjaf8cZ1g/dtSFbAiEA4pHFlgijCIKv5oePJe7SygaLeE41WHSLDVMEaIpMw1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdc9QCRA9TVsSAnZWagAATIUP/0yqWsGcIXxL1W+H8Xd/\nFpHeIuKxSolOd8svm8Ocgaox64V374G+0jXEsK3V81/ueBsorrQYHxd0Ucb9\nEklpC9PwyYqj7eWDJ6m0Qz6G6bkYBzutBeTlG6bi6VIdrbYAXYJgxxWc7K4z\nEHLZDaEX3OXua07stGNtTeeqfzaEaL9CmrYCvzoLdrBvXeNNc70KPpgvakIX\nbRTqNqSI/11x/N6Jw2W95+36f+ik4W/zZdsJ0o+RjbaCrlBMwAMJRmVlvsfa\neVTJDoRNw3Y4fDE7GbNAECgTljxyxaTjjiHd+y5kqrquljIO0uvQzsjdV4/6\n3CIFejNqHSKLDwg59M6KtiNnbxHKK6U53HKywksOibOSXyneGKgr4hgHyou9\nASb7B6SYldsut3rHB75boCB+IhdGwGzI7T2so/fKECax/zLpok5kJFNSlXeP\nUsCZM6GrzyeLe8dt1ZjiUd9ytlNgio+Dv1ovo9GxMuns8lSUfBnPpQdgxqZL\nuLVJGRBGXLabr1BvmNpYihOCKep62t1mC3RiRAp13oQ9BFfXjEUhahW1pLCx\ncxtoMtILuufzFmwXHWHlsqjngYsCTphDM0rRrVr5ZUvdAqNiu7ZyWxNccow0\nUDrADB6A7Rei6yjtvjh+P1TnG/NIonHIODfQ97uPOLW9Pc7t8AHLB6ozDXpJ\nhZEE\r\n=n937\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.6.0": {"name": "@humanwhocodes/config-array", "version": "0.6.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.0"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.1.4", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "b5621fdb3b32309d2d16575456cbc277fa8f021a", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.6.0.tgz", "fileCount": 5, "integrity": "sha512-J<PERSON>lEKbcgEUjBFhLIF4iqM7u/9lwgHRBcpHrmUNCALK0Q3amXN6lxdoXLnF0sm11E9VqTmBALR87IlUg1bZ8A9A==", "signatures": [{"sig": "MEYCIQDeKkoCABcy3apG2BVK0AxjgVFsN/C5KJkZBUk9md6rKwIhAPPfkLSQSABRr04jWA112XkzRJ7sCFXiMq9F+UgYaS5O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfxPxCRA9TVsSAnZWagAAJoYP/0vZrzYFdbkC4zDp6eTz\nDxWHlYSG9G5ERERfrrlk3q6r3aJVVtlLLc+ADdKNWharXSgkTQufYXiE2dxs\nHGNTo5aVrEE1NTDj9J1BxrD6H/SAOOB/tksazvcUoZQHx4jyqGJHNvHRTBno\napqSdqVNs4ZZPsjtdOzGy91bKswrRK9aSvjxzx5HeOeEFCaYQf0M41aYv3+o\nk9A76RKuG03CNoEK3Rh0lY+gC0rFikslUO98zmSe3sP/HKnTJ5awNMxlaXYf\nxofWBM3C7ttyk3ed06LUgaOhYzJ7f12xDS8jhAGL/j4A6bgBy2rsD46+vHbz\nA35iH4Qm79yhKaIbQGVauN1oV1bEdKTkHzg0+6pnAJo4WAe2TN5IDnD+X1c4\no0JctJ7BKK9Oma2aiK6SxUIVKNUlbZ9p5kttxcc6iNNWTDn0atTWpfsiM+Rv\n7hq/pXePPN2Y4UVjkgbNYEtVwpIw1ecBqz+z7iyzfKWq+KiTK0x0OGHikvvC\nOGVMAhqbVeMXh/DYHVvST5bHglinbeJprlUBHgVQ35wznY7c2iIPLxzHusr2\noq6L+rzZNuBrzRZc9BpJKb1NfBNF6nN2AYITXlU2Gq6GfAFGfqIPEMMO5wG8\npeB4O8+zNr9Ddfwdj348iCDt4/ePQRFIITTWiGvzpGeE4cHRLFyx83hvvEFv\nhIrF\r\n=jG9M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.7.0": {"name": "@humanwhocodes/config-array", "version": "0.7.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.0"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "34deda1c488b244ab2ce46b503b45291583b7ebf", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.7.0.tgz", "fileCount": 5, "integrity": "sha512-85ftx5QJMaM6Qdksus9bqgCEv4GkSLKKbZ1ddgIsXs5CviAaFWFSrM65esem2UPqCektRFm/eZRzypSjCzexdA==", "signatures": [{"sig": "MEUCICcUB11JdEnGFCoIHhqUBD0Ktz/u9OPbyMuHVteVMaOfAiEAjIMSSI2DpICB91nAMH/L9DLz2im9laft65szGQlhVx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37800}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.8.0": {"name": "@humanwhocodes/config-array", "version": "0.8.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.0"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "e078aa6d9c9931d5dec4111ea714eceb2e9c5fc4", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.8.0.tgz", "fileCount": 5, "integrity": "sha512-CNKGovso0I95a8ArJ1OMLtkqTovtIRzFugfX3LyNxHygwkbhj5I3fxM/ocJdIM7VelH82EvVULDUHQwOaesIpA==", "signatures": [{"sig": "MEUCIDEximddNeDV1hMjktgWrBo59vtMGrIVnSq2CqHzjje+AiEAshz6R35te1VNVfki9m0eNEmQyPypyaU+2eUSy8lDS7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38216}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.9.0": {"name": "@humanwhocodes/config-array", "version": "0.9.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.0"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "a504c376fe1193a8e9540e1f8b2c051c768eeb10", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.9.0.tgz", "fileCount": 5, "integrity": "sha512-/WhptLLEIe44Wy+3bO/U+5Xjj7rZaPXLkngBhUevlVdDyobe94FYqAMiSG1+Mm4ztqsY1ZRka13BJzJ0tYFyZg==", "signatures": [{"sig": "MEQCID1RVzD6W3T+ffxwk9w57/EIgUY5FmV/NTQf+p/WnUATAiBizCSIifwTRnrjIHu/VGfYHnN2ta6tnTcQhzuFYg6bcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38502}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.9.1": {"name": "@humanwhocodes/config-array", "version": "0.9.1", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.0"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "30a53de040366168ecbc0121128d4ae7bfd8e227", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.9.1.tgz", "fileCount": 5, "integrity": "sha512-GIVfbJq0FCbWSkxEnL/pQK5aR6K5EzJWpMmcHXKx21PfR+kzNL4Sxp7VmgAzB53JFDCbMSpcQzUHu5d0AfR9vQ==", "signatures": [{"sig": "MEUCIH9Kb8BlWfmrJLuV016Tq+HzNiBhNjeE1+rRakS3+l5FAiEAo9OQJBvKrG61OOubPV/oWOsp/bPJc6l4PcgUyUFPQhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42814}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.9.2": {"name": "@humanwhocodes/config-array", "version": "0.9.2", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "68be55c737023009dfc5fe245d51181bb6476914", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.9.2.tgz", "fileCount": 5, "integrity": "sha512-UXOuFCGcwciWckOpmfKDq/GyhlTf9pN/BzG//x8p8zTOFEcGuA68ANXheFS0AGvy3qgZqLBUkMs7hqzqCKOVwA==", "signatures": [{"sig": "MEUCIBn6/pbhgrNIKAxr3p5eYE/rLikeQxiXvd/e0xxiQJhyAiEA9Ic1jgFEtdScGkD9/MVSvVK6EgrHZwvY6LRZijQSODA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2K8QCRA9TVsSAnZWagAAnQoQAJLxNjTTQ5KA8vTxT5SX\nb5Uiw2KLwpCY26c86ywURAainR2cveaSF8dhx/9DiiFHtDZU/Wk5iq9h3Qz9\nO3OCXQ8tttfztaNF1dvhwpZDxmHUhglXdU2F+xXfrIWeRUEXvpugopBVOxTm\nK3wMPpsYmpTKTVdJhEgxDudGcKs+DnVBLiapFcRHmyrrs9XoAYpzm3hXvgX5\nvQFnRXShM9K7+6TRCi4MrWbcl9KvxUjRC8TmucUxhAw+yVF4n018/2b2JJcw\nBHO/3PkfDpoJBSoJnORogj2AbmEpxF6INkaruFFukVX2Xd0mlps0TPtuQUvt\nG0j+gPI0x4P7yFsb6fEeVmW7ip+TzvrRjSP1cCoiVdIjJQSx8FjyGqlCeVxU\nasgIEQ1kwD/jrT9QPALFVxkRsIUpXtXoiqmPujVMAXE9RXqWziK2FTmOsvEb\nMlInsYKvadDIf+B0RU0Vb8933cdchkriKz+j991DmNzpgdoOvFVbRfRUdVwD\nrqgTMZN//O4i+Dv0e6s7HPyBW9lk+NfYfEeNwZdGWlo80M/5bJeyuQVH0qQQ\nBepAzcjMuASuVCrN4/IBKzZBi7GMqu8FrulC+4EB1jPAzU2zY3DgXTwUziFZ\nxP1EtybXIdlL1l/CA2fnoqUYbUNoKFH/x2ShKPgF60sKG0LJJBfcdUdV0Ruj\nkxf4\r\n=W2+h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.9.3": {"name": "@humanwhocodes/config-array", "version": "0.9.3", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "f2564c744b387775b436418491f15fce6601f63e", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.9.3.tgz", "fileCount": 5, "integrity": "sha512-3xSMlXHh03hCcCmFc0rbKp3Ivt2PFEJnQUJDDMTJQ2wkECZWdq4GePs2ctc5H8zV+cHPaq8k2vU8mrQjA6iHdQ==", "signatures": [{"sig": "MEYCIQDsYvhVnlNsxyX5LFizPxrT+upGxAIamYAs/hMssPTt2wIhALPn/E4cAffZf8fuoK2vtFZBr4cizMUzfLnchFFbOyrR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8fk5CRA9TVsSAnZWagAAK1oP/03HBfBsQx0GcL9vtOm9\nnYlypZSqlTSjiRBfLo30HqWwJu/sGoAEEz3amaZ60qIDaQMkaaXY+Wwy1CjR\neqp1WQHyVtfCcfYiSEMMDSUmJYUv6YyLylK0uLZ5Az/qfiKMG4SkMS7fsxTI\nr6B5FuQ0LH3PGb/lMsVJIWr+cHorQx0IosmtJY2aFwvOkW6MWfWbwj3Ckps7\nn7s3dbgNsANSdYllRFNUbr7ZK2l5nVfSZGQylH6IWvsCYYP4sN3eRy7+lfLa\n3BkRG0E3+DsSKYRv1dUVG1+HGkH3l+iUfbGhBEHvCj9BhTQOgcMZQDk/+IXv\nc5gJqEZTrM57OCM7s4NhbEXKlOpH0D8pygV+ARQ9vfrs7ZAxfBiV0qL5eBal\nDa8ORuIm+JvFfPb+2Xf5x6n5orkd9Hv8S2t1B2iF9XG359E9tIjZRqFcofZI\nj1+jHsaytGf9rhS3W9Vq7ycJnx9MizzLWfF4BJtujR6LiO7JgEhFn3bNsYaf\nGz+0dZBoRUwxtP/sTzjzomZ/yc51+QTy8SLy/50U2xKLmYoZHns7Gx8RvYVA\npzyy2cbzj/+zGSiIfZqfqteZ1sk/PgOIrCDUtan2D6ay6xpFQF+0Zyo7SMwM\nUGsCCsOpLi+anfC3Sm3uybEloPRc7NBWt+HaHENzg7RUYBd1FdQ9CDZn5RN2\n3gtg\r\n=/kuW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.9.5": {"name": "@humanwhocodes/config-array", "version": "0.9.5", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "2cbaf9a89460da24b5ca6531b8bbfc23e1df50c7", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.9.5.tgz", "fileCount": 5, "integrity": "sha512-ObyMyWxZiCu/yTisA7uzx81s40xR2fD5Cg/2Kq7G02ajkNubJf6BopgDTmDyc3U7sXpNKM8cYOw7s7Tyr+DnCw==", "signatures": [{"sig": "MEUCICjHtqoenBwhRDB1MXl31gngAfWjtsuQzaBjN/L2Yc6IAiEAt/BH47GabTmGhdcJwmMMqYix0Fa5+y/c2inGzjGng4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFoHnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrD2w/9HwMP6dk58q9DNohU118XonVXT2j+vFD1f2JaHQMRqxIVCID5\r\n++sAYfTrXUyVjpusCQbSxx56rTAe+HrEYF3T58tMxHUKKmgw1f03eOyWwL9f\r\np1JBawQO1oxFHormXrrPDBwnDTI5MQIOfVyTnwYWdjVw5+GaqN+t7AbdZq/a\r\n+jvqq3CwQi7ZeMWhyjZrjvVx5qaDcQvtgd66KeJuLm18Z1naksPnORYnrS3f\r\ns7ZFwGHT/BLVng3Wy2+/LJW6croqfRc3pSQ/jbK4JNn8jqXIiUAKI6zWHOGo\r\nrxOlaOcVn6ZWJWCJWKGit7sDMCGVioB3lWpngoOsj0G7PbzAesmAUEy3theA\r\n37d2ysEFHkjbFg8dF3Ri3Ul8hwc8FEm9zc/cr0Y+c73MxCTX1IpRrUn10V/q\r\n5VIR+Ofd+oADgvX0gtMwTimIx3sglK6tJLAm/cLDulfgT2be2B3ZlptAfNvF\r\niYlFY1vnvlwWUKSupZE05irs3cPg0Ww2Yl8gQw+8nQYE6mOAFyPGiy8t/3CR\r\nlUTSekRINlA6CAR0YlAzrUJOtd9XLeRbfIFP3CkeA6OXUGK/6cyw0dTLXxlZ\r\nXuNl9T6j7Gi7QyIh9gCGFTU8goguVR9DWjUyUHglOpPVlYCy3IFLXn+snpYQ\r\nXZA1i+7WecQkloGcUh3pz8k9L9YODMoGJdE=\r\n=GFEw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.10.0": {"name": "@humanwhocodes/config-array", "version": "0.10.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "e248333ee84c0e7737c1618332de819e8cc0c714", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.0.tgz", "fileCount": 5, "integrity": "sha512-LYVpjijc474MqHFLM/A3eFNM/3HzMXVPofaprb+PPPNZD+7BkihPZa0ENC/D2nOLrfBkrq0KqFchO933lnlaPg==", "signatures": [{"sig": "MEQCIEoK1WPrQ8qSoZd7ak3Sxqbe3ME65H8mzv7ka7wl8tWpAiAxRVUs1m1VzDtP6eY/acIEydCSBza40JDj227DxGLYnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHmCXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHJg//bt+0iTGztxDSDmWANE2EVhW30mgdDbnp1xObkYt0d82b6oaL\r\nFpkoZ1Mf9Z1UuUEy9tR1ftWaJHHZQXV+Wm/+Xh/FERSZ809CpZqq9btCbv96\r\n0wv7o24LQYGQV32kZlYuOwea2mPu3j2HIIsQ97IjniOScaoXafffEhPIMHK+\r\nJQ/6bTlDLKHofnqG15cqf2p+paQjuduVoDxljDyPrv6vaQ9nL6nFzQ0OcApJ\r\nfTzB1haE9XoLKvdb4feKy9/+kIzgZbv20DJwiM891hDMHfUZhuhm2yV0b3BZ\r\nteWaGSE13q/8/YNLvavi+y435IlaFKT3byVMIvcS7sYX2lJxlZyb7duS1vlY\r\nnSPTRYwWYwML3YQg1AtTmQKiY3M8E0b9ohzK7dT4q6g3SCegreKhkLWXA1gU\r\nAiZ8H/8JBI+kWcJmSWNiDxy9+YnfPQg5/rNyukyQ4xG6XxIC95zhaQhu/Mcc\r\nvXVD6QThcuH+MGTSB3v5gQqJoFIwjkt3l6qC/sgdQ6dCDTT4zkKDIbO/2Tj4\r\noouI1mwmYXCYSdzlW8OoniG8SQXnii5zdosbyIVDr2iGV3FI9IIsIJis41BZ\r\nCkTq2+xhqvov0KOmFu7QFRHQ42RC2EOgabj1uRVX0aeAi1/GhgCHCqxyf2e3\r\nHmAWhUU28+eBZU28gW7GqzZzlyWEi3a9ld4=\r\n=tEt+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.10.1": {"name": "@humanwhocodes/config-array", "version": "0.10.1", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "f28d617cd481f6d228cd6090d779077453a69036", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.1.tgz", "fileCount": 5, "integrity": "sha512-xwsLlhr6RCUk22R7ZKcXFjscj5nnqklVZOIXA3wvdXlcdiaDEI2xBkmf8PBWeqerDO7iBMj7gyogY8Gf7qNqpA==", "signatures": [{"sig": "MEUCIFRrBk6ph26R9SHa42kYIFtEmOCuJbujRBII5XOZl2y1AiEAvepgneDoNfNmbz+HpOu62tn6di2StLdmNNRXi1wxwZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIPDCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorMQ//Xyy8PD7+4u1uwwCxPTzOm+4ePchM9TsVXwXBNnU2E04zzpKR\r\noL8irE4sNwG85S5yZtXfAc2V80+ocbQaN8xkhBdWGV8QI06eWfW5mN1UgDWg\r\nIgHuqJXtpyPulNJi+ZfTVdS+/CFloUSiGHgOkDv66r/Qk7Y+bg9DaPFUjtWp\r\nkjitHlGssUchqjW9dKOdG0Sv805rZXh0Psa/SH+05OUeZNlWMWToCa+dLW2b\r\n3Z7xaN0FKrb4vemCnEyiCKc8CiBU2ONJDR70jaLf4enJ7jS2rx7Qxgipdrlx\r\nVwC3fXnt9I8lWrLPJ8Qy01s3UJmThX0sHlL+7Xm1Q5vGlv1EL2tJOPn8gh1q\r\ndbbAoeXOIhkCe8dgrtKoRb6Z53VT6qWaGCmxylRBobDJW4YlKVN1YQwGBcqC\r\nrY2cgLFDEemSz8CYp1RYnvUk+hS+zTlDQBx+hbEcBVItmYfpWZTOZSUEfv7o\r\n7rWnClM9icZ3Rt3phDre1a9RtgYDyvYzAlbTeTAwGqyvnaTG9bjN6EmzYTek\r\nJnjkVFbWAjqdgfCXtyH8Tk+hal3b9H4qBLp7ecAdBGoPZfyHnoKDGgCxLMCO\r\nkOCfShInnscBm3Lw8h+3n8hGUoWc1X+WVqFQM1fjT3WtYGWFyF7HlgPbXVG8\r\nuu2ZARIWbgK1wKqSQ00RxOfj9Cqh96YoX6Y=\r\n=QRri\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.10.2": {"name": "@humanwhocodes/config-array", "version": "0.10.2", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "46b818dee0aae2934b0f49d504d17da113555604", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.2.tgz", "fileCount": 5, "integrity": "sha512-qWhqPrHNz0CsqzSk1m05wQsKhT3d+E9XDbm1dwEB4O/hq9t8DwaeGDnJIpywP1GuxAAXNp1AB4byfaZZnA0fFQ==", "signatures": [{"sig": "MEUCIHDTVOoLKL6NDUY1fHMgBZf2dqKmgsabL/WDSFv4pVx9AiEAs3+hYP1gb3nTfmZ6cKy8F+qwswIfB/eowBNRd5BL92c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNL5jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnYw//d7Qr28kTHQWqmgbStAau06i8JE5D1XqSVNPXstspaxgVHhKa\r\naybDs8WOlfascX6UITTinpIH0yyjiv6aZVNsxGvkjsrtShihzzHglEyQUqm9\r\nkXWd6MyXR6e7sW0EepcKUmWPxz7q9SsHMS2wniwlK7hn/8CQqO7dXWTIxsQY\r\nCZYKPPg6GVeU3jJeDMdxCmseEFvZUtRSGtRMUXyVgnoihr+NwWapbp4A4oxV\r\n5ArmtDyAlvIgoRkx10LYNyXTh6Qemiz023YwCX9vCCznCjg3EdPGtfVUDail\r\n4OHZFiVYE5Xa4GI168Ft5b2c7TbaF+NDfPm7M0xjy7/V4NHjuvscCvhLBp+w\r\nA68AvilUdKvJCGzCPTQCqbrcRo5D41p943fkVBD2p2DngeR0sJC5UQ5M2JPd\r\nHJZgvnWiu95eifaVXo0zikXF9Egj8frJ3fn2GO7pQZv46tonmNVAWv0xwUiQ\r\nG3snjGwTrVoH0Qahkdrt0RORrFiD2Geyu+8qK4P8/DGDJcM8agPdrLPh62hw\r\nN3oTVbrq+5/LP8kFDao7HCWTSV8IVu/wM0nL27fhz6IdvFVFHN4vy95X8OWd\r\nwj9aDJCznYsPd1e3/60YcSE34Iz9dYg7sw5Rr1BIBUGCR1nzDYVKmlIrDZDw\r\nVZdvfcUUI0BpohCVOa8+rk6MiNGCjBwlhew=\r\n=RjcD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.10.3": {"name": "@humanwhocodes/config-array", "version": "0.10.3", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "^3.2.25", "nyc": "^14.1.1", "chai": "^4.2.0", "mocha": "^6.2.3", "eslint": "^6.7.1", "rollup": "^1.12.3", "yorkie": "^2.0.0", "lint-staged": "^10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "^0.3.3"}, "dist": {"shasum": "4e06e5527d8d184bff71532015883e785a62f7bd", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.3.tgz", "fileCount": 5, "integrity": "sha512-N55DIE5hHkhates5RmzEi/n14BxdGIWEFB1JESG8qd0IjUa3IqRbkWLPNt7ctjWwhijl/1/RBabBJ6ntP/xMBg==", "signatures": [{"sig": "MEUCIGtaOuOGBVvjM8xmR/pEBX3bfUUKyQFUtQ30ycww1vatAiEA5xkMfz1UuLfN1T6SUo5iVuCJbN6WbBORVG//j0lCrj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E+OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0Rg//fXwe9wUXQaOtMzgpYq55gxmI+JpieoTn0/68/FnZTElR5omQ\r\n8IEiozLN1Fnu+jxtImow/mkJDAs+Ni76cZlTGQAqVleIufnSOKB1UnZY4ugF\r\ndJickw8emXpg/Eog+36+ZjmqiYAdiYh6C58hLN6ZpHNSJsq8njQVnmlQAxIF\r\n7uH+h8QQmYtjCNVfsLc40MI/SM6lCjDua8C8gst5HVNSO9C0tvjvXYVFPX94\r\n5CyLh2IxjDSwuwMGiacU1PeTa8/uM878pi6GmZNCFMh9XGIYQY2+fH72yGg3\r\nTnqOusV40/9xL3B7oD2IKHaV90ryHb9qFJqbHExy2p+78JTsEFgt2YyZY8tT\r\nBem8cCpaoix/51QQLvz7Davx622MzpjabTlUz7COF3jwdOmpNQLBzirKUAmn\r\n68lQrUQfrHyB6stRLQ1T1NQPa+Og13tRxDR51twf8w1tahVuA8KmJfozPpuz\r\nFQ0je2xZzT6stCy0W0EHatqSyng7QQnSQeas29v/otS0ei2zoiwDkRx1CUjx\r\nsGakYMaoVHqFDpTbHT64hN4X3RzSKKXvQ+66jSZ1e0YnhZDsH3Idk9Sh8h47\r\nYd/4Xp+bCJJUZA/VOgCLUlIAoLmhxXHA3JnvDMWveilR6Ps2FWWrRu+5RwOu\r\nSmw6GhS8s4zIvvMqz79Z5J0Z2ozrN6SSj68=\r\n=xJNw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.10.4": {"name": "@humanwhocodes/config-array", "version": "0.10.4", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.2.0", "mocha": "6.2.3", "eslint": "6.7.1", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "10.2.8", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.3.3"}, "dist": {"shasum": "01e7366e57d2ad104feea63e72248f22015c520c", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.4.tgz", "fileCount": 5, "integrity": "sha512-mXAIHxZT3Vcpg83opl1wGlVZ9xydbfZO3r5YfRSH6Gpp2J/PfdBP0wbDa2sO6/qRbcalpoevVyW6A/fI6LfeMw==", "signatures": [{"sig": "MEQCIBxuj+dx1W6PtdiZk2CXO7M638oZnoi+iDcmIA+Pmqa4AiA2drXqk6jC5rVNXiYX9ed6urO+NTOwoTv3S81Jks1Qdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi5CZJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopdQ//TYCB/he1J3EFQXs/EeygmexP4jkpphDsnbMyzgu3/dLXVJkt\r\n5hyPbfmeFbqzIc6Ge4xMH660zKhvxmuIVSdpXHIf0nLSQ4GIsp92SkMZMvxS\r\n2OCGeHTIibI+iHI5Nqjxknoc70R9xAIJvfOLc9jVJ+ZPJCQUYKO++jWpj0Uk\r\nsULZkIGj5+ubi9obzdtbJ0FZl7ZbvP1sEEw/9cuEUct8DM+aD274Jb8jiB/7\r\ncb2wCVPwu46XPPTQoAMhVg5uHnap6TrrQBzDFyWLyubHX33+qOczfThuBpsJ\r\noCZu2g1P6ZyHbMKd6/yhWVDAMgOtorFulZHjvNGXxj4aW/HPLTwnTdhpI4NF\r\nQhpm8s4ju4HFr9Mj5+8/GJKqF7WCsOU/f9R2qlyu1tkCJm5tS0xsnaU2KE3l\r\n3QkWect3kNIwR2APoLxFecq90ba5Rvlr6+x3rc/xIag1kRosh9lNuB95L1B0\r\nClUIcqWcJuWwxknzqUcELzrqb/R2CGtrmXrU8IULPOy7LoNkMvQvQJxTaKDG\r\nOf4QqHydDw6gJXfNUtQs+1JdpUHZxn0S2HMzOBBHEyJMWn4Ex1O/9MRlQXwS\r\nmZIKJFefdwJBXlz7vZw0TvRtWgTdBS64O7iWNx7fOqLuZsKO7My6xO3kIZGC\r\nk/K7x0VaiD5oxRJsjXW/uSsnbOc90JDLeoE=\r\n=g9KZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.10.5": {"name": "@humanwhocodes/config-array", "version": "0.10.5", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.2.0", "mocha": "6.2.3", "eslint": "8.23.1", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "bb679745224745fff1e9a41961c1d45a49f81c04", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.5.tgz", "fileCount": 5, "integrity": "sha512-XVVDtp+dVvRxMoxSiSfasYaG02VEe1qH5cKgMQJWhol6HwzbcqoCMJi8dAGoYAO57jhUyhI6cWuRiTcRaDaYug==", "signatures": [{"sig": "MEUCIQCSpxRhBOLGr71P2Aqz7uoGj9yKJzN5If621gQaumjcugIgMEr57MQp+IBjvG8Z0V9k/HUKaTFUTWYA6EVIamTqh9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50832, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjK3SXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreLA//VYUCVXcJNePMgKYS+b6BGhkp98iGGxA7a5jEOCt3AQS9vzGI\r\nMsTFQ5uLXx5N+jXRtNnQNQr5nXziz8nIcfsFnNoA+zRftEI4D1/dxmEq1PNP\r\nfllfUEpBsmFvdzM3Vw+oEus2UsTSTBdstvYSkWNx20ljExlR9BUJ4ZQ6U9GQ\r\nJAAhav4GSZeA1nTK/gX4UFs+5tJLEzQf6XaUOVeSVsj3EPbgUInRYYm9sQm9\r\noaEATS76DMsV2POVhII4B86bt9J6MbIqcBVT35ipqcRm1dSQ/6zxpHhmJTUd\r\nKlz/6GZIZlQrelcCwVmJ8JjHbHYznEb6WIwYRJ8ORdwQDtEXI6ZgYvJp0Hon\r\nMsT4jGvNrzAmG6TVZXnl6xonuxRDfmgz32NCD14NmEdpr8rDUQZeTGH6Ka3A\r\nPw/HrBFRxzmSlDwXbIRPxaDNQF8gLGqwG+1NSQuoO2AXnW4faIL5YqNW/zUz\r\n7HaWM0iifGQyaoDA6fgz5gQwYZ/+DgyxLfzn4dy49AXoQxJIiM7TuNt/Iad+\r\nfL2xF4v4fdWwSJvaEvyEfKHWNcUcMA3wAUGEk2j5QLEx45PZokpYkqjVMcxr\r\nR85wQrRqL/hbMwSk+/6NYlMDGhyTekc8HuBbvWqlB2o2/SDznjVuyopDYpt6\r\nEFFIxTtR+xLtVsFQ5dYHPCpDv95Yvz7Gm8w=\r\n=8uzF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.10.6": {"name": "@humanwhocodes/config-array", "version": "0.10.6", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.2.0", "mocha": "6.2.3", "eslint": "8.24.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "70b53559baf544dc2cc5eea6082bf90467ccb1dc", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.6.tgz", "fileCount": 5, "integrity": "sha512-U/piU+VwXZsIgwnl+N+nRK12jCpHdc3s0UAc6zc1+HUgiESJxClpvYao/x9JwaN7onNeVb7kTlxlAvuEoaJ3ig==", "signatures": [{"sig": "MEUCIB2LV7LQrDsY89ltfzp8Nx6p540WH+Jo3G8z33MKg1MRAiEA8wPYIQKoZYFuzwxYMYsodOt7gLpy7x+QSkcYLQfVjqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNLAGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrurQ/9FSeCG7CEoCm051Wh7ZYupVVc1dWXEKrAfawjpn88NIZrEqW7\r\nAxG+dMHJ4wVGn1v17YACFolg+UYe7nxcplEQpMlJZXcen7jLS6VpFLzFUxhU\r\n7JExddAbdtT9Ke++oSE+5P0+K0yUzjCTelEumPyGIvAMzhv67+juEKvjS4KV\r\njmC74WjivMN8Np7etTmvnJ1ozNwEMos02RocTX3Ckr32T0TGg7WWs7+zw2ba\r\nVIft9s5CjU54Z1oN0k5w3iC27uHTkjf1GMO6n+xZJP0Yk4Rend24XdOy9Qmb\r\nwaDxK1JkBU5QZwzlmjVe1IueY4J24RnWSG3Mvq8nLa2LRLsgWW8EV0isJPyj\r\nGT/dcK9EPSMb2iO5ema4samLsY30H0WnEQWv9MbROpgCOYMrytiYTSstx8X2\r\nyygnBD7Q2pf+4e3pwJvUEaeX8yNYQmpRIQxi/49i6oTtZg8/fD+q4qcGasC6\r\nRFL4kg7gUpHD5W0t736oXJzrQLW90OeNHnX10vJUQ2ewdH51eut13EFyYLGP\r\nJDSUChaCKZ6ITey3NAkspKi6WHpq8309dyekXyqKgcPNGRN0kNl+FzaX2XzJ\r\nbynWwRyAHYlfCbJNDOSFQ8fnrc6KgljUa+pE1nQpVKuuceXH/fjb6HMEUGQk\r\nroFLo16+jTNd/JDaX/CW6lEMzjQCjDd5UxY=\r\n=ggIR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.10.7": {"name": "@humanwhocodes/config-array", "version": "0.10.7", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.2.0", "mocha": "6.2.3", "eslint": "8.24.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "6d53769fd0c222767e6452e8ebda825c22e9f0dc", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.10.7.tgz", "fileCount": 5, "integrity": "sha512-MDl6D6sBsaV452/QSdX+4CXIjZhIcI0PELsxUjk4U828yd58vk3bTIvk/6w5FY+4hIy9sLW0sfrV7K7Kc++j/w==", "signatures": [{"sig": "MEYCIQCyrkPol3JIC5UrOqnJs2InHeZPr6NK2yrBkWernAP0cAIhAMRQlFnTospE96RugcLhkIF6TFu1lEY4EpOzyIBdjQTO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNeirACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7kg//VwzNH2VJNrwA0K6SIVQa8wVB2as1t5ajWfYgDHsR3n5o5qok\r\nEbYhROxogF4nMjHWmOKMrGihJEhtEJN+zECfJs7+iwV6Id2d8w0CauZaDwnu\r\nzOZgo7oY2xj8CCOBGO+zZDWQoQc4rYTDfKEUbVLYsFqWNFpHI6ajg2PA/fo6\r\nGvrGGNr9UGoS2UXnpdhl+P2yF6wFaBpD4qgoSjoxWww616oPJ29LO/aU1l5d\r\nvf0ktG1lhj/v1sSNJojGehD+in1Ii8mjPANYLci/FWYs+B0zz116Yw0BTqFv\r\nLzIOanq6ThJfuapuNdexJpUdWzm56P6qTd8QzMpkgq5EjqVRbkomm6pROMIa\r\n12pAFHn7RXrWuiRmqw4+RaP+iNhMf+hvuTGCfvVHRk9axn0UaPFcV940Yioy\r\nXOzVmSbPgzyJG3RN8iknv+8/aXwUaYz9k3BkEFvqf7eLconM5V/9PRtsbJHN\r\nw7aG21hrjWjfGWH6S98hwkcqUpiae//sX4n2xpjHbZUqVYg3PHcyWl/Nk9t9\r\neVD5uVpgGZymH46QrN8hoykd69WG5zp5dKuavEJUIEQ5kfn3GyQe9Ruk9eVU\r\nyYaPIWhZ8NYqJjWYypv0y2M3oa3Dq1NUKxZAbGSIEx5EYeCMwONH+DS8uAA7\r\noKwbMBhrhT0Olu1ew7UQdC5z7/snBLe0yJM=\r\n=w7yv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.0": {"name": "@humanwhocodes/config-array", "version": "0.11.0", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.6", "mocha": "6.2.3", "eslint": "8.24.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "53013bd2882b12b59936183733810e518b716796", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.0.tgz", "fileCount": 5, "integrity": "sha512-ArhA9D0Jlvo7Kd6JYc6t1dMRCjpnpKoRFiukGKZf2COCifO1ynvBapA7QB5YwZscvYByWpR9R3PUspbZsPUkxA==", "signatures": [{"sig": "MEYCIQD4PD4+/697BrsNXN71m+nLsxHKLei/knxDJyLDO93e6gIhANyn3MCPGQ72ehuOmGsHPDLf5UX6Ka21Cjh4gEPsxtPd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNzLFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc8w/9FE8uwbL5Ap+jaXPfgBax0quh1Ms+/L30JvSHipbEp6E1noOq\r\nAvLYj++PziowuOOpk4Q6M82gBPSgrB78hVIgINlrhISZZDEJrFKA4ccEc0JX\r\nk80msGN3iYAhErs0wI+tHbqXMFG0aHQDcMguoL3wfnTmNmn+XlcFv3EmAEJt\r\nePDE2zyLn6el/tA87Zh+braY9WHpJ/VforMJa+dJiE3HT9LUHmI1Uuj1+lzv\r\ne6WHn1+lhFKQ5luXpAQpBZtnvzZbVo9s56V1175nc9fSxe1RtgkLxbk5YHZI\r\nk/PxWoMTrerV31yEkBhe4fLQBAHWksWYkZ5XBwpijYttjGu0rzOARAvDq1q/\r\nVnwv1XZlsBdsel4EJh274fu64FE4O8Q/Ebmjmv7e67kBsucHzVSJVSqsnkqc\r\nunOSs4vTCDoRV7r5Nsi6fruMzUlgf47zSMn5P6Aj6MtFHJR6oQj2sREkHnQT\r\nCj1PTXQFN1FhlPBlHWCn4zvqcmjRjL+miXz3wKpzUS/WfxSqq9LjY6YfsKel\r\nAuq4Kh2nranXuBlXrUJ9kd6NF5T13mqqk8w9QxfKiv0tvczoc+4s8l2On+ZJ\r\nB0Klst6Hj1jKFY94wFYiOZLV10/YThdYBHreIT90dTPyTgJQo0n/aqaGJ3NN\r\nnap5IK6u8wqXxRmYxNAFhKkA4AURfXbBO1U=\r\n=fSij\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.1": {"name": "@humanwhocodes/config-array", "version": "0.11.1", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.6", "mocha": "6.2.3", "eslint": "8.24.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "e89797dca474e21c85ce5fa5596924eb9d072749", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.1.tgz", "fileCount": 5, "integrity": "sha512-SLH3zLQd1pVKCxxpqTI7SFWgGfzuyAAInoKZOPevZKgYldWwbnvUUO2RrP2m/Taut3UGNDzWkdBUv7QtGxirjQ==", "signatures": [{"sig": "MEUCIQC8t9XxWYMLnOAtR9+RrB2RULfzvlHO9g7ktXaoBPkr2AIgBg1FS9Da/4iT3fhHBmWiQ2meqv6L3oYtHxhR0LuzZ4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjN0txACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqMA/8CwAP2uKwj79hnvmHsxXMnFA8LThYHMVn9GpakmKBjuuYExF+\r\nmt5A6evaNPOeeZbji8hzn6AMWZSzoIN2ujeXCuYaqCBjxn5StHZa9usCVE/9\r\npEI04QluzRwKs5to5MNrdxxgOj2cinnv5WhQ1LIUG5RmSjjmLZFPFdx2lhs+\r\nqRENkEeGSzAZ76aDrO4A5O/rQJ3icxgvbA6Ds1fyTQQ7rhqfAfd++vVnnkZN\r\nCIw2uNgT1vpjKp5Dx4Vs+v2vSemUkmqogVYbnIq13Hzf62XJ3dvJR/0IYFgD\r\nkmOyeySIrFo9ocWDJ9wn4ZZz2Xr7BTZa4Yt7ZWeEiey1OlSB5m37TbThaRse\r\nl/GVSkxvtp1VHPCagpcBxebKjDzL0xr7Jdp0aY0FVdQ9UBsEbTTYVx1Sfefu\r\nDbRIrMjpULa5QqvWcAKeMMpFOKf7+nl0WIBzJu5wh6VgmgraqL7ocKZOYzey\r\n3C8u8hKThTT1dWYg89BaKwe7sZNFmJPX8eUnVkDcBHre+jzYm4VcOsq6c4uD\r\nYP11PuA1kEncQzm0WHHyiZMSQHrHDOiOUBBMSah6ewPEPor0qugDMF1Gn8M7\r\n6lzjts7fs1IAkYDcj2sb6L1mpMj8T3POUhfoYCE/quQO0dFA/Rj0QN+T1an6\r\neD778t9IresmmHhwfnw2EBNAPftg2Lgz5uE=\r\n=2PDA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.2": {"name": "@humanwhocodes/config-array", "version": "0.11.2", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.6", "mocha": "6.2.3", "eslint": "8.24.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "6feaaf2bc75951202a515a7346fbf9b2f69e3c18", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.2.tgz", "fileCount": 5, "integrity": "sha512-VoT61M0xGEzHMeofqExr5qaxVLW3VOa+oIftpc8BEqMso1hPAGGBrNIeLoui2qpa98/oiHXZHoVTRlrCIiPCCw==", "signatures": [{"sig": "MEUCIQC0QKTUJPgpncKWKXD0SfdIeXl/2vAcGHuMkDj85AhnBgIgdBgOIhSlqiyNfdl+8AOlCPEFfoZHkVpwbURifFM9I50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjO1YBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG8g/9HbVtd+29Vj/sB6eeDd5EyX6Fuc6oLRoDVOSu9z/XH6dCozsl\r\nDfqekIDzuXtJU0Or5xtjCEzFab0VS5eruPijXdbwOIDZxf7tOd3+HfJ56FVe\r\nQ9S0spv2pvrsfi99WfhFZuD4xVJvLkCO++OLSm3YkF9/SqCjUFGfjbF/YPx4\r\nWsW204Dzvrg2q57V3K5XbKw6kspC1PBoI5F65/uCnEEU1RG/8H8pMviYzR6O\r\nZuEg0Hr0qjoAt5+PMzYngh+fJWe5dNMHsMWzOpvYWTpAzqYxk3Bi6trAm8Lg\r\nRpzMkbopMV+vxZ8hRe7ZaQGAbXqZNR/n18jCHMGYbt9YdBHpa7OPenNH2JMl\r\njnFJCW+qAMZaYW26EsTAmDO++BIYPgAqA5oamKq0Lo882LSwrwnmh44FVRc6\r\nKLf+sFAxyVexHonT036MTuIDjHZ4yZJk3xkgbNq4nfi8KGwttRhnf/kB1fYR\r\nFerDx94c4afL/RhLAz9HozRN4znPeZpc246PyAtGeZktLE0GbYe+gK7NT1fu\r\nvH71eCarf/B2+UOptE4Puq+UqQyZKPBkmpeb+AVNW6E3srIPLYAJktttiaNP\r\ny7f//7L3jjABrxl6vBIueda/+n3ofLKFSXyIgAWCIDDLyB62lJDD9Bisr81E\r\ngIhVgktXAv6STcY3ztxmuGUntSxGzLcbX9M=\r\n=E6CR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.3": {"name": "@humanwhocodes/config-array", "version": "0.11.3", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.6", "mocha": "6.2.3", "eslint": "8.25.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "07bea4fafb8f5e91d0c4d65f15f7a5ae07e7e261", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.3.tgz", "fileCount": 5, "integrity": "sha512-5P5RBG0SJULgwDBaFetPh1uZ66ry4CfrlWFIjRiqT7ycCGwo2uw5Hqd8el5MeY5pWuB59Da4RJZ7Hs8Kj0+kMA==", "signatures": [{"sig": "MEQCIH3jg8MVVQ59GfIElqo1t+J3dMI4uFu68gLQHhbSW9IfAiALD3yJizyBcSGgzHBXXtM/ppq2rwEWUHmmnsnQIGKqAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSFmCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCrw//bt4xafgAL2Xop8BOESFE8wtCRd4wibhz13HB2fHeguYrxHYj\r\nRoub+LqXzqTD0Rh4g4/3y64G4rQQ1EkEsu79GC/joii3PqK/yn0/GatkiCaO\r\nNzNUVT0QI69PkC+1JHDE4hzUIEC9pbqsnqoTKl5Q7o6Q/w+t/SXnE8iDLPRV\r\nezamYkIOjKhoxQXM33J+lSm76tymtBaJi2XTLCb12IpxFh567A1uTnRnXv2d\r\nEHbnt9NJVjP2SfoRA8I0j+4EZnbaIY2JZ+H4BtDu2fvih1an+k3RcR/l9RQV\r\nRIKVupFTz/CjRMCiEAXGhWRIfFELHM6NtTvTM5rmJbBzi6NMz3Q/9xrjkVS7\r\n/ZqrOO5bh2NwtQ+HKQvUyf71wQbbUnHOR7Ud6Q7ZEQEequ0qxQJKbqEYztAT\r\nEHwxivw7xl/1nZClhTpXy/XoD139vTH9gWKTvG7jvMRdq5t6Z7KgDGoCEojn\r\nUFH6yoEnDAsTEYh/wbN9LGhMd3YMqNfouFUTelA51PO9BlnNthc/i/q9ofT4\r\nqKLEpIdHJCh9aep4sYixH1+NoOMHC7MXQlUueCMj0VVOJzsbgZ4Ixs7y9LYU\r\nOUCdyj/DAVVg1ccsnfJ7DQf/puqKa8b7fNk1wdk+1OQo2OR7mIjGjGFELRZS\r\nkZo08+pEHGKkGwBG1UFVY27pzfc0MutIMGA=\r\n=TyMC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.4": {"name": "@humanwhocodes/config-array", "version": "0.11.4", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.6", "mocha": "6.2.3", "eslint": "8.25.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "a478af2ec645f39a94e0fda333bad5b4ee2b5f97", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.4.tgz", "fileCount": 5, "integrity": "sha512-IYWI8d2Q+N64RcsdzNIwE0E9L1xyRLWf8LKIku096EyhtGRikpqEjeo0wH468ZymNni7JmgI1/LYqBpInc1BGA==", "signatures": [{"sig": "MEUCIDKoklLBg+YGfZlwaOgZ9NpA23O740rwSskVbiIeVr4YAiEA45/nnNAHOuicync90nm/7RMeS74ZGo75wsZ3O91n2xk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjScOlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ5xAAkgWxp2zyCjUxOZfpg1xd6XNF3sMoDHbVb0F8xjA+BEX6WGVw\r\n4k2IbBF5WFBsdtPathi3pY2ZhODk3O0k/QhVXaj8P7YPRSMWc4nhIh8ORegF\r\n3j9VHZj83A0NSsQVY46IB1BYOuZu86k0RjLrfMDUZOVHRccMOBA+yNEpEyAj\r\n+mbSq5A+t5H4/us0byxb1MNOUHo7vZ1NvxyB0KH+E78zIq6nuHKrdOhPY6fH\r\nj15PfTPqgElUB72UMcyD8Nd36nwZA8vWKC85htdhEmdSF0qXaasBh7eqbgcs\r\nKhWv52BMJJtNu3aCzwjRYX1jg/c5eoCDEedJ1LMygunvV0I8bJ7GQnDQxQ8o\r\nUbonq434ExRNl0Jq/G+GfZMazMTlGqtwiI0lUF9ohSP4qFPwVOOmOjeVSmgY\r\n7lB17Mh/dTqC2DqjfWkLJGRSQVc4o8cr3ZbKdgO5MGOZX637NYkgLRBMB0VW\r\nfFUEBxFuANKGAGLVo1RmD1SgQlUHDF9VDGW7OY0GWPYGR7ZY45v++aJQRJhh\r\n1ZB6OdPMJ6cZPCy4PqCn8yZTDv9jNDY9+S2ybMORfo+DRl+ddPOt0t91dvL5\r\n2I9MuCYWDedQ69tqV8vkOoNi9te8qEObDChH0VoQ5RWOY587sHIhxoK+GHBq\r\nHvkABqMHaW6EIOSuHay4haWs4N+uazxzDnQ=\r\n=+GSA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.5": {"name": "@humanwhocodes/config-array", "version": "0.11.5", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.6", "mocha": "6.2.3", "eslint": "8.25.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "abc9a7003c4f3087078a4de04551952ee6f2d1a3", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.5.tgz", "fileCount": 5, "integrity": "sha512-jt+yqJ2O/de5Y8iSKAcRcWXihLi5fuZk2sbpwWI1r5EjmkqPrN2Nkl/RvgsyrMiESAjDxovKxxQKKgZfJn9KQQ==", "signatures": [{"sig": "MEUCIQDWQhuaP49Z3qUSHWvsOF8kDq4I52EETE4jsbzyTNLaugIgBO1GqUzs9i9CEuxwQ6HROKCnvlsn9qXm6lLMK6jXsjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTZeOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEGBAAgq2F1/pdMtHjH+KvUAIn5e9oCqcrwhKIAAB2JMjrLbetDLJ+\r\nAfNGwcIo2m+uBCkiJpMyjVbQhE96e2/9CDNeVXKNsMfPjvl1HHwT+Ss1GjkB\r\nRz5tWBMJaXSMuHFXQcreHSvXh/CTR/neF6YpViNdCTaSteekFa9v1mKF0W4u\r\nQ/hr5ODXJ30oeLD6m0K0t+8NI75bg0rFOyW8R72xaeA1hec/GnnARd+jt3hq\r\nhSWhKPnmyesyb0T+M+/LKIcThfXlxVID9P04JSSKOXKkA9b+3GHYwI3PJDzd\r\nxEDv1OGeP7YrhRTaVIfTcDtMFeEYbfWh6lf5/yreFTbAbgrQ7KxdkDlVopFv\r\nlFPx8BJynuOgWJEi/yLwFscNo3m20ROxvx4p74RGZEyEteRuOSDBQyJ1NGyg\r\n9kmSK+mK1uOm6hIhviBOC3Gx/PpoOo9yp7XpxHj0I/GQ+sPGIahXNnsURbsv\r\nX3Mp9DV3cBMF3KkfwCuPb+W9jVqbMx2+qUvi9pzt0XO7Ok2aQBD3ied1o/CD\r\n4tAdmONS67lWYRzPEFO+ww2Dhc8+qwWdMlRbJ8lt95mIHeMuhVZ3p/uCBrLK\r\njljSr6sB8+yy0IeuyqgvUVpbktGsdht2pzsWrQhKphxzc2JOq4FGcdBU6XS9\r\nBUdkjS/hYnViz90OATaS80C/UtnEvwI0IvM=\r\n=0TpA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.6": {"name": "@humanwhocodes/config-array", "version": "0.11.6", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.4", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.6", "mocha": "6.2.3", "eslint": "8.25.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "6a51d603a3aaf8d4cf45b42b3f2ac9318a4adc4b", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.6.tgz", "fileCount": 5, "integrity": "sha512-jJr+hPTJYKyDILJfhNSHsjiwXYf26Flsz8DvNndOsHs5pwSnpGUEy8yzF0JYhCEvTDdV2vuOK5tt8BVhwO5/hg==", "signatures": [{"sig": "MEUCIDIhV/sVGm/f72mZwJQ873maURMN1JcLM6GWOuBLmN8jAiEAx2c2Mo6kmDLk8FsUddAbEkhg4ig6Iy78/WqZFaQKc7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUueDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGzRAAmvNixrkUeV9+Cw2ZsLSluAzwO/jYU2aUARx68ej2Ycbuestc\r\nZguOtalAAmMzDV02jVK/bCXISXWDbZnmo1x1eJfaUWtwy21HVd/tCdMhxZmx\r\njP/mE+mODPwXCwU/08FKCB4eheWDoDqS+JIMve9cix+eeAeHgB/vMFvho4Q2\r\nW2A1UZzugyXLzNA60uRjltuDaEBdQ/ua2pAS4b8q0pBppVW8lkZxaFh6KDe6\r\nxgoO4sQBETUz5q5Re3/PP1vP+GR+vzwWFLseo2rQkcVwxZHvb/LY60BB6iIK\r\nV/8lD/6OgRPauoc6TNoSeRyFxPMfBv6Nlr8zCbrITdBp/sOCw3TbPYNSL2yF\r\nAnzxa4izbmMr6zHx9lTKfAsx/CoevFswoBrrfrZst/qbsyA282vfi4b45i5R\r\n5BwdzI5jckB7tKNrcf+TOX1Bfb3uuPWMnIAQFzSPqq38lOzLf7ZIAbsOH/Cr\r\n/v4n0Y5n0GVTE3G3YxZqfBkHF4Q9XlfGTepBmjs+U8uGDua9DTvBxoL5pETf\r\nMWgDj7tVyVZZNrWVMJZHN50O5E1nyFfosrR2gziuMDS21IdeoEt5qcSocPjh\r\nNykhDFFrVY5r81a6uIqarEkVQkPbS/AiELjLxKXXIv7D1wKZi+faHP+8+3EU\r\nIwkRM39O0PqOBuJlTdzxKt3cc2xgAyEQnSA=\r\n=2u73\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.7": {"name": "@humanwhocodes/config-array", "version": "0.11.7", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.6", "mocha": "6.2.3", "eslint": "8.26.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.0.3", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "38aec044c6c828f6ed51d5d7ae3d9b9faf6dbb0f", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.7.tgz", "fileCount": 5, "integrity": "sha512-kBbPWzN8oVMLb0hOUYXhmxggL/1cJE6ydvjDIGi9EnAGUyA7cLVKQg+d/Dsm+KZwx2czGHrCmMVLiyg8s5JPKw==", "signatures": [{"sig": "MEUCIHByfnaiBv4x9VBOTB/N9wMRmyOMYVGUB6aX61xek8+6AiEA1RjAwgZgVuTj24NYDj4zSzTvSvFig5hvFJZeAZ7bTZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjXCecACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Qw//XhuwIaO9icmkczdBmjJ3tGTsoC//TzGJD0GoBWEfWvQuYnqF\r\nV630781iFrlO9DfYU5EQGUNCPh+wxjC3W9GOvbXu2s3G4qH/RQ1du6ArVmme\r\nkIapZu2IekGdUJyqzneyW7JWZoW9sNLUyJ/A6X2rUl2/t3AhpsJTuO96T/vl\r\nCNlSWT6jN90pM7AdEu7QjJxY6I9kCBeuCBicjSoqpbaflT5KVmqazFlaSuYt\r\nL+DM6DyqkLDkLf6bSF6wOS8O5quE1EhAfK2r6msx8CUGNYkTSqrjvpwp2ipn\r\n0Dun2WZ+c6VrUBvXS82WifECG2x3orwY7GuXpegJgusbms4vFudQLOrOR3kN\r\nMnWr72YGYrbCGAQ8G3gCafs+TsXBOl2FT6hesXoxau5aU8MzDb0BnzBeUdg7\r\nYQkRxulO+4DFjR3YJKRhiDcrQMfG29vWe0lLp3W0thY1iCYtEbd3IuXn6A2V\r\nzpe+uxOmzdcZS7+Jw3VsqQffkxDyvJQunRW1BAcEMK2v9YZc5XONiTzq0z4f\r\nZs+6SQ3dZbrBAzpU1VqcTMaZLx9rSHiGcWXnBpCb7BA69Ep2lTz5TJ7w5Wba\r\nXVv5eI3LnGjOKCy3Nv7H1ozc9yj1mmv116mmsRNXTyTAJYmciz38jjEXcpmZ\r\nZF7ZXGy+vqesSFL27Sb2U5PfUmgPTMpSGN4=\r\n=m1/R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.8": {"name": "@humanwhocodes/config-array", "version": "0.11.8", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.7", "mocha": "6.2.3", "eslint": "8.29.0", "rollup": "1.16.6", "yorkie": "2.0.0", "lint-staged": "13.1.0", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "03595ac2075a4dc0f191cc2131de14fbd7d410b9", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.8.tgz", "fileCount": 5, "integrity": "sha512-UybHIJzJnR5Qc/MsD9Kr+RpO2h+/P1GhOwdiLPXK5TWk5sgTdu88bTD9UP+CKbPPh5Rni1u0GjAdYQLemG8g+g==", "signatures": [{"sig": "MEYCIQD+SKttbZITn73+Dy+jbyysmu7YpHD2RuZ8e2pFki132AIhANJKi6pJKmUBljvGIcEFFUo/6x+Q4tLnqdC+O26002Fo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmiYhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxqQ/8CSi1tP+ZOkO86eWvPyNwxDSNGaTHy94L/pFpK9se9zhjggSF\r\nzd8S5+V1D6sl0OzrOQZWiMA82WriOfcBI34tFiszLZXEBC0YOzeUahqczQtJ\r\nCbMRYd7n7hOvJ55Iqdz0M+XsKVe5M7zyKaCirywZ/VLpFP5yAP2z7kiKmrQM\r\nMvFxMO5wc8VWg8W4UZqr5SmNtUp5z9ng/8gi3DcgHOObcziAM9E1AGVIhOgw\r\n66MTG8rK2mB6ai+wdxq2yms/jof2twaSt0WxCzWyhVViyDXAQw70/joyWDGY\r\nuxNRuw7y82qjxnkyYVADU+Cj3hkxZ1okE1/tF9kfn0bc7hhOaeimiubXB7cC\r\nC3QDdhBhjD7JYT+3OYI5LBaHUKGPcHonxWzk8lN2Kyk4Jx6oAsmgAnKs+Vbp\r\nq4OT6SIbf2r4HAcs6PQ7NNqXslsR/5DETHRFJUG9JuByIzY71/gD2ubbiDaY\r\n1oCBZea9m28lcRJVKgnfHb8q9R1WNgs0hyCOf17cWBwPTSPqy2uP5tlMBuX4\r\n+6rJmGSGrim60LYrz6Zr3ljwzcfWMQFTc5G3ncfPIJ7lCSlSWHPNDu2eoYdW\r\nVzZRB6pZXfBsXIJyGkPzCD3E2xd8OlxGCyPE9hKYtS2M3Sqp+OJ/tC4yHW5y\r\nL6QA8j60LkPUHYmnfZc5z9zXZKwbudSy4/g=\r\n=Gqxz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.9": {"name": "@humanwhocodes/config-array", "version": "0.11.9", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.7", "mocha": "6.2.3", "eslint": "8.29.0", "rollup": "3.20.4", "yorkie": "2.0.0", "lint-staged": "13.2.1", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "eb6a9a2c7c2690bb4abcde8a684112b1004c2717", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.9.tgz", "fileCount": 5, "integrity": "sha512-TtlcCXarBltllJ++jCdnSjzihec7ukhesOAeFWRgCTcEHNVsN1o4nLxq/6JnpYJSUV1/w9v93XkMqQpdX5Zb6w==", "signatures": [{"sig": "MEUCIQDOvZHow0fHIPAxEIaGrt1miQLOrJFybiH179uzCq/howIgDL9XkVTz1DW6ekBZtoToztjE/wzx/ZfmPtv3bbOSf6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62214}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.10": {"name": "@humanwhocodes/config-array", "version": "0.11.10", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.7", "mocha": "6.2.3", "eslint": "8.41.0", "rollup": "3.20.4", "yorkie": "2.0.0", "lint-staged": "13.2.2", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "5a3ffe32cc9306365fb3fd572596cd602d5e12d2", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.10.tgz", "fileCount": 4, "integrity": "sha512-KVVjQmNUepDVGXNuoRRdmmEjruj0KfiGSbS8LVc12LMsWDQzRXJ0qdhN8L8uUigKpfEHRhlaQFY0ib1tnUbNeQ==", "signatures": [{"sig": "MEUCIBpzVjsoQF2ViqOemAA9fROjWu7ZAEJsDq7p7DTogPQCAiEAjeC/WeLjr7KOQiVOxzAq08DX9GYltkuu7SuuPtDKlh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55209}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.11": {"name": "@humanwhocodes/config-array", "version": "0.11.11", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^1.2.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "14.1.1", "chai": "4.3.8", "mocha": "6.2.3", "eslint": "8.41.0", "rollup": "3.28.1", "yorkie": "2.0.0", "lint-staged": "14.0.1", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "88a04c570dbbc7dd943e4712429c3df09bc32844", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.11.tgz", "fileCount": 4, "integrity": "sha512-N2brEuAadi0CcdeMXUkhbZB84eskAc8MEX1By6qEchoVywSgXPIjou4rYsl0V3Hj0ZnuGycGCjdNgockbzeWNA==", "signatures": [{"sig": "MEQCIBoS8t2AJGe2pp3EymuEVXiQe+3mChamwbE77y9l7pnqAiBb4rxaP5GuCfnSDiyQ/6ZEAoNajCgiWH2jqOt9Y0EaVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56545}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.12": {"name": "@humanwhocodes/config-array", "version": "0.11.12", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^2.0.0"}, "devDependencies": {"esm": "3.2.25", "nyc": "15.1.0", "chai": "4.3.10", "mocha": "6.2.3", "eslint": "8.51.0", "rollup": "3.28.1", "yorkie": "2.0.0", "lint-staged": "15.0.2", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "549afec9bfce5232ac6325db12765f407e70e3a0", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.12.tgz", "fileCount": 5, "integrity": "sha512-NlGesA1usRNn6ctHCZ21M4/dKPgW9Nn1FypRdIKKgZOKzkVV4T1FlK5mBiLhHBCDmEbdQG0idrcXlbZfksJ+RA==", "signatures": [{"sig": "MEUCIEf7+eYFyBKiVQi62ReJLvFA96A5+20B0/9k7N9djmuIAiEArCW2MYknlPi+epczS4bv96qvlJiTXBCjMwC5xsy9Q5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66241}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.13": {"name": "@humanwhocodes/config-array", "version": "0.11.13", "dependencies": {"debug": "^4.1.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^2.0.1"}, "devDependencies": {"esm": "3.2.25", "nyc": "15.1.0", "chai": "4.3.10", "mocha": "6.2.3", "eslint": "8.51.0", "rollup": "3.28.1", "yorkie": "2.0.0", "lint-staged": "15.0.2", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "075dc9684f40a531d9b26b0822153c1e832ee297", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.13.tgz", "fileCount": 4, "integrity": "sha512-JSBDMiDKSzQVngfRjOdFXgFfklaXI4K9nLF49Auh21lmBWRLIK3+xTErTWD4KU54pb6coM6ESE7Awz/FNU3zgQ==", "signatures": [{"sig": "MEQCIBnUVE+J95ySVLeZohonSpI2uXr9PjB+IvTCes43MQ91AiBTC7s/9rTTqSqPKSTOKhhfjPhc8STZujQQh8QDQhm5CQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56540}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.11.14": {"name": "@humanwhocodes/config-array", "version": "0.11.14", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^2.0.2"}, "devDependencies": {"esm": "3.2.25", "nyc": "15.1.0", "chai": "4.3.10", "mocha": "6.2.3", "eslint": "8.52.0", "rollup": "3.28.1", "yorkie": "2.0.0", "lint-staged": "15.0.2", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "d78e481a039f7566ecc9660b4ea7fe6b1fec442b", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.14.tgz", "fileCount": 4, "integrity": "sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==", "signatures": [{"sig": "MEQCIDYe1DDKPVZZGh1tIFbnSFmJfHptb1vVLPVBWTM70NQSAiAYDskTFqxPHeueHHVtKGnk6VNzD9q9lPQbr/qdCVqnFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55643}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.12.1": {"name": "@humanwhocodes/config-array", "version": "0.12.1", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "15.1.0", "chai": "4.3.10", "mocha": "6.2.3", "eslint": "8.52.0", "rollup": "3.28.1", "yorkie": "2.0.0", "lint-staged": "15.0.2", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "c9abc0fb3d1c27fb7f50db4289c4187280a5fc9b", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.12.1.tgz", "fileCount": 4, "integrity": "sha512-lNgJDokSDCiry7bFpgH3TVUC7zBLaOpsrGrn1xxDLaehZKc0AjlAAVUvPxB277zKjU2LSX/+k34teiwrqZxg8w==", "signatures": [{"sig": "MEYCIQDWvu9AGJpgPMuS53fYCAs24YgAz4IE/ji/5NMjx9LdggIhAIZDO4JqYwdK7zN8gwI50J5D3e1Dksg/fY7DmCBs3qKs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57666}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.12.2": {"name": "@humanwhocodes/config-array", "version": "0.12.2", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "15.1.0", "chai": "4.3.10", "mocha": "6.2.3", "eslint": "8.52.0", "rollup": "3.28.1", "yorkie": "2.0.0", "lint-staged": "15.0.2", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "9193412499d83a031ec33bd65f3328a829d59672", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.12.2.tgz", "fileCount": 4, "integrity": "sha512-6eNeRpbvLyylURiJVGjcvhBIZ5p/sQfLhmdrqclXM5YG5IBQe2VCwVioKymBnt1BydMV02D1QoUuFL2XUwnaYQ==", "signatures": [{"sig": "MEYCIQDwfy3p8Nu29qY7Nlb9AJqocCakaYIGtp115XRZo1aFiQIhAO41OEZnfDeHj3+nCc6FcOyWWB8IeFfhaGXL+QcIc0Xk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57825}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.12.3": {"name": "@humanwhocodes/config-array", "version": "0.12.3", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "15.1.0", "chai": "4.3.10", "mocha": "6.2.3", "eslint": "8.52.0", "rollup": "3.28.1", "yorkie": "2.0.0", "lint-staged": "15.0.2", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "a6216d90f81a30bedd1d4b5d799b47241f318072", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.12.3.tgz", "fileCount": 4, "integrity": "sha512-jsNnTBlMWuTpDkeE3on7+dWJi0D6fdDfeANj/w7MpS8ztROCoLvIO2nG0CcFj+E4k8j4QrSTh4Oryi3i2G669g==", "signatures": [{"sig": "MEQCIC2JtvbHGAZ9Y3d+SkFh8EWXKjtWlDRdgi/XQ+/IQP4KAiAPYb8hSRFrFffvnQ6FQAkPqajLudfOn9YOz7+NstHT6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57862}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}, "0.13.0": {"name": "@humanwhocodes/config-array", "version": "0.13.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@humanwhocodes/object-schema": "^2.0.3"}, "devDependencies": {"esm": "3.2.25", "nyc": "15.1.0", "chai": "4.3.10", "mocha": "6.2.3", "eslint": "8.52.0", "rollup": "3.28.1", "yorkie": "2.0.0", "lint-staged": "15.0.2", "@nitpik/node": "0.0.5", "@nitpik/javascript": "0.4.0"}, "dist": {"shasum": "fb907624df3256d04b9aa2df50d7aa97ec648748", "tarball": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz", "fileCount": 4, "integrity": "sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==", "signatures": [{"sig": "MEQCIF9B7pgU1IYECsFsewfDXH7E20lJZqxoS5mH2FjA9u8rAiBTOJGQqGxgS713+6ORIM1qoU5IpXhgUp9lSLm6Fs8EFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58381}, "engines": {"node": ">=10.10.0"}, "deprecated": "Use @eslint/config-array instead"}}, "modified": "2024-06-10T15:25:09.486Z", "cachedAt": 1750172038145}