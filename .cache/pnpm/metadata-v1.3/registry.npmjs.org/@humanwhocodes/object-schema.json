{"name": "@humanwhocodes/object-schema", "dist-tags": {"latest": "2.0.3"}, "versions": {"0.1.0": {"name": "@humanwhocodes/object-schema", "version": "0.1.0", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "10b02066ad12e6ef1c791229afbdc0dce6413a53", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-0.1.0.tgz", "fileCount": 10, "integrity": "sha512-rS5ojsDwfEkfnx1ByFOnUtG8ikdBNvLYdfiFJzLE0McMgNACgu6pIz/6wRs2aK8LSEj94LUHSMFrTi1XhyCEtA==", "signatures": [{"sig": "MEQCIGpdY20YfgEUnvnKku6j/Dt4lPL6TjkKGjP4ffm/uVPjAiBnonSjD9wYc+29uCghHlNHEkc6ddKiShhkMx8FU7RNZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVxiNCRA9TVsSAnZWagAAbdQQAJ88aRb86z6Y5Xy8MTk0\n+MjUt5raYOyaupUXvfjT0ZaK9ZtFVEkNz+efforeGixVM2Nuvf3XUXQTFMHs\nzzpdmHU5Cxu7csR4CUYx0EFE2/a9FyDlTl/FTkvoSLtSU4K4LN7dxwgUtDpT\nTmfkyOGdYpKFGmNRbJ8WaEgF1Oux4e0VNU3bw4a9P8zp1rsu/mBhOL+FD5mD\nsYgTeS8BWAgihNZQSi5KojkAeAAXnCQpmfaS5Hl4vfr7MdJ4mLckpsTlS+aA\n56VD23TNxmbAIfZKAOscFTfOdlIq2easaeE1H0+BgoSzYUYaQzpQfLu27nRg\nfo/uNwWIusPeGK6k2dtlJmBoGxY7eeP69/hvLBAW6KUFVf24IPVCOh2YZ3l4\nLKyLoNMLXCjIcUtc5tZbz3YnMw3l/69hjiG6Aj3WwcogCo6yrSMLVvIvNlcK\njl8otZ3Kfuq5L2hP3gqFz3+qN4N6xq1EZBqxBj/Dl/fXX6byWwop4o4cn5eP\nKn0ozecb/KGKvcriXkRhxQZEW2mM/sQvZiyflVnwpCHZZDzD7zMuOFKKYmjm\nZwCKZ7S5PLNt6if7uK1nPr4RD+ICxzsOY/NIKMeJh0gFNfeXf5EthqyUtAxS\nXVysg9RxfAbObQIoemIBejNg67ncIoIWgt2iAErC4u7yhB8Z1fEeHjjNa7lh\neYQ0\r\n=zba8\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Use @eslint/object-schema instead"}, "0.2.0": {"name": "@humanwhocodes/object-schema", "version": "0.2.0", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "593eadf7cb83284e17a90f925e343cee3da22793", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-0.2.0.tgz", "fileCount": 10, "integrity": "sha512-WpyA+AFcRVZ4PiFr1OKyLLx+96Pkk+q+52hmRKwAhtbj5jEALdhfkowKlXAiJAgwqRTe4n2difNZLyZ7HUIpNw==", "signatures": [{"sig": "MEUCIDw6l417a7ztyk7OI+qH2xKeoueYinqUWZtmj8n9+Z7VAiEAqWKoTLX0a6XgGAFr8r/j3cJ0QbOx6vsUOaQ4T28BAgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcistzCRA9TVsSAnZWagAAeS4P/3EUhNHQEmZlyjEo0VtG\nMxCJTXEI5/xiUCw50zXINPidk48XPL5FpeYXcPmPj76bMlMljcpxtlwOHkK/\nAocL9oZp4pRmlI2M/rrRkjy3L3SPXUtXbmfJ5H0F3QldgrQ5of5ofmzTABOl\nH4E7jhr4lIDkSM5sAd+ysavCqvlpDcSjZGiiHHBnu5s7S+7D3xhx1luV4+YN\n+nQuT/NYG/v5JfqtEHTRL6gq2VA7+m7ziYtGAKBZMtQJ4oNwMcAcU3x8pgoe\nuDq1AuAC4BO5jxPcSG9Wy0d9A7H7NxP3HBEwX8gTn/tc7vOQhJw1Q7pLCjrb\nHoLvGRe3xVDdBjpgL8fH1FLOguyUE5nRhZJ12P+wRGT254NgdMmpVzw6EqIU\nAsbmH6FkNBaE3VRd9r5w1vDZUmJBDYR1YWQ7tgwS2AT55bxXG9HTJp3AB9mR\nirTDb766eHCYwlLIcuoDG63EOVc2lCCMl56V0kTHr2KCLS3oX1X3XgTTzntX\nCTtJwBw/EuiEyrmjlLSQ+NYyrcmAC1a42ZD7zxCAluUeGi0bIZUBy68H3hp2\nCnMFJ+D/v+EiMmDqwtQYrkZqRKMlW+lbsiuMR2EH/wv+Pq1WeEk4x72uMrLL\nkBK/BVM0rhI/FEPu4jUxwr8G+Y0F43j4WZTaH1pYbUJvsHB6kQLUTFWZDoe9\nYVE2\r\n=MLDA\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Use @eslint/object-schema instead"}, "1.0.0": {"name": "@humanwhocodes/object-schema", "version": "1.0.0", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "4f30c42058d11689c6f159a4820f1eee216801b3", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.0.0.tgz", "fileCount": 10, "integrity": "sha512-9NgczXoVzCfTavZfw5x4qB9SSU/gyr5lM+3gEQH374ZIouRMrMzC6+sK4Wf4dv6TVR8aetvD3rJi5Br3HkQj1A==", "signatures": [{"sig": "MEUCIGaoPJi3xbQhKY4jhQTWqex03orDQrOCXnOmmgd8Wzu1AiEA7g9C4YQihkjV1AM1v/CVq3GfLs5gSKk2YioCfKnBqVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckBhZCRA9TVsSAnZWagAAAjsP/iawP6CwPtldxSVkTKRu\nqFpK4egfWFr1bjC6ZFzXgltj+uQO+Gr43Pv22oLyBaQO/TVRYIFeoSTyW5i1\ngHtH0y1PfstifCjcWvRcYgcJoowDpgxLqVnsqm00iZV9bF59zYcAWOjuqJYE\n7uXiEwbpVr98GxOe8V2t6dt5rI5INE2U7qkfrixyAYCId1As2ibayIPn6JDM\nwFehLjlAxtlezava759b1/xH+wDX7xb04A7LMowLFVXimYANdnzy5MpCAO3i\n2oVAmidIKqG1e4caV5jthOsvkIP3CQDJbeRfvefJnXRv7mp0ojLDIdZsYge0\naeJkwNmwuWdND9EVlNUyJxukbI9hggGfKyg6iONkZaakqTAl2nYkQ8eqcHVc\n8bGsqE4FNmj4hI61ZuDnM71y9Zgq+fxRj1Bl7YWzzL9EkjBENOYaCtFCs1Nm\n73Gx/l6pVWiE6t8lRTBgc/GXKS6ZvDyeBMgRZTPo2TW4iid7kAAuNakwMzMM\nrNuyQukaElxSFRBjs2ooblZrDl5J8AcGxINPssEfk51+32xyBt3k+PtbJcR9\nMMnDfLboTu25FmJy3kQsUGPsr9KVRn9Cw7d1PWyiwnIGFBwUhXaNW9IQ+OZx\n+/JYbqRTnvyT0DzdlWnA6LSgDxFc/mbqLXbNxj8IqU+VgXN1Sb8qGtQRafTe\nFUol\r\n=zcSS\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Use @eslint/object-schema instead"}, "1.1.0": {"name": "@humanwhocodes/object-schema", "version": "1.1.0", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "e1f3875d9a313dce79019c313a263648dce2a7c6", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.1.0.tgz", "fileCount": 10, "integrity": "sha512-lPytHMV9YQc+eSOH9wGDKGyjJWxcuHtFSzGCh3CpdW9Yqq5Jg1DhZ7tQxDY30YFzlNtZVuFnoGihVLRP1nP6Iw==", "signatures": [{"sig": "MEUCICM9F1i6Mj3ySnFxDazVeiwg5Te+sFimwnNEqGjpG3TvAiEA3CDJ3jkMHwFvq3cHJNaUnwaYGMww4PziA1DeBkgvJlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckmZ4CRA9TVsSAnZWagAAVIIQAI9EFMbnlAMRjihINHhe\n081h6HJd4OdX6Ev/o6c/+LlpAByAZbApdy+j3bIAGR1OiC4erIYs2Xg8PMap\n72G6rKVy1rh+So79EQ/zkkQ6eRuTcrBVPfM8yvpn6XcqWRbbgGX0fvTaEX66\n6MHv5vCi/LvbpIZ4/YgUSfeN1iDd4Xxa2Bsg5NxSC00oWKJggj243d0oHcVg\nKSYtA2LIyIUngroRrhV/vkCnKna+c7pBfIz3y/wfmPsNAQFoc3Cv/aTAO+9L\nF13/bH9htqrPHOmEjyxGtMs0xcu3uKM3uOnR7SGdYKbELMfW8TnGzSiKOcz1\n5DL31yfJtX439DxQJKdZbO3vkKIYz5SEXPv/tkBhutLieEIPOtXXlIdy/iiP\nvQmW7trANL9fSfOVSpmT5elgb1uS240hgyEwDcRXUMZ7ytwW2+aH/bY93OJW\nVXGu9bkjIie/TZ5CBlAazrMN2pE2uT2I0FD2tK2I0O/uskN7paK7M0oq1K/z\ncfQ5rwDY2lvJflQrWtLuJn3hGdJ6jKdcnUvqLRt730zog5DCy9vjSk+sGP1a\njPepb9fvdTDGv4YIBk0SV0bDiX/88AkyjltygHeVeSkbI7aJwHf8EC0fv3AV\nvUQB8fDR/hhBtB7yBYZfEszXbiW8PWkcaJwUWqICTkwDlPL/45Ajv33sz7XO\nxwDb\r\n=1NIl\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Use @eslint/object-schema instead"}, "1.1.1": {"name": "@humanwhocodes/object-schema", "version": "1.1.1", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "d44fa35e3d23e0da99a0e4d476c08be4345be9b8", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.1.1.tgz", "fileCount": 10, "integrity": "sha512-3Qir+CJkZhtjQC54+W+Exi3Wa6m4Nv01snd4VxT2SBuQ3wuTv7fntHEmKTx25x5QYAWaB2/IJSnmshApD67mVw==", "signatures": [{"sig": "MEQCIEkTLdmXAnjJsiRp9L1jYt4TCMUsEj6oH1BSagrci6epAiB5FNnFJe6mTXbBNXHOo/1pX34Ah5Fd1r2j756VuFBing==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJck8DjCRA9TVsSAnZWagAARuIP/3ShV/5Qnm9/sxNztJ9y\nemDkEMXjcApx3Hwh9KQaWKc/LpFFVyiFI7t0qhOj3wFw8in9zFBi9wep+Rq8\nojxXG46852BCQQcKE9/w8Sf0WrqSp8LSYxWA48R4P+JaYYVXPtqmKA79W5vu\nSnZsQnU1jP6CBvX2Pjyx6a9uGaiv5n7gNyLF0yEw4fVicPlnkJ65XgpVL0LS\nkVfmrx30CoW8NnDPApejHZ1w1pcG5DxNu1uYzkFJs1fHE9BaaNz6U6ESkTFn\nszjG77oCnz27cdceSzUuLicwOcxN+VwnwTD37qx1SuUDCGGxVivgFT4AJDn6\nXvDv3j/6uSNEZndRYSUXT+D6bHAOYPtVMMwp871apwuqPLivSAQQ4M5CFLlv\n6A9vAQrKGn3WOIbsqIQwp6UhJZDK0TrINVolkU7cw8mF9n6UNYKRcuoyMzpL\n/O2L0MMlnKaFj3a2waZyT2h/yyzMQ0rV1AKG1A4WDZizRtXL+i/Aj6F4Q50Y\niBkdgSJNJTMDGzMUoJAql7vC822ROS/1dF4dRsovT8PiB7PasyLl52AUJ8vt\nrdNAP+jutubjJQXQc6tQVP23jvbZjaSJ7zCJQd2PRleGM2JwkZyjmpud5Z2r\nN/Lg4KD6VLVCxBNPNYU8+4+1n0dRuPoYWkmXylAsITHA+DpWBf1lQgdqNMwo\n2Zz+\r\n=Mk++\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Use @eslint/object-schema instead"}, "1.2.0": {"name": "@humanwhocodes/object-schema", "version": "1.2.0", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "87de7af9c231826fdd68ac7258f77c429e0e5fcf", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.0.tgz", "fileCount": 12, "integrity": "sha512-wdppn25U8z/2yiaT6YGquE6X8sSv7hNMWSXYSSU1jGv/yd6XqjXgTDJ8KP4NgjTXfJ3GbRjeeb8RTV7a/VpM+w==", "signatures": [{"sig": "MEQCIAegZ4CAtD8t88msyJXtB3W4c4naUGgLEJMHkwrVyoqfAiAKbJLQq0SDJW1CZpNNN/ayC0/mIX2+U7EUEhzQBG7R+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgVVeQCRA9TVsSAnZWagAANSgP/2euzfJPf1oyYNoWK/QP\nKaKV/7eTjiunTaq1E2yg4fuiYoa44xen2FVvKsflNJwablO0ol7899UKlnaN\nd3q8wzhf4BVJj/zFoWC/jHA/Q3Xn+gFySmM9ic2hVnciw7HxST1RtSB1YFtA\n2dzPE9/Peswbezja/0dYRlfa5PgJGML8FW+J01w1NtMSSQmafq8k4RxiYUQa\nbnOq2RtHsdQ2IOXsbfCWjb2OiOyOAkdsKMKlCaI/e5xc6VaG6EU6hnJw+dWQ\n9QQccQLe7lVuCBAOUyBGmQwSNAl9xvCrCEyzFTJvG0u28yjKXYjq5Ue4G5CM\n3Ue/vA0lKleHt8uAiZ1RceZGGITEYM42uIOr/8pfsnkB0Wq3kTc+byw/5lbw\nhrimZ1JHqh+Qej9sVcAnIUEeDsW7t0TB0m7+q0IfWRBl9OsCt09LCB21H3Mf\nVm458MhwccX5TQlEG1QW97DrENsYofCzb+JKVDGVRBPD6Mr2pZG7PUVuHIUm\nbBlpN+W/NC/ModuPs25Z9odJvzabAKpniAzuwyxWraKdGp93cTsRlulTsaUh\ngjsRPqJMycrTUSgrEY+5chQ14flBRxmqhfe+8zu5gT858JWcWn3TNEcAlTrt\n8u5JWT08apJXrVwl2mZ3Gp8PatDXv1B6HHeuVCDjKkInR8mk2I3CwFd7UQkR\nkVKg\r\n=Iepv\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Use @eslint/object-schema instead"}, "1.2.1": {"name": "@humanwhocodes/object-schema", "version": "1.2.1", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "b520529ec21d8e5945a1851dfd1c32e94e39ff45", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "fileCount": 14, "integrity": "sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==", "signatures": [{"sig": "MEQCIDhQBPKQYFpKqoTEAS2IaDKHnVceXIEtbzGFxlJgAOJqAiA25z20rbSlVxDOj/6S72j0h3UClg5ISlT2WYmHB1QJRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2K8TCRA9TVsSAnZWagAA2SMQAKUss9i9x9fXUo0sux7L\n3+HpfzynzzI38ZoeEfh/+NRog2ERP86tiK4lDw3mDLaIASRejdXKVveJNq9Y\nORqSja/Dgd8QwatT6oVSBlDWGmMqOx8FWiFjh7obNIezFbIYzEFnYX5AtcZT\n834bIkd+NND9N6QaTT0HD+7jdxS6DDixNKw6eqLvAyaCFc2SA6BDD9IKDklA\nEEn93QhnxzvCiHmh7cuVHLaEI2eeCvyBpiZU8JtG8IPU7ohAX7ik2SyYuT5w\nR6hcyAquX8PPdj200P7Bzry74zATjZWtXGYQDyFghkGYywWTr6IPZtt3mHCX\nKoMGK4qWXdKXsD/4ONRLVahropdlgUlhudAkK6ZBZmOpCZiP41ERxhVxaxwH\n+b2R3jgRwxMR/KsfJTLHz02ayqoeX+yvqB7YZ+1zDubdNcuKu7NXbNHZLvkQ\nm9LEGDNJ2Zpg0xs9DCSom2zuEMZhML0UplC+OexhibZBOO6BibyZyxbJlqwT\nEqDVzvGAjlu+8R4+cEKV0UxebHicW3gMAS9ebzC6mMfKRvJ4F2fluWAitPoo\n2JAa4Dmnjvy/4XJGVn9H/s983OJFATi+StlVzYqRGWVaB5UhE3YxOLZ9VcrG\n+aUUI5oFw0Fj2WFo0lifDJJKJ8a4JreMkqTroPfWrBeuGV/x5axZUzk/fTyt\n5uY/\r\n=mPSX\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "Use @eslint/object-schema instead"}, "2.0.0": {"name": "@humanwhocodes/object-schema", "version": "2.0.0", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "04ad39d82176c7da1591c81e78b993cffd8348d8", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.0.tgz", "fileCount": 14, "integrity": "sha512-9S9QrXY2K0L4AGDcSgTi9vgiCcG8VcBv4Mp7/1hDPYoswIy6Z6KO5blYto82BT8M0MZNRWmCFLpCs3HlpYGGdw==", "signatures": [{"sig": "MEUCIEah0CB3Wu9qLQBibg4rCtzbT9zmsklFbz5va/nZ4liNAiEA2ns4S36bXc+fH4fyJ0N8fWQzYZn5IcaWQEmoclUJShc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52436}, "deprecated": "Use @eslint/object-schema instead"}, "2.0.1": {"name": "@humanwhocodes/object-schema", "version": "2.0.1", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "e5211452df060fa8522b55c7b3c0c4d1981cb044", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.1.tgz", "fileCount": 14, "integrity": "sha512-dvuCeX5fC9dXgJn9t+X5atfmgQAzUOWqS1254Gh0m6i8wKd10ebXkfNKiRK+1GWi/yTvvLDHpoxLr0xxxeslWw==", "signatures": [{"sig": "MEUCIQDFrroaDBrrKQ+mjblgvy2x1QvQW6RQxsy9WoCT0sMX6AIgUVNYTSRqIZm7EA2at3LmJfK5Nh7Wf85HDzr7juSOv+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52992}, "deprecated": "Use @eslint/object-schema instead"}, "2.0.2": {"name": "@humanwhocodes/object-schema", "version": "2.0.2", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "d9fae00a2d5cb40f92cfe64b47ad749fbc38f917", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.2.tgz", "fileCount": 14, "integrity": "sha512-6EwiSjwWYP7pTckG6I5eyFANjPhmPjUX9JRLUSfNPC7FX7zK9gyZAfUEaECL6ALTpGX5AjnBq3C9XmVWPitNpw==", "signatures": [{"sig": "MEYCIQDYDHJRXjejFn4GQM0KBhgCsu8GtsQCPQkiw2mexrQALwIhAIEKdZf4z/X/AAO4wvO8KSCjmMKj6iUQhbdBcEz8ffND", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53303}, "deprecated": "Use @eslint/object-schema instead"}, "2.0.3": {"name": "@humanwhocodes/object-schema", "version": "2.0.3", "devDependencies": {"chai": "^4.2.0", "mocha": "^5.2.0", "eslint": "^5.13.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "4a2868d75d6d6963e423bcf90b7fd1be343409d3", "tarball": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz", "fileCount": 8, "integrity": "sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==", "signatures": [{"sig": "MEQCIBl2rHeEZD1tBZ2i0DnhZxZqIAx+bIKHllLykrIPiuMiAiAJ8uJWcgzJvWwt+VbrHrCKooMnn9Z+H4QrPfoPaHJlfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23251}, "deprecated": "Use @eslint/object-schema instead"}}, "modified": "2024-06-10T15:25:59.086Z", "cachedAt": 1750172039883}