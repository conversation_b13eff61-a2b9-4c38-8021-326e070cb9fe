{"name": "@babel/parser", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.5", "next": "8.0.0-beta.0"}, "versions": {"7.0.0-beta.48": {"name": "@babel/parser", "version": "7.0.0-beta.48", "devDependencies": {"charcodes": "0.1.0", "unicode-10.0.0": "^0.7.4", "@babel/helper-fixtures": "7.0.0-beta.48"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "f93895cbacee703c0ec98e5af3901c77edd9f1d7", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.48.tgz", "fileCount": 6, "integrity": "sha512-X3pKxvy7vL79zc/6XS6cCObyuRMnsRGRu7d3zVSPZGCdxkK0/wTeFRwseRjcvhReV/6LW2D8H8qHVFFL0c+5+w==", "signatures": [{"sig": "MEQCIDAA6v59EOxXdvlxd1qBARkqQnKBlFXKh6jsUWh3QjH5AiAU2vwRBMq7QoDx0p8aFo8KwEKPV1ZVfucF9oSPqrPbnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 380503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCGCRA9TVsSAnZWagAALkIQAI8MHP43v0t5yW+5eb8a\nFvCKyfUV5pYIAgLwyT6QlxWhW37Yy2x1jqzQPVFDz3TEvXHSetOroranW1v6\nH3RrGgFfV6ouyrKnYgk8JpTXHWlHKXWB0m0BqkOrMQwDdQVVVeIxdpQr1yrb\noyt5AjRZ272dKN0Q4iQTeLqC6uxUXYlarbBRUgWXx5BuLh//rVjIJGQ5Fj2m\nZcFFGRjbyw9Hdb0/wo7x0lV6VEi4Oh2wRNZuskIFBRNAiHdrCLf1IZEDsQRE\nGRXuKXc6Xj8LeHvoS2V5Vvs7dbuUq5lq5oiRiTEkPt032MSftU8tsl4UysVn\n1krvHCQYqNCg5WgKJlqXZCae+AxUWj9UacC98aq4vAeZl3DVzoJ5n5ENcHcI\na9Wrs0qumfODoxaDOSs+DNXAYnSc91zoguH187OOCLfas2Gw1MyRIXnJwvnH\nVnwnQyFBuRDDigxBGrRVVFkY6cf+vzoSaVIs5VL8wI85FDtKK/JmTtGwWLtk\nE1xG2tQVl622gWNZqPvoKbS5U1glbq9oPq7ECkPwWA9DSzdBidXfdezyMxJe\n1weMKBjz4+cOvzDS5ybdDPjQE1HlfQ3ffWz8nMm1agoLMXBz5e9pqYo098sL\n+FbijDCbi2Db3fD3UT1DBFMp3+GHuhe4/Aiow2TgL6l9rPlijvXVtAZdnRPV\ng+id\r\n=kdOk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0-beta.49": {"name": "@babel/parser", "version": "7.0.0-beta.49", "devDependencies": {"charcodes": "0.1.0", "unicode-10.0.0": "^0.7.4", "@babel/helper-fixtures": "7.0.0-beta.49"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "944d0c5ba2812bb159edbd226743afd265179bdc", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.49.tgz", "fileCount": 6, "integrity": "sha512-rFcOXcsUXcfmQJY8mN/1zfgRYq4A02e9GiJRT3ai3TM81F2iPcnUV4wG/DUrZITL69RVHR2FM9iYM5AK8E7fcw==", "signatures": [{"sig": "MEQCIH4Vj+evhB8vvQIAZmVrLVGkQEXmr/0qrR5FwOJ+jkB6AiAiMzCD5uKyk6HkzOdJH94NhpIm6wCekkBlOCA2oI7zEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 380539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMMCRA9TVsSAnZWagAAHWoP/2ySWeX6rqdKww6gCeJG\nojr30jIga9mFPRGncr2r7rdoZTuuk1qZDmBq7p8BurcMusosKwCrlBFuxaVW\nQNn0lX/YsDXr7txzAIWBrZDGc9ZVUyPNaiNU8ycgSeo+++2xBhn4Wb9aOtsm\npy8lcvT7TY6znjVE/oFinjBFNA9Q218mzTtAuxaBbL3HqcMw2O75EGX9nHbf\ndLHxqixFjGkpf1USXU7hxs8MBZ9JY7vD/AcneJnhRc/TbXAhZ7Y4JUXung6+\nC0lz0ph6qpPJvzRASw1OrQsmgJ39Ioa1QfRT7IVtEPBb5rsWdOO5OHpCmmc+\nR2RqkULbunAJx80To0LSCLbDR6Wy4qoFXwdaFgkukNSPQ4l1G0a9RFkdf6tm\nmxl8X3S3J/9/pWQUTGa7A9fKQf1aoTtowZA7scFYfWgWInysn8caTSZ6nCIt\nfV1XP0ytKq6gSyhiPRwC0odisASmowPTwGy/IJdelT/YHjKVYwBJLDeZjymV\nPM+6OhLVzO1LyoxZG0YHEzesjZqxGZfVQYht6TTS9gvT19u938SiSyHhboON\ndrGz8cvcniV1otT379FUwjG46zauuTcv8TgGDGOnoNax8KfVb5qd55pHHsd+\nq4snfd89jwlCqTeEss1xVGxTw+c6gZvHmtjs3dWDzcUEJ5vJaPnIT9vCK/OK\nj4cP\r\n=VaoJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0-beta.50": {"name": "@babel/parser", "version": "7.0.0-beta.50", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-beta.50"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "9ffc59e4ca51df0a6cc8b5d1f22d7e534bb3fa5d", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.50.tgz", "fileCount": 9, "integrity": "sha512-H7zivdk3e61mMaDtmK4XMknROoHlAirhD5rG2gnyMo0VbunMzTWKSD4aGLY6so2FPsi26VtT8tylwjlRz3dNcw==", "signatures": [{"sig": "MEUCIH5tJRAHHAxkMdE97o4a59bzCBjJXBEPzB0ZnuL3gO39AiEAjqAhPqTgXqK0IKNjODRG2MJAa5BEpKUje0JbN5S5pQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372331}, "engines": {"node": ">=6.0.0"}}, "7.0.0-beta.51": {"name": "@babel/parser", "version": "7.0.0-beta.51", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-beta.51"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "27cec2df409df60af58270ed8f6aa55409ea86f6", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.51.tgz", "fileCount": 9, "integrity": "sha512-y62bVWBe50ulqJxTiF6siQRmO5sXCmEZDAvUZiu867U10UUwQFI7QjiI/MgfWXkX966ap9rMims1rfEk05r0AA==", "signatures": [{"sig": "MEYCIQC1vNQM3V8u+m+XN+qdNDepdYicVTcp8MLIJy4Pe/hpGgIhAPuwY9OaV/OeRjxKueiLL+2GJlBkT/MHOV8ms/Hs3+lz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372331}, "engines": {"node": ">=6.0.0"}}, "7.0.0-beta.52": {"name": "@babel/parser", "version": "7.0.0-beta.52", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-beta.52"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "4e935b62cd9bf872bd37bcf1f63d82fe7b0237a2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.52.tgz", "fileCount": 9, "integrity": "sha512-1yK/5GCWjDaZkcRaeym8TsklEf5UiWGFT5U7v7srAmg8H/covDVCYLkUNIKkMxFx/ufEaSQ15+dnuA7BkQO+XA==", "signatures": [{"sig": "MEQCICpzXBU+xTDYlbA4CZ08XFv2ew6y6e1fGIf+G/b8vac0AiAdF4uaFM9zVyW01sS0L95RTkm0JOnt0wBWfFmLu6l4FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372329}, "engines": {"node": ">=6.0.0"}}, "7.0.0-beta.53": {"name": "@babel/parser", "version": "7.0.0-beta.53", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-beta.53"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "1f45eb617bf9463d482b2c04d349d9e4edbf4892", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.53.tgz", "fileCount": 9, "integrity": "sha512-SYoyLjcE+D28Ly2kkPXP6eIVy4YwViRSffri5WHi8PRxy8ngnx6mTXFzGAsSSPzUN3DK+sf8qBsdDGeQz1SJEw==", "signatures": [{"sig": "MEUCIAxzuKibJYdHZIEAWmZHeEClwpPUi7gFYkTvMXdkGwg4AiEAma9A4kAEwpxTxCIafilkzD2i2TgKPUmF708xL58EZ14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373881}, "engines": {"node": ">=6.0.0"}}, "7.0.0-beta.54": {"name": "@babel/parser", "version": "7.0.0-beta.54", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-beta.54"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "c01aa63b57c9c8dce8744796c81d9df121f20db4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.54.tgz", "fileCount": 9, "integrity": "sha512-2jT3u3JB83zwYO5fk/QD2TwMRg6B5BmgF6bFM/1uVFxPYdrJzLrNjip1AtqghSNWYJFDR5fNwKYYtupHCkeCzA==", "signatures": [{"sig": "MEUCIQDr59P1G1TJOOyADvgn6FJrL0REiwcN4rhTEJhNWWvOsAIgePlEcpf1xx4jC5dcSnTT2XLJikCve4VCbPnwEqV4kmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373881}, "engines": {"node": ">=6.0.0"}}, "7.0.0-beta.55": {"name": "@babel/parser", "version": "7.0.0-beta.55", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-beta.55"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "0a527efc148c6c8cd85d5ffddacad817a2daeeb2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.55.tgz", "fileCount": 9, "integrity": "sha512-rMSWwnceZ/MGbP42VKcThkY6YVWq9AoX/Um9Jxfl4ZOnsMnZeOnL03jgNZ4YhB7yOkQNpF9A42UiYz81NDPvmg==", "signatures": [{"sig": "MEUCIQDSLZMiEC5BwdJonoPGscPNXYj1BTuxJbI2kCCEixdQPwIgCRFFCvc11VYkCEsorv5XQD+kn+Mjj1uiBNkdzbdW9IA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374307}, "engines": {"node": ">=6.0.0"}}, "7.0.0-beta.56": {"name": "@babel/parser", "version": "7.0.0-beta.56", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-beta.56"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "8638aa02e0130cd10b2ba4128e2b804112073ed3", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-beta.56.tgz", "fileCount": 9, "integrity": "sha512-JM0ughhbo+sPXw2Z+SUyowfYrAOhjanzjMshcLswBdXVelJCOeEKe/FqMqPWGVPQr7wByongXIn+MKdCpY7DBw==", "signatures": [{"sig": "MEUCIQCYPrzPXHTAtnF2erp3Rbv3hRXgTh08dkQbPUKNrriILAIgLO5KEJYz9alRrQNa/8s2pwAUFx1T5Yr1Ks+BNQQTDJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPtACRA9TVsSAnZWagAAum8P/3eerRrcWmTn4sEr6c7t\nbRt5N3fnCTEarYWICQq5U8ZQmJrjgAJeK9rYVeFWE8o2oQ96gLSLKUz/luOJ\nNOPRkUjUrfyt2GX+FQAWi0dL+/0GMibNOf9cb/DEk2UTXHrNBJvY1G/Lx9zZ\n9nkmor1V91KIG2U+GN85WIGgpsG+vOEbsUn9+dVivAke3AsgwgaIexaOwb+l\neOUV2mYLhEvpZztud30dV/HSupOtD7xGNK7X2yYtnVdPl9J8bIMtnLzHHnKn\ncPZdZE4kaud7X0DU9O0okbnAxla91lVfYxbES4JlPUgiPTuGbY5Ab44OOANK\n09pOeyHQAf5uOGkywXClEo3NtIVz+mIY2+WGPBPvLeTviABHgtBpph8vSmID\nqBOU7ShTckAj5RZ1JjbF7TBTCunpdYZZsUjuErc2f/I+rlg2rHxQUHnF6jLO\nhIwLCxDZCStkWOWnWoeXYR3Kb1ebj1Z8KpM8FceEM5XcXH7BYnCCGn2D2NIw\n8vWcolI4ZGok4A2KToKKrZafAHqxvIrDPzjriBjWPRPcroh2XPfCUb8iWnjs\ndtz08yOtw/dOk6RfsfSlo+hKT/SIt9MdpINqkCDx2IxBvsgOkJqFWZYaBQg3\nLVhzlL0UAKZ/HMRG+bFcj8u07vcmDrmlW3NWdqvOZV5pMUuiNupCZ0XLIX6F\nJ3w8\r\n=sYe7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0-rc.0": {"name": "@babel/parser", "version": "7.0.0-rc.0", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-rc.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "6ad941b6425e7f5feee909e3bc3e6e8712397541", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-rc.0.tgz", "fileCount": 9, "integrity": "sha512-G/NZNnuzemO7mzvpKdTonhsisgKTcXw5sV2lBumD/FlWLxAvmmITfHym0upWMN9xAMBRlq3WAdWXYju42NOSIg==", "signatures": [{"sig": "MEYCIQD9WS96V7qepNF9jkkFqj3fJ/pZKg5RU0XPDyibeyR3tAIhAINIWLUTJcQTV8raXrJ3NQ/kotFYVdSANFwiYuy7OBkS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGQdCRA9TVsSAnZWagAAKHUP/08wduLSFYGKkg5WX2le\nd8aBiiwfiC+hur2QL5K7moIk5xlbd/H1YDkiUnctpn0bV3MEjrAX92/ef2Pu\nKPNBskK5t04N+muAj0Qoa/DDLBeuH7GLuYzLdbKTiWB8d7fo+skcM6NXpRQI\nxRKlPOJ/A6garSmrFgqAdJwlqr14YSk5XPNc9/C+uYmDUxIS357gyhNZfvL7\nUSrFBn6PzgvNEwNq4IG+xLdqSPZdc+tUrakL427X+0zS+lATgI4t13o1zekD\nHxK7bEMAjcWmyewzC4wQkTWrE5WXc9WOAxhVPguuKicNyuQG8K4wxlSTq8/r\nl/DRKq64pYVFfzp9h1hIPfFtvattYSCBFn/TfbLZ+YQeAxSf819aFTH94kR5\nMNZ5pf8DaOao/rP3vzxGpmcmyg/KpVY9LG/J0JKhr1m+9T++DhPGVRcOExzc\n7yKnDWlSn8jJKcYlw9VUCJZo7gROAPUOROzlcOTx1WoHeli/y5dG9bfieYel\n5mT9Z1UN2SanGOBBGAYnUypHIvr5YT90DX9/tFcI6U7N8lQFHDoymLhEvFBg\nFbq3U2IlK8irls5sSCR9DpvPMgpdLDWKF52SynGTlZPBvv4hu94D8zHKu8hk\nUcnpbGYkT/PfNFn0cVXkNW3lMORCtM18oDkuZ3ovEFLprJlfkV6ja8IUwtMc\nxNNm\r\n=AeMq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0-rc.1": {"name": "@babel/parser", "version": "7.0.0-rc.1", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-rc.1"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "d009a9bba8175d7b971e30cd03535b278c44082d", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-rc.1.tgz", "fileCount": 9, "integrity": "sha512-rC+bIz2eZnJlacERmJO25UAbXVZttcSxh0Px0gRGinOTzug5tL7+L9urfIdSWlv1ZzP03+f2xkOFLOxZqSsVmQ==", "signatures": [{"sig": "MEQCIEGPyqVTUQFqbFxLghQyGGroBVQdRQ+VBv7QX/Dy0wTfAiBiZ2RkPJsZXngGI2TZMwPZ5FA++GyWIn0TussDuMXlHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ64CRA9TVsSAnZWagAAtCkP/38Rg+xEPHh5TgTkLZ1H\nHOHnWNazcTVQz5dKzn+gjLLCF9fQoWbygfra+4oeRXWIxKa7OUBjtoeFsp2b\nn9G7p8GiELdYOUYZC/Rm/wZSWrvlOxltBbS5rgtYZuXJA+YU/ujpXWATK/Zg\nKxME1ihN7sS9+z3hlb4jUfkN0Kb8k364b8hDOp/YAKaDKcB27dvufi8fVX+d\ni4+kGK7EvV+ccSPlBTIMQ+gpEsmW6zvIV2OrwR9aUPBPnVtFF+SSxeeYQ7J0\nAQWt8DH+ttaOVW7iKgjdOQm7p8aR5A0dJqIAi59N8Zgw73vlYq5AsbFQzUvC\nw5HOwi+xgDMaMYVClC5PuqoanZtHmvpm6s03monDxIobPQo560ULxdBngvWS\n8N5A78blW0gSX5CxmAumML0I/qFAKJIFqfZEqSTmsJjlf4jk3IZVE5iPmtGE\nYbZ/KI58j7GJt4//oO4Rg/FsT/Kn7SVEPStIy2lSgsWkfDyQiMnPeupxY9GM\n9EAaD1yuMX+eW72EXRg5JECzWVWcYX5hRwYpLge6rAFcxgUjvdeSQGqndu0u\nzZv2b3/zUhNzAf10W4/APJdGBk8ZxeM0vuiHqBnnIxQo4iOiIgFtOsGDZAlI\n4HP9ovf2UgPP71slAd+z7GCJh0hKl/exlIBD4yYXTx50KpJsm9ex4eX3DQ+1\nOz7m\r\n=8caI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0-rc.2": {"name": "@babel/parser", "version": "7.0.0-rc.2", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-rc.2"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "a98c01af5834e71d48a5108e3aeeee333cdf26c4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-rc.2.tgz", "fileCount": 9, "integrity": "sha512-zDB1QPgQWYwuJty3Ymbx1hq7zbBEbZjTprHOhforvzyQFV86LNh6FS0InjnOUXM6p6QUyONz8KTt/v+MRMd0Hg==", "signatures": [{"sig": "MEYCIQCnvfJY1viGmAnalMIe4eNPVnmgOc3hyikPXheFU/Q0zAIhAKPUV9buDxNE5KzfrTJVJW22tinRhE8F/wucdeXUaYxA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGZoCRA9TVsSAnZWagAAvfsP/iJ7wBW9beNsUSB6VUtU\nCRGzDdPoR0H0nKYwOjId+b0kXyaYhSu27HQ96se4YpKZ4P6U65imdohkUs2g\n6NWJUsvkKAanRBYM1lk6bx5FZS9Uz7uq8ekjAVdzKiFfjAABmo+xpoy0JM4W\ndiTxc/ydvAjGJzOvH1J3TlR7nBhO3eDVw5QQ/iAG6qMdpV3cZG8HiyKZ1EWB\na25PaTZtRZgQMUPHCJmIyOX6fg34UQgahy+dWFNVfuWjVXJ753avHdxQSELg\n3kp7s3K1mlYBfSeCS7dmSOuJgVO1eTLw7i1bwzbunGBc+Ssgrbrk/X5cHPEw\nb0too2qomo0/nf354Us1yFAfUrHNiqILuAPoer4iO1s547r2trBY5cyjNLUA\naZMtb8/DZIqJVmfcWLldIIskJ5hbnmswyTRu/3bLWC0WkzK+R2ZueZrcH0eW\nxf+qSI/dDESNCog4y7mh2NVfCyEh1KgNHvxyJ2NedWe5PTrQrQShC9JY8UQo\n1l71BTFDlf81qblEiwZ0RhRxrGgcB9Uk2K0QEfTuvh8gr96ZXpoKXPa2A+q/\nW4sCmABqwV/FBZDu27u+tSsD7OHdRRxeyTF8N7NxyNz6qqAXx/0d1b/6Adyy\nqA7pcjZayKHrWP7NQc4IAcm5VNLiKgVYQ3S0qTVcUOV4uPYM1+4sJjtJMFZl\nJ4rz\r\n=N3Px\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0-rc.3": {"name": "@babel/parser", "version": "7.0.0-rc.3", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "7.0.0-rc.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "859d7b60ef6b939aab5f6d4f4bffbb7bafdc418b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-rc.3.tgz", "fileCount": 9, "integrity": "sha512-oNVEL3NpMaC8q1hQwWrmgj34dAT3KIAK5zhVN4V6YTzotJOSK0ul7SaRm42YDdP56aOkzvGAZylCVtvu6jREhg==", "signatures": [{"sig": "MEYCIQDbh9ROKbuHUCDif9pKde/Ho4RHeAtzSkbq0797EaSYvwIhAMyqA30cjHphEVX0Q06qZPRe+V0i/cMV+5ieOVOt7NH3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375454, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEkUCRA9TVsSAnZWagAA6nwP/1m8EMWQPcWfv2qC6jgf\nl/5HxuEwwrznJcBHqYQaIrl/OeoW8VQsTZ24xpHzUU4/E0WhqxpWGE02ufNH\nZ6lFYaRSIA0vyyR7o3lJqDdHgq2N18EMkl4O8ej/CuviN2eRE8owLOKX1GrL\n2ullKv+ZrprQjHmGr3aGauuApYAS4VqoLUapvAtkuyfUsrzbidlZ7/XzzyWt\nEQEAq8PeZ60qcNnYrhlpeQWhkiAdXtW2zV+fbk9bZZKXSEQt+XML6AbtSu8c\nSwI2tLIYihN0Kxz89R+FgbqtCRO8CEOITUGgEpdff96D/8+Dz3bDXLAX1mht\nPm6gY2dKyrP6y/xQ+eXOxJYBF5Kh/Jk0sYT6R3WcnKwaEzt5/2pqj7YJbQKC\ntRT3mc+Kn/RkANDqUlsOg9HxCVA42TCH6+mCMw1w2VexrDAViY+EsAgiDmEX\n/8b+P7hJwhnZq/raUxDOMUDkWpSOL30pR5Yfwz7rVJO7DcQW3c2qOo2pbgdJ\npYXCYBDdbuF7rsiBHHQ0XSJ/fGU1WolSmqPN2/kOwq6Fk0NdgFuntEZEudhq\n7Mj8cD1PuTawgQEtjgmln77yYTq44fQkpte+oVuDwCtpCkcI8pZp29n3bRMo\nyT5cy4hs9rC8CO3CpbpeVuvMBcilQAlvkIufrYFgkEAXKUN+FGbBo6/jquWD\nd3Fw\r\n=tMuU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0-rc.4": {"name": "@babel/parser", "version": "7.0.0-rc.4", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.0.0-rc.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "c5c773df2554d76c9fc0a3711c18109fce3f54f5", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0-rc.4.tgz", "fileCount": 9, "integrity": "sha512-X4BqD6IwlJleaAV2iDBe4pTOVhhJ/neQqHH8gFQXiAIeTSmwGMdXAUky2Hhdc/2Wpss0zW6CXtxQ5C6NBJmxxg==", "signatures": [{"sig": "MEUCIECSKsAdXk4S17ZU/NdzVhMkUirlJECaP8OdlCanbnstAiEA8yco7V8eyp+83qzyRTCMrGHZZSH6LlRoAeJM55DnBXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCnsCRA9TVsSAnZWagAAfikP/2Gwm09fYB8Fm6pAbXWP\nIq0s3FiSpzZi7OSJ/5xT4G4qpF3O2tuA67EHtC6cS1noIkRuwy6R6Png7rS0\nz4vvn2SQhMw4WBulOqDXmMZyH/NwRx2H6hzVG1yRSIYlpT7Xkvai/+7BBP/H\nACz4p5ntPcrydlVXx+Wz0JCngRYa7VlGf0me6/gIq2/xW4d/GSp8OT4Kgg7u\ni7S+6FZZudSH0b8hi7/Ly2+6iSQ3y/GKsio5ZWpfIHa4fH12HmV502lsQtol\ndlFqDJoe0d78/335wUPSRTakHkdPCepRsdSR74RdIOaNma2CFtk8XjeZhpwQ\nnhVakT7qM2j2lsoMi1D0crQ+YiwsK+8FaudzYFlUMhfiN0e6R+sr3IhOtkGO\ncoU2vVvuX0xHkyPJ8kmmD8+x8ewYbafQlQKqxLSSIv5GWt0SNLlF37fOM6EI\nsB/k1uYXS7+dAWriZT7kLTRWb0W9blrm+JEJAdyakMmebyycKi8j/qdRp9eo\nfAbT8mRviRTSGo0Mx331BsDOv9e9eYogy3H//xqaMjY8T5xT3aEoh8tGG3uN\nny+MueyJs1ALr4+YDBoLV1eQ/i19hvRJaxPn0Ggz7gsOcAVjJC6j5VGNmbVa\noFyF7/V8KmjhRj35LJpr3dDPff6n3CogKHQVUZhI96PoNmMFXfkEoROWzwOE\nzXLw\r\n=BVXE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0": {"name": "@babel/parser", "version": "7.0.0", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.0.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "697655183394facffb063437ddf52c0277698775", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.0.0.tgz", "fileCount": 9, "integrity": "sha512-RgJhNdRinpO8zibnoHbzTTexNs4c8ROkXFBanNDZTLHjwbdLk8J5cJSKulx/bycWTLYmKVNCkxRtVCoJnqPk+g==", "signatures": [{"sig": "MEUCIDTOLFougSDgp1eOIsw7WkRcKIQjluu2fSa9RKqeRJkYAiEA7hH2MbCQwrZ3sttG9rnpO1h64jE4Iad2tsyexJBFnUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHAPCRA9TVsSAnZWagAArKsP/0y/HRpjG4RSrq9HckUF\nAPi3UhsII+oKnCFUrX1WVbgACP8DIcgexWNrsT95vXsxaeAaLbYRJ54VYWa5\nZlFPOmPLu04EAX72n1Wx/0CXbOJF6mmCwpNNCJ/xYwBiPCthLs7W0Ls8uNcv\nsCWRyC65E66YsbsBd2lYbyu694wExK85Ykp8tydkAh1pc8c7f8k5kX+G52dL\nrgHIRyk886sTTNGo46mfAJVR0J8b1pd4gBoofzbQQ8zyrbIMqwtFOKySN6SF\nIKNYFfiHRTuutk32LzXPT7V3J3JdCqjgiIN2uf9DKZ867ib1ast3HEDpwaGR\nMpuNFvkVQe4N5qGWoJjFwYsakwEXInt9HVuNenonukuFTzF/rtXYzV0ZwxRc\nKL4jzQn90iG87cOCZAN2LMn0bHGDVFKGtfNXCTIJQN1lEUUlVUE+YRuHc0zZ\noikaulQMQ5WezmN25z7uM+Owq+/IoHVo1aF+BzD50k/9VQnyV4iar4M5p+5Q\nqI10Efu5n1+bXWSh0LGieQSUAVnc8K3Auo2dTN6WqulSJfgidgUE9HZ58pGK\n5Qa2q47SkQxO9TAwxaYwjQ+FcjBQvJgfwC2PO2bfWwuegdWDKU6l66/gqwf3\nLLztH8+pGGTv902PKmeZmHUg/YTikR02OOH9aoo1+2ND6NcrJdPybWm6C4SG\nFh9q\r\n=VrI1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.1.0": {"name": "@babel/parser", "version": "7.1.0", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.0.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "a7cd42cb3c12aec52e24375189a47b39759b783e", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.1.0.tgz", "fileCount": 11, "integrity": "sha512-SmjnXCuPAlai75AFtzv+KCBcJ3sDDWbIn+WytKw1k+wAtEy6phqI2RqKh/zAnw53i1NR8su3Ep/UoqaKcimuLg==", "signatures": [{"sig": "MEQCIA+2nMsFarriw6VHTrSlYH9of1gO7WBBG5jhfhFT/XX1AiA9JuSkY9gz+SlOuSMBVW6El01Fnq9oSeX+eZcw7FghRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboADGCRA9TVsSAnZWagAAY+AP/RYrLwR0GmLVGHDrHF4r\nE2wG4Eb0yf0ZqC2mwLgpTdnuDYFbJ4PZ07nRcHWp/OLNJA0HDvVexSvpWnZ+\nRI/Jy99OgplpILirKsw6eQQRp7UY7jDNEgqKcPXdTfYne/nlued1lhkexQ38\nlsFfhkqA22cNtBmAtpHXSBwEu3w283HSEKn5aTTTbT+Ag+lxE/Pq4/vWtlFr\n/eGFK1FNE9JPEuik0PMM6k7pODn52O7NTTiiv0CEtFepaFCdFve2p5f/YW3S\nFkHMxsLIBTkw9q0Aizkna+tMzf3WgYvysKVZafzxqWTE+bJhEhmv75mEZM1s\nN2B085El/TPuv2/jlv4/B1iuwvKuAtYzVsQ11L+0WG7o6XhM9F6BgKLEWgfN\ngXOJHTDjfKCKhwVIrpyCy7H9kJ4ORxQKD9KlOa03ahD/WQM6qoEnZWp7IikF\nVaNxaOITFcONs7Hm0eWLSTYxN8vEWQnciIxaou1nBLLApBJbOCy+sAAHivmI\nFVptP11N3qgEfq1haL6HurNutnMyIad5mrNsLpQNbRa+bDc/8sMzbdJ4qqu9\niENs6oEZUgbM1WnDv4Wzx5El2LXlxyQ9Sw8DzwA1S396Ob2OtQ6wVpgPancL\n1EYwd9hsJ0VSenEirIfmcN/wgHTWdnBK+tOcus88R3XztyQmcLQZxHGipbPk\nY9d/\r\n=EWLz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.1.1": {"name": "@babel/parser", "version": "7.1.1", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.0.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "c23e1f3539047b898e471ecfbe7752082384c572", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.1.1.tgz", "fileCount": 11, "integrity": "sha512-PIP7vcQK6XPA2cpJy3LqAmFO/fWMehuCa/NNC8NVdqcXblDfV950g2QeMpR4FcU4zfVpJ32WbkvhaTbShiaWNA==", "signatures": [{"sig": "MEQCIHTTn0ia4GqoA38ZdaI6Xm5QUyQ60khqsTlrF6APJLjeAiA14YbgLotHYOEOPaeseBrA4+4q+LVkR5pucgiXDu8DTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrojmCRA9TVsSAnZWagAATN8P/22f6Nm5w3asrL/y28aZ\ny2ij+/PsRHHY1snmVgAUIuHVd8KFQOaZVXckXek+7zlmJQz/EyEVhh67L61m\npEMoXJPM72rFhB5zjJX31ezfv9FFJ1AjwoIoD0cpIYle7X8DXRbBG2nE0wzp\niIftO3a5j9aDgsVCnNgrpsRpY5aZPr0QslrgWDj/q7ZY5rfJIqLalvHpAs0d\nQiTb4oiP0R85TeUGCjZXbq7PytQDB6qp3sak7RUiA1dsJG3OIINB6n5GLVM2\nYPy8tf7bOY2E52EE926VHn3CiFzem5Tz3mVU8NMyCF3hTXgnwEjb+F/J0GDW\n9HR/ydSHdWuYGLgXNhR/z//Uky/Fl9vb88N0YP3zEw7lTI6+kxhXpG8Grfvd\ngvF1qM0hb69973fBttsSHXze6iUgmG3G5A69CHTs0CQyUf36X6otnbhlQYLI\nIsMW1Y1+T0YQIdVhkWQhdUoBrG76fK8N8kCBgzkUXnk/QsO78UU6DLvGd9fb\nObXicoF72U2Za6EE9NmfYNxBfRdVJ0ZnT1XJWUmgXxBunP/AZP4bIPdtcUJe\naDtISP9ibH9p/h90j1/cz1Sl4J7DgKgPmVGUkhsIsouuJT10W7BTn9gGjPCn\n03E8693fC5meRHzsCeA972SPvDFKJhQcytIX8jIa9id16nWzO8QS88jYINvn\nlBHE\r\n=yPDg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.1.2": {"name": "@babel/parser", "version": "7.1.2", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.0.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "85c5c47af6d244fab77bce6b9bd830e38c978409", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.1.2.tgz", "fileCount": 11, "integrity": "sha512-x5HFsW+E/nQalGMw7hu+fvPqnBeBaIr0lWJ2SG0PPL2j+Pm9lYvCrsZJGIgauPIENx0v10INIyFjmSNUD/gSqQ==", "signatures": [{"sig": "MEQCIAJiorKD2GN8VRt4h0D+AwX6ENzat91ONRcF06bxcd+tAiA/jYxvlR53/JMor1+bHf42Esk3y9Uuf6kPsNPoXqStDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrqj9CRA9TVsSAnZWagAAYrIQAJ44wVqn4Mlheq/a7EWd\nX/2LWf1GCU1g0oFmFzNqQGss7JehaUAx5e6GxDGTPQNP0DkV+hxKSFN9lV1k\n7HXXXPzqEV7NUlK6FJ51335ZW0TIy4ptXqxIPaTGPDrvjZFWsxRYAq+g76az\npJ8udjMp0zFgZAxtdP95Xhzz3j2EDlunOI/MD2QQXouCWHItS11n/gGT/DJ0\nZySf7gQ3djHLnGzXLXWLMReIbPnVSR6Uo/5oTdGGO9myphnJlCq0lz5jP0cW\nk+t2qz0ZfeFNp9MlhtKKpHp4LBoSNEjo8FqCttPriXb/uWR1KFPsDMoJmRNr\nA5UTzrobs0eF0dL/PzrwQO1tgYAGriuNE+iKgrKAuoAICGGEvWPRgiUX8icw\n8DMkrKWaVisloSQ9kK0aENXfg6uPfRKxfRPGnA00BO7HMDpiOws+nckNFExs\nZsqBdZPDa+J9X6S0WTiJh51rSzlTa4ofx68YgGaxlU/OQyb5TRQThwdQ/Wk7\npd4N6rOAYZWIf3E45HeK6JwaNFtgjf1tK1EwVRR2E2uZZikY8tBKoB0eu7hh\nmHBr7wDc1sFrC6phHoehgRSzyDU0QsjDjKw6RSxForLLMtTmByiwKA++Unub\nuMLsQKkI3WLSpDu6q/zdB/a7fqHMxwqgCDB6YM480smyG3HY8szn/AGIS4Qw\n3IBm\r\n=d+wQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.1.3": {"name": "@babel/parser", "version": "7.1.3", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.0.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "2c92469bac2b7fbff810b67fca07bd138b48af77", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.1.3.tgz", "fileCount": 11, "integrity": "sha512-gqmspPZOMW3MIRb9HlrnbZHXI1/KHTOroBwN1NcLL6pWxzqzEKGvRTq0W/PxS45OtQGbaFikSQpkS5zbnsQm2w==", "signatures": [{"sig": "MEUCIFeTUFBduhM6UtmQm6yAVPcwJ+/RczCPKjSAJovGTlIRAiEAseb55OUvRrgZkobFWKdl4frk03/odyX1NdZ0WfzEt4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 380749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv3G7CRA9TVsSAnZWagAA3agP/Rm7qWl6AbXD0/osPKD3\nyk22fFUndD96FsCZQE/JLRIOm38yptRqs+uaqu8Nz+RTy2xKZHay+eNRqFde\nVKKbEG25Kc9GdtT+aSsSWL63ybOC6/porWjvjpgSpL+/4AY/XQu9lMzEKgY+\noRqWP/ztBJzchpbmASoQgHxwVXMcAIrR6SPN7LOHpoX+wMo/08sIJBjPReNy\nzxoFx9zJ6nvwRAUafrWNdhX7PSwBkfOZaaXRRuW26qvsRM85SWuqLseYkbZG\nqgvlp/ljfcDeeRkr+Gb1dTpMPu0BJsmjLJ5HQu3/RyedeFMDeG1Y/JXXJU7x\nEMUPN7JR6vN64ZuKqNQIj2Cn8AkUwBs+Wjy+cSbPyYdbmMYceEgAWxsJChLR\n6KqkE1noe2b8az17Ki3lXdMM2IAvN6/WSB49dJkEkGcM/aIU6ZFgYkLmb+eF\nx1K+Fqp06qiC38vtcPZJ/jR/KY3M7Lcth6RUgNLJWdoeiN6TVMpFqEEsyAAn\nB2p7Xm+CqWepaXfZYpO0Q1hex6nTVXvh9YrssYBFZDTQHiPEgOjGWkomyqHl\nC+rey8bJoHbdp709x41yScrICKnZ9FR8PoJbreynpnB8igKk33Ek6QyjGr9q\n9LwNaJUE5rWKkWEiUsYcNWhbRDE8LIsLiOTm4vWRTPTGVuL82B4pF+VlD1et\nhlvw\r\n=F6Gx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.1.5": {"name": "@babel/parser", "version": "7.1.5", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.0.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "20b7d5e7e1811ba996f8a868962ea7dd2bfcd2fc", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.1.5.tgz", "fileCount": 11, "integrity": "sha512-WXKf5K5HT6X0kKiCOezJZFljsfxKV1FpU8Tf1A7ZpGvyd/Q4hlrJm2EwoH2onaUq3O4tLDp+4gk0hHPsMyxmOg==", "signatures": [{"sig": "MEQCIEZN9Pb7lrB3EhCW8OIwd/Ry6ecjwQczaGND2lb7rHkQAiABrJumDC7ZRTAJICedhWCyl6/aGazf59FL4HzzSBQcMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4hPmCRA9TVsSAnZWagAAvzMP/j2jK6vrlbCf6+j1ZmpR\nJyUUlamJetRpCdUtZVRsNKow2h8n9qCpqnlqeR/uIod+L9XcTOCfUV8jM3O7\n9Gv3AasZa1Wc12Qyve7FgAKTPcgRiToigLQ0al9JAr+WdnfX0aqkNvA4RGNJ\nnyN0OByi4Pj2xdJalyPhFatSGL4kCyqEDaNuDzOWEzOaMeMO5hvi+ysNecja\n2TSXoVNTMmRx9csdvbe064yV/PqZZ5fdObFH0ppKcNmlQQoG+xovU/2YIVCb\nVz5LHHaqMZU1BLkDHxP+WC6ZBUYeRnx3ZI5VGJJOJAGNi2LSXqMS37bOfIFp\nG1jFzIZ7qtMpsvZF4RALaNFaHvbohKOQHG0kFLIG36Ua8hER9Lc+4KmslZrJ\n/SeuvH8eUpy9ciuCMwumM5TOcFVK4ZcDtWmz101rtb7XbROc4ocOurMNMht7\neBFaB/sdQvvXLScy3WcbFdeUZ9BOQdOXH5N/y/0Ff9jakpcxXswQ16/qtGcK\nOTLNr307O1ZNQ1Zo1NaFs+vsudKMszSwhnVei3IhEx9pZarJUiB3dbdng5Pj\nhuz7jblpZ+XHAl9Ode3R8THJK075598yun7qDsfJfalt0NHgvL6FOGUzbU4F\nhoTklSk96m8Q920OWB9lrCe7C5FYDMzn2Ptok1ghcGTpFjQCBAeHr4Z5ZScA\nTot8\r\n=x7dd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.1.6": {"name": "@babel/parser", "version": "7.1.6", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.0.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "16e97aca1ec1062324a01c5a6a7d0df8dd189854", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.1.6.tgz", "fileCount": 11, "integrity": "sha512-dWP6LJm9nKT6ALaa+bnL247GHHMWir3vSlZ2+IHgHgktZQx0L3Uvq2uAWcuzIe+fujRsYWBW2q622C5UvGK9iQ==", "signatures": [{"sig": "MEQCIEjZxZLv50WB7IG11Ua581aMRt1RoHhL/23BLZ17GOUZAiAFCToPp1B2O73CtHLn7XVQdNH74Ah7iiEcL23EPcCFWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6z3FCRA9TVsSAnZWagAAdxEP/1PGQJDnXRIC3AYvU3MC\ntvonX2hmeNyeEK9dKhrjo+9ewakAUvDbomrZ9SNbP2b4FPJfS+1mFpGYfVCq\nFJ9P/Pae5gqUR1qvZBzlo6gPS3IHtLiy8ZLBlihfOq9q71rWaUzed7e3sPw8\ngridQdjei9YnS2rGuMUQTzAPGAhC8BMxM9bLk2/JY1Zldxo95VJgWHk+Yu9+\nf8qdlYsRncA0Kmc1f5Zq5t4nT9v1jnjklTScnOoURLviGyh7K0aFLKczR2rs\ndFvlUoCZlJHolASzgu68n7NrmfhTESzwVhB05yFJOxmL9+IOE+jhF4WA9HQE\nt1O8CLyX9Lrsy1jdww2bhFEGlbyxstWKQzN/nygehupWLWrj/bmuFikjv3s4\nS9palvy7Zr4ZVKXQAGuuvtIjMLeKT3fL9idJgGtudhfVwNAokLZc/Uib7Vi+\nvm5NxqjhQd+MaWMPquboIH0UEbyEMGeg8llsBI5bD4cX4D4jPv0Anglp88cc\nb+J4xgUEfKVd0foJyNgZTd84EPoZoO/RZHFSNiatus7igL033JuU3zfWI1L0\n9HRMlNeG40T33gAjYrBKtsyDMZ297PrD8W2HVjsXx2siVfcE4PH296X21L8Q\n1Sp1Bwq7WwzyxSblXgejAaPRK52XQB7UhC93BtGzcrswiqbRM8VFqmq36NkD\ncySP\r\n=28qE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.2.0": {"name": "@babel/parser", "version": "7.2.0", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "02d01dbc330b6cbf36b76ac93c50752c69027065", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.2.0.tgz", "fileCount": 11, "integrity": "sha512-M74+GvK4hn1eejD9lZ7967qAwvqTZayQa3g10ag4s9uewgR7TKjeaT0YMyoq+gVfKYABiWZ4MQD701/t5e1Jhg==", "signatures": [{"sig": "MEYCIQDqrlOa8EAaPXXXAheXfxHtxUwKgWJHoofmroCqjZtCAwIhAI5VoCuB4eE5g+5lKc8jnlUbrry3kqb4I0aQz2epqbR7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 395009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBXySCRA9TVsSAnZWagAAVWgP/1NvvymRa5yKYV4EG8ek\ncj59ojfvUfTfMh6dt1VAWkk5Gj03R/O7C7rPsXNM4mHiCBW+ruoiro2xq3NM\niH/R/gpug8cwoC1yAmU16Og2tnVgKXtDdI83UxcAaTRKiRPSnDEFZ4HigC2N\nML1HTo9qH+4U7b/KesjA6xbRz6vcuD9jAFo8A2y1wHPHyH+/xxn697jGwrpi\nNBgL59mwRxfoTovjOLPuYyGNQZXcyuFxcz1YK0hxRK0vby7z0aVfeflz3Y4y\nFFmg0wwmk3BxpmS7SMoL1OIlnYB+Vdq7nrSKhWYKHViBXKDICRLa6OEkIl4I\nc7BIE5c6YnG/gZEiNaJxS+c+dg9WAHL7Xi0B6r/g8RxG/ViJ8eBt9sm/vD5n\nP/5pNYKS8JJHrjyqzTDyTW0RCBQ/ZA5WzJaOFxtWfb9Yh+4i8TT0Dm3J7BPR\nc3jNzEXMFFP45RQyweeB4tXG2JM7G3D14BBpDMjkjQQ2AmfesD7I3joqtNqW\nvN2sO3XE3TFOg1OTfHRPZhm+ejiJuVbXB2X/jfXp9sfkSLXcWNKQpSuSpGWj\nufY2FJ5Fa8C97nYMemB8euWKoa77J0TQKdPfMT7MZjLSsP0Siybo/AsXCrbS\nssVjjLzcAMN3REqoC3U49MMcsPpY1LjfD0WbujiRbl1eCeUwwLD2ZEhhQbYM\niUoU\r\n=E2wm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.2.2": {"name": "@babel/parser", "version": "7.2.2", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "37ebdbc88a2e1ebc6c8dd3d35ea9436e3e39e477", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.2.2.tgz", "fileCount": 11, "integrity": "sha512-UNTmQ5cSLDeBGBl+s7JeowkqIHgmFAGBnLDdIzFmUNSuS5JF0XBcN59jsh/vJO/YjfsBqMxhMjoFGmNExmf0FA==", "signatures": [{"sig": "MEUCIQDAfpeeWHzQ6OiXlz62fwcfI7QIbSD8dwKMrzJH8J/itAIgRjPEq41iMCHO96bcEu59kD98k7Kq366cI+TBzJ1OyG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNHcCRA9TVsSAnZWagAA0PsP/A9NJu/qSOlwNwNFRLH+\nLkC71kyGJkmsh7hcPwkbMP/NQLT0pAxGgCbYUNwQ5qKmCYHZCIr1M7p73g9p\nOIScv2s6SmIjdp/O0ausG2QL+yTlCTyuL9j1XSpeJF44MNRXOyrvPAfA7JVN\nlZ5UOe9pS7eDXdRUc9KyWbgxhbXLDbjc3hC9Xuj27gMUyyFGykoayQ/2xpMm\n446cr8UWJLfNPovWKby1KDHxtp3XAWeTOCjje+yO+u+NIe0HOzCNVUuK4bZX\ngVrzPMw4jhqlE5KnSzNFA8OM3GtvjrU+AaJ5QvTHxRzFU0fUkUA24TV+oKa5\nRlXsCiZ2w9yJAaA6uyAgxpd3Ns/nStBGAkx2MxcgYSXwlA3sZ5I6CToS61Ku\nSbREfyVRB2ImU/zLE4rX+1qeA7fxCJj3pW1dDrYPnDrIO81ZyKTHYxoXyHM4\nEzb3oDM1JO1+y7gaGJGzI9P0aBNux1QFtVBcGMBcKVFstZ7EI1WqLGPtZbPF\nhk+yxPix4AMuHzJxpHTZVRvjUtjg054a19X9Z/A0LN2ZFVDjkXWD3UJATwcT\nMkV7aGpr42V27LkCL8Sd4EkNmTZioqdO7/p+LO/IQoNY/1f/VZrotu9gq9Cz\nbfaiqwFVUcV16g/EkzfEPoYtvND0jkSG+Pship4cGmarji/rP4BvrwQvcsLy\nHYk5\r\n=gwX7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.2.3": {"name": "@babel/parser", "version": "7.2.3", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "32f5df65744b70888d17872ec106b02434ba1489", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.2.3.tgz", "fileCount": 7, "integrity": "sha512-0LyEcVlfCoFmci8mXx8A5oIkpkOgyo8dRHtxBnK9RRBwxO2+JZPNsqtVEZQ7mJFPxnXF9lfmU24mHOPI0qnlkA==", "signatures": [{"sig": "MEYCIQDSoCmmd0vlH2YsI9k9esj2w7ehQCRf5n1Ae0280nHvHAIhAPHX/iJ2rzr5dfhDAUGy8n6CYMav8d5xjt1mFEHHBku8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 396327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcG3kzCRA9TVsSAnZWagAAbVQP/0XMyixB24NJdh+ewZzA\n0g1kq4mDBymMcEwjVfRRrqgnNFL5/X84HT1ZDuNeCxCUJVHYgu/Srx4i7KOx\n/IOHcEDuEbHQKVYEWxuqKVf3y8ZzDxTQHQQaZuWqIMaLNewjY/ayvxglX+QK\nU8eIptEetyxJ1feXC2gaSGHF0QiYdF9PoIX6WwdoUO7H9jaxknJc8ImxrjQ2\n9bzWkl7WCaezBAeUsIc8NFwKa716rp3HORHJU35gc+xCr5dAG5G4cRu2KLja\nEb0RjiC4RauJw4OHMRE//nIvirc8VphxsKtE+SriUwq4kkn43fN0aX50YCii\n+FDdI0j7JJzu0jWuiZQmXltoVTWFtt6NToG4QbQRBH7vhNQ7FAzC0SgXc23p\nyHFvH2ejACI/BH2EyjxA95aBJZHxRjUGm0MCMf1BB11yg6WOy57OQLZbAj+s\nSPKe23+Nfd/2rztlEBD0BlpTgJLURfW22w44lvrgilyV6ze+0VhLSK97TDbd\nRGvqbq+oOwpUHeu3NaU0peMpVraXJp4mjy1M682mCbyeBeS+YbPxDP2iU6qe\nrsxHePruVrfR9XqjVrGiA+as9lleGRcIscCv4JvWpcT9ehgm/82T9oSYC+sB\n3NyQO2GyrNnPjA3IPV0PMfclYS5g8Xg0Tr0OZ76eIZweXDUJXCgo67OuDbFd\nwn0Q\r\n=iqx6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.3.0": {"name": "@babel/parser", "version": "7.3.0", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.8", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "930251fe6714df47ce540a919ccdf6dcfb652b61", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.3.0.tgz", "fileCount": 7, "integrity": "sha512-8M30TzMpLHGmuthHZStm4F+az5wxyYeh8U+LWK7+b2qnlQ0anXBmOQ1I8DCMV1K981wPY3C3kWZ4SA1lR3Y3xQ==", "signatures": [{"sig": "MEUCIQDqYWmmMb4aEWH+0ZISZnSh4hKbW0YHWjck8hyUlutN/wIgRFDyls1sxsQs/v+kKN9+6x8wnNBVInFmC6eLVI1W7So=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 357807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjn3CRA9TVsSAnZWagAApG4QAJNJ12isGYqvsHFTjspU\n1Yf2FMiMjRce6H6z8cdTH+C1gLhOGSWIrEpEc+A+Z+IR5NuM3xWOdtYxwc72\n8R9lrtPynLVYeMq9HCLZ5gVd4dPzkmxI6YW0LVjV8UL0Z5EpZyXQDoloKtSg\nwJ5tp/eYE3xFxgtRLdrW9cYWg9i9rvt3LlH6aSDUR0TBfAIaV/3F309Iz0Gq\njhW1CTNSPngn6wEvytb5GKMmJMHKvYMWajhnXemDTbY2fz0zvsHcnt+zAqe5\niDO7ivDmkWCWy+C5j5f9RQ/JhahBSIYDLqKY99hnoURLTJ882I87KvdW+WD1\nCxrYV1qCsPPqkoloAqVJ6CwaPIsXd9DCv5gFVABojHEp/ysNFjZMxAuELa4Q\n52pCFAVmWbmB40ckW6lpIvdSqEnw0xU14qMEytR79W3Sz+MSlNdrinb2fe0W\nG7nBinijQGG8UUdwzucxKyYyne7YlQ0s16lgs/HbLNX9B0CJjxrZ9dmpdm96\n8biK5pIAcRLcImroayOzPF7i5lQZBAvuCBI2Ck6Kb8tnhzeTbxWUmqelYM7M\nrnuooXafO29fGbvMwyTIJQxsC5YYtyTDX3XDzYryjrAPBwgZeITVeYssarI8\n0QjCmJba6I8jWXRfTZvhWtZ00I6jkWT08UoKFcn1gRoaCFZYR7DRNp2Kwi1U\nS4WF\r\n=wiCy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.3.1": {"name": "@babel/parser", "version": "7.3.1", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.8", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "8f4ffd45f779e6132780835ffa7a215fa0b2d181", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.3.1.tgz", "fileCount": 7, "integrity": "sha512-ATz6yX/L8LEnC3dtLQnIx4ydcPxhLcoy9Vl6re00zb2w5lG6itY6Vhnr1KFRPq/FHNsgl/gh2mjNN20f9iJTTA==", "signatures": [{"sig": "MEUCIC82jK8ZtUBWquVwBWgnVdhMznhPhbplSNOB7qSY1dxYAiEAkTaZ4L4nYhbXtXG8JOMBUt/qKahzoZ3pbq807XtcrQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 357845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRsKqCRA9TVsSAnZWagAAV8kQAJgvZrJ3ZBeQQP9Ivowo\nzo/H0SJsc5E6rPUBZgLfT6z6gcrBizu5qzQ+kNezOB9u2eBLXHoZXWvxArl4\nSCHSSvZ5LjJCUvLPBiWCIiSip3ceKusxxmXO26PLAjUt4vx8GfE2vThRiTzi\np94CXOa0s8w/t4QU98SaMTCzGnGPyg8iNYOeA2J7npfXGnvOkfO4wzFcEonG\ndYvzwGSM/DjV0EqEP4J30pScPVCJY2tCs6pS9yp91LxEumVFQKMP1Q75G1sO\nMcFM9rwme1uQapZNrQxlNDdn+o1WDXWCiELaxW4nRnYBOauEycAUFYFwfAvL\ndE/hQceqH1+RYmfCH3jVSKkEUAmBwlMsowG1hSzIYfMfwclvRI2rmiqb1kRL\n37VQmCaxnWJIHPzX9ebVxeqD0LNHUqX3g/hplOlOcOh0huH188qXpi+KdXl8\nQW+20EC/+fVxDrtSGgLR0ni/jgiIVrIptEB23rkaSbEofSGInF1AKZLJU8wW\no34vTXLL/RniSXnznGG8hWmWh3KambygpzmNt8IlqX7JlhqBggipzk/GbnyT\n3jxOo20mslKr6gB8aKS48TmcyP5OZNpIA3BOfyI+yowwaw1mmn3EyZKJqTNH\nzuBVZnzlA4ddtHXE7LAvZcmSk71VV8g6L9jsN+YefF/Je0eP2KrJjxSYHa+4\nssqv\r\n=+ETn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.3.2": {"name": "@babel/parser", "version": "7.3.2", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.8", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "95cdeddfc3992a6ca2a1315191c1679ca32c55cd", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.3.2.tgz", "fileCount": 7, "integrity": "sha512-QzNUC2RO1gadg+fs21fi0Uu0OuGNzRKEmgCxoLNzbCdoprLwjfmZwzUrpUNfJPaVRwBpDY47A17yYEGWyRelnQ==", "signatures": [{"sig": "MEYCIQCRLh0YKaax+2nQqWvhdB6Zoup9S0aERpPrvvpd6jWK1wIhAJ8TcqeKndXIVprWm1PSASOkZ+OxtkIDArkwsyIZhdbB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360201, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWLtCCRA9TVsSAnZWagAAswoQAIP9DZNgxQeAH70hrw91\nX8OPE1M49iSJJY/ZREbbTs4kaEIAVRdrmga9r5PK3lOo+jtj+UOxxin6SCJT\ngdYL18VysgVE3GJ7B82cG9kDZTVJM8cHQAkHaCljZpvPL1i7WVGLaoMEaIDd\n8RTfHm1IiS7HcFt9oBIFtV0zkppaRou9j4x8dlF69V070jMjuhzpbwKoK3Zi\nv9M1sko/XW/zxPqDC8OhIVbWBA+xIL14fFZhcJzwOuCEoqJZKca3PtuE1sMk\nIEQDE0NlUB7H3uHvqmru8BPAFd7WjUuPntj10nAZq6Xo6iAqWYq9y3bFtrX3\nIVEgdIPypf8/KyFCkTc9af1XX5kMX+rWpTsZYuIqZkKHjxkOcI3pL+PhIgi1\nZVBsBjL5GnuaxHIVNovVIj0wLqz1VZh9krgBysrwmFE/RveS2LLKs4xNB8cR\njvGNTpPgAuERcLG4z0QxzWo+CTcFaTwe9RUZ6bd97F7/QOS5MeuSrr0YehCU\n2SbndJHk61+PUimR3VatbVgr6nFrz+7teqSQBeaUMyXdPSAMQi3wxYbCLQYz\nbSEwt/MvMSRvJTpO2e/NWVR2HkreKZIeecMzEXDur1fbLeOZGHtgQ9FgLs5N\nykOc5cLGQr833uE9/67m7KpjRWFoWv9APzHYTxSerbbkKc9zOoXFkTqQT6j7\nVXM/\r\n=M4M5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.3.3": {"name": "@babel/parser", "version": "7.3.3", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.8", "@babel/code-frame": "^7.0.0", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "092d450db02bdb6ccb1ca8ffd47d8774a91aef87", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.3.3.tgz", "fileCount": 7, "integrity": "sha512-xsH1CJoln2r74hR+y7cg2B5JCPaTh+Hd+EbBRk9nWGSNspuo6krjhX0Om6RnRQuIvFq8wVXCLKH3kwKDYhanSg==", "signatures": [{"sig": "MEUCIQC0xlJvkUBjfLBeYqbxaNCJ8ZQBTm0VYm+v9Id8JkJPtQIgAL2/X+OIXY8YHH/cDoMRVALhn5YqzgeMVL4Nspxv9BY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZyu0CRA9TVsSAnZWagAAYoQP/RVs85ESCIT9u+RukdaP\nI/GScCm98fAMypPiVPJ9pcEOh5bC0U+XB09fX0ArhjopWIASKg9XB0/+oYHD\n0qYWu1Kw6MSyqy0Ad6UJmWBMdmvRSm1ZlGOaX46ByGsAPBfzPOLwo7gMD1Jt\nJH/bF36zL+v+q9Y097e2ky5GffMn3plMi2TMyxgsdzQXFpRTk6w8U8klUdUM\nlH9V5i4u3dL/jlt7h6bDUXP+0tGUdIv2CRyWXjTFlUrhyQGi8ql8cByqRHGL\nisePzb0PJyHLcDpu+Yc8XWReeKbJ4nHaggJTLF5ZsYxyucqqjnXMCwHXG0h9\n36sZ1qx8qGNochEa3XVOk2KKTzZUJregunSKVNO+6D8ozvCZuA4r63RlDHmg\nadUASFrlOPDju0TR3fEHeyUggagQGaJu1+gCbK4TDtCXWj70OTL1OjwAd2ki\n8F2gTpP/uWmJdcK+DLNhvrEmE/RsJvnxiTX+Rz5r1R8GcUO+hB1mpIH6aIAo\n5E/WB3xoFBnd00ZNYCoavQJibvRBzVo76g60P0jnW+W/TII8cEKHL0QkH5SF\n36o06NGCDioVGeUJLTmepQ/o8Gp9YradzZpf71JjcvF9mWqPGUjrKL668l1Z\nJAaWLwqjpfK2o0qCi6fZsSujBdFHV3NZVVHQFvfyMGfBEfaRv2vCSyX6pxgC\nueWn\r\n=mHMV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.3.4": {"name": "@babel/parser", "version": "7.3.4", "devDependencies": {"charcodes": "0.1.0", "unicode-11.0.0": "^0.7.8", "@babel/code-frame": "^7.0.0", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "a43357e4bbf4b92a437fb9e465c192848287f27c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.3.4.tgz", "fileCount": 7, "integrity": "sha512-tXZCqWtlOOP4wgCp6RjRvLmfuhnqTLy9VHwRochJBCP2nDm27JnnuFEnXFASVyQNHk36jD1tAammsCEEqgscIQ==", "signatures": [{"sig": "MEYCIQCwQgzEyUqBNnJOwGkpAcCpc72uJVq7MqYquztvWGlNoQIhAMGp7U2IEwHjysxND6zdSkDGWYWWSopv3W2LKqeYb+aI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDVmCRA9TVsSAnZWagAACfMP/2pdeZlIPGxeY8MZQTb2\njpWGUGE43p5/V5OYEVsuh5NqlJHlOPS4mTymPerGQpmiSv4CtP/HGUFLO91/\nFZii42NIPsOpzM/Qb2jXmpEEZCnVc29Z7ddQ454WK7BDJoOyNWwmI+mKNvVt\ninv+OFllEO9fxYOcBBteDeLHGYD25ARtXZt2d3H7S8bFLoOKbhHA73grcz3c\n8DnC9g8/iFRyzAQ4oqHRnDnJ0eyxKboR1g9omtB0GSybYwx6LpnHqZkr8bz7\nkYdEWa38ey9lK04VGgG7ZHSDdXJN8Hp5/PvAS3nzAj3SvdRGeOdsIib4DdZ0\nSxtSSVIgSca19Kiy3vG4AoGZyIFSe4anX4DugeQSGaztRW02tGID7qlbIobD\nA1GdrMqcs+CaD0b8F4bGn4HnUV7C4SPtBQRvnbzwBsWGvmUi89dRAPd5J+vE\n4bnv4o1LXO3QoxWY3mDd5+7CQxamz9wdVH2AgTFqsASl8XvsuHcfsULCtjSU\nBEP4kSnIemNMu1IU5nJelHnMgu5atBEUwF6Ft5bhJY32xAQtW8ENUydtTAjx\naux76Zb+tWwBMeQlOCs7eSR94Jy/wlR2ehyfbhNyOaVwTYVqxwB4AKWtdcXV\nHXOqmE4kuMJaBz6MUzeBt/ie3O8cDWCLwU5ZY5iQ8AvMQanRCxadEbJyqv5H\nWYtE\r\n=K4JH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.4.0": {"name": "@babel/parser", "version": "7.4.0", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.0.0", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "6de669e73ac3a32c754280d0fef8fca6aad2c416", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.4.0.tgz", "fileCount": 7, "integrity": "sha512-ZmMhJfU/+SXXvy9ALjDZopa3T3EixQtQai89JRC48eM9OUwrxJjYjuM/0wmdl2AekytlzMVhPY8cYdLb13kpKQ==", "signatures": [{"sig": "MEQCIHlkW+h68R5p0Jtp9M9fq1uaXuK4iH9y0wQ9zKL+U9SkAiBjKSJTrM+xMtwT1L8ruUoJPKnRg2Su0Xni3sWVdOs/TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVShCRA9TVsSAnZWagAAuwgP+QDB47GhNNK+lftZTbwO\nGSAikiLvfWoShQmjyRqjYWSrtZjJZjCDSYWnJ5YdcdFS+bZYucMKpISXBJJM\nadz2D990PfBt3yUw4u8vIds68fsi8vpUyMrICw10eeMMAjotQoEZTaIn6CEK\nu7colJjKSWOQ0z36NYX+CriSsK37pXILOpd9bMFxarS/8K9d6+AsW0g1uyic\n673Gyx4wvIsbaCyUCdCujbyfQkuQ2PAZ2+l7eaedzCmWYVsyE6U6BEd0Hpq3\niDgUgvuEvzF+vjiaPbExKyTW/93cePH8XhgGS6M2qzhRIXHycIWiM0Whg8S4\nKxSQ0ymXxm+RhDPBkq2DH8NDMuCQSQQLguylljzdsVPBF76SCBjPxDgr4g34\n/jLbdF7iHRgQUNuioHGNf+a6kQlVdArezraISHB5RIRdql5B1XEsWgkFulK2\nO4dxsnbwRXnlIxsOndvxTyOPM+QdoSLiQLo9KTS63jlVRSBuYfJDmifYLMRj\nzyzIPyukUvedcyTg7dOw97KDnhQD31lDkJdfBBcc2IcBzFDZq4gtyGmzQ70I\niYqTemrfCuxHM9m5FljykMJSFBREqR3xL5uGZKT6n/pQFK0vbv5X9y1ufZSX\n8czpcpwG4lNkmoxHJwGsXvGAVnKNSUGx83uC+yYT4KNY3dvWM6BJ4nqA9uCN\n/L3a\r\n=Xtpi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.4.2": {"name": "@babel/parser", "version": "7.4.2", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.0.0", "@babel/helper-fixtures": "^7.2.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "b4521a400cb5a871eab3890787b4bc1326d38d91", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.4.2.tgz", "fileCount": 7, "integrity": "sha512-9fJTDipQFvlfSVdD/JBtkiY0br9BtfvW2R8wo6CX/Ej2eMuV0gWPk1M67Mt3eggQvBqYW1FCEk8BN7WvGm/g5g==", "signatures": [{"sig": "MEYCIQDjCYwWsGAQ7THuvWvh6RFqhGDycAiY9RPufkxYitDu7QIhALo7TJqs+vbUzXt/bPI7lL29t1WG52sAScxF2THB1qUF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJck2S1CRA9TVsSAnZWagAA6tMP/iU+eTGQKmia+5r5KwPR\nbjqZKLpSp81nz+VaKlu3yN/T2WMmJcJutmI887wtBPzlGfUYOfMgdtGOh2bB\nN4mC/RUBNtF9RDr2oFTyx4gXQhGJcy3lXO2w9bEZovxdvNUKp6ENpTYNrBnv\nJ2UHiGelLm2WGFdAHhwVg32QRATCH1beJ7QKOcyWn4f0yx32ATjZfKbHQ/8i\nA7oDx+HCUln4WgD3/nVqjaAb0RrMwx7yjEAnrcCQbAXFvv043X7eYwTNQiZV\nlqAscYE63mO2xr63Ja0KWcvlNZlmf4AnfI7vqpiqlnD7y+px7aySj+8o2HnY\n<PERSON>ngprOMD39mR6H1Arc4zOgpyaFL8g8hQ2iKBdEag/HLNYf9f11UfQKrJwCN\nN1pO3hN5y3dqBi144ieRIYUzHbbzoe5YLd9RRYQseW1kZtQNrNVOf7vinzMr\nTugoxHKzsKNRrlHPBlVXiCGNt4RJyUlpWCEwIEqYMIkLIYCiho2Yq+eezc8F\nXh0mFxk1fCvJI9/SIFY9/ZNI6RmIGj5kJu93eZQgTeeTCxS5nHiDtOMUdAC4\n8XrAJTM6aMjMR11xmsOUtYcBMxSVnUljY2UR1zms4Fb/WwHpsqR/sadY8fGp\nvBzPBLyha56HpT/pke3aJeVM9MIwWqB7TFLvSu4vTgKSnM3h67lDyshg08Cg\nb5dL\r\n=mpV+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.4.3": {"name": "@babel/parser", "version": "7.4.3", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.0.0", "@babel/helper-fixtures": "^7.4.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "eb3ac80f64aa101c907d4ce5406360fe75b7895b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.4.3.tgz", "fileCount": 7, "integrity": "sha512-gxpEUhTS1sGA63EGQGuA+WESPR/6tz6ng7tSHFCmaTJK/cGK8y37cBTspX+U2xCAue2IQVvF6Z0oigmjwD8YGQ==", "signatures": [{"sig": "MEUCIQCwtT388CR3Pp+SQlWkR/2D1Br0fIBYtevZsIWzmQ8eiAIgEAWm9mnFTjUI6w+JMCpx5BWcbT93K4gBk25L9eTsht0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco749CRA9TVsSAnZWagAALJQP/33wpsZYUkYJVNNm5NTh\nm08MgT69tABFAZ/s9H19P3O8yXxxQgnr479QvehrN36JwLy+R4vG3HEUD9b6\n3YIu9/QqJnl+VI5oBHqAQim2JnhOK79cL2YFCuKhSkBD6FfhBE+LIuLUEE6x\ndcFGI2y+3XtPjiljcLUjVLu04WeFo4zURS2a0aT5hJdlWS8HFQHGIVl5z9V4\n3SyUoTcx00bFd7nEUma5/nQe9EfcwqmFQ4n1PNyrSlzHouImCbrKCRO+tgPY\nrK8nHa9If4ScDXWcR8BNjKSK+IPzeB8KswpEQ2C3nlXruFPYppY8i/z9v4hf\nLd993mf3LM9u1vEKNH+LCmcA2zW16Sqym09ArzlaytjZr3GP6bJpLzJ2IWO8\nQMilL2wyWTvVLOoWthCRxr09ariEsFbBj4eY4NVAycMXuvv0SYJez7/xYqdb\nGhNG+jzy0HYXE2WrdrC4u7IaPkjGEb52osiMhZD35R9H6DraMJfzbcLq1v8X\nnc2US5cX47S4G/CBeP81IZqVHKCMm/uoOwP6XNx8/Ml7l7saVRj9wazem5SR\nfolhbXTbAQ4d+qpuOlxjWzZ3c+/3+ygrMLAp2Qid2j746S0TBIp9EP+5YkZs\nA883NltM1QtuM1TAWimISCF/hmVkBFS147xJNAiSNtC7srq8QkLKrY0+XUuZ\nowM7\r\n=bBSx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.4.4": {"name": "@babel/parser", "version": "7.4.4", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.0.0", "@babel/helper-fixtures": "^7.4.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "5977129431b8fe33471730d255ce8654ae1250b6", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.4.4.tgz", "fileCount": 7, "integrity": "sha512-5pCS4mOsL+ANsFZGdvNLybx4wtqAZJ0MJjMHxvzI3bvIsz6sQvzW8XX92EYIkiPtIvcfG3Aj+Ir5VNyjnZhP7w==", "signatures": [{"sig": "MEUCIFlDr/6gATmsIpcO0QGXxUb2wl5XlxvjhkFxUzqQ17TuAiEA6T1U+FucPVknNvnP/F1dlsfoXgHkWPQny27ClcjxcSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3IuCRA9TVsSAnZWagAAll8P/0CRtLLRwDa5tt8qGqBy\nZsW029gD6trEofLGXXtDfU5nsDukUGulcqUK23BgbtAVPpSVHZ8+Y/g3tnyT\nCq2AfliZT9LBfDK/dCBCgbNBIbBsZPk8d5Exwr9sXui9dRpUVU858dJ8uMlU\nOzoxPo65y++ifc+j4O4Rgf/+M0+9+KIiQGpUvxj9d1ws4Kedzh9OuqwUKZsn\nsZ6Gs4vyVxTM8OrdcLMD2ljGMe2TMTrBaiW/ih6r36nBdwF+On8eR3oY4QDN\nVf1SokhuyKsthXjys7UYbczdgz1fgPQdEHrXiPw14DbW8w3stdOOjTGlV3do\nADVnAFUpNBPmPbDh/tzLpbGJbodgllmlAmzty3ym6KuMc6u5zC1pcdmXvIhs\n6PHTfseCOb2oBG6f8YhOUelPzsZfYknDguGxDAT9Ys9W+QEQrwk44ua3Zvgn\nMbGU9i82nmqTdPtFSO4Va/pINOyPFA8z02zJIwipCggY29Zo8RwBYqhbclRp\ndLVejJO2KDJouDnWneSAb3fL41b0yyGONEyMIUIBNvdOwE7/rJMaXkfhgIO7\nSQQbX8UtL/qo2ZjlY2uoVNecXOG2oSvjlp2Ld8rqQ0PvgilInCXcs95i6ZHS\nL9TP62/BMZKGj6Pjnikb1OxGGCbywk7SMYXYMJMz2jqYgwHGuwfQosfbTym6\nlS3p\r\n=pzWh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.4.5": {"name": "@babel/parser", "version": "7.4.5", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.0.0", "@babel/helper-fixtures": "^7.4.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "04af8d5d5a2b044a2a1bffacc1e5e6673544e872", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.4.5.tgz", "fileCount": 7, "integrity": "sha512-9mUqkL1FF5T7f0WDFfAoDdiMVPWsdD1gZYzSnaXsxUCUqzuch/8of9G3VUSNiZmMBoRxT3neyVsqeiL/ZPcjew==", "signatures": [{"sig": "MEQCIACwjfzb9aBCVbUP3U6GyJlmxpfbGOYYI5JyOF6b2LBdAiB6Wtw90rm/aqRTI6bZUEGdepgGXsNFz26NOVCoGsP1Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 380255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5DlBCRA9TVsSAnZWagAAVGoP/j8+GyjrybpUcRw9akKO\nwUxQElwFee/BZQxEGCo6L8HeAMM34QGltF0lLkU5a387vj/1jf8vkEZJj3dl\nbTyAxX/7+eS3lzeUD6LirNmNneVU0nUACgGzTpfGp/vPt+WMWKutsog6/WFA\nY1lh8lynwWwabOGfWUFgGUy2gIj5kpcmMW2bWRko2xlZqNy3wb1EdbwZRrEl\nWwFIO7drAZ9l75AD9lVDoKuLxduRZJmNPcma5UMhYojOjcXdikOVM3GSisGo\nVWZKKelmXxDnLH8sO8qdXrM7nEuYYLkLpR8iAKY3e6VTC71UJS0mh8NMN3gx\n2XkdxpGY1GHd8ZnSoaa8NoTzR7R03PKInl2We27XCQuPb/6i3Lvl6nY1RGxG\nN8AtkhWgqGW7B/uhm6X5V5nx8IWcOqk+t33VS63+Xajsavis3N51wjnR/dsv\nrHedpy0mDlO3EZ5Y2IceOU4BeNkUlnFctwihnVkigmTVlXHDdoJYBuU9sKZy\nwMY+iIUmSHJQuAPVOQnkXylRUczrunfr2xJndYHXOKW7/zYJWJgblO0vvKM5\nLcD8fO2YWHdXeHq4INwBv0Yq2ZqKSXWvyzY2QxV/S+lh2xoQIpIBRLNeYOf9\nE5vwbEZ2wDJgy7FJZfp+XY+OU/BASa64WsoOeS+nzT9XscXIGLHZup0N2pUV\nIU9z\r\n=kVVq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.5.0": {"name": "@babel/parser", "version": "7.5.0", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.0.0", "@babel/helper-fixtures": "^7.4.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "3e0713dff89ad6ae37faec3b29dcfc5c979770b7", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.5.0.tgz", "fileCount": 7, "integrity": "sha512-I5nW8AhGpOXGCCNYGc+p7ExQIBxRFnS2fd/d862bNOKvmoEPjYPcfIjsfdy0ujagYOIYPczKgD9l3FsgTkAzKA==", "signatures": [{"sig": "MEQCIDwn7aLI2rxIIA0DKFu1grc3HE+0RSHXLW4wz1iJcGjaAiBRn8Qq/fYR4PXQ6B9zDTUt5gHuNFdR9qtD8XD0FvhwXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 381965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffWCRA9TVsSAnZWagAA9jQP/06OdoTB2pMqBTLZ8zvg\nBq+6vDCqkG+GbleZGmKdPAE//ViAVISi6NKc9X2BG791pMsOmN/S7NhzmHlg\nfSOFqGZ/3LS/eDvsuLvdTnvGls8bC+hsaldlrN/dNB9I9z4A2hrW8R2Snqzr\nPePG7tfjH8FoEnoBWnj985LG7KmhlCNnIztJJdGlMwCS9hVMm1dTNMdrqZdJ\ny6MLhDWhIZHcSbbVgdwYhkPDSBgpsFWastKuChVOp7sMdVH7Fu6yGo7D80vT\npn1FtYc1m7g8h45sgnzDTJlMEucsSRS8YwTHdBTQVT9pZtzXoNCuHudOAcVO\nUowBrrxV5AKqTMXTHgTODjacheEHbln+zUsa8wdlfnEAlhrvqz3oAnF171Nm\nRcdqHdpZY0vJYvHRrVitepI86jpprbzO5AU1goGU0UVUSxU6cQ6vGEdQxWtS\n9J0hkfObIyShJf5mxbT5K/B3jvzYQc1pjohiNriCX4F5Mmr/ykpsev0nsRaz\n+83C1fip3+XQgMqsqPh+BmpU8rkRFrX9gM0TWxpAZMhTsQweilP5h/B7g3Oj\ny1CdUIIWgliycoz0IbLqR4LSlKrv+EBkSUlkBLO0nuGgjm+Kp2xeNLm2Q3bB\nvtpqnZBXVGvgAtv85HaV1MH24oWDfbrDfc1q1bfyoCFcnBrB24jybDLD/HFk\nIvdR\r\n=3QHN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.5.5": {"name": "@babel/parser", "version": "7.5.5", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.5.5"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "02f077ac8817d3df4a832ef59de67565e71cca4b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.5.5.tgz", "fileCount": 7, "integrity": "sha512-E5BN68cqR7dhKan1SfqgPGhQ178bkVKpXTPEXnFJBrEt8/DKRZlybmy+IgYLTeN7tp1R5Ccmbm2rBk17sHYU3g==", "signatures": [{"sig": "MEUCIGveRlEsQ9b/MTb1sTSEjB3dGocOi5w0ZfQIqkgXxkNtAiEA6tcnSxmtLQtBz45Xv3dOUMqxqugTA1+0jkqpWqPJvuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 381965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FXCRA9TVsSAnZWagAAvF0P/1z5k4yexJtyJMWGULou\nmVC4HBAsjD4Az4v8B/EgpV9pE+3Oa0x3cklS2+wV0OKFK1Sad+GA1zhJCGAk\nkULfyA/MmPJEitBMjUg/rjxL+qJtohD3+xgmxDQemPecenKI0fM4v95W2ASg\nrbbu16FB8IIcEwtERoPytfPJeAPuvQg1GQJ599EE17R+SK1TJWYwlCZsJLU1\nzz8eL0RilPZvPT7NU1mrn+W1nfUBeZHRo70PZupTaCHLGijfg0/CZAv5Lp1Q\nTUtGV/MJMA9k+3mjXBukPgi1sg1y7p7jVTqX71+QnQAdCnGrS35wQKzHFqrX\nUL/4+MMqz09bhkiBwqkQja9j8psoHdpRIgzXASvr0W1uOa4gtlfEgQFkjDti\nMQTdH8V790ou8UKKK3OzyekStdICtqVu60QQAfW2OZX4pCJksEtKRamGLSpM\n2JxymjLxHgFBujCbURoyXMIaqC258bjZOgZ+MsHJS9RXPeu3j9SyM8SctrX/\n7u7CIBI+ImI8V2Y5qxBLm0uegrr3T6MlDwxVXrvLDzLAsX5lJDvUaloqAs8z\ncUQbw2mMqoRnRHZ5s2T1NByLwCCkVHdoqDpNlfLOiyVamGtcN1p2M4oOB3ll\nSFVS8c2poac9NiOdGnITa7zKMxpiC/N6Pt+VuYAxuNxjFcFGnl4xYPyZK5/7\n0NU4\r\n=4w4P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.6.0": {"name": "@babel/parser", "version": "7.6.0", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "3e05d0647432a8326cb28d0de03895ae5a57f39b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.6.0.tgz", "fileCount": 7, "integrity": "sha512-+o2q111WEx4srBs7L9eJmcwi655eD8sXniLqMB93TBK9GrNzGrxDWSjiqz2hLU0Ha8MTXFIP0yd9fNdP+m43ZQ==", "signatures": [{"sig": "MEQCIBjFEdnicZ7H/SF0WxJ8/HuFnGH6upwe0nzZU3a/Ae5CAiATT0vPGiKbUabu15OlbTJWAy7MjujBxnE/6olKfR5Zag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcph5CRA9TVsSAnZWagAATBsP/2gAMDrvojmv3pidLFGA\n/D7Q9jVU5UD+N8vwR2OJyqT7Ojnha6fhRZQ1NS/UGBMH5DpQH/7cppWA3SUd\nPY32sZQAhyd54Pt+rVO4MBblnmtA0Jk+dYKEAYktdkYaB3e13Iy/Vr3IagfQ\nuqH8MkGWbXg5Gf+pmR0ZtSlqFu2zAbJgWYrg4nX0fQr7jW424RSfgOgnYeDL\nb7gC4hMweOuOqDmLn0em3tj1R/9yVNkCrOSIgo9nfOsNzRB9UHadsht4kVcD\nDxvJgdUar21w0M4qXnmwFcPI1YMb3O2sOByC/KdpuqITLDa9PFG5DoDTLSjD\nnT8c3LXvmEkkFyaJOdfhNrz7eXsdIXzCfBUnsR3rI0ZYIo45OIW31dhp83RO\nWCveTbwjioOr7HOcBOvlXA0sF68/I2X/05rSx6+KxeBVDyg/SblmHz8fDhzq\nWXpila1Q1IA7BGyFjicrX+qT9Gh9AIPwlOAQ0jty8BoDVBoYlJOIyww+Ed/w\nndMjQ/3afce8wYaGf0qoNWKSw08/ydSFiP7+8nb2/O/6yg4niRGW881ZOfws\nRCNTPomZyGDRm6NFlBVi3MQQH/A1eqzav/N6EqA5+0WAq4MxJoZrA+T5/rPo\nMHBQu/4E0s2SdQufXFzA3D0NJO2uBXTBisWRe+HmhzGxwZbsLqjl/f3RmAr6\nzJkQ\r\n=RiQl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.6.2": {"name": "@babel/parser", "version": "7.6.2", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.2"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "205e9c95e16ba3b8b96090677a67c9d6075b70a1", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.6.2.tgz", "fileCount": 7, "integrity": "sha512-mdFqWrSPCmikBoaBYMuBulzTIKuXVPtEISFbRRVNwMWpCms/hmE2kRq0bblUHaNRKrjRlmVbx1sDHmjmRgD2Xg==", "signatures": [{"sig": "MEYCIQDOo44rvRI8qU5G+fkmI9M94sf0GQwyqw9HH4ke7xMcsAIhAOE7hHK1WJh+XHtmJETwRvbJEp4mNQeRLLMRvvGSdPUS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 385617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiTdmCRA9TVsSAnZWagAA+eoP/1TrYrQRQ65M1nmPOrjK\nVDs0baY2K2/whw+eqNrz02YNuZ+siFSWV4gfuo+2x3lnEuupCF9pOf9BR1q0\nncRM87r0kdH9T3MrfnSmfwqGXp6cXzVdeNJvaqZxadaoZ165gye/FpDFlboO\nbScQmuG1pUSwUG4ga0gWSZ+VOdhuoY2xOij5y4deO/kmkw0fHK36TzPewaqa\nX60SornVRYeNsz/h2egWDDCpb5i5irzN48Pb/9nTNhLU2x9Z88v8Sp4eOwuG\n+DAKXFChemMOMP3XkU1GzuxwX69WS5H8s5fcT1inzB7b1OvN27YGdeAM0QYK\nzAy/TrnjcAo+2HOPkqZ3dlNbMVFMshiHzaV5FgrpmhZOXt7Oi6KmsyIFbSRS\nN9VLCfYlakkHYBp3R1cUrm9tVRWOb4fJGazAZCEqCkDTf4t6erKuDy+MhqYK\nxnuYb/3gYQeY3gKWjUjVDRVKsKZK87FbMHCCMX+KX0DiLtP/wpG4CorahVfs\nsm2M/+lEKgmIyBHl7sGldOmwI68DIb298jjzO/kxPgb9cIM+lXM7wmD9/HV/\nnaDXW8bkb3il0U+2rZREcofUe2BOK7GiTcq0sp8QTfhCC0q0qyqNnliYwPKV\nlAjA5QkBPBuZKeYIxHT7sCt5Ah5J6SdPyipX/j1rA4CzVUaevja/Bknb7csC\neuZn\r\n=clRS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.6.3": {"name": "@babel/parser", "version": "7.6.3", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "9eff8b9c3eeae16a74d8d4ff30da2bd0d6f0487e", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.6.3.tgz", "fileCount": 7, "integrity": "sha512-sUZdXlva1dt2Vw2RqbMkmfoImubO0D0gaCrNngV6Hi0DA4x3o4mlrq0tbfY0dZEUIccH8I6wQ4qgEtwcpOR6Qg==", "signatures": [{"sig": "MEYCIQCD/M6Ej0Hec37gdQgaFAonlPQY9ACmqdltAqj6XH0LbwIhALemru3Rxlmd0REOeS4Lu2zXQflM8Qfz3810EhdjCzhG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnOhTCRA9TVsSAnZWagAAWQ8QAKG5aphIsZbW4sek0dof\nctdMeOIgFw2wnCGPlorvGkHbJoFWntged/oHpdkYyak5TdL/2pWwwbvVWVKP\nRO6U4yqSDovB5VDDzOSO1Y9kGaZwYYxf8GZSc60qvFjeAyBgvH+hgFRutofk\n6zwCJ/c+Qq+b1bHN9hysBAn+UEjE3K56RWewPjk6d9FBoOZeYRRc8KSUp3PA\nFbRM4RG0rB2RgiI0Ywh0OKI6rdR40CQolRfqEIXn3PWOLUuKHS75rnQ8mKcG\n+13aO3zk16jbkkLPKwM+YLq3fAe6Rmi3LoUWmSsDn5ng/HSgI/kbMK0mACQa\nSLDFnhbO6/56kUlnIKz2zMQ3uhdeOIT+DdOJM5seZyhXOY9nY++XSPL8dLKO\nBuWmU0d/r4KRHHqla130XEnAkm22aDrOfwQwSd6HTyK5ESJwAddAgCpcskI1\nnSeI/pJLC4Xkq9XhgUroZy8WxcfR9cnXVUGgIpX6SdJxGJnzf9WGYOXECDDc\nBUNU0gmWYZpHtUmAh/HAoGS+2/CEJNxDkwWt3aeLiPgQcGLKl4Kby0TV/xAm\ncKWIl+BJ0xapKPBdPmxK6PHmRVRpJW0GWYZLDjwKqcQPstWlTX/Mvo1t+tR0\n8qcrj59J9BWsHj9SMwcFBUHeXJDdAiGvpWfnlyE5wf2w+n++mumz5o+v9lw2\nDMVj\r\n=dNVM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.6.4": {"name": "@babel/parser", "version": "7.6.4", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "cb9b36a7482110282d5cb6dd424ec9262b473d81", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.6.4.tgz", "fileCount": 7, "integrity": "sha512-D8RHPW5qd0Vbyo3qb+YjO5nvUVRTXFLQ/FsDxJU2Nqz4uB5EnUN0ZQSEYpvTIbRuttig1XbHWU5oMeQwQSAA+A==", "signatures": [{"sig": "MEUCIC6v2yY0UtVNYv68WJPpE1cAgc19q6RpiuUQnJE+iRLeAiEA1QTfqoHBjT7RsDhInxBYgljftSVQPxyd1Qxs21A818A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 388020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdn0A9CRA9TVsSAnZWagAA5eAP/0pLhki/oAnydZPnDk6p\nYOcuCNlgJNqt5GPhXBfEvx9xaT4TpY6obvplPAiuMGxQyRNjx/ZEAI97v0nO\nlG/h0ER1v0o0C1qorZEu/5RwUls3vwDcVaVVMie3lL9sqW8OeArY1plgvFXp\nZIl2lvp/uPRoFUFLO4DskPyaUHb6gTOaL2xCAnTmnBh0MB6uCBIFDwg3m8yi\n1DsY+aUGlht9S6ewrzcp9SkO5E0BOnR7so/NuPwELssZlg1oKLMKEzeo2SFa\n32CGOGPDU1d7lJj1HTACzJnjdYrEZdGiemxzPWwTK5OhZqIWXj2juk3wO1yh\nllXd3f8YDNMPLhApPhQWCiyLC220qXmmQbasY79xnE6nUXHuV2w67ua9UnVB\njd+KdC/x7eFClavt6e5oxcdrEKaLjhXd9XrZweM9jUHJoJlM0Pi8W58e2Bsp\nUd0HnBWomJH2kjRiU8gvgqVRraRRlEbiSmsoG1ZDjPFnnXxGxrNnEpYY3tCB\noVpiDRfskTTmardgnAQDEEJny5Fm73R97HagRkypU8dISuRCdoWMB9PtBkS2\n+JNvVhZw6pbG+us70zkmYf1FE7vGg2dzld4YRWy1uarcJHWck1ZrrFMDhpYY\nayiM74n9FxOD1jEXwPD7TeXdOVPU8sy5e+8dNCm5zGJvqwmUjQ/GKFa3z51f\nf95o\r\n=ZAvh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.7.0": {"name": "@babel/parser", "version": "7.7.0", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "232618f6e8947bc54b407fa1f1c91a22758e7159", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.7.0.tgz", "fileCount": 7, "integrity": "sha512-GqL+Z0d7B7ADlQBMXlJgvXEbtt5qlqd1YQ5fr12hTSfh7O/vgrEIvJxU2e7aSVrEUn75zTZ6Nd0s8tthrlZnrQ==", "signatures": [{"sig": "MEYCIQDbZQWHOtdbs4juJafO85FbefQE+S5GMZje8P6wGO+CJgIhAML6m7cxKM9G+fIs8h9xTdIAldeJIIr1+JMl6enMZtIj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 407523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSYCRA9TVsSAnZWagAA9hYP/3y2hE6FkhSshERMJLKR\nwEPGcXzmm1EgOMFAZZvRLpCSvnCeXZRTuYo8l+L/SNA9hay5lgWSc64+4MmM\n+172v8nA+UM+GW9d6mHqIdXOmFljYlN6wnoMdLBu7jjojWCIle0oNxiNNrVt\n8PkZ6/bhP4KQyjf+Z7kabwWwuedp8+Ol66usy5fsFu36HhPcIYhSpJUaMr3V\nJCBUG/JtUB9TZLdb+ETamw2KRgjnhjFnkZ7biWkDbccF3t+o2LeJ/Pkz7w03\niLHHdNAskjJ8IwjpYZ76SBPjPic3Dtzgv9Xo3e+ZcKNpTNNsyo3HQ4NIhm7T\nGmwqSWAaKYZMfMPEY1pkdrDpEqMA8m+VXREniGAr4vuCzKg/yG+ddZj7vhsQ\nu0dHTSmg5ItczX6tkQxEwqBjnmYlqR1i6vzHsfpkmesUXKZk9cVLnDPhSo6c\nWiCOeJsUC7bs39WYnhrsspSkCtiSe1Vdkkz1aLsvniz96qZVgdoQO6GYPPUy\n28O4tC+BwV+8Ha+ZCcpVu0zkYAjLEH6Qo9vnZTF8mXfkm+Mh+WnGDYZB22YT\nMu4CwxJ2NTRwLbXelnk1WAZU+KhW+y30fKrD/GsPu43Wp1Vxe0rlx/cWjn5+\nHh0RWFfvZsxvgLcyVZkWUlpJ/Q/H5Pa+UIq/3MuHJDTGm3c9Yd7DoNLklMO9\nyCy7\r\n=UIFL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.7.2": {"name": "@babel/parser", "version": "7.7.2", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "ea8334dc77416bfd9473eb470fd00d8245b3943b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.7.2.tgz", "fileCount": 7, "integrity": "sha512-DDaR5e0g4ZTb9aP7cpSZLkACEBdoLGwJDWgHtBhrGX7Q1RjhdoMOfexICj5cqTAtpowjGQWfcvfnQG7G2kAB5w==", "signatures": [{"sig": "MEQCIFDD95NEx+WY18CQnWGG2d1E3OP6fS0Ivn45N2NDQsXmAiBdn/Y5OYvT70BsYgsTa6rsG3HRr6HsoTrBOalLxEXnww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 407556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw1bdCRA9TVsSAnZWagAAnF8P/2hc5XrCQmgogAdTr4jY\nzThYg7Y+jLvAv/iow9jo/6INmjSLbDAmXmTZzVkAEKfI2LX4iPfrLdOFy0U9\noP5zYItf7i09RAdz7OwhINiL4lAF4IwplzkqgM9B9UbMMhJo9rgkbKEn4qIS\nT1lMQeuqVCGHRcDkOg+59Ye13ET8LHIavEbogBtjT78FVgVkVYwfwrVCIoGs\ncxPi/0q5r4OfpZdqC8gZKfIwZnAXT/66Mg9TTwh/oydIVsPoTVECNWsRA4LF\nJuijfhKjc7OS6zNXBAVJzsAuqJGkJXL9TltMT95yyIr2gi1lT8OdVvshOn6I\n1pbapwt+9pPRiOsSGX/QY1AubPnDYIRLxQqHg626Gne7GkA9DkG8BjK/Weu4\n1+9oNPTLiqf7H00PjQDCZ9mq09rHgMUR1YT++2pcxDToBVZ8TI1S6y29/l4D\nlSrmCMICbyHQgkmpVVsgRgBOHq0y2qstk3qcctIG/RnEYpBQs8c9dxLyxkbe\nXArVt6AHEGirZF72Mf3pVw9m+6llwL5yZQU0n6pvcewBQbvHWP6beJ/qGUgy\n9U2U5AKVTA2IJZF+ycAOtrKU1YP8gRQ7j95xrGEUIMiGnkR1wO+4hZPBgzwV\nXzZAwfmEiZQLrZd4X4vsanfrzGfQW+9BmUtM3Pq3ETD0W6n8YN4vZky1NePI\nz2VD\r\n=Fzso\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.7.3": {"name": "@babel/parser", "version": "7.7.3", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "5fad457c2529de476a248f75b0f090b3060af043", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.7.3.tgz", "fileCount": 7, "integrity": "sha512-bqv+iCo9i+uLVbI0ILzKkvMorqxouI+GbV13ivcARXn9NNEabi2IEz912IgNpT/60BNXac5dgcfjb94NjsF33A==", "signatures": [{"sig": "MEYCIQCBUI4scVC2gAAQ7Fl5fv+v3Uo9TrEaeYvo7x3qtjSZDAIhAKuHt6uzIGFIaTWpa4Hjo2/vZneYJAIaD+7kCOoeWpix", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 407486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxdUWCRA9TVsSAnZWagAAd2MQAIFKoytL5qCIbiCw6dyF\ndbJ2oj2+TxptJx5zgh0L0fz6ZtEqqLmQKc/EdhFZy/Nn2qMIsh/lWQ68qOSv\nD3bS+KDSM6aay3sN5YWSY8KZYUvmupDe7ECtWnedpk5zRC7IKxlTOzmOaar0\ntIat3c6ldFXGS2ZajsLA6OSa+wsjvGSNFdbhTRxfROovYAqq4anSy6wjnn24\n5WQPEgWKigGjgNIsf0rIrad9nGvlAwMnq2y0TX16Sc9JhHQYe1HouuEVg8Pb\nq+cMi3VTM03LFsySldB10uj7JIvUoVZ4GrTBkGn21MW2Jyz5t81QYKpNrCvo\nCyxckrOa7H/TcrhHHsAGYf03hrGgkh+FIyE40tVPUhohtezGDMMU+S42gQWH\ntqdNTzOf0yL5ZrKJ2EI6bUsGVaor3Kz0BYUu2NGBnemD0wiEo0a8003LAcI5\n7uuuxR9W0ZEuunHB0WyQeXRgXIbzbyC8xo4NeP02cUx00eCkVojfTsGdMPya\nNnuVeAlcRUtG/DkSTQQRxKzSRYqQUbt41x/F/Ga8aqSHUfu0fnoGQ2ScGqcv\n//3XCnw6NJmTvWanxmYoB+1OgV8KweX67r+ULsAwG55fjSD/VUbyXxCRiDqw\nMCjVW/t3Yj6uFJBxs50eQkUWV8+vacsAphCKLSQE3T20GQXCW7u55UME00uQ\nOJFw\r\n=5F3V\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.7.4": {"name": "@babel/parser", "version": "7.7.4", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "75ab2d7110c2cf2fa949959afb05fa346d2231bb", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.7.4.tgz", "fileCount": 7, "integrity": "sha512-jIwvLO0zCL+O/LmEJQjWA75MQTWwx3c3u2JOTDK5D3/9egrWRRA0/0hk9XXywYnXZVVpzrBYeIQTmhwUaePI9g==", "signatures": [{"sig": "MEUCIASD0cdZGYSmcn2QYoRDhDLMV+ffw9rWiqLZUeZk9D5nAiEA3yDxZAPzFDAFzU9VLBz9q2dMb5gA9GwTf3w4LLzCPgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 408851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2G/jCRA9TVsSAnZWagAAs6IQAKQ+4WcsP1B+mQW67heb\nnZVanZrXLKotNXYmFVjnpH7hVXE8/+PZ77TsqPhlBG+9p9Y/OjBw7eX8vM4u\njVHZC9xElrP3l4H+OxoyLSsqAsyrrSAN0W3l0q3FqY9bJ5tLlmzQmsQRjxKQ\ngjpGm6taKJACb0BMIUvIpt8RrfY57SmIsJ9w2HmUdWRLt726el+FNBl/5EjP\np2eUzPvRU3NMc0MkbwIZUDBHC4Jo77/C7lWSZuA5WuuI20TffEY8NHCAMW4L\n5ofLuUwyrNW3teq7AhgEal7bleeNecAZhLbtqrYinf68odt1+hil5UKvfMnV\nhQJCi6A5ms2aopBg8HBwNFNp2r/U/BjkLCYDtbTZA4dwJJxx32NmtX7gSO3n\nPSBn+2f24BEWc4iB2ThDbao032gy4q012ErYuldBKl2d9Mm3on4vgrn+/r3s\nlfagxNJB74KdV7b8UDtsYXq/fxFtxZBW+SdtcVM2kRBN9eoDtHGMDuyJkhUl\n7t2Fe0yqK1yWJAyeMnJRZJiVwpqSIQ9w+HCWHwoly8xxd8P9l52J8Tx3Sk3k\n2DV8VaUwBUbrMzfhL0nWCCzfs0D40KM6/OsmDFGIRaiYDkTLkYy+eSmYThcS\nQHGrH7RYT8+j4PJ/KGHJ291TOmRn6Lv/HuX1u6h34TXVoT7OxKILdzyN7Jpf\nrOWH\r\n=8MXl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.7.5": {"name": "@babel/parser", "version": "7.7.5", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "cbf45321619ac12d83363fcf9c94bb67fa646d71", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.7.5.tgz", "fileCount": 37, "integrity": "sha512-KNlOe9+/nk4i29g0VXgl8PEXIRms5xKLJeuZ6UptN0fHv+jDiriG+y94X6qAgWTR0h3KaoM1wK5G5h7MHFRSig==", "signatures": [{"sig": "MEUCIQCs3Stj+fGyX6W+tGjXfclQgMj6mUutao3gGUMjIoOfcAIgd/H3EHE8vIoXzxbkeKczDDVK23Gmi7fM9+SrKU5s6SM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1700219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6lT+CRA9TVsSAnZWagAA5lwP/1dsOUjcSywtC1KiksqP\n3k3QptFAc7js2odHYXwaoBEWuRAtvE65DJCWYRCytX4NuiqStAMdpw/foqWC\ng1blIoVnV7lyqV8Wosf7ZPJCuyIUrzrgTd2g/MvR3zQEPGKZqzKpGPE9sXpu\nV0Dx3A83WF7k4yZ8hbG4DXaa/OLZkPhYb1BFFpHts9RNsOb1ws3qDNDekD6W\n6/0B9g7L++i5PSi9g6402ZzbEbCBkpRrJAmKY9iXFLFEgwclZHIWQ69Xyxbn\nV+qoRn03GKhPa4u5kRwte8WhocXRy6mobTOMtYV4+TfiWU2UgAz7N0+Wvqs9\nYXFpy4Dz3P5VI1w3O0XbhF95XQvm4zRQmuhJNh3rx6rhDv/47ad7PiWPTL8a\nlheXK3Ha7AsCeZXJeJqGi5NoQqGbXRrsf5WGQUU2m4F81TBAVg/2t2RPr8EX\nVrTNj4OETiWH6vY6u7ySfvSxt3aUkkTfuxtXnF+a7d4wx38nELNNj7vwyzbN\nGiWV+lIjTg8teflvDxbaSptQEXKGobkU++bRoEYOe0nkGxL8WO/N8ABoUxh3\nT1EAPfLaZIOYHor7C7/0pUwBZ9eIxSmUl5675XlAEmQZ9pXTSfUoBU1OMOdt\nGuW3pdT2B04LfFR/rcycqN1/prixUNtXtXDPnRXx7iMbMorQNenSELjYmBsC\nFCXS\r\n=PHXg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.7.7": {"name": "@babel/parser", "version": "7.7.7", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.5.5", "@babel/helper-fixtures": "^7.6.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "1b886595419cf92d811316d5b715a53ff38b4937", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.7.7.tgz", "fileCount": 37, "integrity": "sha512-WtTZMZAZLbeymhkd/sEaPD8IQyGAhmuTuvTzLiCFM7iXiVdY0gc0IaI+cW0fh1BnSMbJSzXX6/fHllgHKwHhXw==", "signatures": [{"sig": "MEYCIQCyKqwcnnT/o0j1ZBuxlVuUm3bcQyyMUlBAYiRm/Uf2qgIhAL2PosgNAWsA4gN2kuxXJ3aH1vx6GqwHiGaysWIVUI/m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1703156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+sn2CRA9TVsSAnZWagAAjZoP/i6BR8jehKZo45lFdLdX\nri6fGyNAJ3g0+JTgJTaXoUgax2KmJJuW1OMfZjvVjMNEBt398fpZunLZjHVY\nFsnCT7Gf77V8qa2qLpUcBk4P46ZLbWAxf/yw3yKi0klOvQ7YQ9zPD39Pv2b9\n3ZDQg34fQ0aUNiMAPs/RZk2SCYVhr99aN2cl4pKr13xmPKGDherLcJQCKxD4\nTV0C85NNnpNhYJllA1jSKJdxeFaX6/+uWOlbR34JTjUl9RIg+OQEorJT6xfC\nmBPmWSwH+N1m9aLyzzhD8W45vtE9mCE9SeYckzEG8km+Atcx8B3SqB5VJLHb\nJubY1AeZO/NuETXLikok9dDxcLn9W32DqPWQCU3Jz/1Wv7K3tjxJScCnDg0B\npPN5Irf3DUYUZ0Nxq56cTMG2/x+52gG02g7PA6z485BpktPhbJBmGFQdCK8u\ne4IgBNoHJA5S2zuhJ1UlL2VaXgUoUKB8C93Kx2Cb/KdwaNAcewGq7yaP/aQh\npcfu4Jcb/JPc9RkNsaY0q4iirtrQr2EXYSdkvuhIcTsFVP4ezSLey+BidrlR\nWguU5GL50etjeDkBv6K1tQYa2uakDuWvFHOvgo2FbR08WE/13Id8Obe+WYel\nlt3OULKgc7ggoyUWBeAcdJZMVHWbAKvstp0sAFDj6hSKXBd1UiHINQzt7iEJ\n90ps\r\n=1mcD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.8.0": {"name": "@babel/parser", "version": "7.8.0", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.8.0", "@babel/helper-fixtures": "^7.8.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "54682775f1fb25dd29a93a02315aab29a6a292bb", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.8.0.tgz", "fileCount": 38, "integrity": "sha512-VVtsnUYbd1+2A2vOVhm4P2qNXQE8L/W859GpUHfUcdhX8d3pEKThZuIr6fztocWx9HbK+00/CR0tXnhAggJ4CA==", "signatures": [{"sig": "MEQCICT9sdEdbU4gvKBQyz57kCJw0rfI4Dx3kd+V2O+JBtgPAiBCQRLZh07JDUajGx4rfJZ4PLhk0UPEx4LER1+woqqhUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1714814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVDCRA9TVsSAnZWagAACDIP/17UKltrFxgVe/fN7peX\nTohEQ/bmoQeerceo4o14HeRz6ckKFd4jsYo9+WucGz4xJSFKheXperPt/xgL\ntor9r45wPqdAuMlOs/bnERM8lOclzDaTe7xUHgOLG537jdlfIhURhR8AmXBl\n5/VJ890L9dSKIqe/yH7r/y1K64jtwNOWUUig4DTL8oWXiMJD+mJltsLO2na9\nGaqyb4jlrZk7vPv1QkHz0i1SU6IXEwhOSQ6rBjXMWGzwJkpWZDakQlAnJ2gU\nrlaLaji/DvSZzMaqEUShi7Cyb9FMgqhPLH+5n4o6iKv2EaT7ZU4GU8Mq5SKY\nty3/utk0G8fde7vc5U40EvvmyIw4eRFYuOer1PT1yCXivkTem8cifN3Kua2f\n/mGxdkLHhx8KYATm9NG1VKyeOk4NKW3zgBAVJuU9pTZfply6g9I9A2piBkhg\nbll8wUrxLG/roG/I2HIaDMYHMS1O+m+kDAx8N1oMwiP5D8eOYakIx9GURtkS\n4Qd/np0OrVVgr0KzTrUD2NdFM53bJFhp8Pw2op4PUigJpS636d53GsodRvFY\nSdS0eQ+Q5/uk7ZggvW336mylnmTJEL/8REMgvXuAcG9az0DCPiUVzDi07lzw\nHsFvit0N/clbVh1nfndjHVM99Bc2kV+WGKaAxGFY99aa/9pIBPjTiAWeVdEu\n5NVw\r\n=SYn7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.8.3": {"name": "@babel/parser", "version": "7.8.3", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "790874091d2001c9be6ec426c2eed47bc7679081", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.8.3.tgz", "fileCount": 38, "integrity": "sha512-/V72F4Yp/qmHaTALizEm9Gf2eQHV3QyTL3K0cNfijwnMnb1L+LDlAubb/ZnSdGAVzVSWakujHYs1I26x66sMeQ==", "signatures": [{"sig": "MEUCIQC6/LQLx/kyfS5H+lNnmIX7eEDGFDWHTt9kmnp/cT7esQIgV8vCvn/AE+Rp5eavSAxeSw6rE7Ia//iVbhvk0rs3OIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1714792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOPyCRA9TVsSAnZWagAAO6QP/3WBXfyPAl5UaTogg1X3\nt+dOvMoUsea2kA62eqEaQx29rT1PiZes7Uz/P/fSxaKZj0apv5Ren7MquEYr\nuNyiFK1zpC13Bdfv2OB/UVolFs9/A421FicCRrQoSocTZTd99QCEHtUFSfJq\nW5dyoL/jifARKmnqx51UgOWSBrgBG78lSxhv0GNBeeQzfaroQQfebKecd9cK\nvmkE/EKtpIv0Nf/9f4bgjYsZFt4hi+z2eIihvrAM6ulpaAcvNok/g0uTSg2c\n3S9uG35AQwdGt0CdsGS4iuv0dg446zHTtyV7BMfVGi2Qz2LMiIZlKzanLyzY\nYal4F0owxxIdQiclTzv5qOBEq2YbPfBmjqRNC6kbYH7jqlksZeil7uncOfqR\npXrzu6Yd4Z2h7UMvqzSSCUfhMg2+T0jUyOArCYEErT7fmN6W2vj1nmyIo3Fo\nbjI2UZr4ZFxV/5p8uRmKpvTsUnZVmyS31ZRcXWByKeSZzH0LDlTrDqkZ07qy\nMfKiw0Spc7kw8KN3HrwBeryYCdvy3d0YQ2AGB7LXpKgQA3M4tchqYyLgzpBO\nGKpD17XLvJIjkXKV5cjfQM+JHQF1u5j0azqo7sm3rJJbFyI61x54uelcR5Bw\ngK173vdBPXlWBjXYKAPYLyd8xRkvYJkFz75iY23jdDuaXEy2cMNtSlI4k2fm\nuIdk\r\n=lDCU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.8.4": {"name": "@babel/parser", "version": "7.8.4", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "d1dbe64691d60358a974295fa53da074dd2ce8e8", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.8.4.tgz", "fileCount": 38, "integrity": "sha512-0fKu/QqildpXmPVaRBoXOlyBb3MC+J0A66x97qEfLOMkn3u6nfY5esWogQwi/K0BjASYy4DbnsEWnpNL6qT5Mw==", "signatures": [{"sig": "MEQCIHg7aG+c1UrI2FQXPz8Rnu5PldlQD8+bx1FetvqWew8TAiB3VkY7RCx6xfB6r2CrXXIaq5a5/tAaN8/BjxmYublarQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1715052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMs4GCRA9TVsSAnZWagAAIuEP+QANn3lN+20RKkJC9kCh\negRqlLKWX1lmuy1RzSpvjYrSV4VjO++APQF94PJS0Vag74kGGAmDFY8YVZAy\nGrvQW1MhDlpdRqcutHiS5G329jITKQxqE/HBdEPKiQKbK/dF+7qNaCh9fej8\nj1gcbwb5cPegxlZGPlAOYz0xUQ1lTCvRLEmvYJKvKJdaOFzZ5kkWMixKvRAA\n6IaBWP79x08r2Kxde7Vg2oPU+EN3YI501ZsBliUYfi5MdjpaJhjyL5DNtqjh\nLEs4NbFkd2hGDlt9S/u4MXOGk6k9Kc2CrF4vnPa8TmDC7huGCJ3zYBpu+5wy\nWgLe1d54pJkjEk41kkyQZEEbUjjAWSfkVGHe/zRbitUh1yhjSS/cfXDny70d\nn8cnYZsvLFULr5qMUv4VKZmJyBJxo/YhmHzpJJobi7WnjqNRBUh6s9y+8b7M\n/O9NeDK8z5RQNAYaAddeRFglkA+YBsSjqQILDc/zgT2XtPKll3iQoDA7x4bC\n2zlK+el+F9y7sKEUrIt0qVnG6PqdehB+1yfulJqKbItPNFDuojFaKyLQeZHx\nmhOJ1avhOfGjQiz+0MxG9B+fC9d/vwvPJK21fFcvpkF568eu8SjnyABd4PW7\nBOQv/asBTxTnFjErrR9fCn0/NfXdZYt1ix6C+Ib5yaIvNsf2Zj915xr+dVCU\n+av+\r\n=mgY9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.8.6": {"name": "@babel/parser", "version": "7.8.6", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "ba5c9910cddb77685a008e3c587af8d27b67962c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.8.6.tgz", "fileCount": 39, "integrity": "sha512-trGNYSfwq5s0SgM1BMEB8hX3NDmO7EP2wsDGDexiaKMB92BaRpS+qZfpkMqUBhcsOTBwNy9B/jieo4ad/t/z2g==", "signatures": [{"sig": "MEUCIQCgAbu2cuZcRWFuAAc/2jL9CQ3sT3GlabpDL1b+G7FX6wIgPIcWvnvBjVHvyGY/rpgY9X0Sg0Oyp7lFoVmetyXyhHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1723532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RGCRA9TVsSAnZWagAARM8P/jhcfTYooe7fxQVMMUf/\n9czqHzprMEwiTt/dhl/eQhMOeDM7cheJmjN2Ak+KFUJBGQIjhC56MuaVaZPj\nS7MtYR2liLC8GuzIlgRHoy3qpCb9kW4Y0MFDCLX9mNCf/ZuyjiYKaR2vZ9LT\nXTuykPyg3TUMkJ+Y0DqaUmgY9wIY8rhQGvQxlIwOETYdt9HDnc9VOrmb7PSN\nX1T5jYuNL+UN5apPQ9sMNWfXd/KpCNSBLYnNgNg03SdT0wP38FPes9xlDtAh\nkUYq9l121Ldiy5bmcYFf1sazC1r8NZOgJotcToIs3u/0Ys4X1JK5LpPNRwdg\n1NNP0pRvLGYUBMwd5PF2KdfAIekp1nDr5lJcYv3FQcJ7Y8S8bXcHJXogZDRx\nInWXrw+a4lGuKzoOc/NyBhsgC52GOoshQBYImuIubi5E8IG66Agwvr/xiyKW\nve3OyIcNgpIBWt+PV+cYLzg78dVz9H934YQwseWkoleAnhgv6xz2gNqV8tNO\nEJ7on1hS+Ff8KHk8ZPhIQBHvmLSVflJgpxmxlqktVcZoSzc7xae50vBsDs8P\nC0Nsrjkw0A7Yp/lwFyqZ0DIToz9ROuDh/GkvWpfQycZ1qeRXnSq+LvxDtNc1\nJy+LtNnJ8twxdNzzLZx+v26G+7fTvQlQMuwhWV1xoLkYyVVzA5wkhw6HrFb9\ngo0a\r\n=RUC5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.8.7": {"name": "@babel/parser", "version": "7.8.7", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "7b8facf95d25fef9534aad51c4ffecde1a61e26a", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.8.7.tgz", "fileCount": 39, "integrity": "sha512-9JWls8WilDXFGxs0phaXAZgpxTZhSk/yOYH2hTHC0X1yC7Z78IJfvR1vJ+rmJKq3I35td2XzXzN6ZLYlna+r/A==", "signatures": [{"sig": "MEUCIQCcIhf+lLSqlAp/0+h2TRfUzAbEfDlBAVm8ymf+7NBB/wIgchd2fhmLf4PXCn1+Yghod8LAim2d+35ev67SJ2pXfVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1770613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYFw3CRA9TVsSAnZWagAACRUP/A/CPW3DjvLn/d6w51Xy\nWIKmpZVsA6akvKII/sbjdKfZq7epJN6IvpcHk8vaTOeZwq2Fu7C+qHk4/sCQ\n36sPGuTY5sWFWJSWIunaxDyXPWix1In3SRyQegG/cq3pApcBUaHttepR71yS\np6NWRFAghy4c3tUNv4fD3iVdmv5xRKpilfz3nKhozY2lPOi/Qg0NyQUiJqor\naX3ghCKrefWV5m14l9tQOD9jAL90T4+PjN7I9WvOrnEz5GSR1rsS4F2h1nPL\n6viy49zf134rp/wu7PpLajzTZbzyamjETgkvEgJlLWfNzvP50r5SGkUrWSD5\n0yFWPlde9wl+sQXk9HfQVXbVoX9iOsPVyM6xFu3obRMwBiHZYd5XJMYsRm/5\nok5bD6zHSj+/Ran4rMmJI9Gqx0JalconbHPHSFo7riotJ48RIhoxAZ0hyGT5\n/e6cqVwTuBEwheu/KEUS5RVIOZMclFQfVgid0MinuGgjhElqlWlOuLSU3Nc5\np5DmkfvU+tKSAisS+wZ+pQbdBTjd002CP+y8cRocUW9MUNejE2vLuhVqm+WZ\nwhzevnhM3LNjo0BbrsKsGdDrjSQ7GfdU7fv6/dCFdj6bfVUYw0epYySEokOU\nHSBw6HZWWplU013T56hufQ/XlXKRh9i89N4y5eGpK79nvnEnbu6M+STeTLeM\nfoAm\r\n=3cCA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.8.8": {"name": "@babel/parser", "version": "7.8.8", "devDependencies": {"charcodes": "^0.2.0", "unicode-12.0.0": "^0.7.9", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "4c3b7ce36db37e0629be1f0d50a571d2f86f6cd4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.8.8.tgz", "fileCount": 39, "integrity": "sha512-mO5GWzBPsPf6865iIbzNE0AvkKF3NE+2S3eRUpE+FE07BOAkXh6G+GW/Pj01hhXjve1WScbaIO4UlY1JKeqCcA==", "signatures": [{"sig": "MEYCIQDPfiXt7NDuZOZYsEXdrXs159A2Lcu6TCjgOrjCuVVOEAIhAJCLntwoYyT1OREUafHE1B9ZZ2Kg8IKkjrPlslMrGnif", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1818941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaoQiCRA9TVsSAnZWagAAbLYP/AlmX1zLjZ8sn1I8QCIM\nEduJuYae8ZjKg7WniJExPpXY5mntYA3kHojsgBcieTWqrCBmdvQAZSinhDW/\n0k4bq4UD4sh3gWtwqrFSY+7ygH6owSV0YiJyy4X07joDQRl6ivl7uwsfW17H\npcVklsA+/J+YzhCBozjS5RZ/kbn2PchcmSxSCiGkJzrxFIhrTxtR6d/M31DW\nn81gJoDfk+de/1L0nu5BljpRr/eZopxtWUi0HR6FB3WrM4QCylovDilPnmsm\n8zaxU6jkGfFKauyHjSxjDovS2MBOXZlEkXu9nbJM1Hxbpw2HFUKB5DOYQMqP\nwGDJefRj7Ym7eYEsQ35s+GurXuAQO7slNlUYpmac+ED3M816QvkWtp4jT8qD\noF9Lna2yD8DGiJQnzOhcdbLQDEEsN/Mj3NUy4evyUsaNplQN1D829lbCRvPP\nCkrfuMcuxSS+KsTNp8HEA0Jeg+bnqwKwobVxs0ko/ix8/bMSJ/icbPArXvVO\nDDKlT51WlnB/X2z+0WZYBTNZdgscPIOdChFo66U5+3svvs3oP4WMO99KrtiP\nSxw5Hf6iIStNRSOdwZI8z6H+WfAQCUZuQguASNHwuVXSuqQ0q1bEL1JN5YAN\nj9FVRjJasGY5aQQx9YLekxfzbBPH50hQkC+JEaM6VhqqVRMqJ5cOY470JsVH\nMQ4V\r\n=TaS1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.9.0": {"name": "@babel/parser", "version": "7.9.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6", "@babel/helper-validator-identifier": "^7.9.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "f821b32313f07ee570976d3f6238e8d2d66e0a8e", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.9.0.tgz", "fileCount": 39, "integrity": "sha512-Iwyp00CZsypoNJcpXCbq3G4tcDgphtlMwMVrMhhZ//XBkqjXF7LW6V511yk0+pBX3ZwwGnPea+pTKNJiqA7pUg==", "signatures": [{"sig": "MEUCIGUqn9bx5/bFQAagUczvPGsD2s3G+TGzrlzVau7MX8LlAiEA/loosNEXEn6Wb7PpNbMf4Cst+5a6SJJ5H6ZFnFhpwLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1843888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOOsCRA9TVsSAnZWagAAF7kP/RuZp5WdQKWPyZPrzBDf\nMltXtHT9BUdwCQFHSxRF2uNBWDudmnubwrQTHmVv1FeNLp2ycDpN68Rn2Qxh\naKXPH1N6O6qovGzKvEAHQI0qJxv5bzph/otPViQG9SexSn9+AdWfqiFnGSZ5\nQ/OmbNHiTpN6PO06KV5L8x6Njxov+pqgAhwcwWyQZ3JNTCMyZw6ZJPTL8mfQ\nXDmocpf0/L9eMMPDv8jld8dQ4uOy4k7Cm0XPw0WEvOHd9k/DHEYgRftCxyM1\nCOgHnl8XyHmVRNqKdnuh1rJm16vh4LUxVNNeZdALnqcoCxs+fZzRg9YVmc7r\nwOD0nx6HZVQuuZYrvfcjN0cwzmZsTCJ7byond6XVfQKmzYZViJhPfcr5XONN\n+B41cxdjRMrklS9bHqppQgvibrHLmFzgxT5wTfy1RtLxHqTOFczaCdarfZC9\nq/vB5WObdnrA3VeqfsaVi9IlKP5hXc1I1vuzYHbyN3muZ99mi5x1v9VOxJ/1\neT6stbCsmdxKXOsYYn/Ti3SHUjJiMtrpTeoNl47rRrPJ6pTFKZZdwToUKGV5\nx80Wi3gIGfTxQotmlD5m3iXAY/vDjPOiEp8LbTq3uZ0TcOdc6Kz/2vqj8V8F\nw6Kohhw9EkmorW1JoHBCJBnE+DUrz4APUxM/vpq8b6Mfl32qiFgV9HRqnonf\nPJXb\r\n=kjIy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.9.2": {"name": "@babel/parser", "version": "7.9.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6", "@babel/helper-validator-identifier": "^7.9.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "4e767f424b479c514077544484d1f9bdba7f1158", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.9.2.tgz", "fileCount": 39, "integrity": "sha512-2jyvKdoOS1aWAFL2rjJZmamyDDkPCx/AAz4/Wh1Dfxvw8qqnOvek/ZlHQ2noO/o8JpnXa/WiUUFOv48meBKkpA==", "signatures": [{"sig": "MEYCIQCdTiEOqGTHigZfTyFZLkje7P+bCqzjHQAj/SwiO+u61QIhAKOZ1kk6osQ9V8Fq7VGOqeJ2/W1zUij34SnOLAPPskde", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1845137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJediE8CRA9TVsSAnZWagAA07UQAIaXenuAIIjAnZrUJ3lR\nvU4wUcHD+Jko3bNbB12iuNivkv66Kh4tU4e5czt8BLWTHKnUgTkhBTOXlPPs\nTSmhEuaG/vCjmd7i8zt4ygRGjHpYXSLZuknNxZ5qVapwS0cpT4cn4mY5HP8y\nKTLJzkS03CojlVKW2pdjo++1wqVqhWkLkb8uhfYYWI8DozwMvu/PZMQZLcs/\nk6JM5FYH0cYKu+n6/bYPe6oLncdDO5AS0o89DfR/G0sByuaypEY7XqRpUiWg\nzufjwsdtrIQsmwopC3y6GIGgDfw3yKkMQIG6981JcgaBXsevDkl/B0e9UjVA\nqSUVExQjwbc6XjyjOyzeyo4EVwxv4waV5WGbLnDFy40sDKYP/+e20MoSInTy\ndWCbpWtbhEjPXCkked9MqNCxHdBQLilKhNAmxCSE3CS5wtjguZgRN9GqZdbE\n4G76ZcIudtayosPA8B0Jr3ywQ1WGtnJQxZYPipYyxVgkwNmgLpTuqProWQq9\naCIMhKVjyurIeGsHq4RqVV0E6ITRQPgG3t5kATnD0pOz5+WAGV9C4yNOyLsR\nXOQ1JevlRauFTZL9YcRJOeZmKr8PFfXJIGZ70tf2SWdmT91zJ995rrqayTpV\nkvlr2R9WHXkzVUZ8GoUKtXbuKVsjYGm+OblSvsJk1cVl47c+rsWzujYLHEEE\nz5y+\r\n=iMN2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.9.3": {"name": "@babel/parser", "version": "7.9.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6", "@babel/helper-validator-identifier": "^7.9.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "043a5fc2ad8b7ea9facddc4e802a1f0f25da7255", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.9.3.tgz", "fileCount": 8, "integrity": "sha512-E6SpIDJZ0cZAKoCNk+qSDd0ChfTnpiJN9FfNf3RZ20dzwA2vL2oq5IX1XTVT+4vDmRlta2nGk5HGMMskJAR+4A==", "signatures": [{"sig": "MEYCIQDRcMpHjPEdGnfXX+q0X3iLBjiqjcmXTD0crTBlXsX7BgIhAKurdexRWMCvcVGLLXK3Lpihd0RoCALNL/wXShA3A3G6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1434835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJed0XdCRA9TVsSAnZWagAA/9UP/iZtRFGwQjsECHKVoh9K\nANJIuz3U5IzP8jwGxGjjLCXssSlwpzR7y4O31B1Iit/oPFxq1ia2PBxGDr+y\ngOwkJsSXvy0X6b1ndgK0ICZX1zsMh560mx1L8UL2t8+3pVkfoiXR9sKACRvI\nCdoWvIHpjobKFdcIGe7vj/r9NWmZB5zeSWxdzIN8BlGVZV8kGpiKRmVZmBvU\nrn+jNjbsqb1pqAOxNc57+gLQAKMBTYXwbjesFNBmrsVo+lb7jc+1mctFs91n\nulJ3qML7m3PJuVOatMqeRDrTj6MKz4FFOANWLl44Cb1HVUKcAx+ePQOAJ5x1\nnGsj61cW8ErxW4CjXv11MUwpCMxPs+/tuxvEIp3PNSJaPCmjx5+JwRzHBQ/y\nLqrxbnxYRpm0/lR40foRDEXlROeEkvczkKave/5IrnvvUla6ouzEXCG9Vl3W\nsUEmhy7hh86iLQXzPigXVE/kLgK2n3jscWIJuk2ai89zRsQQcVeiZ4h7KsfE\n3UNSn60pp8ChVs+pBpWeBXJHvmsXDm5A8sBXw8m19wh+IgMZQx9g9IJ0UVMo\ngyR4K+0BO8BjvMGMhzqagpJF9AS5+LBo8sUF70XGSVhQFTj0H3pDiu9cVa7K\ncDyQGjp40YE1BKOj3mjvUo7NV5mp+t9IVrH6ZcVJEm5cnWv12F9dH0b53qUy\nN7U/\r\n=8HF5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.9.4": {"name": "@babel/parser", "version": "7.9.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6", "@babel/helper-validator-identifier": "^7.9.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "68a35e6b0319bbc014465be43828300113f2f2e8", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.9.4.tgz", "fileCount": 8, "integrity": "sha512-bC49otXX6N0/VYhgOMh4gnP26E9xnDZK3TmbNpxYzzz9BQLBosQwfyOe9/cXUU3txYhTzLCbcqd5c8y/OmCjHA==", "signatures": [{"sig": "MEYCIQCS7gIrSWIYiYNEAw1dNNv6l1dN1kNGhvkrozHrD1vXqwIhAIgKUHRohiw0ZpP0nmz72OEqjcqLcjzltu+2yrCvaC9C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1435324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeecVbCRA9TVsSAnZWagAAF9YP+gIWWbhxSRiEgsidFRJ2\nxDYkXk/VTHpAUR3eLpwdys07wHW7NK3eYdA0RCEAjMopa6A3MKiGrt3v9HEN\n298yGyYluX9erbzWtoTyeuldXLJcXbX5QsvZMaOzn8zI5TGuNhpEUlyBtlvM\nIQH4pnMs7xY9CjVpywLevdBhBiCSjyB/bily1BA6Hq9sQUAYOvla7fgaOgUO\nfJo1Pd3fZffAHB5BrxBojD/8R66FzSCQxTX77Di9Ts89t766bDCOK0DxEQII\ncjhJKWrVrBDrIbrtBp/VRP5a+53vRyUG+HI2TsMBR9GCKWC9lV9F2pogn1fV\nBSnx5ehNcql2HrqejGBYrLxICugPGzrg+opurQCo8kyVKb2A8hgAYHJAVPjq\nRA/TpI3lXCv1jPQMPOJWB0j5r0eio2z9gH3aor61Xhuwcj6Bwa15v2kyQ9/F\nZK7A/RqL5lE/tVcLolbNfJ9h7RS1Ep1Lc0492KjXirWlukvTAqdCbWEYISRN\n1aOy43yVuU/Mu6zYnLxiWCcqKMxQwQ/s/AAGz4ObBIYfxkXoHGPRgvWvxqTA\nmOnu0U9y788oeGjQGhFausttoCjP0vdTGlq83BshtrmirIqSM+0lSoxPOklF\nFR17OApfpRDMPAnsDneBoSaEdZ6OwXoGqWRa8TByAu+oF0bWlEqm/rz35nQl\nhE3y\r\n=mhns\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.9.6": {"name": "@babel/parser", "version": "7.9.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6", "@babel/helper-validator-identifier": "^7.9.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "3b1bbb30dabe600cd72db58720998376ff653bc7", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.9.6.tgz", "fileCount": 8, "integrity": "sha512-AoeIEJn8vt+d/6+PXDRPaksYhnlbMIiejioBZvvMQsOjW/JYK6k/0dKnvvP3EhK5GfMBWDPtrxRtegWdAcdq9Q==", "signatures": [{"sig": "MEQCIDT4cuya0FIF4EB+rO6KgjibM3olQVs6iw6HZ4G6XnD8AiAQcaYvdCrgqGMKR4RETpP5eBkTvtWnhCM0i1s4WftwuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1438793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmNCRA9TVsSAnZWagAAORUP/1TeYpEdRtr2RtIIPBG4\n/g1bGwTscRLj8IK8opBts7No8r0/L6v/MflzvIxS30rZ2jK/WKZLngFnfx8k\nWRkciXA3qHUa0VqKmsTUr4tQGDOvfFZUYHon1vQI2wWW8/s3XQ5oOWLFncZq\ndMTkcvdyBDiOSFQjF4Fedp8hsT24eQO5kw3TYXoHwJ7Om22eKhMWwxZQaRia\nuL2HVbz7qq1zP+szpZJI/FjHZxLeXa1ukZ3p0chU6rTBoDA/lB4sljAguwTH\nCkarUj8QEq74Fz+xm5+SiPA0wb5Qp+3FR1lxmfl+turMtCZ7dv6MEDru5w8L\nnEY9HAL+jPEmsCXNC6hbg5JVba9nBcjv0S6AaezfSrE1VAaJvNhOR0aPjupf\nrygaGyTZ9hxQbrlmrKGRWBZQyUoIfbCFK6u3qJ3POWf4zZWhC4uCDHrFCTaS\nDZ1uxM7y7RVQfUNEKj8peKtE87ZRqaKm3pgmHTH0T1RuIDkKG/aYo1+brK2T\nPQ3YS3nQ8BEjIFoKr1sDUYBHrHbsFU1fqj9WyFgaGuHd2ql730OWr013UZm/\nD2JySs27Fxy6gOygZ9kRVR0PvthSc1Td5Giy6+ZDGSU729hhl83khPSpP6vE\nOPr+Mnypt34e9HhYFT1tOFc+QoomQFqzGepB4rpI62++VRKcdM1ftuo0OVjU\neaUl\r\n=McMF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.10.0": {"name": "@babel/parser", "version": "7.10.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.8.3", "@babel/helper-fixtures": "^7.8.6", "@babel/helper-validator-identifier": "^7.9.0"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "8eca3e71a73dd562c5222376b08253436bb4995b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.10.0.tgz", "fileCount": 8, "integrity": "sha512-fnDUl1Uy2gThM4IFVW4ISNHqr3cJrCsRkSCasFgx0XDO9JcttDS5ytyBc4Cu4X1+fjoo3IVvFbRD6TeFlHJlEQ==", "signatures": [{"sig": "MEUCIQCIXQhJ9ju3PvFIAZZkGJMS7rb1VrFsocKx5yw3GQ2GIQIgLpahTDu+NdhtchhYKfpE/AumZFP3Pop9oVb6A+lTKQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1398085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY16CRA9TVsSAnZWagAAz7wP/Rux9prGFhwpePTC+0+c\niiJ6PijleO84QgbXsLGQJX1dXL6/nAldi5B8mDuIlXeKFF1dYDuRsezspUxL\nVLadcwtCsA0FVFp9ZQwj1jlZqARNje2BXDrxFscQuIdIhTr/SVgYXhduyrtb\n2JSPM0EVkl2SQ9nQy5+LdrO2gyXNxjTFNPnUH0Y5zfuvq+N+gLu4P2amyo8B\nn0g6SiBURy39iyxIBFAE/9AevF0ByYqVMgX5bRMP4Grwu0AT3GJ/YA/IDo3/\nLJ2/1ZlFwCuwYu2rFXCtoR6FVvTo5nGqJy6jL4P2Uh2xfa9TA14xl/k7xF1o\nQC/6VoNvVk5en5hx53lqr3Ztrezz1ZsvqqYNTd7jA18xYAxMz/JjHMY5B4JX\n6mKHaLD9XCeKGxsSnecKFpzKF56QA372Bkq1Hlz+2Tw2pekRomWONXT9Iqfj\n7EgcpOG2T1P455+IT2fMO9q24vqmP8Kxpc2WNRJBg2+JhsMElW4rW9LE6bQj\nLTQDGtABN4pkx68FodxUKNrg5dIbij2edExs8sNWJ7k4ognDCyN+a03BDNmn\nxedIXt3UYg+93CkWdmzHoPOvW7Pr4pYwOsJmtlV7zLCzooWUrLmIoEQBhxI4\nLaP40Q/Cpl/XkNrfDGIm99uhdtUDk2hDd0LsPsXc4tO+MbyVenk5192b5arf\nG8y2\r\n=B2u4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.10.1": {"name": "@babel/parser", "version": "7.10.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.1", "@babel/helper-fixtures": "^7.10.1", "@babel/helper-validator-identifier": "^7.10.1"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "2e142c27ca58aa2c7b119d09269b702c8bbad28c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.10.1.tgz", "fileCount": 8, "integrity": "sha512-AUTksaz3FqugBkbTZ1i+lDLG5qy8hIzCaAxEtttU6C0BtZZU9pkNZtWSVAht4EW9kl46YBiyTGMp9xTTGqViNg==", "signatures": [{"sig": "MEYCIQCqTmiMBsfDMNNpgLbvqaoUvBCsp5HJh1xeqG3T/b6G0wIhAL0PuycTe3KUw6KfEBke6YnG4hXnvmaUUmsnrwNK/W3y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1398136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuR/CRA9TVsSAnZWagAA8yoP/1cXtTNi1bBwMkAjnphg\nH1jQfKahZV6g8LCckjwIzT34pnRIqCVORXmce9MoBwhW08ALHjb7CYIZBT/2\nqmRhjXBE9kN6joEsaaSlIKcmCdc/cjDbCnr9RmnqO01t69rO5hrxalAX4Vx6\nxiVlw6s+5Y4Tv4BkkPHK3GskS4ifGMMvCiZqUcuvDrWCErzgwSxT1+uKgRBb\nC+QOjpuylYfcHCBN3Bk+g3fTqmcTsE4J621exYA0BNy7MaJ1NHgC8bEPpMQA\ntBzr+4ajSMyvInyxFqyrPkSaNW6xkLoFbzsyNOH00z0z8D+xZAsKsRHyN5vK\njVqE+4WeT5Fuo8oNkuYADvbiMmqNuQMC/WxiV1r4rmKtUJSbTkKVYJumuS+w\negh1RTeQKlKxKEC1iMEvA/pknNOuikGOnltvWnmDUxLy+TExD8m1vxfuFN0P\nEBhQtmkr69djsfsOV24vtrJGljhkjeAmAyQ+xH1GO/mH5Lc7BMPcFZB5r3o6\nftayj6cXkh6Boz6kz9RfMpYrE+mxYpUFABghqDvXFysDAWnzzTvyJQqQRv+n\n58Fn///LFoFFDFVUF+KAoexGMW/dGoIpotno6bV5iHDnU/gBa2sm6rYcBhhD\nRuZLoShGuka9jIei3sA0Xio5Y0NfuOqvfCdWhoe/GT31N9OBTbqF21Pg1CGu\nk7If\r\n=6JM/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.10.2": {"name": "@babel/parser", "version": "7.10.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.1", "@babel/helper-fixtures": "^7.10.1", "@babel/helper-validator-identifier": "^7.10.1"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "871807f10442b92ff97e4783b9b54f6a0ca812d0", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.10.2.tgz", "fileCount": 8, "integrity": "sha512-PApSXlNMJyB4JiGVhCOlzKIif+TKFTvu0aQAhnTvfP/z3vVSN6ZypH5bfUNwFXXjRQtUEBNFd2PtmCmG2Py3qQ==", "signatures": [{"sig": "MEYCIQCpW3hbOpwJ3I79jcZtC/TY+rIqntC2gny5OiYArDqQiwIhAOs5wVGFGSTfZROQz/ooTnNSQTaIh+0fFhRJAvKEXusY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1399406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0rMXCRA9TVsSAnZWagAAc9AP/A4hTcJgROTW3ncges4z\n4EWImTXMSsYRqDT2+66yf83lh7kARhFyG5IouyquGFs8yD+aPKpRprlbJBon\nLDhB4HpoH4Rv5mF6T7miFA/M73kdnl4AyUslIjSP6JyEqGKs2R1u+3kv7mfH\niQMtRTQN45/xfvbdVMBgrQdLiSQgr0qyqKhykDYNYTC6tm2yTOKhtuvROmuu\nAKCYeyXMtUgmic/inxylPKYUvEU6DQQi9IVWO8aupp6RTmq+KzMDQa8AEWLg\ni6J30kQ8F+fzY1zZGzUVS2ueA3gncRlfMEj+lqpgfBRZ3ypFR3jCpU95wrMa\nIqRZEn35bqth4w+SXygkql76FE3GYp4ruZFiPDhHcPQp4LV0xdCTP7Q1GK5O\nxyenF7FeSq24gDdCCGhwHCUZopl2pnH+iXIvQ0Pn2JBzoD1dwgnE/XYI+oHH\niojKEE6GRu3QlRUjPixkLv7w+FRGsBdbwTW0DV5d6mp1HrKhyR9gMurmSHp4\nd2xq9C+kKCl95w0WC1wJF62va1qWPnjG6J/nWDZuP2Ps6WrlsrgOfnvtehxc\nefVP3d1B8YJmY77+8HLC8jVEKaRkGaRc/aalCBwrX2yO6CcsGPtAyvnIdh7O\nbdQRzsyrVuyKo3bk+nIYnADZTeBIbTp+lpw/zAFduCjGwWxGnzmqDy5P06Zy\n0wzd\r\n=BcoU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.10.3": {"name": "@babel/parser", "version": "7.10.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.3", "@babel/helper-fixtures": "^7.10.1", "@babel/helper-validator-identifier": "^7.10.3"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "7e71d892b0d6e7d04a1af4c3c79d72c1f10f5315", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.10.3.tgz", "fileCount": 8, "integrity": "sha512-oJtNJCMFdIMwXGmx+KxuaD7i3b8uS7TTFYW/FNG2BT8m+fmGHoiPYoH0Pe3gya07WuFmM5FCDIr1x0irkD/hyA==", "signatures": [{"sig": "MEYCIQC2rz7o9o8tkAg9ZUIKqVZoMV6i1lgRcMXuKDxm3iJSfAIhAKvejXfjDbhbfiVQlvcqNgD0EEBjdt7kFUS7yw2DP14d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1405459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SXwCRA9TVsSAnZWagAAfMAQAJ3uIQaAC6AK407LgfAz\nFTd3dZZExoo0LhxP8i+xN5Sc2go/I5YoSjQe3fC2xDoAKvOwbYDE9KbTKJDn\nI7rJDoMoZRWC6fJ40NKcDMLtXx2JBd4jYdXZZeLEBPDO7wdP6u6AJM3vuFRd\nvKtLnHkhfBxhDW5Or5UMlGnTdMtNiQZFYmUPFW30/g++URAjjIjVZErU93kW\nTaPgaUViiGegkhOUHxOyv39Pr/oH/irh/H9nnR8xGCo1hHrJ8I0TYRskR9k2\nrol3bvqmgHKXFq0hnPxj9vLAMhbLLM4QeyGjaZE+7pJmpoeRt4DUrLyJNfGV\nCwQOlOGAsRtUQ9UC/2z48evH3vJO5Ej+IpwtDo2YcD/NSeUp6zR+/QaEHLj0\nVv3lnTHQslY6XiEdkhcftkCwLV6F+yf9QYxEqXwj5mURF23iWzXtU8LXqtQr\nEWmyevM0PgJ//NtCJj9dgcIFvqceEkwheZxiDud3k5Xczd1bMWaBPS94JWfA\neNBthNyzU5LGMar3edhzTCl0ctveddXEezS+iexCtNbZ+Xe4vKwQX65FzmcQ\nVpZCxH79zIrtrW4Q2cY+yYiDWfaYevQM+OxJWA6AeYaPFFvs+nZh2dDcWg/g\ns92swk3+RV+0pnrd7EFGkeoz+UcKbxRH0u+3zkHKYfvXW8suZtLMg07teC7c\n7/Xc\r\n=LmWh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.10.4": {"name": "@babel/parser", "version": "7.10.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.4", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "9eedf27e1998d87739fb5028a5120557c06a1a64", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.10.4.tgz", "fileCount": 8, "integrity": "sha512-8jHII4hf+YVDsskTF6WuMB3X4Eh+PsUkC2ljq22so5rHvH+T8BzyL94VOdyFLNR8tBSVXOTbNHOKpR4TfRxVtA==", "signatures": [{"sig": "MEUCICu0DE04nH4o9bBVb+kQj9sNOsuPqW8+CNiprsRYrDtgAiEAmzv0Xc8KnZZMV5/CYhydh56McUbQ2XGKc7QGFp3vN5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1407877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zn6CRA9TVsSAnZWagAAfR4P/jdlZEyFksGeG3pVLB+T\nEtDygUHsrE8FLKq2FRjw0/MJ3583N0qYBOG330OCG/vtvTtq3kANveqd+zDp\nQMQQ64HQYIMW8af3KQaR4G1WJAX6RnU5m0rRvl3PQqb4mHhH9YSkLjxannut\nsSzxDemRIWfolhdKM0NaZXGAviCtW2fMKFXYIl/YCWtjBxVKCZT0X5thvQ/y\nLu+h+wV3uyfc7G8sp1fCVM5AhFEkhSjFILFMwyRIHzfiP3lqMIZEg/zU+Fcg\nHL6FNI04QvhqA2OsDGmqUF0QNYRsIRMeCEY8AgxEUqF0sTc+U0H31EeCuavd\n7UJbn5wlybJjzrJShnNrJpcTIUFJ5yVi+xTG0Gx/M2JsTgAcif31+CqI3/Fo\n+H1DVIkMHvwE9t7b+Tq+477clps0gyMnW92aXJbUqMke6/qbmVorTtO4YCqQ\nNn31gOxc2Bgv5l9uNyf9iiXL7RDOAbzBBltVr4K4r5ki5DGViDW2XcC+dazi\n+HcKl+92AlDWfsfsUZ1JuqRpD9w6BgOy90VX02vNVhWBbR8pYaeTwD5wofvq\nvcOMtlQRTbkVBokHFOu4MX63zixh5ajxoMIT7q8KS/GEOhDRmAqjXna4+uII\n6Q8+h4Zh4zXirNRiq/04xU/xJyaHI6o/bLbd3co/Ore4C7EGF3+EC0V36CLD\n3g8w\r\n=Gcqf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.10.5": {"name": "@babel/parser", "version": "7.10.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "e7c6bf5a7deff957cec9f04b551e2762909d826b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.10.5.tgz", "fileCount": 8, "integrity": "sha512-wfryxy4bE1UivvQKSQDU4/X6dr+i8bctjUjj8Zyt3DQy7NtPizJXT8M52nqpNKL+nq2PW8lxk4ZqLj0fD4B4hQ==", "signatures": [{"sig": "MEUCIGDxueWnLg5Cbg75rm3hBWM26veUbKZns/vElyKLxMQgAiEA0pbbyww42FhWR29Ta9rtBIJWH20yyrf5on8jRbigm9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1408584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbGCRA9TVsSAnZWagAAKfoP/2CBUlb5uMkYnIbzIam6\nYWiZm4W14Ej4/1mny1SAmTYHonG977sUgbfuCaizWbV3VwwKDLjoyKYujVyX\nxJCpVoypoVu2+yMD6nyd1XtDvckerqaHz+jD59/oFVSk/sDclGdV60VwWlhv\nnboSqtrVyiwfz9YKyiwD/fS0KVd8175D7o05Tg58FKOFcitltAtE5RSwBt70\nR61W54zT6pAh7bgGjKAEoRGGDfjmZZb17PLmWqXvVjJDLM3xwekFF+8hpAAF\n1Hiar3t94H/0Ug51KbHXbV/x1aPSVcb2yU5Cv3VZhijJ507TITpxEtCkvMhp\nxiScQyaw1I5T0hY2/YrUm3hRTuHwzDIhKSW73Bf9Rp17BAzet/3nPdtQprax\n1EwtxUDIRKHmpFZwfqcQhpwX/PknayALB2vn9dF+YeMgN7qHX0WbR4z2laxi\neG9eXytblCckKa+UdOxvn4HhrLgI2lhLaF7aszSS8fcQlEcFGTpfyjbovesB\n4AM+7N3c+xv+9Xr8bJpFJDgyLl9iX7OBfBBMXri4btzHueLL7y31p+XdLhPM\nMy3EDREEC1aUyJbfOg5us33rmlf+s8LCsfkwQXEJ8s87nkXrMQY2ICwDfcDd\nDGk+GgGk6aBziXMZuiNf3hsVfajE6IukWKgoxsg3A5lfCyO10arAWGQEmZ7j\n64Km\r\n=IuJK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.11.0": {"name": "@babel/parser", "version": "7.11.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "a9d7e11aead25d3b422d17b2c6502c8dddef6a5d", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.11.0.tgz", "fileCount": 8, "integrity": "sha512-qvRvi4oI8xii8NllyEc4MDJjuZiNaRzyb7Y7lup1NqJV8TZHF4O27CcP+72WPn/k1zkgJ6WJfnIbk4jTsVAZHw==", "signatures": [{"sig": "MEUCICBjZ9RgXCMTbWiyGkbRkN/IG7KllYfrermIQHxAeS/yAiEAnuE20UHKJpDPwYOAZDXZZkJTixVw0Ou3ccaNWRKqrHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1417718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIzQMCRA9TVsSAnZWagAAgXIP/1U7wNTJYEZSdk7erxJv\nTbCIZ1eK18Ko+btHGeKoOdNLE/AlhmmtmOPcCMhde4bVk2r2eUtFLJUPu9nv\nq6X26ZOEEWWn8BvY3D6HxEb6tLkZ1hbvQUnu/8xR6r76OfdIfyiZboFC+7Ux\nw+fQlTp87UekYcF/neJnkL76zX/W+xdSNxH4zqVs5YKEGASHnltN4SvBIBRb\n0bFf2m3bHNUzI0wn9YleccGTlOUXmvF4bq+LI32U6Cxf1qUm8nfp19cscsvI\neEwth512/iSB/LcR1Qeg3YHw+r3SY8gXTjtNJiiZE8/D+vC+xiYTeeFgmzlN\ne5LEcReTLFqOHJ/WS7VJn9XX/GJ8SaXv4Xjxu0MJVsPU/ER2f4ZulAyrfjmv\nadRP2JXOAapKCWhKS+8D70yg7megey52vmZiaJuXyzZl4Or2VWlzJXPmGsqW\nuTZcpmeYgeMatUPASDAM8on3Ez6/7kkenip2rpqwLqOOJ81I9p2wx/h59N6K\nA3Da9RcobUPuHq95GPqBG7hQ0GtpCExMeATRuOZKSDlZrtyUt3ShtDBE4Jpw\nL9Yss4NJ5CwE9+TraZGJ1raG7OWYPEgp009F1RyHop0/YTkKhWGGFp8ziJgH\nQgPt2ZmSgGVNksKWzbLvgAKNH85yBYqJzIV/+0zxJd5WPOFiAIq2oqP9CA8U\nvBWl\r\n=RaCe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.11.1": {"name": "@babel/parser", "version": "7.11.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "d91a387990b21e5d20047b336bb19b0553f02ff5", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.11.1.tgz", "fileCount": 40, "integrity": "sha512-u9QMIRdKVF7hfEkb3nu2LgZDIzCQPv+yHD9Eg6ruoJLjkrQ9fFz4IBSlF/9XwoNri9+2F1IY+dYuOfZrXq8t3w==", "signatures": [{"sig": "MEUCIC62m0duktzAAVSdrcacmH6k8uA0nuRuGDtxLRcOHvZ/AiEAwAL7xEzbkSOrbMjrUaeqlhJs+o8wOGDHqr533EPbylw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1845074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKdu7CRA9TVsSAnZWagAAr1EP+welDaFmNHbCFEKg/wff\nju90f6Ucy2YTQHuII5RmJ8g8mvjEpxzwTkf4BwN1tfCSw2p7XVoBgZlyEEOW\n4xSEj1KlFrGL4Ecm/G5FB5K2qcr0Bm9V/CLvQlEt0eBo0q6dlqKZx+Mh21c+\n6olk0f9Sq+LvMptw87XcJ7nEyiamF8ITyJe22LvfUn3uAQJ1cK3mlt/G1CTV\nuVBtJl08yMXuhpNk6IhxFWIqjjjdU5uMLNEAakG0vlvb8EEWSBL6Ja1RH21K\nX68gqliNFvJ4+YuBfjPuIrG2TKwuiNHTGLFLV8YB6Pv5THVflQ0jT9Dne5aB\ngfCjJ7SAhhMWO75r1/jqpN8O+w/ywSSoV6fKGaojbfxScCb78fD0ToxkKNgM\nxPqMYlcTJSus3qeuwtVQ/KBj8BAvAeIK16fg0ZOIlvwIMv2FfX33bi+1mflW\naDwvH/ZvS4MxWHFKx23R4YfR/0QWU5kFd/dyQWK5ACo2+LpiKeVnUj4cii48\nqzcKAkVAwWqhtCRBaRJaZWQZq0j4lN1Y2CC5KUthNkboVBknGhzIuJcvhbxg\nwUG/9mzAlK+Csvvx/iNbgzXuf3YZeKKlR+6QzFgwkWPr1xVjeDXZH3AiV11o\nsXR7Lq+F0XztJeb0qu0bCi3mYFuy3MSdw49+4fmAq61Djdy4PEGZG5HUrDec\nZShd\r\n=P0C9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.11.2": {"name": "@babel/parser", "version": "7.11.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "0882ab8a455df3065ea2dcb4c753b2460a24bead", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.11.2.tgz", "fileCount": 40, "integrity": "sha512-Vuj/+7vLo6l1Vi7uuO+1ngCDNeVmNbTngcJFKCR/oEtz8tKz0CJxZEGmPt9KcIloZhOZ3Zit6xbpXT2MDlS9Vw==", "signatures": [{"sig": "MEYCIQCCRae2+sKcYhWcunJ845wbtsLLSqz94VC3wG3J/Ux53AIhAOll/kLg4K/P6BC2w6M6e++SH82PcG78LJGcxGxE2cwb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1845103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKsIOCRA9TVsSAnZWagAAYmEP/1jFDm1sHSx+LdQJGdjW\n9FFCwjzPyb93VlG75cPF6Is8rrFvWLH6pLiYiROkcPOYLQLecCL/5mD4lPod\nGc5yWQ23fiVnPWmcFQKPeKejmHtlDsIeVE6qs+JOz+P27E65B3JHN+FhIP8K\ndbAf5BnT8+m40iLSy6JbCzpknQviDnfbmOmOCqZD7blJN4ESecQyT1tVaN/h\nSyS/TxAQ4oEmU7SJwK5Vr3bcAbB5c6aIcF6R9xfd1dBMylAiN/CFoB2xnzCL\nDvF4DH4vWC9ApRVvZOwv0sKbyIJJ+Gtqnrul6SzOHi5bPkDiG0u9Mcd9+d+1\nEV/ShIO65PLR53R9RCpQgDUPyDVn92DKzmmQvo+X1+w56TIqW1LoBT76DZO/\n37Xo/rMR9a8sPUjtRJ17yTB/h4Hfv1vsoMwsKZthCa81Kk6dIZ4dc0m9onqC\nd99YBxQhiEFbt6/KBQP3cyLu/87rh124S7ijSGeU/galoRdavIoDcjYa45vU\nAbZ0BiVimsOU/cY4YRA2pQ9NWOUr+PQi3WiJ9CZBol0lT6Jorx9/LCxxDaiB\nc0EroPiqkqpztslsQAatJ5BT1eh0LvZp/10cm7lyWxD2sZFJfxlM9Iiya9d0\nF7U/TrpqnJuA/Iiw5W54utC5COgDOWbJS13wdrd7xIBJjuCFyAGh8YQeSHJL\nQx5O\r\n=DpTN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.11.3": {"name": "@babel/parser", "version": "7.11.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "9e1eae46738bcd08e23e867bab43e7b95299a8f9", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.11.3.tgz", "fileCount": 8, "integrity": "sha512-REo8xv7+sDxkKvoxEywIdsNFiZLybwdI7hcT5uEPyQrSMB4YQ973BfC9OOrD/81MaIjh6UxdulIQXkjmiH3PcA==", "signatures": [{"sig": "MEUCIQDOCUe7XTAnaoCeQSzNsKer4inPD4H7M/j1S+vKU4GPVwIgAIqN9c9izIP5u12Dg0Pg8TR6NVR6/6/gP8xMko+Ql5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1423216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLwYHCRA9TVsSAnZWagAAqFMP/ivE/kWjcjYNo3QWmIFR\n9UfwCETTZBx/4VDTs6I3NlMvWO7Wyqva88PaPjfwpo/ktR35baSYhXkkVDuX\nuxq+B+hJxHm/CYxjf9n7a2H9jl8eCcfRw1h3xssVveBXXNVoNUuYTX1FZOt1\nIeS2iR3YclIlXgn6Ue+DkHg0U4Uh/UCp7OubhbLl32Ke2qMNlI5tXqHTPtv7\n/ur4jNR8EA2nCEssYRiTOaSSazAMLXwXoaIi+cVTgN+m4WpDv9m710vIndIP\nPt/8ggeSv0A5PuSZEPhP1T/2MnT/rO5rd2GOyGaR6vamP7nVKmYJMwHUpmw5\nhii3bIYaN8U5NGGcN8Cm7wYY2feKaHdvI+Y6O9frQg7DYkZgEVPm0Y62DL8Q\nKUM50N28G+oeQMjqGC+lkh0g+Xv1Yz5nriTTSPmxPB11EIEnHove0OnvyVAf\ngobNyVDJXXnQ1YF6XaiT1LFiMi1vXoNC5TbKsHdRnwPsxywmFx3H/7i81JVg\n+mgrpwmA3odTcKUmGqaBzIXG89piTGMqbS2o5NxTaMZmNt0P2Bs040qeu4Jt\n//am7o+nSuBRYhWdwCLEcQrflRVS4MaW/uZFL4yCTnGWOwsq0gaJC4A05xCx\nDEDuOf2V2raoQAR24/Qwc+JwDTJ041EDs/E4czgFr5e92JaqSfZ4j6kjM3LJ\n7VMT\r\n=VKP4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.11.4": {"name": "@babel/parser", "version": "7.11.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "6fa1a118b8b0d80d0267b719213dc947e88cc0ca", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.11.4.tgz", "fileCount": 8, "integrity": "sha512-MggwidiH+E9j5Sh8pbrX5sJvMcsqS5o+7iB42M9/k0CD63MjYbdP4nhSh7uB5wnv2/RVzTZFTxzF/kIa5mrCqA==", "signatures": [{"sig": "MEYCIQDyBYcd6+qrSVe9NFBhDxCHCdlH4Bdiq85tpj3Eo6oNHwIhAOB6atsJsf3vCn5n4usBcEBOITTdu8nmDzmYGS0W6C5p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1427066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPsgkCRA9TVsSAnZWagAArAYQAJ6GjnUqNJGJU3fnOkng\nzNFBCy4hTjVHhAy83IrGhlVn8qDqSx21FRWWmYPX5U098rkmS8q3kIvUB5Da\nrgWhZmvNmISlpbtqYCeMw96UvG6KQzHEWWq1Eed7701OqRWKTuoqFBVCRYnT\nYIzEvLX8ESNCAL9wBpLFwbxllh2ZJL2rK7o/pFTMYNltF+Gnb760VZCMD1f2\no9BbZIktSAeREx1gtg7T3MXg9HSvMXc05egLNYakYheFF5DyjjOeBKAYWf+Q\nmV9Lmlh9cmTGIvwOYP9Ij3Xc+6lXWVpDePU/293WkF0HkNs/R7mQapeIYu88\nPuykVOPGQ0KEPjmYr9merUW3C4z9k4fHC/hCX5yEeLekeuwcBb8GDTU+5pQN\n5d5TJqG+eGarxYDW+7BMMjR+Bg5WwrY5eQlmNFlAsyZDkhPPYaHMozUrBCTb\nhe6jqbfxIbOUe/7E5V3QUtRNuJGbypBzY4QGCcE5V1xLLqGjD0AHP0MlBlCj\n5p+dY1ZIkyPAtopFBsDkCL+fdIwNei20+1BqYmITMNAQx4lUGI9gWdvt+T7T\n21pgdN2ELLaegqmpM/7HHbvZUlOXAQPFvbWApfmtd9Fc5yWtkdoII5vZvwH5\nEWfx1s3Mh3sk4/2kqTHmjf5DJCn+eZYWAejLyyTEd2enCaNVgQyJJGIxxtgJ\n5g2i\r\n=pbtk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.11.5": {"name": "@babel/parser", "version": "7.11.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": {"parser": "./bin/babel-parser.js"}, "dist": {"shasum": "c7ff6303df71080ec7a4f5b8c003c58f1cf51037", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.11.5.tgz", "fileCount": 8, "integrity": "sha512-X9rD8qqm695vgmeaQ4fvz/o3+Wk4ZzQvSHkDBgpYKxpD4qTAUm88ZKtHkVqIOsYFFbIQ6wQYhC6q7pjqVK0E0Q==", "signatures": [{"sig": "MEQCIDoM2hUYNDIRTZBerJ7Ayh4f7Ql1ZsNv624M72x+WpW7AiAM88JtBoLBy9sJ13UWpeWaty8hxVcniLPgDNFzEni3dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1480522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTVdGCRA9TVsSAnZWagAACGcP/2lbxOyXY6rVuUn/wCWG\ng7kq1QfwegUD4DcUXb6Vn6lE7g5JYUXgAXVdrRMxIROiNXhTUVSECaCtRW2E\nURFAFibgMTFbA73br5fWwedQ/3/eML3Bj8EG5U+vPTOSBjjRKj6cCkUxqpI4\n7NfiTxnIIyGYd+GFL/0sFnvreW8RQRqyLB9+qNHZlBq8BDDxFatam7simsR6\np7ZLiehcfPxguUKqc4WsurNChnl5P1jXg+OLjdSDjFUPDUYYDW+/g7YHj1tT\nt9RUVjUkEze5FVJvigNwG5+LKRsHuQFExRIg0iRdZyWLeEpYhwB4FnGGBh0R\nBuaVRHN0cbwJY6zQKMT21EZ38biYt2wEKrfLGkTqHFbCajROBmkfEzGlMqOM\nn1p4aH1bVEKhWccPpT00KYPMF+iQ/I3+uJ9JXk6hwi21iqKJEEEN6KDCQj1X\n3ne1EfvdVryYyPmwAi3D0N7IXzJeLD1wwNmas7A0BNEduNKMOCw+Xj+FuilK\nSX5YVJfXsFpyxmyfJjFucGg6TriqRINXb/I45EeAp+DaR3zA0ze4aX5RzlK1\nz3yBhjGo6kqhsR54z5FdNe8dsEDbQFqHLsluwj4bfsfUgQnxTi90qytHe6VO\njlZnky6/PDcZJQbg0kMpS1sDUf2prvsrHm5JhOFL6OzWcHRu83IyjUp8pgM2\nOFfZ\r\n=Hl3Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.0": {"name": "@babel/parser", "version": "7.12.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.10.4", "@babel/helper-fixtures": "^7.10.5", "@babel/helper-validator-identifier": "^7.10.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "2ad388f3960045b22f9b7d4bf85e80b15a1c9e3a", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.0.tgz", "fileCount": 8, "integrity": "sha512-dYmySMYnlus2jwl7JnnajAj11obRStZoW9cG04wh4ZuhozDn11tDUrhHcUZ9iuNHqALAhh60XqNaYXpvuuE/Gg==", "signatures": [{"sig": "MEUCIQDdEAl7SWoL1mvOOdIznIooa/KxSAa0GOvXEsvZDReaFgIgBgiBieUbxVpw7HPPn8JvHZG3aHpXuMDVclnYEc8qJbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1509468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1lgCRA9TVsSAnZWagAABDYP/08xIWMan/Eo7XG3ZV4o\nEMD8bcufWpm5piob0FcURC787hC2eEry9+WuM3FyqGEpiqPK8qvOTqEPuAzg\nlwIWIIpzExW/7RHQmXd2fLr6UPTTE2XCTBhokUR/GRWhLxdU3s3DprmZdRPr\nd0RfEEMfkkJOG2xzNqGVnc4mzvZZJFYJ6+Nwe7Ihw0GrKTOIM/SGqxd8FCvL\nXwLy9cZW4M1upcy/JHfAgcJadaxuB65fS129j2cssuvyIYDjbfqTdXmPMowb\nb1fK3W9p2Z2t9zB8wJLLsoOXu1a6Q44QY/mN69jU+8ZQZZdDKfBumD1JfQRk\nshJ8BF6TzA09u3xRqfIuIePagZ1IsI1G8UrI7+X73xJy+8sqjXkgIfS0/GwK\n1AvLKNnPkWLQuuce3ZWM0H0p6MWJ+RSerSDcJ08m3liGvkLx8rKmf946WjLB\njfLDYLC3UNsfShwhb3Pf1XVzLaSh2223aN43RylN+y90KjiL3Ltli1j3p4m7\nyEtRVXnMjyNgBUfdf6hqNI6eVMlXOcAQrIzDwLe0SGlfdNKqL05L/DjbxXJa\njWweAnzbGpk9ck9gsMAaGqcxCuUbD7A1BLPjUckZ+/IS6wG/dEaw2Y/GL9NJ\nChntpCZfoSysCAvaQWobcLUynwQ5MatIb6MvTyZgo4MDUVqxzGoPsE7UrQTS\nUFOm\r\n=w8zh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.1": {"name": "@babel/parser", "version": "7.12.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.10.4", "@babel/helper-fixtures": "7.10.5", "@babel/helper-validator-identifier": "7.10.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "dc03f543a0ed51396d4081463df66ecb3a2efa53", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.1.tgz", "fileCount": 8, "integrity": "sha512-xjZsx0sBjb6J2+QkoHI69UeD2EWbsyUW0WyZKOoJ9sBrQLxfOApWEefR9dIVOYJVj97VRXnLKLDvnn3dPDNgww==", "signatures": [{"sig": "MEUCIGC8qdFA0gxwS5IYRNQfln/Iy9GMEO8vyOJ8TYn3RC4YAiEAm4yAQJ388wGlP97YlCk+ux1D5hGQZ27T4dMAkCNY3CQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1511816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+ZCRA9TVsSAnZWagAArJUP/0V9k/gVlUWVpWTUoUPD\nZnLWlIt0V94ISMKYxpeVSxsYMXFadEAUI2GgPoBtNy4ReIswXamWHW6J4cXW\nkRDx03heqIYgVD61h52giNxReh6FABBFls6qQCB2412Lbxwk3aIYon6UJzse\nORmbJ23SG0ziNrVVoImzouSKrLkB7U0CYD2bq21UVT1NG2dWXS+TVK0gwIW7\n5CDcSKlC8QCzNhesodV7QZpYJJeala7BXEKwmlwoxGfO4bUWc2Jh3xkOlJJ0\neafewH0a2GCjpe7DaHZauoXZ80oL0hJOXeD9X5CkNKaQHTlKFAYkBcAZqVZs\nZ/WvL4cGCPUXpSuq/zYFmJWI7jMXFagVhQKvUyz09ILnw0+f/TdiZOGtmzMo\nBimbZcM/b/xmTfQO6m1+WbT25vlZQ105kxJ615ytyfLs1FZuA4DyE6SBNV93\nL4RCOlmEPbU/pfxZuHLxWKelBNP5mv8GgbAHV44xGRSfRLnh+EsffZ/pAEa4\n0cHpblzrnnYtR2Y1scR/S2MaqOzSc+jy6+h/i9gKad3m3eYNdOnhHlNfqcS5\nX0XLA7yqlD9TmEo30KRyQLcwHVWB3MGijFhY263A9Y8Owikf5gIMFCyc8utk\nVILjHnwr/7S/nTkoklHA4P1ZNfIHPKmQ/Ru/QX9QNxrWXLWIzLpXVuzUIpWO\nYO4Z\r\n=MGxS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.2": {"name": "@babel/parser", "version": "7.12.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.10.4", "@babel/helper-fixtures": "7.10.5", "@babel/helper-validator-identifier": "7.10.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "9d2fcf24cafe85333ab0aff9f26b81bba356004d", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.2.tgz", "fileCount": 8, "integrity": "sha512-LMN+SqTiZEonUw4hQA0A3zG8DnN0E1F4K107LbDDUnC+0chML1rvWgsHloC9weB4RmZweE0uhFq0eGX7Nr/PBQ==", "signatures": [{"sig": "MEYCIQCZ4JlaMH6//dt2/MUgJQ/tv1by/sRneStSZINn0KsTjQIhAMkwJrtiQVh3jODCuoB+o3XWd5pmhXxhrW2h+0Kd6+Ky", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1509465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiUKaCRA9TVsSAnZWagAASKoQAJlzY45UG4wZo+Qs5+06\noY5h0PXpdw2MwY2Ij7aPnmPPCygiMwaZcstt0+OgcPNNmeQa/9ZiR+RLsuxK\ntULaR7WhxXtHWbOBfOYdML8pHjP5g2jYmvYddZwUHYAeeYZFqGWJEo9cnrJm\nA0CCAd3/YDyI+fYYBRq68uzDh82l5LbESGYHSU465oPOa6C3D7wPSstut/un\nN0F9WEJC7a3Uj0PnkVLeBZhxBCVwvXOt00oRu7EXy7kIYLidB1N7QmzQiPhr\nNIDBmMISMgp5RO7MeMNWiVzSze4Vi2dVkm6zs0ijhM8vDsGxh8OHy39iLcI4\nZ674S1HbR+cyWh1bF+/UzREAryZKEQtLnB2PlL+fwu+0Nz9FHI+Aej1EfQQf\nVo2FNzGhew+XLsx7+7S3MLsSMb3PskmSUfj7CW7RANtuu3BfMmkwdY4a6DWL\nwDkHXUwNSGLHr9cUfzJa1Gas9AqsszdDmIwxgSxhmGAsBxWeQNXVMLrF86fP\n6ItC5K9wfDk0shXvfzmxwSE4KuBdz15i0VJkLwryPpekCJbmLdjY1g5H8s9Q\nOROn1UU6Xw+NronWd4SHz80W9XQqfdNWMeYA72bguUYq1Hc5obcsUuA+b91g\nv0lNAEwjSaAlgmmyJZ06hUYgD5HM/IGuQpkZ3E55jKpLYDtOeUbdwRnMetY4\nNFSs\r\n=OiwC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.3": {"name": "@babel/parser", "version": "7.12.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.10.4", "@babel/helper-fixtures": "7.10.5", "@babel/helper-validator-identifier": "7.10.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "a305415ebe7a6c7023b40b5122a0662d928334cd", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.3.tgz", "fileCount": 8, "integrity": "sha512-kFsOS0IbsuhO5ojF8Hc8z/8vEIOkylVBrjiZUbLTE3XFe0Qi+uu6HjzQixkFaqr0ZPAMZcBVxEwmsnsLPZ2Xsw==", "signatures": [{"sig": "MEUCIQDOFV8zKCyxx453eQaNDvGz4m5Iv0Ufpwe7w+VfdTNBygIgCgMdV5sJ5tTUD0GlXqe652baWXQmVL8CjglW2ZkYchw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1509905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfig0vCRA9TVsSAnZWagAAGgcP/ih5RhU5Po9WGBgdP3gU\nB/3rY72SxGgvBNR8adby26Rqnh/NkEvTynT2zc6CCZE77mAjTw1EfZLwAJeC\nRYaip8Ev6e7kHSWCiYFwZ1BewYKlDGz7M4/JOpwUHYrO8LblkFnNwEiHUZXC\nBbxBas7SFlV1oJRDJMBVP+YOB8LRz6n0A4B49az6mkjiTckIjWaw5X7au0Td\nqsDgmrzQhV6wntexPc19buaAGMuZuS0ujBKk3vUyRWeOt8HvlVK0292kSGMI\ntmu2wBrZYs14Y6/gTfwLH3PFEMTBI5GOBK+Bfw985B4X4pMkWwIZ23hGD67k\nw9n44ORQCPhuMyxQdD7QL29/1cWch6qSRTyLOv0Vuw6X2sT1i+/P+CWHgoaz\nrow3A+bCk9ysjyglmud+xKYEIlZmCyRWyzLIkZpvuPhBwPFtvbhe+vIGcXp1\npQbAumYAg5dJ1lAKcweobtYfCpK+P0bxPSbfIn6y7t4cBFzIK+IA7RYaQM4a\naI/zFhkwmgeQ9FwaeWJv1LZM6in5t8CIt2pwEHrl7wOBEblaKbF+yFQNU4V3\ntTHU3SLMLnaCsjWgayQlSA62lmS3LoAghUvmoaFUbdijzQ4PcAfale1KkIcw\nZL24iCPCbbiFKPircsBL3h0ySkY2UIRO1BPgIYHHRYHFPkHAPRm/jV5AtOcX\nRiX0\r\n=558V\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.5": {"name": "@babel/parser", "version": "7.12.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.10.4", "@babel/helper-fixtures": "7.10.5", "@babel/helper-validator-identifier": "7.10.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "b4af32ddd473c0bfa643bd7ff0728b8e71b81ea0", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.5.tgz", "fileCount": 41, "integrity": "sha512-FVM6RZQ0mn2KCf1VUED7KepYeUWoVShczewOCfm3nzoBybaih51h+sYVVGthW9M6lPByEPTQf+xm27PBdlpwmQ==", "signatures": [{"sig": "MEUCIQD2RgDhVmYybh2IzYJ2jv5tO+pXQxELqhcO7/L2l/HJXwIgITp21OV+gpCa4pqfZ0nLllAhrxdJrXOGZGFQ4jwUjq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1949896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodrlCRA9TVsSAnZWagAAwiEP/3XhcTV2Y9A9lyHD0Jdg\n36NTUKvlOnd6n0QSqmSXM7pFB+7g2k4ZvP/IjOI9Vbzd+5QCmx4bjpWkgy1f\niicvXB9caEAxfzXvI9ONYdIhpbH4A0JylDxFMdlGg5zWnX7WuCYTovDWgcUy\nJ3tzKgQ3U0sFBlD6AnJeyTO3UTYuTnsNY6inFW4bcOs7dPXfiNnF0H+WM1Om\ntKuDxGvqRZ17CIDN9XNyYnN7LXiiK5mlHyCDpgQXZ8nb+2aT4T91zWVaEHaJ\nbSSCARb0cvl1tMxyFqjPR6HZSq3yH8xSM9M0xSSOQRCBB5i+UaQc3EwrnblC\nhOOgjH+ABJkrdtwOp74qlwl72t1rOGMxfbm42PGWJI/d33/UjFM54YyMVPiD\nfdMW83s08AlWsbiU5PKJ9auvVdfYAN0u3Tnoc/nt5GtMuB85vJF5xJ8adIlU\nCX4dJb1JStHAuHRtMF3r7Y7AtrQ/TmouQdqaol6fTGyIhFpQzrbyZWCTthNQ\nGG3cISqC9qaE+oRM2tZ4bwkqVLPUFZfFMIxy1i9JRyU6MoxJxrurcfgILYmJ\nEZW++68FXarZTUI49Hw+QUgmJaa1fmcGvLjOaAWRmgH8IdVGFOSAmUuj2RXp\n5Y1HqYzprjsCod94CTy0VM6cjr4oDAswOirb33SVS4mT848aan6ouz76Z9cq\ncRq2\r\n=8nX4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.7": {"name": "@babel/parser", "version": "7.12.7", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.10.4", "@babel/helper-fixtures": "7.10.5", "@babel/helper-validator-identifier": "7.10.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "fee7b39fe809d0e73e5b25eecaf5780ef3d73056", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.7.tgz", "fileCount": 8, "integrity": "sha512-oWR02Ubp4xTLCAqPRiNIuMVgNO5Aif/xpXtabhzW2HWUD47XJsAB4Zd/Rg30+XeQA3juXigV7hlquOTmwqLiwg==", "signatures": [{"sig": "MEYCIQDvW3wFHQlDaaXbsbNoJ9DlKkXqN4NkiLTRffR6bitAkwIhAIeO5ml8JK4JS9y3n8ynvbGJlkulLvlYbdgehdG3KnXN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1519298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+fCRA9TVsSAnZWagAAmn8P/0jC8hWmIxPaw2snOvH0\nL2TsI7rNqRFRaEjFoH4k+HG+rUbPVCPe1V554hYopvz+wWnkVM6FRHDPH+nr\n5frNa0cxMy1rbpxnlXhmyPyvI+IpwYLtz3vXiev6gSl27xGka0y1SeD0X6en\n4enQEwotxoyPhUA8VBSlmPyD9JE7ujX86hFd4i3/13g0m1F+r615rezxIHsv\n34Q5DKq04b678d3jqY6FvY7ZZsdFXkC8MAwEdWVki96R1oPZ52f0vrPtjRfm\nBhhZx5Ar/0qyosHYKdK2/cna3DBpGxn3BvpBPJBhFRUu/LXyYkqwqY4eXDWO\n7Qor/txNfWkz3XQOIcJGTgbdLYHx8tfzY5CL+QgvDKfH35uSfB6B3LVinv4Y\nsDfRL63mSrEE9cK0Nf/IoURDwm3xMOXMVIp4bqTJEGBSS2yqU5UVJrJbBhG7\nKwp7nq7G+nenfqRT0kncsq5NNkc+lhk0179I2ighBARA4wtgKc/DumKeRYxP\nUZlaMjMCb8vGw9krINiwgJWCxhrc6wx+VKvMUbp/C5n19210qi+akIoGN7rQ\nEhIQl8SzmRkeWKAGQH5J85QechfQ3ZBzOE2s+yFm2oHEsNdLwHpk87kQOdkR\nvhHHVlSRVySLkzOmbmyVdGXhZ/NesrVS3sk7jSekl/VdPkVDfGsR/2sPkEWS\nISDD\r\n=bimu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.10": {"name": "@babel/parser", "version": "7.12.10", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.10.4", "@babel/helper-fixtures": "7.12.10", "@babel/helper-validator-identifier": "7.10.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "824600d59e96aea26a5a2af5a9d812af05c3ae81", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.10.tgz", "fileCount": 8, "integrity": "sha512-PJdRPwyoOqFAWfLytxrWwGrAxghCgh/yTNCYciOz8QgjflA7aZhECPZAa2VUedKg2+QMWkI0L9lynh2SNmNEgA==", "signatures": [{"sig": "MEQCIHovf829VDhbC8Wkt77nkmFeyFNFXQlmHdcgjWV3Zi5UAiAVSs1IBHMwEK39no5OAWsNre5GrloL2IWpxKyRn70AuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1521188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQYCRA9TVsSAnZWagAA/RMP/A1r1QNzKOgp7H2iomSL\npehku9oClYbvhD1QAMgrI5t4+ImpPNNXwwW7V6yiNS0QrYvgjABUfUiA3/XH\niuNr0GhhusA1oE1y4Gdbt4gbBs5r7QJ53qGmLjj9aBt9tnKD7E4EYMJCuT75\njWMjPOM7zX/vIyF8QDxhJgGNb4qlUIdyFgMoIye6b0cPpfeNC6MDraD2FuYw\nNkhEwTDB+E/t/z51xY1ifJc2DOgdCV0nj0pvZOLvYvCeiZ6WEcVwwlaCcCm0\nx1+/obuhlnoXxxdzDtxq1bZ4ZkHci0ZzqxDt0ZRaRKIUYL+JVvPWUGv0anaz\nc5aYyHtkM/22ihyLBm28QQhIE5eL7nbspYQWiJqKK5gzbnD2aboddb1LsDi0\nhlN2uUpPKZuk+1u4kv5hR2sWeILUTO77Gp79xxaE/HvKj9dNQXOF8DXBlR1j\nnDtfmYcNOQqFPg9k5RnbhDsAAG9F7kZ4RD8BWIjVpIwIHxOjgrfocCXKasDP\nEc3hFazKFNJCbPHC3Ms6m0YCfcwcxiD5ZDbkLV2CuAVb09gagRWI2OxxN2k6\nVyLjtGzlsFgxV1weByoLgw9a0fp9R1sSTSML5QkW4uf/E8h4YlGE8GXKR4q8\nrVtQ8cJbZTCuh84pCG3D7T5cqgMaQbeqnYVQwNCoYSymqdYkskrlpZQfvKns\n8aQK\r\n=9hO3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.11": {"name": "@babel/parser", "version": "7.12.11", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.11", "@babel/helper-fixtures": "7.12.10", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "9ce3595bcd74bc5c466905e86c535b8b25011e79", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.11.tgz", "fileCount": 8, "integrity": "sha512-N3UxG+uuF4CMYoNj8AhnbAcJF0PiuJ9KHuy1lQmkYsxTer/MAH9UBNHsBoAX/4s6NvlDD047No8mYVGGzLL4hg==", "signatures": [{"sig": "MEYCIQC7XiGXJzw6g4iAAe8ZQoUWmx2oq41DzoufQb2g6MR7VQIhAN6ZNlsVA+yiIqauMU2CU9UGfa11iA2j+CJybHa1rd0U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1520522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3VCRA9TVsSAnZWagAABUQQAJinBuUdRQPQ6bmhLC1J\nJjpm7lDkVcs9YNHjQRRVpOn7HMIriaNQwXL67/8RFsya2XPRnGAf0rFd9uQT\nmupcxB6lqtzV583KufUU+qMoj0zlQn1/VZeEUB83qAKMHJTo/Tqub0MWJ16h\nGT52PZTD1srBVah0nvMWvuJJyKqe3mev8TKhPpYrFLv1DEhF81+4FhsRGdiJ\nfrXXd92gYb4FxZhMtz0v/+szAMNzlXi3hVEumXyUnB1GqmCp1I0avTVqco9V\nrEH0FNkSX4oWUSoybeXEuGHZ9faE5X61P/Z0pp0FvsJjnwCLiTX/C22z6mXy\n5iZ56MMm1wDXit3EX4l4gRFdyPhF4G/MpUNIxwm4hAE9rzorTKDqtnnX4DOZ\n+G564DHn6VmE3VEpbB9NEdPtSx3SazCERQPE1JTHuC2l5QldAvFot86TbmrV\ng+VaKTyr323jBke/caockLDfNZnLo7Qj2S3bM2owZOjB8oevguj1cMWGVZ9a\n7O0jWUurgxMgib27Nk7/Pn00tyW/XmQ65KuVFeZ3c0v+czCw5obR7SGRvpIQ\n9MH8Os0QxX4WgSK+L6qBZdhzXmLkf4CG+p4nYtMtCaYC1hWwhePmr+ftp/pM\nouRWIUnvKKuYr50PFJnjoNF27Yi8ktDTRtXQJnThEjj1ER8Wq4fwULKzamGJ\nSYKJ\r\n=1kIz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.13": {"name": "@babel/parser", "version": "7.12.13", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.12.13", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "3ee7be4131fe657ba9143d5c5b3a9f253fdb75e9", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.13.tgz", "fileCount": 8, "integrity": "sha512-z7n7ybOUzaRc3wwqLpAX8UFIXsrVXUJhtNGBwAnLz6d1KUapqyq7ad2La8gZ6CXhHmGAIL32cop8Tst4/PNWLw==", "signatures": [{"sig": "MEUCIBsZF3OM6cxlQQ1KeHpKJUQwWtpFBbRqCVC1PpxiVSDQAiEAsk0xOxKUmKdCl3vKozbGAUYuxUYi1LMfG4sxXAz3S5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1531794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfffCRA9TVsSAnZWagAAnQcP/izpG/YntzGHlB7MiJfQ\nFSNf4m5EiXbW9EgGRhQFxt17Ujva8eOQZ0EARvCUnFx2gx0JYPwq74da7BTg\ntv1dvXrG9MyhiANGfjqKYmDBsI16adrDEYtcUeOZ0bD4A01nh4AjtCZWUxYB\nbOlWF3Td7DnUKqGBxu0lsJVJPa2/a0Rt8AV2oXgl04qoFNzftIbZltkKtEhU\nBKlyGCtiyKIo47Y2r1qvn0N5qQJqjurCC9PO1l2FwHOmGnA4drJXW6HNPE88\njOFhZg25rzvvFmrDdtLOWcyMrzKAdsiBoooluPd2EmBTm5LMX0yCKamfRUlX\nC3jvckHb07MwypmRa/5HiAV/8RdOaXkKWIcXAniJbfT+RHNBUh75cW6K3Gis\nDJb405omOtTN3qZsJ6YVFY29MQttq/RGdilsbpZiK3sFEAXkDTOc2d99SAAH\n+G/pJHiVcWEVU21Gqq5H4gMW8dUXH1GMQhoavLQ1HCvR77BKNO3Xq0HGGcdV\n8KbCST1oHkMmMKTjW2JIeca0rxEjTwwHszS59LcfL7hiK0j6+BXYeTa8B2nU\nH45xahsqSDsdA7LWXwFB1sUsqN3wjDIaTavUMFDp1tci6Efoq4TD/dxXij79\nBwcSV9yX6lbXzJVRLPkH9CmGlRA2qG/UD9qBLtHt4p6sB/hyZ12UE0a9ATf0\nnaWi\r\n=txsj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.14": {"name": "@babel/parser", "version": "7.12.14", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.12.13", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "4adb7c5eef1d437ef965ad1569cd826db8c11dc9", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.14.tgz", "fileCount": 8, "integrity": "sha512-xcfxDq3OrBnDsA/Z8eK5/2iPcLD8qbOaSSfOw4RA6jp4i7e6dEQ7+wTwxItEwzcXPQcsry5nZk96gmVPKletjQ==", "signatures": [{"sig": "MEUCIQDAgk9Ko0ZxNlwB/62JcrHXnuEOdXUqca2yBiA5BSS1fwIgUYFerSxTlVdW4YRJ66QqFIzcc1Mu9yGginXgRDgPsaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1531794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGrv+CRA9TVsSAnZWagAA+F0P/0xLdyZPsAVwipypX1n5\nI8WXLrkx3nMAl3P2aZN3IhBBXuc2TuvZ0p9qb2X5WacIPTJfsLkAxwABQPPk\n/5feyBRChWG1zFs8P5SC5VBV/MOOTnqXs48sdhH7sPIqq9y3cN93r0vUNhcs\nqTiQqntCeT51xsFK9VI2y7UcS2zsDddZSJX2Osbh5IwiL6DARAw/Y8V7FV7G\n7slAd1G3P6AE3g7o72vtNJN9cbFePvstBIrX1/1p94WJCLyDkGvnUPpWlHZb\n7EnpEa1hgyecSoZ5Q6y2N7v1yumKhIIMSfafvhE8ZaiHfDdU5vjq34zxheTG\n9Ft3t6fdC8SnSswXbzTuaLqcGHgnWapwVOojxwRic+2NsmKrR9MwK073qx5j\nDH63y7jpkRzZ/yN9W9r8QOZ3Zjd7s/hFwxsmoZFGZyFGSl5xHdoJQeWslHw2\nE/LYJsidBnpfoi6oBDEGa0Ekffxeq32U9lw5ze2xHvOhWLHssNITQSsTKXNq\nrTl3a2mXAXKciyDeN4x4PY9pNXXMU8SAvkkd8Qqhf8orsmj1bagH/W9Xm/7K\n+8YaejI9ityqRiH27xWOl0RysliPRWPiCXzttqC8sfo4r3CX2CdWAdgDbkCG\n/+qDFHdxR5TOfreUmMN37HtMTLst4yErI2hvgYQrkjS3mCOykeNGHOnu0A7b\nXqv8\r\n=cHdW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.15": {"name": "@babel/parser", "version": "7.12.15", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.12.13", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "2b20de7f0b4b332d9b119dd9c33409c538b8aacf", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.15.tgz", "fileCount": 8, "integrity": "sha512-AQBOU2Z9kWwSZMd6lNjCX0GUgFonL1wAM1db8L8PMk9UDaGsRCArBkU4Sc+UCM3AE4hjbXx+h58Lb3QT4oRmrA==", "signatures": [{"sig": "MEYCIQCUdI7+LMfgb/o/m6tCfLlcT3krM8UlyiRxwnmZVuV0UwIhALTXLtaEDS3UxqxiailoTd956YhOJiQkPEw2nvp/sH5X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1531735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHGk5CRA9TVsSAnZWagAAanYP/3Jx6HQTKtUzZEfU/Tz4\n7U8Sz4EIe469BFPVXa8ipAXLIX4AmQj5nHefd5mviQC6WuMjcW7iFk5jgl8U\n1oDb9MwnUnhrRxcXsKYNNqgKQSeBcnBpOsxaXJmVSaAy0hmqvGBQcwXWLfYP\n/Akv7xfEdmMuW2A2uHnx8hnc4zBoYdICfOjGNP9EpTipqRV2+9abSkXQ/0w2\nfat3pfLPGy2bwvEf84/TTaDJz4ZSwCjfG2sWPvguOaZirPQ7JgUP7aEVprgg\nyfk080eQYO7Tz7bc6dGWtBsjn2Wx3HjncLk11ynuRilmBedNNEpkQHYzEJEt\nBHVdaGRLTJusFdGOASbkt7aStHL3plYSYkLWiAZp4BrzA06bNdDKDHJtcjE5\npOcROgC8eipvnn/L0J2L+El9XUani6LdvwiG4veGYuCiP/cOC/2YE2ujLbJy\nZcOmbU7TRv3+S51SzcvROC5svcqfimla6XRE+PbaQ5yNC0PDesu81LWDa0AJ\nIkbZjO06Y527NH56APIrSJk5CRM9EzJ/lju6Td5JkbWixNZlFd2jP3txLG+B\n/39Z97rTiOBLpAWNp3rwRSHsqSS2bdpAg4A+u93Y7h3SmQnj3B9RhUUJNy6t\nsZvoYizJtNvlFVQStlDoeRGH9X02VTcRvVAOF6jwrbQY+ng8Whs1SNDSTaXx\n84LA\r\n=+6pa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.16": {"name": "@babel/parser", "version": "7.12.16", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.12.13", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "cc31257419d2c3189d394081635703f549fc1ed4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.16.tgz", "fileCount": 8, "integrity": "sha512-c/+u9cqV6F0+4Hpq01jnJO+GLp2DdT63ppz9Xa+6cHaajM9VFzK/iDXiKK65YtpeVwu+ctfS6iqlMqRgQRzeCw==", "signatures": [{"sig": "MEUCIQD84nfNaNIv7pMzqDBnuXjyKJb1JSYuc72AENVnXKESEwIgDNnuV6v7fvslXBQ4VJdODodPaETod5enPXSKB80VDQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1540097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbPjCRA9TVsSAnZWagAAxt4P/18+LwwKMUQlBrv3LIy4\nFsBLLlRZosI7vlss23L/h3zAINKUq8wQMs8eniD2jp6icbIOLw+a/2a6xUa3\nzjl7rhkAVBe1Rr5c/JkIR9KEfeRghQbBcYDTnzQWGfiuzwCocfdNqDc/RGPP\nBpoe+jxWGTeAmj9JPPc3vxXJeFMuXM4DnqhvBYAvZrsNxgLOB5yi1diVv7SZ\n6I5Ll1sAYPXc4+J6gB+vN+uLa/D3z0tAFBgD4OuTguUucjlzNRO2nRc3RZLk\n/3IKUIGMJngkLWmNnwT6QtnG27kP2TtqIs5KY4kNFsE2nAQC6u9RbG7Vluv9\n4hvg2TvmieXsTz+sMbkn3Z90Rby+Sc5W5HDChlxhn/dyAWGeCdhOvgVRZ830\nim2KpHPGv4BcxAPr3VtaZ5jmKXj1ziwX5xXwWlut3k32V5swwvWw2ZHiWzgp\nVP+Bg0fVrrGXnGpTzTloVVkgpS6uYGNvDNsXLbR3eCi4xS+xt3pzrXgYm4Lf\nD994Fgu35/fJ/Cf/yP4Ezjd5h3dB7jH6Qu1bFmpaPYHk0xE172NISLJG9c0f\nc8Ms4wvYcZJI46PWnkiywk6uRnJfUv3rfLIkMWJKdEFAFoNyRnAiX+qHFbbM\n9o1Dh3SWMN4XgfR4zCPGkHHyKqTf4kOQj7/q728KKz4WKkOWc2Ol8ZyXsjic\nieI1\r\n=Tymf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.12.17": {"name": "@babel/parser", "version": "7.12.17", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.12.13", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "bc85d2d47db38094e5bb268fc761716e7d693848", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.12.17.tgz", "fileCount": 8, "integrity": "sha512-r1yKkiUTYMQ8LiEI0UcQx5ETw5dpTLn9wijn9hk6KkTtOK95FndDN10M+8/s6k/Ymlbivw0Av9q4SlgF80PtHg==", "signatures": [{"sig": "MEUCIEAQn40SFh6aBKbWbm2TN+h+cNTADUGVvOasZCm3zrIjAiEA88ibf/3Goqda7rL+8n4UYliKrR03v/NGF+leTpWBp38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1541792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoOeCRA9TVsSAnZWagAARVYP/1eyFawDTs2reCAOG9uQ\nM3iFWRDvjQ6hr0UeypYOae71AcgVGReTB81iVW/pAL0AbhcnRhij0W2n1DIe\n/DKYfQJvWdcR0Pat2Jszrm8iQOTJcGlmNIi7uEd+J3TB/AzO38L0wkujRuyn\nzQ0EvR3yTFTwrVd+/Z+YZR23cw4CFRcgXhp9V0S+TW5Grc2J1X98oYN6ywbm\nNYfM9AkleOfFvZK9dTNHYoaI/nK23bW8DT8gg7Wca4aF7AdqC/BpG4hwMUBU\nQ5rElj8x2s7RsJnhRLSfYZmqocC4PXsViaqBAoiUaxUni855/uxAlcnx+Set\nUCcknN9E28AS9VJ8bt+/MIh9iQ/OVEpXuJx3J/2CGcrE/Au1JWb03PkeN6oz\n+9mk9mKORcxhLnMGuYr0ah/vZpS4d2cjxEbebDeRR9w7VFiPC2fFaZunlvVb\nJ7vNleqbcOLYKTYcXqK5ZOHFhKLotTaWgpzjWJDcmr+6SrJBMbxLi60sdP2w\n5kqQGQWLts3IfiKAcZaw5cugT9AlwDRREPR4bMRSTnKk8JVpLopuP/NrBgi7\nC/LLmfxtSEkh9nDLdHebK5PC4h7bVLkyahQm4VrbBpl59AED4ClIafffrGLQ\nJ0cGkhT5jlG6AzfZjduha75wVvDq95VRDBrjb8FtaqLSVl6JhRfOqlNNYZSX\nx81d\r\n=b18q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.0": {"name": "@babel/parser", "version": "7.13.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.0", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "49b9b6ee213e5634fa80361dae139effef893f78", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.0.tgz", "fileCount": 8, "integrity": "sha512-w80kxEMFhE3wjMOQkfdTvv0CSdRSJZptIlLhU4eU/coNJeWjduspUFz+IRnBbAq6m5XYBFMoT1TNkk9K9yf10g==", "signatures": [{"sig": "MEQCIENAfuBE8K/qkCveNZe3ttN7WupjjzaE/h+uNU1gvO6rAiAi/hk7+vFzoE0ta2V3fJZG63GNc0aPe0RP6HIbxTTt5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1566505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDT9CRA9TVsSAnZWagAAu1MP/3UYffefLgK0VIWY+lOu\nc0pIt0OV6lOKGYMdzc1JOTdgs1Yt25XYnxiL3g/sILeEZb8kBb5w/QBG0jat\naw8+EqR6lZlPfJkAqDVX2WUrVPnhmCOhEwNo0La9hAWHWQ57r83YLKDppCag\nBDj2T/HfHt3+Wgfgaj3/91tsdFrKR47muF+c7nOBS3k/q2TWdzfrN17BwBuM\n6JQXxFa8LsK3bpD5YjKXnrblM0yMoDXBZy6H1Bh+fKf4Yh/TimjxzzGP0MOb\nNe2NSs83dWtFx3DGRNTSUGehgqX6+NeuvZICqFz4PiRebZn17g9+Y+2O662w\nUXeuVcO8Ve52GSN11exIHroX1krTuXi5jeEO6w7KExKgTl/4Df1LHHgvJopN\nYX7QZEfDA59CJA36/GdgVlRRVWD3k9CND+4o1SPjyz3TCnXX3nUEl508ayxF\npnwtMNFH2/Pr9aXXKRsHNOJg+rb10SWFzjuz3ILvdqnuTYj76XxqVINM1a84\nPKTonxsGpCFKyiG9f36GPJPTzE7p6nB5lY09ePeHO3aytGSUoUSCEkXInRuz\nh7/jggn+Oy5r79mw2EiplDmBlCmkvqFNPudrdtwnwroMjNMXURd0TOqSd9TT\nakb1G4To0CYkWrc52RSHn3MurMkIEHp0J2ERTDOZ9jyVcA1wT7lmJcJqcrMS\nzzm9\r\n=HmGB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.4": {"name": "@babel/parser", "version": "7.13.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.0", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "340211b0da94a351a6f10e63671fa727333d13ab", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.4.tgz", "fileCount": 8, "integrity": "sha512-uvoOulWHhI+0+1f9L4BoozY7U5cIkZ9PgJqvb041d6vypgUmtVPG4vmGm4pSggjl8BELzvHyUeJSUyEMY6b+qA==", "signatures": [{"sig": "MEQCIHSuvT0oxNrVm8wQjGOTIAaONci6Yd1tV3GL2kFiLc/KAiBOYcNOkqxF1wzE1GFAyrXWnOFo4DGG2AbfZlG3rOvCRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1567784, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNNuWCRA9TVsSAnZWagAAPF4P/jZAAGIn4hdeXFoBBsXy\nt/cvzOGTFY+XreHwlSR3Aj9utlZCcnyCtI8QibDTv+G8O+V7QbMujGMzNIx9\nTsUTJ3rBn4P2igXx3IzbwYhWkcW42lHzfJDe5LMO3S/E3wTMy8UShsbiJ36Y\nOBvQEkp6ISJlQbxsbsoFmaf01kPif3xMMlh637bhfInXcROlNf/VXNREES1M\nIDjR0ZHC11IidZGUkTp+QCxtZGVz+TwXhrGfTmEWnnaEEWLqz5fTi8QhqkyT\nJHxOKAqf675c3wzAlmfKgDovNHbqciTgRFMvUkbI74x++TY4KtAOqjYFsFyH\n01jNaJPyU9ZPU+LGM2Cs/QoKdh9BRN3jbNzaOXpw8Cv+uhjgL5ljsJ397zBG\nfO9WLP8f0okNwQVOqO2uCKSVLAgaLlY5JHKbQaMpbV3+Uwop8eYdudFhE0KW\naD+aWLUNTnTVtDcC20EHCObjmQYgSciJAw8NQalnA64IA4xONNKC+BxbWGZE\nN40wu93RNtHQP+GmJ8ZlLanIrSyyJKWXacominYCx30oB9wkakGgsH5Q8Eh0\n0kLgpvywgiIjNkzMosvWMpcdF4XfJjdPkZ1Mb85NAeUFJ1DwJtIarmv5uaVY\nJ3g9ID3U2ok2santaluqhGAZdANCgFKHl5As0O1r5sG6hcS68rUuy29U/S/P\nu3Iv\r\n=NH39\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.9": {"name": "@babel/parser", "version": "7.13.9", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.9", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "ca34cb95e1c2dd126863a84465ae8ef66114be99", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.9.tgz", "fileCount": 8, "integrity": "sha512-nEUfRiARCcaVo3ny3ZQjURjHQZUo/JkEw7rLlSZy/psWGnvwXFtPcr6jb7Yb41DVW5LTe6KRq9LGleRNsg1Frw==", "signatures": [{"sig": "MEUCIHl/S+DZ96082OnND94EbZITF9kH5VTwqOrfjfAdvUJGAiEAzvLn8ZS3BCgqo+Eu1lqyi44BRLuWSbfsusDGl5CZ08E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1566001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPWAfCRA9TVsSAnZWagAAbEkP/1YazCjG1Efdco+OMs+3\nUYoinOfOJ1xp+LOr1/jQ5qXoclizQbofIQNWYsIYV9ovAxiCdYysJKeVlnWe\nhrMpp2kf9Ca8K09KWQ3Tt0mH1X7O+lXXtay2ydS6ZHilUBO+3j900R0IDkzb\nj1G7YMl9FzsUGKQil3TIfUEO3sTjxnAjEGngmu8hKoNhCwquTnDXassmnNL5\nBop4edhNyaN2uJgjW4Bwn7D+7fho6EezAQ377bcVXrYElBf5zQPaNus6/NFe\nxmzANJBT0Yz6P3wKW6McMlqP2IT8CMRZb1fjRYrxkmAMvrrUJjr3nU+rkFA/\nvnUDdESSwFQ1stNCc+uByNFBKhM7prAY6RPoX3iDmHwzpaBwJhskHF8WQnkA\noEHNzx63gxMTbxDk7jbPQL38X59K4qJ25NUIBp4QiMRmV5iCVDj7eol4FGG8\nzVOpIlXYkAhVqzfOJTvnIpSQKhU7R6XlPVAZmaJAq79cI+CJdXYzCSy9SB6F\nItKIYxzSNkAV9nGwavyUESDCrKKuSiDKnNroZkIVDZRRZm78TWdFiQSe5X4x\nX/TUZcpfP2ZPykCPqCdmemh+j8wrqZOVJHst6yz2a/Cd22YuRJkvWixnyLsB\naIFbdG/Kk7EncCR9Qnp9dfVQUeSk50LbGB7wcr1tW0n2nZol65bwC6Ad5CMh\nwTCg\r\n=bhbl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.10": {"name": "@babel/parser", "version": "7.13.10", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.10", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "8f8f9bf7b3afa3eabd061f7a5bcdf4fec3c48409", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.10.tgz", "fileCount": 8, "integrity": "sha512-0s7Mlrw9uTWkYua7xWr99Wpk2bnGa0ANleKfksYAES8LpWH4gW1OUr42vqKNf0us5UQNfru2wPqMqRITzq/SIQ==", "signatures": [{"sig": "MEUCIQDl+y/NjyItTYiSZmE5wYmmHdpGDZMJdL6R2pq9JahsKgIgbEEGEb2jIVyBLvFPaEHfb71Le+NCqQjsfmSQnbvBbgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1567938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRqbdCRA9TVsSAnZWagAAsPYP/iSsbqHqKhT3psuqV2TL\ngHBAMiOS8nBKWBgRX8f4aVcxLiNUqQf/Q91dc/seNSMxwpGdg08WdJ9qrfrN\nmgPx6x9coSDBy3A66fzTKYkBm1j6vV0AIdbtLTCcTvOH7vRnR+jPmkqanKcf\naOyKs1600KcxXSlpkDXT9VRuo1K+0NAXxJoPlFWxxEVurpz15tVM1PMXXbIp\n3htOTwZzDIw+Nl3azTecuRcrLKLrbLr1Xo5z/rJNfGc+ZQ8aQFWqfpe2Bgyj\n9fPsEIH1m1Fm8TyNvOVtdzMRVo40oES7Do9GFq7ERx71N502wvJYF6M9k+aQ\nwJ4cnDvYcdJI04VUXeMP6VBk4sTNX91vf2ZL3aRFBFIrsPrKvhj10T6TNn+c\ny+C3n8sWxilgBEQUjwExizFcLdaHpLt9wb9h07MjSqxziETTWmaYw3QSOK24\no6zEZErFj+P/QY5EJtS/2yTp2t2U3q+o92b/j0Pm6+H9KJj4eh1DvQkF2KoC\nz7G3uPSsi6ru2s/YbTI89b+kEri/gKBGQsisav78hdg5ePl6sGlPWOfpPyFh\neMHGRP1Z6ApyQBE8vDCeak7Gu91QiDLXmgEjA6z0Zu2EmNw+1JIfyYIwc5uq\nr885owthBbtODfH5v8AHNFaTr8FvVNmIt3udHxmN30XEMM2EQSZQxs0PeNyU\nlxpN\r\n=7ad2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.11": {"name": "@babel/parser", "version": "7.13.11", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.10", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "f93ebfc99d21c1772afbbaa153f47e7ce2f50b88", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.11.tgz", "fileCount": 8, "integrity": "sha512-PhuoqeHoO9fc4ffMEVk4qb/w/s2iOSWohvbHxLtxui0eBg3Lg5gN1U8wp1V1u61hOWkPQJJyJzGH6Y+grwkq8Q==", "signatures": [{"sig": "MEQCIAPLyPXnXHFGgNSJ2z4YKiqj/YvTe/3nG+oSpILh8JrsAiAKcjrmMSsMBOLtWfG8EfEXRwYUKXR1YKXau40qBcgDYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1568723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTyx6CRA9TVsSAnZWagAAOsIQAJN2Mwd43k6gR0v1Phw+\neIkJCk5mmjmfKExddmdHzTgMcjYz6Uj6ID1ZUTkX47DZgGfEnBg+U53Yok6/\ny94yp3/qjJZhb0IIO+vfRQBbrEfhQgPCsCWWO5o28BqfOOkvpLMPp6bWRe3H\nmTG0OH7ez0okggkVniDX+MiEhTaQ4wfV8YPDm3BAKrDzAXesqFkuPwhWW76X\nQVLdV4atR4MpWEeUj/NH3Ev+CGeswTqHz3ZfEAuxdsF4pdRnkyNvzetv+vnz\n/tov0aStJfetI6ZVobSpd1lZcYOQTIVJwJYudBw0ZpVzJl4zi5pWLLS9Pk3Z\neGDa4nOPA7xcDZFHkDU3n+wrMz3OQAJGCe6yvAgwjkruJFxLbRMVLk6qDj0J\nO4UJp7WES/jfvX0Pn3ew+ozINnvc3NIstpYc1FNiYksMI8cYEkLwTDIfyzBE\n/MYmatnlRL/uH9mffY/lm0sVYSE5r46pwF7pDKaJFEWXXFEHQaxms2bDAxGj\nlM5NAlJj4MBjRe+OAFz+v+HhMIfsiJMnvxf4BgRV8uu7zeUlbGlDHZp3pTMj\nvXN5tHbglvF6w79p5fOdc6ooFPyhlXusJ97exmEs0k8gL7fUpjWNJNW4mtYN\nmMUYTp0Ox48Kc8jB6Nb2T8O/YmUpm68+4Z/FpYEgh5K0rTPol9oJq6GqahOW\ngsST\r\n=sZsg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.12": {"name": "@babel/parser", "version": "7.13.12", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.10", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "ba320059420774394d3b0c0233ba40e4250b81d1", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.12.tgz", "fileCount": 8, "integrity": "sha512-4T7Pb244rxH24yR116LAuJ+adxXXnHhZaLJjegJVKSdoNCe4x1eDBaud5YIcQFcqzsaD5BHvJw5BQ0AZapdCRw==", "signatures": [{"sig": "MEUCIQCyFgJeRFz+FYJG+1w54JQjY6OR88SvCwhlRC+qGvqT2AIgX+8ue2gZlHb94rdvcejz2+GqAdHibRePm92n+N5/xsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1568857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWLvzCRA9TVsSAnZWagAARcwP/3BQoPDww/sbp+UIk1Wt\nDzmamGk3Wqx1aF7sVw0+aCZKDKPEpQWz98zUHD2S5Gin5s+AGCS7pesYuplX\n9ESxWKAG7ce22fscuca0H8PotCvBCjgs20V80RKoG7P4aOEhxEUr1Yo0ckzQ\nbH6hcEBd4SLKEzaA5mVsn9tYyJJppSzONdQTU6mr2HC+UXvvfM575v6ileWt\nW17kc0/usz5UCWUTG+OLlyhyLLfmdDdAsuBmEH4FNpK92+H8XJQG6XMsBaGc\nCmAEJzfVpyXuws3M6r6wd/oIQHRJjJhsJOT/CFtR3BubQBu/17RMdtpYLuE4\nLdNCMB58CYwssJKGUd48PLNTm1ma3Z5pC6EnGP1K7E6JDizQq4r0gQbURjBu\nAIBm0ngj3aEyyvj1CWzl+OKAFhasIx0yMlcr8RtY1c6cFdJ7+u41ezv/gEJM\ncEiAtA+mHjPgRHr8umJMtdUDe4Mh0n/HR1aMOW6x49NiiLnv/ZRlCMjMRMqQ\nxTvP37F/cmUGM8qbzb740uHaZM6HMdTLJqxdnUCE311AlLXGtupIaRfTEzaL\n/WbkIHHB7j69s6BQZBa3EAhIVdQ+EQ45m6bpSyqqJNYWVScpLbAyfKfmNNXK\nGnBGDEv8DXNTrxGhJIP0c+SO4QVy4/pPAgH2eqAvNWNBGzkgFum1ncNhyL3p\npf5I\r\n=UVD7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.13": {"name": "@babel/parser", "version": "7.13.13", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.13", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "42f03862f4aed50461e543270916b47dd501f0df", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.13.tgz", "fileCount": 8, "integrity": "sha512-OhsyMrqygfk5v8HmWwOzlYjJrtLaFhF34MrfG/Z73DgYCI6ojNUTUp2TYbtnjo8PegeJp12eamsNettCQjKjVw==", "signatures": [{"sig": "MEQCICwpI4Y7ERYJkBGnt6aO7zNaXf4F8FwxOx1rMe0tC43iAiAawdJCHEHfKKqKib/kSnbpHbF34+V2dCP4h/q0UUGBUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1570679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXlAVCRA9TVsSAnZWagAAlnwP+wTLXFGqkZgJpq+aY573\nBWeZfWwT2ROS66FDBXayejHhzrdEf+1iURJtYFn7Ygy5Fy6cfDTU9LdHZSoN\nrChRFdl6WoB6s3EwktR1gimOKllSzI1YlbVpBFqKimiIIsd2dygREKR/R04C\nt4D0uXPYTHjaf8u9CAC3Okk/nGSkOzu3OMzzY3k41gwtcyrHL0eEON30HAWd\nWhvlfKTEnBqDkMap58zp7dSVN2d2VbHFt7HFPuyunajL9DOVedZbRdtjLX5E\nretQOfp6ycOxT+7veUZEpK4tNFpWvQFMH2sD5x+RW54bJTpVqywFCR2VO593\nrW7tkvzphwUHcqVioJtF/WGFsZ+OP+nYnOuCubeLQzXB+p1xePOi9A7pSoIb\nPfitUxK4s0Q562E1YcKTRcolzVx+/tMpyv74MbmQ8aafVC4AEhTGzVZn0K+g\nCbzImREbVRN0QY6V6H3NdbvHdqUyGPSFgc3MUlMQXcJg0UTbQ7ExgiQeC9P4\nc8pCWD4GIasAdwNu849jef3OIqPowu7JjTZvgn14tuKUp1kEw/2e9QuQPdy2\nTO21CgBQGiATBCalksje8G1tZoFesokKLOoJQBKYlvd/pH5DeeObkv+dFmrF\nnF6mDzB07wCuOAI80PN5YWXuvoH4q6/DFtmev2SdxwzPK0m5P8TPvisyejLX\n08XX\r\n=d+1S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.15": {"name": "@babel/parser", "version": "7.13.15", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.13", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "8e66775fb523599acb6a289e12929fa5ab0954d8", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.15.tgz", "fileCount": 8, "integrity": "sha512-b9COtcAlVEQljy/9fbcMHpG+UIW9ReF+gpaxDHTlZd0c6/UU9ng8zdySAW9sRTzpvcdCHn6bUcbuYUgGzLAWVQ==", "signatures": [{"sig": "MEUCIASfojKIQ4hfovXGsFA+682W71rDEN/wUKU5ptzfHM7AAiEAlRY63LI8y264hCol7pdMKvirur3jKbJtlAf9DEAYoMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1574055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbyY8CRA9TVsSAnZWagAADHQP/1K+Vi4Zu7W39O3MOxAn\nsjdGUqPm8SQs+j/mbQwNjQ5i3QOhAkdNmapTQ9WQdCa4S/akhEKLnCHlwYD8\nVG8p/BXnhBUZ08eZNw8xSANNINN0riLwiCGTnNQfqRTGz+MV8LKOJ8a/oirv\nzWPyKzx5BrMiq25uUHWbZx3eHftulZSqCHDXNuCssbRn4KUPHPRpB2wB9GUk\nYZc9KnlHB67JQTnMmszJFw/ChyF5JO4Qv+cxtBrD5sIgOJa/CeYOVGwwzaPD\nN4a8Ee0187Eeg3tjnQgbHQ1w8bVhNjhaAB1Lsbh4/4//IsGpU8FcNV0F7o6O\nNH0tJ41dwoXdtBfGSpDT5ATe8Qu+LEN4A1IXkkdNO3anZSrBcldor5hzfjN9\niWl4ef2ZYoyNTJnTyGfHe9DzTudMUK1x9cnpN2aP082JONo1AsFW/u0vN9So\nKLzpPVuGulLA1deN0rwtncAsulJrjHfIYkGl6EKox2NfthOfyCh4bzej50Te\n9QRFrzWubLREdFbrWyS6QEcuK33YrJaunjCYfMqg+geWE1WYlVYOT3b9xWOv\n+8BCHGJRAiC7jnPRpKz7f/vyFn2sOtcVjjvTtArhpR1E3E7Um74BuSlif9iV\nblvy45jQ3uGh/uqZlYJ5ZZTngRZ2bjEMFetYX5r/XeCv45g2o8mY4j2+9JH3\njzCF\r\n=aH3I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.13.16": {"name": "@babel/parser", "version": "7.13.16", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.13", "@babel/helper-validator-identifier": "7.12.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "0f18179b0448e6939b1f3f5c4c355a3a9bcdfd37", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.13.16.tgz", "fileCount": 8, "integrity": "sha512-6bAg36mCwuqLO0hbR+z7PHuqWiCeP7Dzg73OpQwsAB1Eb8HnGEz5xYBzCfbu+YjoaJsJs+qheDxVAuqbt3ILEw==", "signatures": [{"sig": "MEUCIFN1guS5WHXLAFscud7V86lvApAfVKlStndNt6cEtqcuAiEAmHdADgPCR2CoXBvLI+9AIy48VFBJc1FQ2IfszVB8LHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1569999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfrkiCRA9TVsSAnZWagAAe0EP/ROCpPDD5H15wMBpfdnO\nMYTpH0FuZMSVdOFRUaUgSe78kacqUF+9t3LMxxis4XEv7pydSkYsDvvAq2jR\nMiJzCL9I3Pl79xDQ4bV7qsqjWdYzJRFPKNAjPudkwK5QrhsSheAcQpUXmcB+\nUBb+IVqy5/XzJpi/E8VN9FqGsioz0XJpbLKeEoaC/uGbSddL76mHr4dy5wD9\nFWcC7687D8UEbi1X8yLSKIJndhCJ80Qd6GPfQSlckBgF3xUAPvYQ8YkWHFIh\ntgm/0QgC+Nw5wovw+8rI07G0gv7j8+1K9P41FKofHA8QWcwGzBJqSdtzqdH5\nuseLFuf9z9bJwoSOxogwATKbJS6faxbzB91geF2AP1NaAeO7RXKxCBd/AE9Z\n0GxHCEijPxBR0W7ATvyzM0cnO6QmFK8mDahjqhgYMahcbvc4JKoEoRpXYb9/\nR8vB0JKOj1pHqLphLpWu9bXRDZowSfuT2+gsTDrznmKAkKDfzigiKX9zR5xs\nweVMKtKFqtpFnOLMVz6CQdUkBnZ6hyAtQkXVyubdpX29Uzo7MbbwnfLGXPIH\nbYxHbqqVHJSkNUihw8lv+ajxqN99W0WVnuyuVLVXmEMEnNHIO1IA1AlumKdU\nEv4ndiW2FEUdWlvBvBw4r2hoYypovLXiljrUwIZEoUJYSkQPjPB6v1JcSKAb\n7Nd0\r\n=h1Q9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.0": {"name": "@babel/parser", "version": "7.14.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.13", "@babel/helper-validator-identifier": "7.14.0"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "2f0ebfed92bcddcc8395b91f1895191ce2760380", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.0.tgz", "fileCount": 8, "integrity": "sha512-AHbfoxesfBALg33idaTBVUkLnfXtsgvJREf93p4p0Lwsz4ppfE7g1tpEXVm4vrxUcH4DVhAa9Z1m1zqf9WUC7Q==", "signatures": [{"sig": "MEUCIQD2ekFoucwoOwBaI6/Jbo07varXhVnc5ZFIVyzSkbw4dwIgXWBJ2v8mTfOE28n6oCkduBgtD0o/Dl2k5je7H08pj6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1593149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKRCRA9TVsSAnZWagAAqqgP/RKnpT4s7lg5JU2jbzp5\ndpHo0xDEdkgRnqnlzpVHBaIPjtMH7UvWhxmSGP9fItn98yT3kkjBTAnGBhYR\nhBVqX4gnUZlrE364hcSe6liFJeIpzYQeTuQxhQrxEi5yNXOfGldpLLNbFtFM\npBql1psc9Bi8xGZ7qj0wXh0DDPiDPhHj3jJu4Yxu24da7ayVMHm7E/hM7p/I\nLnAASckQPADYa1PGUIq3c9O7ArVf5YE45Pch0uYg/RJ0b0ux/1lBGehMZJLJ\nnUyBSOdJ4uuvNNlJwwrdamX3xuNWt7ls6kZJep2UdXWASzXo9Ld7kowj8Sqn\nGnGvBD+0WM0+DqOckR8eiad/IqdXRoOOSwz4L9ZY85eAVMkd6OGGLmSX18yR\nPmbmfWaSAAHf8EbgsFgcYlH03TQ+HhgkaTINiVjtZ6Onj3vUq6Wju8AYjY1d\nG1RyEa5GBz8mImVf3IIr0q1FJQ0ZfmvBiL3viP38HZ+bGT0Nmyn0GNhlFlwX\n0Q0jdmRmbSixXwUqCv7UHq49jwT/QiolXcQBWznGEz2Tr3EO7uu1qM9/5bVw\nf9AFPogOVIUzeRC/Z89GCg/Az31F2y1QcMpN4dpyh3v1RdseWf9eMoDb21V8\nc7xp5TjSxSdRLibc8QaiGQEAhm8vChkUlmKIHdrMRkrRUASSf0gpTJqSBbJC\nuARV\r\n=sLuJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.1": {"name": "@babel/parser", "version": "7.14.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel/helper-fixtures": "7.13.13", "@babel/helper-validator-identifier": "7.14.0"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "1bd644b5db3f5797c4479d89ec1817fe02b84c47", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.1.tgz", "fileCount": 8, "integrity": "sha512-muUGEKu8E/ftMTPlNp+mc6zL3E9zKWmF5sDHZ5MSsoTP9Wyz64AhEf9kD08xYJ7w6Hdcu8H550ircnPyWSIF0Q==", "signatures": [{"sig": "MEYCIQDzksZhB9nM8ng5bfChQYzScuINWcmC9a9CgA4Unodc2QIhALhxVJ7uG/J9YGDuT2ws2bwn9U/qqUqIbSawg1YAn/fV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1597946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkKmvCRA9TVsSAnZWagAA8PkP/0QEKebRtHEFDQ3oykMN\n+vWF26Z7KK1KoEAMoutd3gur+NhVXjEm4cApIzO+qvL2V6VuU/UhufxcCJbz\n9/393xLUP90/F8KcFobx4NA8TqZEBzK72GXkaqSoapWqbRr4E3eYxerdOxuu\nrqEVQ2V2rv6i7M3F3YlIZ0uLB+Cqd2eo2oPypR2Go4AVIVGpuhPS805AOzWs\n2v9wSmwuiBOAWrZ87gyEoJV3np3sA3q8RKi81w93seH7wldojgNcjSpvgPx+\n6+wpa/kVwbnQ1AwWA8eee/9cxyqwk5lfKNc09my4i7PQbXflrqaldAvVz03L\nVkSlLSYmb3AJQjFQEZdX2bMj+9j+eXqaSVTa1MOKrBZhNNqBgtnLzWB+5pmm\nE2HgGqN2xqigba4ZVYNHrEQQo1ASNdbBrYZekjBODeeoyTYgGKebVEIFaHbv\naMnVaLuPZKkv18YhJGSVqFnHXKgviOlPq0EZua0PJtNUshmqRBdc6UHPiI05\nwkA48bRON+vARjuDk6fRxDWp1pgjc5enyywIccfScAtsGDi8QSGikRCLRY2m\nYskg0mXPqxL+ZP9v2iIwSRfrrr1eIC0im9l4mTvDqOupx0c7CxOySuad2rpz\n84FhzXsbtZdayWCkXzsepRAV0uzOb7Uf7wFWT52yZLStQVZ0uDUccZSk70FZ\nJA1E\r\n=sPTX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.2": {"name": "@babel/parser", "version": "7.14.2", "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel-baseline/parser": "npm:@babel/parser@^7.14.0", "@babel/helper-fixtures": "7.13.13", "@babel/helper-validator-identifier": "7.14.0"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "0c1680aa44ad4605b16cbdcc5c341a61bde9c746", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.2.tgz", "fileCount": 8, "integrity": "sha512-IoVDIHpsgE/fu7eXBeRWt8zLbDrSvD7H1gpomOkPpBoEN8KCruCqSDdqo8dddwQQrui30KSvQBaMUOJiuFu6QQ==", "signatures": [{"sig": "MEQCIEhpR6VwD9rNnX8A5nq4rXa20slr7bUWAUTBqTd95SmFAiA4vbT28OLhuKlHJKa5ubnObdN0qewoZJv5pveBfM4bNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1607570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAu7CRA9TVsSAnZWagAA0aQQAIX6aDr0jTp7VLxgi1IK\n2N53IIOHvlOTWF/w9WJF3beg7Cu3uC25eFyJ/iv2i42bedPagA4NJm8aZWaJ\nUsIX3RyzMRA8KzP5bIRptKuA3BmcFcw5ZW1DVCrOCJBqXvz79lGF2WAiL2uv\nQUonckYWcDMAj4WnddjyQFp2Ps/ZD/EgB+aSsiEJ6HHXKLHCmZtyyxRE6qZ0\nnMxARSkFipukzCV8Qfui+JtJJFciUUREygYiUUC+SnVZo67PlrwwqP2BRW2E\nmDtneqbvRRFj6R+gXW8AFsgLvhd66SBSWuOUI8sspqaKMLyX/xSzY5RuAsHt\nR0ydA2pvpsjKI2CbsVONQo2ePUvZ1jY8s6MUTRLohymWrKGgqqF9/+LxWVXS\nT+J/JhDOh+X9HP+8rvkaWmGgk7RQVd92u82azjx72OeGYMn3L1HniMpBEh1W\nfzrNnaC2xrssE9xtf2ahlMNas7wRlAwnxXtdd8r3L0ZmYDOc36VpmES3YsCy\n89bgYCwu8YdE9ayhTvgYeOypJZBbNncMkg7R0okEjNB5dAF846bTpasXACJq\ngypL9fBImKsDBr+ZALnkwMrl9CS31+KExnZ+bQxK56dRA4/uEcRlwsbFcjou\nmZzRx458vDeTT+hfcsPJ7U4eOM5nMuV7zh/sGljXV19N27SGnOuxTSLKU8Tp\npICO\r\n=EI77\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.3": {"name": "@babel/parser", "version": "7.14.3", "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel-baseline/parser": "npm:@babel/parser@^7.14.0", "@babel/helper-fixtures": "7.13.13", "@babel/helper-validator-identifier": "7.14.0"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "9b530eecb071fd0c93519df25c5ff9f14759f298", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.3.tgz", "fileCount": 8, "integrity": "sha512-7MpZDIfI7sUC5zWo2+foJ50CSI5lcqDehZ0lVgIhSi4bFEk94fLAKlF3Q0nzSQQ+ca0lm+O6G9ztKVBeu8PMRQ==", "signatures": [{"sig": "MEQCIFAWsL/0oz+k1UBZ5qwm3bVTdIKsZpM/xAx7uEhX50cTAiAcqTdUp3c9/VyL+LEdjms5Xl+sGqV3zRHvna6BzYXV3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1608470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgotWnCRA9TVsSAnZWagAAGtYP/iiraaDZ/fS0Fbn1b/A8\nfCSTmHpBOycYomixMwbcwrS4HkW3TmNme4U+ehMEPwt8mdWmma3KkhDKtiJm\n2BBzBx095Izrg0OsQoWY5N6b5HVM2n9xJurni7aKAZhYiuWmp681DjrWGY6o\nf/Oec7pN3aFCR3D83QahyA3iP7+/LN9g2+7mQUfcoHVVmD/0Z6IqLNMhdXFo\njLvzUp9VTaRRoNMOZUtJQNFOnipEhaoHqxFE+AG1nATv+S2Ey735AvNHjWQB\nbhtRERDXZ84mhESBGGIUSwoG2AUHgfgTJ6jLslTvG1VdHDB1kgGoOUd91mSK\ncmiH/7KPxmPZq+yHHOxfUt2EqMcBaIDaAagbv8mg/qO3BVn0QHsGrFXQQRpu\nmE6STUVMpmk/apvak6VirrxIrfzqh9XUG/YNIqgqQvHrjHxM2tRNnnkZjVoJ\nyKEykTYy/5cOJ+cYoCzsosJPB6ebUQfOCGDCEArSogsV2tuQY7eA7Ht7xRcy\nAMcGPFKu5ybP2I+8B2Py2o0q7xCCcF8Emz/JjP58vrs7U29Hh8V2EGmX322M\n/cRcQLpv6FfRcOiBSUJfp8IG9JfVARPC6kyr4SlTur/ez5PLYlBQZWpT3Bw/\n1sDTfEvYFoiAEmB8H2c24BB8bp+uxszvA9hIuG4V0LHBCBn7klXZX4AkCLkK\ndM4/\r\n=Yw7o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.4": {"name": "@babel/parser", "version": "7.14.4", "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@babel/code-frame": "7.12.13", "@babel-baseline/parser": "npm:@babel/parser@^7.14.0", "@babel/helper-fixtures": "7.13.13", "@babel/helper-validator-identifier": "7.14.0"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "a5c560d6db6cd8e6ed342368dea8039232cbab18", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.4.tgz", "fileCount": 8, "integrity": "sha512-ArliyUsWDUqEGfWcmzpGUzNfLxTdTp6WU4IuP6QFSp9gGfWS6boxFCkJSJ/L4+RG8z/FnIU3WxCk6hPL9SSWeA==", "signatures": [{"sig": "MEUCIQD3BzTcw1hBmUpD/NHN97x5nvclPcoipLZS75fzdI1gUQIgBOr8VDYGARZ/xhGYanrkPN77OgD7Kngad5BQ5cHvqaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1612578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGDCRA9TVsSAnZWagAA5BwP/idzgqGr/uJfFq/qZOYF\nAZJG6aNa7auHXmV9m47ZbVpoTXKvD9rRy1npGsSWsDSoH2XB+p1Jp5k2goVL\nztcVSfhCPr7CTv8K9VOq2hBucnjLJVK5qhPKBTVCQo9Bv6Z3/jh8bNAm9BmL\nx9F0nqBfSCFdW1p0wVxDukFNEqvnaApdWL7UercZ7ekwT6RPWjqw7+eJsxxu\ngX2UFjHpZMOgVoxkuhRQQtIWASRft0LKw3GKkAp6Ebj28u/8eNAq1VGmDEc9\nooX92Ne9PBbpAxKkWsUQ/+TTcB0B7ocw3Byffy5048Y6bxUanbSnqHuy0Alc\ninQqin4aujbmwjFprHxWAPEx62c3iHJF7SJ937vqNBZBk1kME2ywHyDiogIQ\neJvfXmB2pa77pt8umu12pEvED8uj3C1i/Pr046mDR9GvrMtqzwhEUJ908wpm\npCk78QM10637PUuAUnzStX42pzTHDOrTvxDx3jSgzhuPEEG7y7LYBRuNehRP\nvwrpxFOOUqePeqylyi3RppuoiLIECd8iMOLAZtdBpFa2fv4JK/wGg1GJlgG6\n0gwHDg1TxeYtpE8PZZ+mRjedB6eUPZOhqUeQZVdpZXUQMR9kqGZ1mWzEkjpL\nQvcEweWUftVvLbDEZub6h61melz01gRHby6nMHTDVdTVsoC/BA6APQ/I3qZd\nXj/v\r\n=BxHN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.5": {"name": "@babel/parser", "version": "7.14.5", "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel-baseline/parser": "npm:@babel/parser@^7.14.4", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "4cd2f346261061b2518873ffecdf1612cb032829", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.5.tgz", "fileCount": 8, "integrity": "sha512-TM8C+xtH/9n1qzX+JNHi7AN2zHMTiPUtspO0ZdHflW8KaskkALhMmuMHb4bCmNdv9VAPzJX3/bXqkVLnAvsPfg==", "signatures": [{"sig": "MEUCIQCOMmslRctWQr14GBPY/xrLMX4uhj7Z8qfyFw23VIu90wIgKH2Hm+4xX9pdLMFiLEPa7uXB55PDRWoIwcPapnuSYQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1601613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUqYCRA9TVsSAnZWagAAaqAP/jY6vajwo6wXqt8s/KK1\nXrHVp62hAK6ulEX47aYKRUX8tgbAqOVgptYRhiqSZy36ezownB/2HKJepsYN\ne8FRHtElapvTfhfLek4NrAHn8CLXogYIF2eF6SixtR8k91ZST3hzdfAO4nN8\ntJkg5L82v/YXM9E3pgSqUGcKVwUz/sNExwJDOhLb6rw7Ezz6ZtW7HPVJ+1Pr\nH0hyWeEJeXBOVpNVptO5h0YeCdRfAFbtF9c4WTJ0D05mGoG3f4xfaVf5EOmi\nhkNE3AsfCK3bre/mPxHEPtaTrB9KPlJknSSUO5MTAmzE6ymMo+ttUykMSQpW\nNFth0IYtWWKqK7rIqQ+HnoY5GJL6D3TRK/+XzuLtOUp3oxsTVB1QUnsbfEI3\nw34KjtjWmfJcAjrQ5m+yBin0X8WL5dHBBEQjt7I9438AqLYaAJXaps/ZqAPN\niROSRHv3i5nT7NXH3Tngv2kebKEC+VY7jGEoDs+VYiMS05XsgGG1kHyz9UxZ\nyZ3dCTCzcDxHYRq/GbeKjLn4Rw187H8zRe3aV18ACSI6S2mzhVEl0sVuMACE\nICB+NYkzteiLiqPGkDeMz7fd7V2Qv553JtuCr4PEUgMRV8KIiJC6GFghKeEg\nQrioLI6smWL9foJO37xeaPG3lkXBG/Xbtg8lcUj+a+YDJ21/DvmCcN3gxozp\nOI+z\r\n=fVSm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.6": {"name": "@babel/parser", "version": "7.14.6", "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel-baseline/parser": "npm:@babel/parser@^7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "d85cc68ca3cac84eae384c06f032921f5227f4b2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.6.tgz", "fileCount": 8, "integrity": "sha512-oG0ej7efjEXxb4UgE+klVx+3j4MVo+A2vCzm7OUN4CLo6WhQ+vSOD2yJ8m7B+DghObxtLxt3EfgMWpq+AsWehQ==", "signatures": [{"sig": "MEYCIQCKJ4sV4SAPFkWIOV8xABxRjywxasHqgqV0ZESVITg57wIhAPn0fl3k4iGL0N9pwBn05e3LnMr12+7R1gGe/bvNfdMW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1602823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgx9CqCRA9TVsSAnZWagAAAiQQAIa2kMArzdYtFqwvQk0x\nKX9GljtHgQDZOY7ASLgxDg4o6ReLjXnj0bOWyCGpCt7S+jbekUexWjS/ocl/\nH7YYKZHTIMyjKwyfRh+DRTz4knscnRHSLIIJiDz1iajYTxKW6hkfnjwt8aVA\n/oBG6igivLstOjfWOmqLykcthZOSwYdt9XSphvlW5VqVRu8h2JBbbrXC3a65\nesENB0Z6S8JKTjvVNWe+4qRSkoFpOwD85BT/xKhG7isfbkC+RE8E2IecWs7l\nVX7lW2CpWbLQlBotlEYpIdkENI2qQwzre3nqQi6RkYW7YKZIUTjSxPjlZjYt\nV71h6oPwxaHMckEO5yhPRJ3wpVZYdcW0HLWrR8QOES71L591PDOjhhp8Jjw4\nTHK3olRch2M72fGYTQ+LNWMPbofHgKSEYUCOhvCZP6tmvc6ucVj4O8dI9kah\n75HCpVsF/EPBjDnfN8FffaQ2gZWIxXPo+a8Dpf3D+Z3FuVwO1ObsoV/xkimA\nJur3OzRq3pDvwqTLxJj4RRCyBasxiwlIvazaQpza8jIMrNPw4CGn/SMoTuSZ\nC99vcb222RThIbxeVlwyhr05youPHicp4MdG1iDE7KaF5rk5e1Y8O0nAzY9v\n4pE6BFkOzLvUEtq9Rfnds//RcV3eRj4E3RyUCz3rEzW8TczDQTavBDOkRHrn\n4Jrq\r\n=54eV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.7": {"name": "@babel/parser", "version": "7.14.7", "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel-baseline/parser": "npm:@babel/parser@^7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "6099720c8839ca865a2637e6c85852ead0bdb595", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.7.tgz", "fileCount": 8, "integrity": "sha512-X67Z5y+VBJuHB/RjwECp8kSl5uYi0BvRbNeWqkaJCVh+LiTPl19WBUfG627psSgp9rSf6ojuXghQM3ha6qHHdA==", "signatures": [{"sig": "MEQCIGtUu4/vtzb95yn7cJjVzp4DfgArlflsf0HhrcDN9BWAAiACTDc/sf63YnzWWom44/rgP1Fa3LCRvB8N44atcv4GrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1600989, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0Qp4CRA9TVsSAnZWagAAW1AP/0Vc9J1VeQjji61UDgw9\nscufevrvdxE/YsXDZEuO1eg+QF0TrCmmFlu+7BLmI/IIg9SO9Z50HTUCutvP\nqvUtzoeyguAPBCFFJ+aTEDfJPzJ/TtxzJ1K0ZY1xxsNUZi4uCA4BpH6BtP12\n1x850BpapnioiPAhvg2Xy/RLg6jDDqs0pp+kEZLynM6beiXx4gGXOa20l82X\nvCpD0pgK2fIBLEqNA+TMjKHeTT0YI9Lz02WUwuykmpdLS9WmWwyuN/criCel\nli0BYoTWlgsBNFnqW0/0qqIW21qx1HXw7lBNGYIbebzZj4lm9T81MO/rcxiD\n0EeJ817tQSxf4CViFNenQsj1YSlFHL3uuHzGDE7iOflg8FlXsXBqdCKDi/U0\n5lKSrfCaRjdVy4nQ1Yacj6/qxMFfEU7O4Qw9WHCvT9aJfc+SdYMAC0fvj/LK\njgtD2yUPfEO4QCWdJP0VtoRAi8R4ViGyJd6MJvmxRLyKjuPrs2s5lb3xO+Se\nD/cPZi5klqMAN1nf8AsdjeC/Rd0i97zHE++CAg/jphzv95zSJ/aGNxJQH9Lu\nnyoEzklVAoEMwmqs335hu3DDInB19ujJ3sJhgbULmkMtSa0M/mYkIWuhpCHJ\nNeD/m+PO8nPhhIlqdOtSHsB6QYyeqlMH9GaiHI80ajr9KOj8rP3Mpige54K0\nhsuN\r\n=+H0R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.8": {"name": "@babel/parser", "version": "7.14.8", "devDependencies": {"benchmark": "^2.1.4", "charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel-baseline/parser": "npm:@babel/parser@^7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.8"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "66fd41666b2d7b840bd5ace7f7416d5ac60208d4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.8.tgz", "fileCount": 8, "integrity": "sha512-syoCQFOoo/fzkWDeM0dLEZi5xqurb5vuyzwIMNZRNun+N/9A4cUZeQaE7dTrB8jGaKuJRBtEOajtnmw0I5hvvA==", "signatures": [{"sig": "MEYCIQD9NKsMvLJQv9suqIQEKoMGxsbNvvEvknoK85gkFuKieQIhANVH4hGqDutbW4cwwGXROO4F6b4AxMdWcuw/2lnXfyCV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1600391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9w+6CRA9TVsSAnZWagAApFMQAIypHVmjpV4zLkD9yJI4\naAb3eipEyasIQZPDL0VyNS7mexCx90SIcOcKluGrl++Mo5IbJmTXq1lTTr81\nvUBze4bx/fNdfSRyExyhsVR09xa1qEggPiXhPZxQTKWdw2dzmu21qw4Oaxve\nw77Gy8JSIPcrjxzD945af5CJCj5eJ5ckGS+5TLx5VZUTBiAboF+kgbFEv3YY\neJWHjWCFrGbOP2EoMglIiQNLz9EcdXJXkAqJe/51i216wnZRyOCZ1UH5eSi9\n6Rto4h504blGXJlCvXYNpSUxd8p5Skt+07gbCxxoCynUZ0Fw+JXzZ97KlkRf\nFXRk/h0krT1/jBdZTffF5172H0sve6Mg+1u1Kma8Ux0bLdUPErpdbeejAO2z\n40SA7yHxykz1XcFXBRqE+UoxouIVHoMTwmuJUmlNlC2xOgf39EImd+KDbufI\nTT0VDWaR/8gnl9cg/AtWJ9WXKE16TEPkyKFvS3wnjaQZwgNxNc1a01DKmm0r\nzZ6fnYAsffVDu+PvtIDjs900llwVJYIDTjXnnClDXPxzFcxSvzDKtieQ+h7j\n2pBztN0BxkATDJCEYjINaZzwY30NuPlNA0Zjezq9j5osD/jc2iw3wcbEMI6N\nZKpdhMx66VlBSMoVfj3u/w18Wv8k5vEKjUPWKIiBp92YhdI14ot1XkT2CN7S\ntJ3n\r\n=rbqK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.14.9": {"name": "@babel/parser", "version": "7.14.9", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "596c1ad67608070058ebf8df50c1eaf65db895a4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.14.9.tgz", "fileCount": 8, "integrity": "sha512-RdUTOseXJ8POjjOeEBEvNMIZU/nm4yu2rufRkcibzkkg7DmQvXU8v3M4Xk9G7uuI86CDGkKcuDWgioqZm+mScQ==", "signatures": [{"sig": "MEYCIQC26NAcDAr7gPgh3fMTzpECovGnVbD7B5zrwZryaNbkMwIhAMwrXTZ29i24bj67gp0bhzVxZeOYSK42Uo4BLtHvUlBD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1608037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLpCRA9TVsSAnZWagAAi5YP/jEEZAq8rn4BHlNjN+rf\nc77UGJ6ld/y7KaMC4HeFIwsr801PZNvzyJyq7m9ilnjEjznAvcovM8NXs/ex\n1Bsl0827m0A26pHltWYGhmWwJJU/wLFRCuUPSVf5xFJ4+cdDvRG+KgZ/BPgg\njtnMQvbwd2muJQuhuu1GgvKZ368OTr2ZNpLSyZtltm/qz5qJgzuKkWDZGlxC\nVRPnTWQcpWB/wJpfxOJaJF7PerprL935qV0OptvmrciI0cWMCMzt0jkbM6zg\n4sTEajj8jgjRMfc4P++AeMfFVO6okewDBlket0IojC2kwJ24xu0qd1QISKRe\nhzSc3/ItND/Cjh5bOsFFmInD6i9RPcrunQbNgMzOthZn1Ian5yMpOAaFLy16\nHs7x9u4oFDAx7ax+we0nj5UczatUYpwpSErZxah+N3u1GAWBSigzqITXRy2A\nfGaSBUBKBLPM4v9Iy5mSIte1D4wfVGQepyU0egM5c+apd/Y/KLAbU504RykO\n+XZjx+u7ZvM3rdU4zF3RBNGkwE/ed9uzP5BZxAhQUirh62C3EeQcoE6rvA7n\niIy574+2Ye8pRKgtLyktOqOvJfJVfRQmzTeH0rhSwHxMHvk6u0tKtYouL/4I\npfBony5aOPahZ2RfdRNFM5PpYUxtJWmzKSu1+kvLNuK7db9jjfmJnvLp110i\nNY6Z\r\n=7dSU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.15.0": {"name": "@babel/parser", "version": "7.15.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "b6d6e29058ca369127b0eeca2a1c4b5794f1b6b9", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.0.tgz", "fileCount": 8, "integrity": "sha512-0v7oNOjr6YT9Z2RAOTv4T9aP+ubfx4Q/OhVtAet7PFDt0t9Oy6Jn+/rfC6b8HJ5zEqrQCiMxJfgtHpmIminmJQ==", "signatures": [{"sig": "MEYCIQCYSo6dolsQgZWhrmOfyRDljkZkxohcPkUayhQ1NrX5jwIhAO22l4UpkkAqZZwoFpTnk+V1qpaZ0CdPv3Umvks7S6ut", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1632248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLXCRA9TVsSAnZWagAAXawQAIshUQ+aeMtKMUgQv6XR\ntcZSmzcLTOqJ+Ue86prq+7N7ZRQT1cAVvm/+5PRg5FcGPPoPyRh9F4gjHmcT\ngf5jFWUPL7xSzZaWDUtIudQ/TZnNOQrg1HMFeV9ZjEvlAwu/o8k1Qd+XfiFK\nwHv3sdfpEUrP6BWBivnV7RR952+GUaNFgZj0LUiBrfsSp9ApxBCdDVf3BzX5\nppemFmy2S8vrjBBpE/a57AUMdI0+4NOMTYdxFP3K877jVdooIYZ2wiRCAOTZ\nKBYq7dAZHWU5AnU+AMkPr7z4JaI2CbN0D0Pt+6ANLNESpqF+f8YDkowW6Zj8\ngbQRTgUz3jSFEoFbVkHFkSEhmJC2xerm08W8+BZehLbbGT9+fyoON0ZZIL2X\nR1jLVSRPslWwrPgxRpTx4VzBM/xe3XJUF+rpqwu0MPUp7rI5k7TGyZjdahp9\nE3rTZEBs9Vk4mU9O4xNqBvHWzNzqYwAJ543p6t4RH54ka8+LVxZjY3yh3xOI\nfTWAM4GCB/Esx8fWL/HgIP05c6awrGmD/manRb6ilgoQ9efNh0W86i0E3o7s\n/JLKOtTwofgh0sn/AjsQlvB0blGJS4g8WldZ1FKYZ4suB8tCa6dEl1whNF+E\n6NqN4+tr/LYbvaXyqwgxbXIHsg8cycqt66JLsTD8U1eABculEgE1bymR+O/T\niitk\r\n=R+OE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.15.2": {"name": "@babel/parser", "version": "7.15.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "08d4ffcf90d211bf77e7cc7154c6f02d468d2b1d", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.2.tgz", "fileCount": 8, "integrity": "sha512-bMJXql1Ss8lFnvr11TZDH4ArtwlAS5NG9qBmdiFW2UHHm6MVoR+GDc5XE2b9K938cyjc9O6/+vjjcffLDtfuDg==", "signatures": [{"sig": "MEYCIQDtgNWvxGE0a4gE+hfz7gNGK2hERASJwJZ1Oqa9qvF2/wIhAJUrsAAIb/8XioOcKu4Lx8MVg7dc6dHNe2AudFlKFR7b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1632451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEALUCRA9TVsSAnZWagAAi58P/0/U5j72jmvHIgaXj27n\nWJKTa3FD2ljYBwyWfT6OBtCJRu9Otpu0P9mtqF9sfE8n/J/m+NZ1HHhEn5/J\nDeoFaWNM0ytQYbBFWYlVWGaFRPWqFzX8PcEikKwHuWhN76orYzn9La4puQEC\ns9+PAyEhfYHg6D4O4wO7HyBUbvcAhlFb1GDtwevUhZ7xdbNj/OR9dzOQs/US\nnMvtyS0bDHgYIx9H3FwkvdFOkWDVShmeQHiwODec/+CdZk7bdK4VQ+SptKm8\nTN+oXMSp0Ra8zXiBOj/am74fLTJfwhNcum/ubSMuk+RcZ+6ZadgRB+x+TDAO\nubPCyzNtA1E1GyYX4GRMmH653FBdq6iFiSZEA3ulwD+KtRl9c/5BZUAw374u\nNwaU/HEyk9dUK7oHs3yqLk0Z1oOsTY4SQvzTeErxabUfYHbSQ7OqtBUmWnVW\nltfTfzgit4z7Q/O1IUVfzzy0avl+b6XFwJIszrb9K7Q+po8iUPYBRF2/H2rp\njScMg/1Qt09sB/D2a+5oWsUjo/X66xPVVg/3jom5XWG5f7Nn2vTSBcY8DxiG\nE9R/93RubNb3AwD03dGLpGcskNniT9hSNMBSD96Ek/zhTgdXIAUub8kfpl0i\niz6QtZNWPip0fAM/WoaEcm0af2pQg+Ts6zGfY96IvKo+QpS8it7uFN62CaMF\nwn9S\r\n=8iwz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.15.3": {"name": "@babel/parser", "version": "7.15.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "3416d9bea748052cfcb63dbcc27368105b1ed862", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.3.tgz", "fileCount": 8, "integrity": "sha512-O0L6v/HvqbdJawj0iBEfVQMc3/6WP+AeOsovsIgBFyJaG+W2w7eqvZB7puddATmWuARlm1SX7DwxJ/JJUnDpEA==", "signatures": [{"sig": "MEYCIQC4Jm1KnH/yvUmt80/fTQUPeUO4H4apUzE9O5gayOP8HwIhAKi9a/iFjpUpakCDg5xjlTdM1mDGbwf+xTzf1RIM8SvR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1633488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhE3oFCRA9TVsSAnZWagAAQE8P/1JJ8JY/xAl6ZxAGE4bP\nYx14Lw+FblR4J2OCvEl5dECciCR1IFLRVv/JMLpjzel7ZxSIoB9NDo1h6Y25\niXLP6MCFaVyHCzc1x9FMDaRvHZBTuOjxNvPhdeYk6tUXE3i8F/8ucMpvt4Zu\nEB6PX3Gy4EmpdnKFcAu4/hZgHxszR/3FsWRxBpLrBk5Us9+eDk1/LUikfEHE\nZ+s7yyFjQwgKGcXFJI5DBjtTC+8nBOn8+taUofUFO3jj5Z58Cf9dRZB3gwUI\njQK1BC8avewk4xyuArcBWqVjGjNrzWuJoc1nMoKY0vPWbIuhdrkWrDBEbrVO\nMSPI4o9iL2McI+bptjVYo8bfijALfk0xwEieVGfV0qAwbhSon2Qzvmk1Szng\n5kZj651FZpPxmLOjwyZwR+LUNCT1ETMaLEejWUOyzj4RNoNEDBo0L050MvdF\nm6ySmv7T01mY0m8tnawMGh9sD1stsN65cc0/KfuR7vx8Az2qyy+/1bFPW2PD\n41cuBdI4P0bDgs78SQaCfdBGo2VeL/i8RkFs2Hana9TldxvUgRSzqQxsq+8M\nBoyuw5J80tjKSVzjWv+P0XlOoMBzNJjn5hhVbcz1B6ckyI+6oUhROmzytgAB\nRc7iRrsi207IKHBU1xKYGf7iWw/wCsc+o3TCc/YooxbNfmuD1RvOEGFGFSCo\n9jre\r\n=Iv7O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.15.4": {"name": "@babel/parser", "version": "7.15.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "02f2931b822512d3aad17d475ae83da74a255a84", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.4.tgz", "fileCount": 8, "integrity": "sha512-xmzz+7fRpjrvDUj+GV7zfz/R3gSK2cOxGlazaXooxspCr539cbTXJKvBJzSVI2pPhcRGquoOtaIkKCsHQUiO3w==", "signatures": [{"sig": "MEUCIQCN7uRCe3yNpZISCVieUXRewTXBy/r7WDW1ET9/UdoIHwIgT2fmjYk0Grjw6+ZTlB2i9TyChzovqytPhsBqzJKURGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1635976, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSLCRA9TVsSAnZWagAAtL8P/3SpK9aGcqfeLcFX4l83\n2Vbtzj2xVKffyDmyTsDbRHelZQm6WDEKs2Va/hCwty6rYqGLqR+JRE1qT3jB\nT5Ab+hGcjIashGlCJAHHGRkNH9IxrqHab7vnFRP1Q6Yq4BeXHAZ7RKg/8dfq\nPUkGd2nTJRuJ3LMqgw6BlwaGnP/TG/VYmNEnAJqe1pkUhzQrCwtgS9Z+FRfw\ngDOmMIDTf3TcMHY01By6wRFpvpr536lkpJ4l216HchFTrvhkz5gLdjqwHbEK\nYRocrg0PIMSEt7bQ9VKIhXU6WnrI2YuLChffTO7e61pVfi1+b8UfbX/ArD0Q\nSobgoGLeJZAa1Y9L2oJlXjaoaOgUb4Np09tAnD7EEAOoaN80j2ktIIDXQ/fK\ndBi5E4quA3jtXAapoxL1FqVT3RJSeF/tCb+QY7KUBRv+oPm4yMoGnQgwHZad\nYmLjupuBZH6l6dQT63cL5U3wI13PRpGpnREu6aJ5Kh/b4uJFb769uBcr23Bw\n578Dj94GiuEcNYQGOmpbuFgH6ArIdESKVu5pD7/9qx1UlQVkfetLpywdWbot\n009Tn5I/3rzmF98ZczVUaJzFHbJd0ohqWaTzw0httN3KQeO6JKqlnzN5kJyE\nFJc87S7SmSogbgathSFzAjvg+Px/u9rDx4Xu0Y9t8yPfoDyL/9zv1Fl4m5n+\nP3ev\r\n=6u7T\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.15.5": {"name": "@babel/parser", "version": "7.15.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "d33a58ca69facc05b26adfe4abebfed56c1c2dac", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.5.tgz", "fileCount": 8, "integrity": "sha512-2hQstc6I7T6tQsWzlboMh3SgMRPaS4H6H7cPQsJkdzTzEGqQrpLDsE2BGASU5sBPoEQyHzeqU6C8uKbFeEk6sg==", "signatures": [{"sig": "MEUCIA3N40QTetPxCE+jLIBXe0C7QWAYotciqRJfdH3RQhT9AiEAk3oqUCW+Hng/1WJuh/NZEymwtxMiTlSWXmdk6+fENLc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1637754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMzVHCRA9TVsSAnZWagAAKZwP/RDzLNVJzX5yVvmwkHh5\nWX9ZpZTqNxQyZarCLcMUMCIqM/auNkKotZWg4WD47B8S8Xdlb+8J3BWB6KS/\nPiq5zZ8KOg7wPVOGT0WkT8WLmBB61VG9SOj3L1+e3PgUb1/jlyLtiSARI68i\nhoLKQVz7YfAYWyqStFh4KyolwK3pcms/m1IpuxP0YWSWbvDal0jGIHpMN0Eo\nXjYnKIO/pj+U9oJQNJLc9pVAGI8DhFAZbASnySjuncaiI7DgKnooBnt3WjKM\nxlYQaLofiaFrrbLvPfFncOnp1wHD9AsJPk4QY9g6jlM/mGFMiJnEWghI9udv\nXzkK9rN1gkN1CVfVvGrUgnL2s3E4dBRmtGzKYft8y0gF+YqaFA3DQGBw97e0\nbv85cTHaVOkgIcYoM/yjKxu+uTNOKawPR1SxTyYCjN516jXLlfz/bqqmtKus\n6umuU9TWP+Hc5yGtAANaa74XjxCOic3CX3BVjXYIB7Ebk9gCTu5AplHCZ3kb\nIpNBKFANkESk8MCeCOxD0iHLi+dYWf+0qrqeB0cp/+8rJJdq1Hb0xi+CaTnK\niIYFF91CFFKpekOMJIvlowYNARVd+hJRV2JWZl+OF2xDIAvoRF7vQIv1SI4L\neshCMy3jDEC5z6o9eEHHENPVEIlQsmryqVajzxOPGIj9oiza8CkD1jczhxOe\noziz\r\n=0X26\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.15.6": {"name": "@babel/parser", "version": "7.15.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.14.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "043b9aa3c303c0722e5377fef9197f4cf1796549", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.6.tgz", "fileCount": 8, "integrity": "sha512-S/TSCcsRuCkmpUuoWijua0Snt+f3ewU/8spLo+4AXJCZfT0bVCzLD5MuOKdrx0mlAptbKzn5AdgEIIKXxXkz9Q==", "signatures": [{"sig": "MEUCIQCn8Lj5La/i14gyxJ1n8rIv/n5cRVOt2n0OBL8RtCP93wIgOd5RlQ6QOcCcZedfIYu8xtrHdXPOzU24NQAEhnVbyjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1635565, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOmHyCRA9TVsSAnZWagAAi4IP/1kthSMo8oSxUbLj27B0\nJKwEkYEGlXKpgxqDr7Icl5CEGQ/M6hxEZunEPgA4cQs3Y8kveBWfMo7gVaws\nImGzzqMORMvsnLbsN8oYrGD6E2EbKrT4QZSHJCg7V+0Ce14Vr6+JAfD0SzzC\nwoM8HlT7rP1wD0fdmC/T/Pg0ahjITzc+sknALEJgEqQOkQOxXBgOL7O2Ud9N\nCUr7eWb3x5v7epumklM3bfprqLASy8YmDMj7Sv/rPCeqVF1pCJsA6dEzOwVN\ncehfkPkZtqkbyWEltmHVmjJ6ZP6iqrpqZQDTLpup0TIu3Xp18MDAqn7ObL79\nRuHYKZ1sla9k8ETqfhUSPO/2BRKK/IedbrUPXSZ9JMUbqeuiQ4CiqYBp0bFQ\nxZ/Fi9qrEi6JaZnQ3DOJfHxoNdKRpYQDmOw3GmtKCrVqfpJvgiMcbJvHUSIL\n7GMq5nOSu7+O3YrLCM6UGP4VoRGCuHRoDGnqRWbfOVmmZCnKOvEcB/u6HURS\nvMHizdtIKl+apnVO8paSKvsRrOy862e3cFvB37YnhPaj6LBqj95kQhBK6e9F\nlkj+052FXW7hT77unyNcz+4HzngI+pJxyILHQqq5KPHWP8EIZrloUkDE6y2S\nIWAMe3q1sPGroj8E2BMQGlamdnAyFVcMy3OKFJ8nR/yGoM/yriVJHeRYmFS7\nhtaQ\r\n=jFBG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.15.7": {"name": "@babel/parser", "version": "7.15.7", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.15.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "0c3ed4a2eb07b165dfa85b3cc45c727334c4edae", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.7.tgz", "fileCount": 8, "integrity": "sha512-rycZXvQ+xS9QyIcJ9HXeDWf1uxqlbVFAUq0Rq0dbc50Zb/+wUe/ehyfzGfm9KZZF0kBejYgxltBXocP+gKdL2g==", "signatures": [{"sig": "MEUCIQCrlHSfbU5kH3GOWKVXxyctSoB1W4e6f3Ajs+I+X1ccAAIgFYIZxX6uSenMbm3joqWIwb7v81KCK1GQyN99jvMKbvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1614099}, "engines": {"node": ">=6.0.0"}}, "7.15.8": {"name": "@babel/parser", "version": "7.15.8", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.15.8", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.15.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "7bacdcbe71bdc3ff936d510c15dcea7cf0b99016", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.15.8.tgz", "fileCount": 8, "integrity": "sha512-BRYa3wcQnjS/nqI8Ac94pYYpJfojHVvVXJ97+IDCImX4Jc8W8Xv1+47enbruk+q1etOpsQNwnfFcNGw+gtPGxA==", "signatures": [{"sig": "MEYCIQCf22Z/A8U0zbyrNJUXJ/pGL+/TCL5qgUHwzWqo1QpgHgIhAPQ9HDZzTWkIFqNRMJ2pqrwgudKfcAVALmDUywD6LGoo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1630102}, "engines": {"node": ">=6.0.0"}}, "7.16.0": {"name": "@babel/parser", "version": "7.16.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.0", "@babel/helper-fixtures": "^7.16.0", "@babel/helper-validator-identifier": "^7.15.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "cf147d7ada0a3655e79bf4b08ee846f00a00a295", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.0.tgz", "fileCount": 8, "integrity": "sha512-TEHWXf0xxpi9wKVyBCmRcSSDjbJ/cl6LUdlbYUHEaNQUJGhreJbZrXT6sR4+fZLxVUJqNRB4KyOvjuy/D9009A==", "signatures": [{"sig": "MEQCIEcMqnCgKtkZyWxn6o+27sqz/XThdsCL0CdAfJjSQmVxAiAHtVz3p/WL31wZvfHMSjGAAGnyyONBxMAfWhKw8pJgIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1651921}, "engines": {"node": ">=6.0.0"}}, "7.16.2": {"name": "@babel/parser", "version": "7.16.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.0", "@babel/helper-fixtures": "^7.16.0", "@babel/helper-validator-identifier": "^7.15.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "3723cd5c8d8773eef96ce57ea1d9b7faaccd12ac", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.2.tgz", "fileCount": 8, "integrity": "sha512-RUVpT0G2h6rOZwqLDTrKk7ksNv7YpAilTnYe1/Q+eDjxEceRMKVWbCsX7t8h6C1qCFi/1Y8WZjcEPBAFG27GPw==", "signatures": [{"sig": "MEUCIQDZO63JIFbg45RhqC8ktq48YFmu58g5BSuG6bejYwOSuAIgKHShr/ywzixfacV6f/6N9qmQCTZbrjmu4OGmabxRRYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1651921}, "engines": {"node": ">=6.0.0"}}, "7.16.3": {"name": "@babel/parser", "version": "7.16.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.0", "@babel/helper-fixtures": "^7.16.0", "@babel/helper-validator-identifier": "^7.15.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "271bafcb811080905a119222edbc17909c82261d", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.3.tgz", "fileCount": 8, "integrity": "sha512-dcNwU1O4sx57ClvLBVFbEgx0UZWfd0JQX5X6fxFRCLHelFBGXFfSz6Y0FAq2PEwUqlqLkdVjVr4VASEOuUnLJw==", "signatures": [{"sig": "MEYCIQCAF5Xfv2aNsLxaTWmBd0XqDJwVzIq0jWVi6W4qNn1xrQIhAM0LgZL2ORgiK2C0mjuc5cBHvriNy7dgSJjOezvsGmLB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1649971}, "engines": {"node": ">=6.0.0"}}, "7.16.4": {"name": "@babel/parser", "version": "7.16.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.0", "@babel/helper-fixtures": "^7.16.0", "@babel/helper-validator-identifier": "^7.15.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "d5f92f57cf2c74ffe9b37981c0e72fee7311372e", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.4.tgz", "fileCount": 8, "integrity": "sha512-6V0qdPUaiVHH3RtZeLIsc+6pDhbYzHR8ogA8w+f+Wc77DuXto19g2QUwveINoS34Uw+W8/hQDGJCx+i4n7xcng==", "signatures": [{"sig": "MEQCID1sdcdDnh7ZAq9IUi3zoYjBnogJUyLSegIMC+Izdp5NAiAH5TVayyt8VX0cNm8OtuyN9sKhJb4rQ7wLfQb8fxBZSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1652803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlDS3CRA9TVsSAnZWagAAhzkP/3Myb6HE4Px3wTXgI5Ia\nc+ieDTz925ux0jrGLwe5jmbpeFPD1e6bTdYAC7WQLZDD/KaW7FlDnXTlod6X\nxVCAutjOTSA9q9qOX66X5UTFoAaiM6PStnhqxVV798y19z1jxKPRJVb0PF8v\nSX3+aB/si4iJlWkAn7l0AQqNwMfAbH9FSczwUUWySIhIejz2pwHAZJWphyxG\nP/O/0YLAtxm3/VYHEHn88kjbajNNaIt3bG/kHtXPsyKu802kVGwuUG8nTCph\nR0v1T6OZNVNvqWk9yNcPot+f9/DWBTjmRNZcEPlgg4FB16lY3fB4uLMyFjA3\nKoFdq0saznPe9KfdfRla75Nr+nYM/PvfrdKYS1xbJD6H+P2Pa90opFr/wC3S\n0ZFWFI/P1hBrTdHCbFh+837nUSBaqaXuiqnEocBB+0T0erXMlLRDFwQckVa8\nropWF1JRv0o12sJq8g8qGdZdQdjNrzu3CmerDXkNFfChcRR48dQyqQcy7cUH\niY7sVBg26dHSxqvJ8E66u6nZCjsCD7TQQi9bHDVX8kB0iC81AjBq+gjemY0E\n1UPVpCgZX3XSKOYiZCGnqdBMmCs15oOoMgx21HFRyBIYE76AJNha6sm1OF0A\ncWTrKrPLNe8TMW+fGmEQctkWs13wsiNJPPndSPkmNxj6nbwuQIKqnQ3tps5N\nC4lM\r\n=EW6p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.16.5": {"name": "@babel/parser", "version": "7.16.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.0", "@babel/helper-fixtures": "^7.16.5", "@babel/helper-validator-identifier": "^7.15.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "beb3af702e54d24796341ab9420fb329131ad658", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.5.tgz", "fileCount": 8, "integrity": "sha512-+Ce7T5iPNWzfu9C1aB5tN3Lyafs5xb3Ic7vBWyZL2KXT3QSdD1dD3CvgOzPmQKoNNRt6uauc0XwNJTQtXC2/Mw==", "signatures": [{"sig": "MEUCIQDyG5lKy2jVcc7bP4KxnnIvF2YtDoP1swg+iO3F27jvwwIgFX0Dk2P6BCYnZ7r5kQ6zHCo32XxXdyYsUnZCnpDt5gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1668741, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8jVCRA9TVsSAnZWagAAzFcP/R+5/aVVysGpXJSTaha6\nzQvkQjok9WnjMeALaggVZkHh7oLgcpmOLFJpRmi8Dz7BFtl8Stvs5C8nfY0J\nVfMKKsRDMyV+9XnWm6nztrUmkvhu/JvNd8Eu4GWnRtBr1HfhIvSuzBt40yrA\n6IZ/Bhq5XB042O3+t0aye6upCmtjC1wKAAsqlOpXNm0VIAh5PT3bZ7C2IqZw\nQFyLA7HRh6xw9UETHybVr/4AHYz0SLReP82QaQlmUVn/gjUoLYGzMjBVi8h2\nMdE08QEaWPKHFyPPptCXuRv77EnXLMlak4zC359Jm49UeepG+6WE4UaaT9jH\nNMRH+mGy5Et3OcNAqMuCFkQgcX+EhHe2O2Djg0QxB0SzfXWaQkcsx8O2f4jF\nrPdSuRIvKZjCiUXujy2uZ+I8By+DL/MlYMb2XHJUonnsefMRbecqwqjEOIsH\nZU9nlifINkHuTCyIXDQLw/87Ognnmud4Vg0vt47SixRE99CncR+m+bAIwC3+\nwDYDPu9yNITMqb3UpBscggaNA3kDNbWVVsTKlI3tRBLwbpljCsaLs9uKbNBz\nmw5K/a0ZE6iSOm8X65VJ1Td0RtP5MLJmWt15rkeI57YVnn0PC0PjHwm8vbtp\nBYPalkfNcRFjqo7v9V9v2FUECiF9YzDH++XSbTcPpxecxeRawGxz/lmmAMEL\n8raw\r\n=kNof\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.16.6": {"name": "@babel/parser", "version": "7.16.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.0", "@babel/helper-fixtures": "^7.16.5", "@babel/helper-validator-identifier": "^7.15.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "8f194828193e8fa79166f34a4b4e52f3e769a314", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.6.tgz", "fileCount": 8, "integrity": "sha512-<PERSON>r<PERSON>ujcNuPDnNOY8mi383Hvi8IYrJVJYuf3XcuBM/Dgd+bINn/7tHqsj+tKkoreMbmGsFLsltI/JJd8fOFWGDQ==", "signatures": [{"sig": "MEUCIQCXPJIyBZbThoIHdzIQMvL008iZA6VBkRPb00VCfyB+WAIgeYFdnTdoLXuigrQvPyclDr8qu3lEs0+ter/2zs770g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1669018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuSsjCRA9TVsSAnZWagAArRsP/0czjA0YdFwPRd6rhG8j\nBsd2QRdp6wuwOf3cFFolYAoQC+MUAErLbKlQQg52rm7fvAYTT/xTe0S341To\nnDEeDRi/iqlsfQxbpwFwdDENsSORh0cLW3x4jWy2ZVv0StLGA7YKkZXmMKgd\nEBC9z8H4wXd6yygT5lg0QEPrzBqDI5IeYUtdIRlwJXE+65RuazjBmAW6HYSF\npYZpPBmtwKko+CgabcovpJ1lXefq2FKJ+qEG5a9r93RrYjsEsSAlLg96nxHD\nJdZwzMOz82oukTGIQcYJ3eWB9DXWsHtCYeq3JEIn12gpnNf568loXSUCYpqE\nqKUZV5oZHLX3x0QDIbt88/ooZo7jd1AIc8TGW3DnqQ2JvfCZEAuyb/vyoWxk\ny4TGvj0Xh3pIjZ10sj9BfKfwacTBPcPX/GL+6UHja7+c6d/1EGSfRz+74xse\npTVDSmojZXr3gB+3IHKjC0m+ZxiPM8fcDDFjXwbo+mUEr/VzWcDgUDiE7GGe\nTFhcCKK8zb5NTf1Dy6wuRRDusyzNGDRe3hqOamgYOWzUPCsWWo2ItGDgUFUc\ngrEmeyXhNXLQiwTFIcvp8Int95CPdAnDsLvEzebOCEHgfavfvd+21iO1svRG\nzildiMXf9Ej+4gBA3PypZ9H8qnI8YUXJyDP/tdJnDpcbym7X9YwtjeahFVdj\nNKqW\r\n=kXpk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.16.7": {"name": "@babel/parser", "version": "7.16.7", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.16.7", "@babel/helper-validator-identifier": "^7.16.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "d372dda9c89fcec340a82630a9f533f2fe15877e", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.7.tgz", "fileCount": 8, "integrity": "sha512-sR4eaSrnM7BV7QPzGfEX5paG/6wrZM3I0HDzfIAK06ESvo9oy3xBuVBxE3MbQaKNhvg8g/ixjMWo2CGpzpHsDA==", "signatures": [{"sig": "MEUCIQCaqWTsjnbL5LW+kxRbId+Mbu0jNzXRG5ltjp9CvJfAKwIgCx8QcdbGwp/S01Vj8ssk4lwIaQHoRWMwLviOdCtC+f4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1669144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0CCRA9TVsSAnZWagAACsgP/A9Mo8deJBZJ0RY5wxZQ\n3tNG6VYaG1r5tH2YgeiFjwDiNchtUVNbxDEsSaHvH3J9M+LSpSwbrhsxsaNk\nbvTuYhbhmawP8/dsq9DXNCnk3b9okxuaBbvZZTaH4eN9JS7wC0X2Fb5NKGXe\ndpf+WLWn+GardUFbNPrtkZ7OYXnS/6rzWvpnT2zfCS0DzMcWLz3FrmG3wM+c\n6BNG6qAxAEg3uNlIsO4hm/qYgjVgYeGjktBqMTVARmHDjisah/9hZjs7wT15\n41+1Q7NdQwuZx0+dRjAXAxWDwB0OWr5HdwRMYd/L6Ww84+jAaJwVh2qWkUkN\nw4tNhodvWN0hk42osY3r8o4RQw6Fnk1SD82DV3dgNRdzPmgMj4FJOemtEIHT\nRllTC4uxyThrinF+qbr+/yzk+AK9ffxVj8m/5WnNS4v8Gc+y0oBu21RjVW1x\nCwGveLq0q/KwnVu1W83JpA8Cv0P+S4PsFqgxdLq+pNwxVJO37yukv9VMSRJv\n0Q49wfRMdJAN77VJTKy7A7J6GXnk+i1mUgLuSsOGgzNlI2DinwBuUDWrvPNT\n8A8OyUw4JusZteO9G8/IgoE/qGm5jrBldY4/XKTM+kOUgb7p/xE/cikL3c7N\nPfXZQvkOi/Vi6CGdwYm7BLSV4UcO1reo1J14JYke5s1pM9tx+9hLWur3fPaa\n+vno\r\n=lzGb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.16.8": {"name": "@babel/parser", "version": "7.16.8", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.16.8", "@babel/helper-validator-identifier": "^7.16.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "61c243a3875f7d0b0962b0543a33ece6ff2f1f17", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.8.tgz", "fileCount": 8, "integrity": "sha512-i7jDUfrVBWc+7OKcBzEe5n7fbv3i2fWtxKzzCvOjnzSxMfWMigAhtfJ7qzZNGFNMsCCd67+uz553dYKWXPvCKw==", "signatures": [{"sig": "MEYCIQDB6Fl8eOyBiMDq/udskRwZLoHMEEUOoFTDaxpD3DQg8AIhAJBCEFFPCXl+AAJz3dRM6d0VeL8afb9HgSwu2FRRSu5J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1669968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKeCRA9TVsSAnZWagAAHoMP/RGerJJSwGqkCNJD3z5s\nwtzisUwUg3zBJPNT/6yPB93cDn5z0hgyiHCcT0uLOi9Uj3jXqxOUHwfusR+J\nskTkNbnBkAySkh2qd71vv/pC9eefTDrsG3y50gIRPfQw4hpS7Y3WG5MY8zoz\nzXxNFTvCaoJHr5wVMdd5XmA4yU+ZTd2ieevr+5Y27dmheeLGrmH4gTDIYgYd\n0mjPHMsiYZkyEO7sbz8CsFuJmb/fv4J1rUOhGy8MF3LH1Esi1luv8VRVj5c9\nlNFDNWs6bQwZHKDCeP73M5QhKjjAuHBkgaGI36LSJikbNVsLHzk5ScEEVsjK\noz/k/1w+xOYAt8qdrnpbTL0JEyl2NCETO25WSiQ4z9vf0sKfV1rWQ9IQVEtc\nalLYcU97uHw2Bfvhl69YXst6UgGDslDe+WVHVUT3JVrK2YHqSTL+1ynwIgZE\nWNULEuBm+Cvo2L3LxC33exU0UgJPU3Ny7DjD76rmkoQMZD2+E7aqvVMe0OON\nxTrycZ5Upuy4dEX3mMxmBN8BMdaofWpswF0Ryt2hiYGA+VOqqnWglpTuzOdY\nnQJYB/2Ym1d+qpJGAC3BpdjcSgf1BmhbUWcddI85s+qSEIoQhuv+kHbw5MoV\nyUDaZKclxIws7NjoY5eEUXTci5PPi+BnvZtDTGxfBcaY7NPyx4znMBIT0arK\n/Ie5\r\n=t60W\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.16.10": {"name": "@babel/parser", "version": "7.16.10", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.16.8", "@babel/helper-validator-identifier": "^7.16.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "aba1b1cb9696a24a19f59c41af9cf17d1c716a88", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.10.tgz", "fileCount": 8, "integrity": "sha512-Sm/S9Or6nN8uiFsQU1yodyDW3MWXQhFeqzMPM+t8MJjM+pLsnFVxFZzkpXKvUXh+Gz9cbMoYYs484+Jw/NTEFQ==", "signatures": [{"sig": "MEQCIBAIpGe2fGCkkAp/Y6yC1d/n6nmMNpo31gMf48E+efHDAiAFXLNvU+r2c3cpu0BV6+jrHOR3rxuQ7tZvOfIdr8reUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6FrECRA9TVsSAnZWagAAQH0P/3PRGtAX8Jn9vadoIPwo\nORpvwrHNw4b18VRZeIuzdzF6xkXKan1OipW5BN9/xeEqGX5NEPglG9pP2NR9\ndrtl7iSRNBpzjIqwSCxXaz0hHQOpI1uAXsOHsojmGEVOw75UCnyfmSe8nxgY\nO0gDYC1EpRGVDCjwJgD5iWNOAPbMLzw0eKaZ6rtFmq1xLth1xaY0OxESam+l\nn6SDNo5WzEoNiAP2kwUrcdVngzUAFVfBoHqdRQlKtXnl85qYJFxyOC2yfPBy\niVrehQfe+P6q4Q201iCQvy+xbBquSq1PzpqJnXV5drHPAbo7ZXZRIYfJBtJ/\nsLqrWfQgFAeI0RtIbd1iBgSKMg6GubQbcyTwiko2o4Mu++cBY3YTB801+orC\nV4sq563W4CjYmi8x0twLAjKdiqeKOEGORu1eUbCrI1OVMmMETY6Iu5kwm4Xi\nBD74QcLTLYFIb1CkXBORvHSYoMc1AuA+/812pdcRaqz3wAefIpm4faH59Jih\ncr1+YEEkDt6caV902w8AkpNmasQik9MjMnbyciKjDA2dRdxVGG8FxZgOSPJd\nnRiOkj1YmvBK1JAbjpblKMFzNnLS7W5Tc6ZRV+4q7zvOjbhk2WV8PLcLUVA/\neeC+4Cz38hZHDXbaKIYVeaixP76J5CKhSAvbP97DbW6lMkCwJzbAYuYl2dmK\nRQAn\r\n=h29y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.16.12": {"name": "@babel/parser", "version": "7.16.12", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.16.8", "@babel/helper-validator-identifier": "^7.16.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "9474794f9a650cf5e2f892444227f98e28cdf8b6", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.16.12.tgz", "fileCount": 8, "integrity": "sha512-VfaV15po8RiZssrkPweyvbGVSe4x2y+aciFCgn0n0/SJMR22cwofRV1mtnJQYcSB1wUTaA/X1LnA3es66MCO5A==", "signatures": [{"sig": "MEQCIANW7uzlh3UGIcdCky7TMXPr1JY46rS5aQyaIarxkpxjAiBalf61gGBpQtzXf44h3XGZx/wxQBlTVpvrtHv0YN47pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7B5vCRA9TVsSAnZWagAAhpUP/1DCuot+V8RAmD0O8gdh\nAv9qg40MZs840l6yrD0KBlIcqWnFjAfeJmrjKfWYOugNWOtIcWfvxKXI8ooz\n5iCzAVgGat38f2D68obRNPBsNFTNmuB8/Ibl07qT66XwFZwScXkeJH5ZKbxE\nb4L7dpxHWTSWOU9poEyPqN7byCoHMFah4071xMn8YbkvoTegg7//2aXVBguM\nKOu76Alss0e/t8qo0utMNMFq8P37cJ+IJsJUy9qpHoAPa0BOtHNDND2HQk9/\nGIAhWcgcwzH5uMbmBiRk49cgho5HDKIrTrXvy7wzCcezFrQSHzRV5/x1EP55\nHdI9HvvHEfNzbHuZ3J98ZBXQDjthWMj8l1d+fkI+KzceS9CJ5ynIhmKUdPxM\nLr/YC33JLBDPJLqfX27ykDBcayZX1sib4y/ZONq8bJARrgOjNy8b0Dz4Q3ga\nUQ92s13cRYFU8qtS5Cd7i4QBbUVOs185zTaAUfdC0/MDWdLD7bep5MpPGRE2\n1qvTlY0sGEbAmFFotfm1V/5YVPYgy6bp5p2P9AfttMZDAGNllBLHEe6QKr7m\nzzP2C28G9LXszNYdjtE+lEkl0z/3NXitOF2bU738EbsFlrosnoMmgGbb2snR\nhFjz7ERoP1OS0DXBm9Us8CBNMVUR5tsmCTg5JeXoF55j7dSNte+LnDKvwp6G\nM9wY\r\n=gNnY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.17.0": {"name": "@babel/parser", "version": "7.17.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.0", "@babel/helper-validator-identifier": "^7.16.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "f0ac33eddbe214e4105363bb17c3341c5ffcc43c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.17.0.tgz", "fileCount": 8, "integrity": "sha512-VKXSCQx5D8S04ej+Dqsr1CzYvvWgf20jIw2D+YhQCrIlr2UZGaDds23Y0xg75/skOxpLCRpUZvk/1EAVkGoDOw==", "signatures": [{"sig": "MEQCICJwNbdlquk45ZLSh8etzCheDfOqRRG7pGQ/YpPy5AeSAiAd+yt3KHY1DLuPRyj8mLkVYJWBlSdLQZvAUQVzIiWdBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1706476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4OCRA9TVsSAnZWagAAMlgP/08KU+NTpScb8FrnO+uL\nv8MRqSksF6mjZcYg0QE4hebm7V11CeticYvGRCB4GloBXBklpnCNOs5EZFdG\nNofignK8mvAZY47cjBhKp/VvKuv1Wva8ixX6rcXgrHzKV5K2dDQ4OL9QOMUw\nGWUGgFGfF2efrBH2YkA2UZCE7sh3CryRKo68rbIi7v5AKkte48Gh5Ai0iPrK\nir+dvy/FYzX/YCqpTIpc/4OJtdAOnfB/pUmP9ne4ZDer7RRZtwaRhLxmcvfZ\nnlF5YElj1rmt5jaiK0O/15lZdTBS7Tinm6H+x163XHRW+3z2e9nWXGKWdnUQ\n+GWhQkt6yL0gcvrHeS+BKBPu8VpSBf4KTEMlGYr+K2NIC32+Vy/n5ypoW55a\nQF1xcLGOazw9EkbG32PgckliSdMIlIh09jlHGnPK/d8XbaG/w2v5LdbCu4wW\nEKv5SCe1lxBH3M+B+2zP4iW/X3reo8mjfoi89U906mVlpuotGMcss2Adj8rz\nY0AdSKhAOZ950Nv6AJih/0z2h6wZaObZKRFGQW7KVi7SEwFfvNwjFb5Qccy7\nRDYvjQB3QlVqIC57z2zb7eDxJ2c4wqXLtsZvpIISKfWsW5Jo3eflDCRmP7zA\n+pqH3LkrxXxG+pWpUJtV87D1cQ+GQ9v7tWsGkBaAolon4qGKny4WZn4WFXoq\nmXeZ\r\n=Ydal\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.17.3": {"name": "@babel/parser", "version": "7.17.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.0", "@babel/helper-validator-identifier": "^7.16.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "b07702b982990bf6fdc1da5049a23fece4c5c3d0", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.17.3.tgz", "fileCount": 8, "integrity": "sha512-7yJPvPV+ESz2IUTPbOL+YkIGyCqOyNIzdguKQuJGnH7bg1WTIifuM21YqokFt/THWh1AkCRn9IgoykTRCBVpzA==", "signatures": [{"sig": "MEUCIB9HOa/S68INi6FWMU4Hbm0bn0q67sUJgD6eQZa2T+LbAiEAkr6CYMADdxlh9GXKTA9N5q21muRS/NKkDzCKy0vYMbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1706519, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC8pTCRA9TVsSAnZWagAAV6QP/irQPxvjFXRRN2Jkr9LR\n2JlHLtloFxS9TEixTgrmVTGUcj2kzhplv8o6+R1WKThbfSUXmLtl4mGIM3mM\nAENcLSJ8Qvlugb8wwLnVTWUJTS5WqtSMUMndQNY1yJoUDwo7ML7g2xvNen4r\nLp4xMGnFuCzLn/tIRpgHxvosaFXAjCmEiKb6eKsbszwu0DvGdCITcCqxKTlE\ndPVYkLgLwIJEsw8pPiridAfCPCr7/DoHESN5aUW5U2BuGM7uK++HTcskCSW4\n60eJZTXa9k8wyO7vWRztOsPNo81HnI85U6rcijqLi3xDUXuLvflge/3+ywfD\nvfWJutj74VfmsdBwDq2ZUlRq6E+l4mZ1px2HnHPdYTVFSFnUFfm/wRAHYKYv\n3OxDBHWPyfmgf9HWJEGjlYoV7LgJRCsWqpqCKYNSOH8AvaWdnh7+btcSWHn0\nBiP67mRATuy+ysZbxKDBtNHdcqI+AiVJZBYdFxemNxBGpr96CxYm0nELZg6L\nltqwGS9ivvX6MN5cxzVUSDu/+n4aAbqBvtSuki96InavyKkeAsY5xKJ3Q5BR\nfiFMrESZGVsveccY7zvffTYkRVGz1oQAia60XPRb084mVpfsgBJ3Ds2HA4Oj\nQpFcEAnH+dplUrlBAW+/2d+4EBjlLdciDEaUssMrg/+FZE1lM4NOE6mxRFYz\nB3SW\r\n=UckR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.17.7": {"name": "@babel/parser", "version": "7.17.7", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.0", "@babel/helper-validator-identifier": "^7.16.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "fc19b645a5456c8d6fdb6cecd3c66c0173902800", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.17.7.tgz", "fileCount": 8, "integrity": "sha512-bm3AQf45vR4gKggRfvJdYJ0gFLoCbsPxiFLSH6hTVYABptNHY6l9NrhnucVjQ/X+SPtLANT9lc0fFhikj+VBRA==", "signatures": [{"sig": "MEYCIQCWK4Z47aKJ+auMHGDlKNR8sXCDzZTw1xlTWVivM3FsRgIhAJfgGvceneeojNnQUpdHZQ+lKhhD3TmFrARzgX4+RgD3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1764250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL3Y9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3iBAAnyrzWUnry4C5OjLddaeWJzjdVFF6WXTmUmhE0sKX5juyNnwH\r\n5jJToc/T0VocSlsluJ5NKLQdvFCk1Gzvcl7gcRIIoCyUz4XxzwjjmMxwJoQm\r\n82hcpSOA3epOFFVR+AdY7o6diw40Ao97aFHHhtpGZDoDUsNXVZqPW5no61wf\r\ncqqDuLQnTgFQ+zgys8Nrk/EocpDqtTqblyEYXLQQGhYd1Qp3pE7tXagH+Vr6\r\n9hkRnLo541MT2hNHIm9uPQkF8VAQ/T0R4ybbRZglpN/shCClOyBjXDbIMLKP\r\niCdj02YrJ4zN2bvmG5fawixIszKPulFMvDxDgAL8eiwFGzy6KPRZAEgQ4kbs\r\nAIGM5YKiMSHQc/In7jC9w3MZZhA9IpZxdcayWLulMQqwPg/n1TDKaPYVz24B\r\n7hf3EkppiMCQjbDaXEQjt3ohf4Vim/2bp+oDCs5ZKocTwBaSHmAZt5gx3eeM\r\ndaUy1UjxFZhT0lSkFVu5qVGvf0KaysLytzZnCdlNUh6haMmehUmW1zTX3xdw\r\nDzYVoqKNVdxHW7nSz2rbd5zNEW1WV4K5JhuUvlfcyTDqjVmNrvykW7adwvYP\r\nQcH+V68ZH4Q4wEGIBjqiOxToaXeUTqpsWgabY4cPikCwMn6gmo49bhhBRfj5\r\nnvGQ36cWdrCppVk/mzEOk7QXS0bdRd/kRJE=\r\n=KdBU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.17.8": {"name": "@babel/parser", "version": "7.17.8", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.0", "@babel/helper-validator-identifier": "^7.16.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "2817fb9d885dd8132ea0f8eb615a6388cca1c240", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.17.8.tgz", "fileCount": 8, "integrity": "sha512-BoHhDJrJXqcg+ZL16Xv39H9n+AqJ4pcDrQBGZN+wHxIysrLZ3/ECwCBUch/1zUNhnsXULcONU3Ei5Hmkfk6kiQ==", "signatures": [{"sig": "MEYCIQDHLLsioQicfFuC3RnyNTjZTNGU1Wlzu5H4qjwLyV1s1wIhAJ3JFFQw7jz7GUa/vjHg5OFezET0rlKyi+BF2WNNFDhk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1761568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiNOwNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrE2A//U3asvqYCEX7wUPbDM9iYkjFi1C2USQ2HPi2b/ezyTxr07UMu\r\nzY11YMOyyNruSH80nvQScQqtl2ph1pmBAm9bAIRnDT5uGhWzgnnJXvLXOfzx\r\nZxTUTzeQk4opato5AMPPogzYqmoWmSYAwVfkZoygzZCNgLKcvAY69256clfh\r\nN6GYfbY1h4UIDfyLK65vnc5QTUmphTRuQAFhMZvF1WqjINyfZ6s9BlDqmK+K\r\n5k5Tejk40egTQP+cVUi5Fw7yxP/KO3sLGrZRWUz3H4p3tEi0dC0Zi6zWmnnR\r\nte7nmpO1ypBcpLDsaDp9Y32ZdxF9pBt47GGJrnQJ/M7bVTw43OPcy0ITsCLS\r\nNM4Iq3gnEWJQ6uLT0hWYSl4EnfodXmduMJtuHKzhJiIF1nw+K4DSIkICEcwc\r\n7Q35tPXqOZ7sboIoTepkLi0BOuSbsqwdt1JjsQ8hNu73NWRgRX84TNpaXCsz\r\nYHZ4GeyVZVn14zmWe+KNvQN48tBKmsrJQaEEt0sYcNIxNXSz4q/EA4bfog2a\r\nGOVIiZLW7lMPmGVTp2k07rGddXI7rXxJMIERHMPpRpJEiaaXBF+agwJMn19G\r\nvNxO2SiXiSCYUbQXMt/GlydcyFkVY/34aHPk8Pj3BxFRm+l/LEfa/c4Sdfe1\r\ncICLNXktwyEQxahZgJB02Weaqd5AjE361fI=\r\n=bzf5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.17.9": {"name": "@babel/parser", "version": "7.17.9", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.0", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-check-duplicate-nodes": "^7.17.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "9c94189a6062f0291418ca021077983058e171ef", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.17.9.tgz", "fileCount": 8, "integrity": "sha512-vqUSBLP8dQHFPdPi9bc5GK9vRkYHJ49fsZdtoJ8EQ8ibpwk5rPKfvNIwChB0KVXcIjcepEBBd2VHC5r9Gy8ueg==", "signatures": [{"sig": "MEQCIG3ANc8MHRSYNKrMcLjvBNPcUFikwTjWAuEDrC+m72njAiAgg0vFUF5sqo5J/O2g6PNDC1f9GUDPYRS2zqmXSQ0Ocg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1762051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTbfqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmonvw/6A76Qk6pgBI0gq9MytYwuaPb1Z/o7Cni3tG04QIFaTE81fXx1\r\noLyj3MppMRlMOCpf0w6AlhzIfzFAhb+EHrKQyqyWcgLa4HkrEqqiNIFXlssN\r\nstoOXE35zHHpGCBwnkKdq8Udo/0/lBKoZcEvjxDsA1RuZ3xuHreNgYNjhvJ3\r\n1It6zeLx8Diun2QuOa48ymt0v8t0q88biDoNA41gN6XWMkAIRIM6nUuntRwN\r\nDNYMpWaGtv8mGDksH+UJNp0WJDoNIKhSHCNoMYxvXyxcr860tnb6lSQ/p0QT\r\nVf3JynYQUNlVB1QtRCoxXpJP9PG9o306FFv9ughb88GmLYV2JFOZohRB1NOo\r\ndsPfI6TSGAFERYBMfVWEEa01ucWkOJprVp0o/tBMUuosE0z3CKcr1cdDh+vr\r\nJjmYeWiN7j0CdmNF5do+i3RUlbKUoHNTjv3Ihg/lDeWaZJNabCTiWDoPEhDo\r\nbEKqv+FJOnwTTE3EmQQCLpXRVfwQZWZYiYqrjl7hSBj8dukTOXchfZpYlT2x\r\nZ6ocUvTDqGbismeiO7PP/1RoeU8gq52EBK2bCP0e5jyyx72C6jcbCYHWOb5Y\r\nX4lKT87h9iP0P3Js7QEOtfTAAGwTYY5Q89gOJJcCYm/v2t72UgoOL6XIPPzo\r\ndWHuJ9iuZnNg5K4Ah/9oUAlYr3fBRN7Ljss=\r\n=PQ+c\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.17.10": {"name": "@babel/parser", "version": "7.17.10", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.10", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-check-duplicate-nodes": "^7.17.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "873b16db82a8909e0fbd7f115772f4b739f6ce78", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.17.10.tgz", "fileCount": 8, "integrity": "sha512-n2Q6i+fnJqzOaq2VkdXxy2TCPCWQZHiCo0XqmrCvDWcZQKRyZzYi4Z0yxlBuN0w+r2ZHmre+Q087DSrw3pbJDQ==", "signatures": [{"sig": "MEQCIFq9KRwui4aqW/Xa+kYcIlVVDcO2HBFPNvku4OxFfgyYAiAWn9wi3ShwUfMqNWYKXxr+ghHdAHf04UjAjYgo45fYVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1886108, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq40Q/+NDXUA7/JZgRFqPF8uAGPxiqcCRc4XtB7R0/TThcM6Ss4yQfd\r\nIvb+qxjgILg69bC6OuylapYK2skTx5b9cj3oWTS+DlV8rUmepV+BXZ81KhGp\r\nW1/n8BMme/8YRF8fVzPGR0RPcRz2F1noF8GBwQLO2QVzsuAZQaHHQGqsxbRY\r\nijPF7WEXvmitSInURQsz6mrniVluBGxNppNS/xq+MkfN//hgOgq7sRMMlM7S\r\nClAGw5upNnA2nXCB5blio6WNgDpXnRK9bnLQmAKLgCA0iiBza7FP/BzOQDts\r\nh9J+4w6PuHMpPlZrcd6mI8AoJ4PTXTYvJaDI9drnSCpW3b/zjF1eWOBeBZPK\r\ntkaSMTaV8z9g5FhCfHW73Ixaktt13IcpHJ586sNKEBIL+1PeBmrf9cktkTPN\r\nTEl/3IpOzqGE9csdCNHPTAu4l1rWfQRJzzCyivwQsmNikFj2ubSd/tlD/AN1\r\nKpu3d67g1tpthtfeZeToT3Q1SqV4tKwDB6C4bVO3n7QUZy7QTKtknfxobw48\r\nuknxMw9BX0b/TyPbCnGrOkeNxiSaKbf1frukfd+Hl8ZR38dSy0G/MufroKo/\r\nXXnVQ4okzx2XEQYCrO9N8nK+3DdhRy8VcR3WA/evxAgNRxVCGk4n5F7+fTQA\r\nzOmo/ryT0xuVWay0mQb2eUjB7uYeuLsADcQ=\r\n=tZcz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.17.12": {"name": "@babel/parser", "version": "7.17.12", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.10", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-check-duplicate-nodes": "^7.17.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "36c2ed06944e3691ba82735fc4cf62d12d491a23", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.17.12.tgz", "fileCount": 8, "integrity": "sha512-FLzHmN9V3AJIrWfOpvRlZCeVg/WLdicSnTMsLur6uDj9TT8ymUlG9XxURdW/XvuygK+2CW0poOJABdA4m/YKxA==", "signatures": [{"sig": "MEUCIDxMCC4Y/Zsg8xSH7FilNWzx4ygORqVbQF4xQa1o+OsVAiEAhjZ4ExDGWvB9UU3EROvVKqaprXgtjHOxmOU+LfDA19Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1888050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqa+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4DA//byBo+iCZhgbiGzN6ycrawyznNIrpwYasSeqxTXfiLAbTOVqV\r\nYgxgpIY3QqqPRtarSupO7FJ3H5X4NiF9kYfRHXnwYfLoxzXvP9HZWsQxW0dc\r\nKmvu0Y2Lxr6ZcT4vnA1XqZYtMPY0UIiUzaEiorF3JPlC1qbnOg4manRigQd1\r\nIhifB8wdNYvxgCpdCITLuCXhFBuuzRypxUrRZ90oU1Lx+CJbyAwzWteO6kJV\r\nOmEDi+6Vu16wfm9rlEpkZ5Kk9H3+zLvyQ7kKHWKl81Bc6iOJWmLbRrffyp74\r\n5GPc/2fIih9dCjAsbm7saBGrRdoHI2yBm/498JiDBLLN+b8q+7BGYe4oeXxC\r\nHn90R07menVw7b1JWCSsFSbZTOXomF8o2Br/FX6lqqUf4eFqMPK/xBh2vc9h\r\nbfC2IaekL+OpYYvTpfcbyuHAe+KgnK25ScyA+tGz+AAxiK6yhSbOflEJ2Byq\r\nrm1bPTxU6E3LgxIgh+EJMuUsprY6/hUQTnzvf7nq0TktCNxcZbo/95RfYUeX\r\nxvpfeWxjaU7owfKq7AxyCzlhGj8Z/DTxhwWTvzrdWNOm7j1L7Q2YNWPJy7Vj\r\nC6F5mrnWYYjUaK4RPKPJbVpzQPUa55plbsNc2tADX1o9GtQBIrwrDUIcoMo3\r\nAOrlyPcRzDbXoX9kQG1L62XLf7xqGoKn5rI=\r\n=imHS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.0": {"name": "@babel/parser", "version": "7.18.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.10", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-check-duplicate-nodes": "^7.17.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "10a8d4e656bc01128d299a787aa006ce1a91e112", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.0.tgz", "fileCount": 8, "integrity": "sha512-AqDccGC+m5O/iUStSJy3DGRIUFu7WbY/CppZYwrEUB4N0tZlnI8CSTsgL7v5fHVFmUbRv2sd+yy27o8Ydt4MGg==", "signatures": [{"sig": "MEUCIQCnIrvvfuGNEceLSkm2izmkh1fkH/raVAwHgyonyi7H9AIgElNFjLwij4WGLKhX/V0L0P88bKa1SVortQH8OqCRQRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1900942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+TA/+OqSDBrCdR3fEF+JMzs5mO9A3CxMxLsnjmMtX68uLdo+M9tyM\r\nueb8kyGLm+uoCnPoLgmDbwliMixRL0aPdTgSxXyIpv+tki0drZvnLuGMz8Hh\r\nZoKIEQJECAIeS82FLCjJWJ0J9k5RPThjcViJ97VAWXZnUqM7o9+5ZLEOdNJJ\r\nCNMWMpiKJmkmezrC04n79ptfbrgg8EBz5G9zBpXlicqUY3reRlU9ULHWS3I6\r\n+vn3FWRDCIUh/HM/9xz3hgltYmNv+8bYADoHmxfprT6aRQiyOsowHhVenyyM\r\nhQzfye1KXzUak4knaGwSwmmhYPJB+Q7Ciycot+EiOFYBBGfhXAQct02KF7c7\r\nJj+k4bLwax8D0r+h7+ibUAiV6cNlezPsDZD/UgJj5ILTY27ONrMgKrO5+Gnw\r\nZFe/sJcgepajx4DgBBlVOHiUSh09lSkzUjEBAfjjykYbWTX9aNIieoWPFIKw\r\nRf/rapiczG4fZCrgtxY/6zH4Y3p55aDbfxoBQxXxOD9JusMfr3tE+lOGosiE\r\nIvi9KeDnDl/r+RHju8HmujAjXvQkYQXr6kqSVX9mo3xLMEilFD6zM5S+UQGS\r\nFQzjr/Y6lJdLUYuEvUtBPb+JyLrW8vM6NKGfD5bnpU9+luJ4v4njHZ+l/o7z\r\nszQco+5Hske3cNndYSu0JdgrE3kVzvBcslc=\r\n=fPkk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.3": {"name": "@babel/parser", "version": "7.18.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.10", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-check-duplicate-nodes": "^7.17.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "39e99c7b0c4c56cef4d1eed8de9f506411c2ebc2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.3.tgz", "fileCount": 8, "integrity": "sha512-rL50YcEuHbbauAFAysNsJA4/f89fGTOBRNs9P81sniKnKAr4xULe5AecolcsKbi88xu0ByWYDj/S1AJ3FSFuSQ==", "signatures": [{"sig": "MEUCIQCdA2Hnf+V7maWU9/prjLvCmFaH4pL6XjDXOckZvzhmRAIgNDP3BCBYHsLiE3zCujKjP0ahtOY91hi3eb4qPsn8dzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1879960, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijkDPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmosrw//Vg+GmR7x1FoqZn6Pjoi2sHi7tn5zrev48zScprNfjcr6C9fg\r\naBZyKfn/Tdy92YYEpcVZZSJsHcmrNIFmYoq/7YUcfjyK4zs6o2dzz6Qz3lIc\r\nmAJkjzzwDWv/XlE+/xk63U1KxmtfdGKOFaKaawVySJ7M88vQWhrLCYl1lFc+\r\n9NiNl3+yQx7vgog3UOTHJZOH54XbcitwC94KJzrfocchoxfMfl8w1Ekdi2w8\r\nR/hEaVuNMH/ehI8eSc4EP6Q0hvofo14mOsRvNbFSjaL04vq7CpSRLhLwRjog\r\nQJpTdqdFuZ5xcBIgML7zeatJj2KyzkYQd6bHG2oXLW2fhQR2XLVr5r3CLE1Q\r\ndB8y02UMrSIHwuZGI/yqHduVVa80QXIe5y16Aki3FQono9ZbyAxaX6+9RpmD\r\ntle8yxb5sRPFo5RNiBaUpOKUMYuHV8Ns3tlZTMXoSFt9JZ1mQAotJhuY9X4+\r\nd8AuYrqe2SjucR4EMraqDo4HmfwjPfcmghygbsjsQtG3ZbBdODEu5z9pCx2E\r\nIQ2gVzZCQwsWpWXBmNMgChPSPOI37Bq1zf61fVvqcw4mljYStImrj292v1kq\r\nvCJcv/3HFTqWCxqutY5OZGFaWbXa9dSoY7r+oy6vqtIfSDZsdp3pMpYap/nU\r\nbK+vyN6DZpqS+mFkzyIw1GpCVJAaeaS7UNw=\r\n=n/pn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.4": {"name": "@babel/parser", "version": "7.18.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.10", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-check-duplicate-nodes": "^7.18.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "6774231779dd700e0af29f6ad8d479582d7ce5ef", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.4.tgz", "fileCount": 8, "integrity": "sha512-FDge0dFazETFcxGw/EXzOkN8uJp0PC7Qbm+Pe9T+av2zlBpOgunFHkQPPn+eRuClU73JF+98D531UgayY89tow==", "signatures": [{"sig": "MEUCIFqUkDDUhM/KN0RRLzNZhWwprBAeBc5T4vwo1XfaOOtqAiEA80+AZGGFn28KINwbD/XBgpPqa8SCGHdvlkhvnSzxeIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1880764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik+qTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQihAApM79UyYhq7Qa3yKVLpIOaPhEZc5PskGLV0hKRkNHl0hIoyWT\r\nsbH3g/G1Oid9eTHGprnyHGFz2FwKgQxv5qETEFHE5xKSp8jCaEzxUcJlA+Tu\r\nc9rvRqBzpcEycFzNhsDtaO7EDFU+N1V2MD0nB+K9CmUmqGkp4bJcuv9ES9cY\r\nfGxaz99dA7DrNprWYHy+4SXl5N+kzeHwlyepzwDRF8NHJdHHz7X6zneunNMh\r\nRdS86XlBHmlAf47nVcJM9ylWTFEa0lVyap5glAj2SFLtzzl5o8mmY/0Xvw/8\r\nc1XS6fC/YZlbz49URzAQRREhnfk4WqSasVfC9hOVhngG02KZqsY78p7q5Iyv\r\njxU20OrFGAVf8P9HE2NYxrgB97Eeg1zsKENi/4pbO8/7zL/DpT9RAJwodFKL\r\nXtwrDOd1cx156IdqU1GBXnWw6qBZetN/h6bHDQIBjcE43oGwyWUmbBjGEwUr\r\ntuWz4gw7ZKN0jv0TvWPFugJopw7B97Wn/bHWwaLBzoKdb36ub13POfh9o8uK\r\nwo94kFsHFwiIUZTAnCnk0hV9P2EfvndjgHal5jadutKdOMMaq1znprdpbRwm\r\nT9AKb6Qxj2T3fNHnrvRUion7aBNsQeG67uPR5IhSPPk8qSklzqjf3vra54cy\r\ndxyi0Z4XAJ/IjC08UkH/IpLzSd4ictXThY4=\r\n=iwH2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.5": {"name": "@babel/parser", "version": "7.18.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.16.7", "@babel/helper-fixtures": "^7.17.10", "@babel/helper-validator-identifier": "^7.16.7", "@babel/helper-check-duplicate-nodes": "^7.18.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "337062363436a893a2d22faa60be5bb37091c83c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.5.tgz", "fileCount": 8, "integrity": "sha512-YZWVaglMiplo7v8f1oMQ5ZPQr0vn7HPeZXxXWsxXJRjGVrzUFn9OxFQl1sb5wzfootjA/yChhW84BV+383FSOw==", "signatures": [{"sig": "MEYCIQCEAmqK4p1UmPRqWrsMpToWgOcdZxBd2rQm+KLI9gyJ2wIhAP/zRf2DGbFby5D1wlmyo9K7I0CplZ3eGB4R4RJ7AvH8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1880268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiptvVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIaQ//bvt7ji0JQB5t/UzfrFn3FKbGiohByYy/rypfojDTothSs1Yz\r\nAzJqXjFmh+Bw5hLBJuwxHbq0lymkysTIVNS1Q8TvDtbENxa1uStEruts7ByO\r\n2PXscabjt7jjFcsv2jBi89daylGS3M1PJkQEjUcdkb968k0OvOrrt0LoTfH1\r\nDQT5qqg7WSZvIrSWtel74hgaLgbcFWfQFAGQa8DMA2Gw8k+zyW6UsP/WwSjA\r\nNQXPyNjB4UmDNc6dY0mzTpveP1/SKy0zXvnSJgl7d8NjkUxdvsU6Eu4G2oka\r\nK1ZUs2aKU48cHGhu/oxs8P1+GLI3JPOpunjPo0yZMqrSabNqxAaZ1Kw7Auj9\r\nHk9/heNeQNlJsVjq30VsJhfEXLwBOccHqKgqpWMpaSkY6dzo0RJt4oQ8NbOj\r\n+fFM496xjTfZosG39O+cVY5Tb1oaJkhBiMRZSEebQn0S5lypEA6fEzVuHNOR\r\nS8pRNEz8Cg4+MoU4PLFqNPdc6msGCmcnyDXsT3sPMQN3bl4P0fHAZTF0Ptv8\r\nICsH+fz7gxoye34NC2PMJH3cnH4PtelRYMWqit0MYrM0MzylIHL+OHiwMG7Z\r\nB7LNFSqDR4GpkuqADvcFe164X79jccG/MRmHsM5O9td9OknJ8ulfyxJRrVIf\r\nOvbAhNm/QOfSRuVTwVXRU/5oppCG6Q5xSp4=\r\n=OuC8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.6": {"name": "@babel/parser", "version": "7.18.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "845338edecad65ebffef058d3be851f1d28a63bc", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.6.tgz", "fileCount": 8, "integrity": "sha512-uQVSa9jJUe/G/304lXspfWVpKpK4euFLgGiMQFOCpM/bgcAdeoHwi/OQz23O9GK2osz26ZiXRRV9aV+Yl1O8tw==", "signatures": [{"sig": "MEUCIQD/Y3XvK8IAjL0lhh1fbt7fxSrHZlFWEKJiXuMjfOtiFwIgJspv1JnSqA3XeVQ84HzZ4Uw0hONU2ZHM7ndAs7gTBxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1885546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrzQ/8CSjX1ALGNJT2+aZDCI1H6WVCc9Kv8JbcVWiuEBflz0TLMWOS\r\n/+MqwqGgyr60GwXA5JcS/g/5xrHJD/cFfIDLz+lE+34UIZdFOSy7QICvnplc\r\nYiGQ1C2+xlpEsMmOUDzPnlA8uWWscUtA+VUpohvNzyE5pQe6CVQVRcCpHIOL\r\ndCJtM8cJOAs2LwP1JT79IR4m6gErOABuGnCm29cK7p1JMKIPBQGp21E102W8\r\nlyOb4J4qOewkqX68zpgQ8su+dkMdj0rY0KcbRkHMOJ8HAuS87xAQbCg2LEi6\r\n4+uKxbrO3h6nxlW18Eh2C7bRzb15qlF5fKeSPKHDVPNHPbpPoCWvOnGpD/0P\r\nHo0YeJAfthE+AEJfKKFwxKWqOENleC+J+BVpDvJnVHpVQRGr0NmuYyHBkffB\r\n43R6rmR0dz2BdVLdInHRdcv0iEF0TqSt0PQ+uAPcOBBG81GdizvTjLh6il5+\r\n2K4VETEYuqAfa7Og7X+q5by6snFEd7dwoqe3uhx0SFqHYs2yBpq6xp1FYQ+A\r\n+akGB/UQm+ufR1g9JiTtd+y/YtT13MaCoSOJQvC+oKe6lFQazQv2rt18yQxZ\r\nB9ltzwcQaWl8XxjaKxwyqnP+ag8rXa0KzPYfju3jE47gsDRpZwqLVy6JYjjq\r\n1fpNf9AiBhCbAuzN688pNSideWOzamVyuHI=\r\n=vvfM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.8": {"name": "@babel/parser", "version": "7.18.8", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "822146080ac9c62dac0823bb3489622e0bc1cbdf", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.8.tgz", "fileCount": 8, "integrity": "sha512-RSKRfYX20dyH+elbJK2uqAkVyucL+xXzhqlMD5/ZXx+dAAwpyB7HsvnHe/ZUGOF+xLr5Wx9/JoXVTj6BQE2/oA==", "signatures": [{"sig": "MEUCIFKLeoFckRyypJcgE1zau5PXICp2hbX9iw2YU6zI21yZAiEA72uCuw2UqjSgSDdRzuHuK1C/+3tB2q+QHTe2a8e6ZWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1885544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/myACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpG1RAAk+MEVrpD9lEehMLw9eNAEmlE3v6Yepj2eVJ88f+vV5QzPcnu\r\nmDvIB/BZh7HcieqqXGqV2HKKBHh92rv3fxzscsOD9GdjpZ7Z+alUBdzJLIQ4\r\niDnONYFdcW/Wskm7OplCQCBtaKuK+dKQuFNQNRO3ti/86ENndN01vYIlAdCA\r\n2j7FRAerAqjuT5r+Y397wcDiPQf+ceNLbwn8dtG3Oem5dkEhvXRJY5DptpLz\r\nt60QFVDaLZGInfbGzsBdulOwT1X8qRYxpkJzR76RyCtO4+wpD+OT+uBAIygY\r\nF+8tCikG97glEBHrY6mG3N1gk32R/ghMo4UcZhU/j8N8hzgDw1IXbzOiGjO1\r\nckp36Xce5XGVyFdu6k8mbdv4XEdp83WgY5NozXB39rYq8egmi0ZDItyrksun\r\n5nsLTnqWBlFqYVBE8HKXR5Tw1Vif0RIWCBxf6rsO/9qe07PajzkIsbnSXakL\r\n/6+mMbBC1g7Fa+CfKeCOPg0hpWzYU2Vdo3j4TRyRd9OxHJvlelcyTNu9/jbL\r\nbfNWS6orS43LhU/sZ2lcDNTTS3x1i5D47D3zhcOx0eRXG1aI9rKVt81k0TrS\r\njjPLsoP9RSrT23Er+VRE9FgTGQtcC5BmjfRGc7ZZzF9TLD3dSVGHJsKq/94k\r\nUtlyN66Ohow7fn+IQEfCxr5OvqOgWAJOT3I=\r\n=RGOJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.9": {"name": "@babel/parser", "version": "7.18.9", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "f2dde0c682ccc264a9a8595efd030a5cc8fd2539", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.9.tgz", "fileCount": 9, "integrity": "sha512-9uJveS9eY9DJ0t64YbIBZICtJy8a5QrDEVdiLCG97fVLpDTpGX7t8mMSb6OWw6Lrnjqj4O8zwjELX3dhoMgiBg==", "signatures": [{"sig": "MEUCIQD/0Qn7tw2FhelyEDVc2GZY4Lg5qj8ONxPOqtCpha/imgIgdNlqD643JDC8ShJfocIsJmEijjIPy/DVazPxyjylMgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1885672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1dA//VIQkV0UJiZ/l14dEnrPgUoozLNvzhwqOcKKuESsEP7VSw1iE\r\nOKVkUN/GGbgJp0+nW8tu5EflEViiikT2S+UlYQXNluK7lx01/i0ktlIO+mfM\r\nbX7PxXuA6mh7At/cJ+L3bJEuMnHsgDa3X/IeEb/8i9BKfvseGlLdQe2YtZm5\r\nkEvqPPn88GIqZ2iNvSst3Mb6qapWzZNn1LT+y7zTmDN9uwyW+xJhnUdNqanh\r\n1QkFCeee7unXGdzvPVA+0ShDO//IAPNfuwtEgyVPbVjfzOkQBd4atgXn5mBt\r\nwmfAPcqFojOMolKUQGUzJyodjqYYF+XRwLMPaandT43zzyNgU7in+9ew7wus\r\nfhMyeOLmEmNsSVF4IC4+tZxk8HVpOV33VJeGNZ5eT/AxrHRiKvVMYQ+yQa8r\r\nM3p7y4io0S5NsvvuZr3gGtylIogoP9YGK0a8Pv3HLva/tPuwlF7Kb48cillE\r\nl6CTVUawE+wcXw7+wiRYC9GhBaHUeW3RR8iEXnJPYtbRnILMQmnch21Ew67/\r\nrjmKkpkA1I3ET7l2QmtDKVJ+xnuqwAsZGQqfu0sxjZ9FhVXBkWBi6b5+kWit\r\nX3bVIPcdRI/Nc1E3UMKtRTZIvjbd19seCE6NGi05xsXyhR7seRmKXu3fhloe\r\nZzecN3RSHrHl3oT/WJLWGqiZCBKxqisfETI=\r\n=Jgnh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.10": {"name": "@babel/parser", "version": "7.18.10", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "94b5f8522356e69e8277276adf67ed280c90ecc1", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.10.tgz", "fileCount": 9, "integrity": "sha512-TYk3OA0HKL6qNryUayb5UUEhM/rkOQozIBEA5ITXh5DWrSp0TlUQXMyZmnWxG/DizSWBeeQ0Zbc5z8UGaaqoeg==", "signatures": [{"sig": "MEQCIBE0yRl4c7yey8fagb1L73JPMKvs4UWkuEy+qZwfDMeiAiAfjUtNYDMyE+81JRtSlESiykUc+pZLb75kNfEjit5paQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1920633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGjw//Qx0VQBMIi0/yagaSMkwWvdtEBuY8TLYPdzTm5hbBpl176cT/\r\n7iju/lSXN+WQjJ39IbmbJ76bXXoEG+bMlqLhVv3DnIoCQibBkUvNCzBim2xq\r\nc6AinXXpn2Mn1b9NVPw16N16PTVLeJlVHtSyivjpksJzx6vLbhYE4X5dzoAu\r\n5OcfBTw6OJe7fcC3YzOJTP1PNNuJZLLVN2SEm2l7ej6k60vGJcDQ10UWpyOJ\r\nq8F7xl2EJNT6O1ADxncBlLgaUsNModzNUFPMKS0G1PWWXrw0xa98QFOElINj\r\nnotpRCrT5CnN+o3P7RHPgIaN14lzxU6JJ3zMBgnYxr5lQYcn/srQMFizRIgb\r\nsCsl0fK2RNlwp5ghMR1n3dkRxnA+IFfApnp4TRmysZdtaVQEyD2BmTZX5YWJ\r\n3gFtC3BEx3g4byAMw+izH7KRNcDIsoKWq3QGt7Cg7khKHxn5e9gTWT6l4jNM\r\nQIZi9KNXsJe/vBUuk7P1cvpC2BwwdwmZ2gWxu+P8s9HytjORhtUdMHf3izyh\r\nW1f0k24lQgphUc9MT4bCdDAd02Yiri42kP4ZQveDlxsDiNsxh2O/NAig974v\r\n/YRueYkJ7x5wbal2Q0sflKgrObgS1Hjc5CbbMyMpNDblCFaHLgBmrIOcqp2U\r\nZKzx5l6qoXd7Z6hKEwz55aP4CkpXyB1P7ww=\r\n=ZZOD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.11": {"name": "@babel/parser", "version": "7.18.11", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "68bb07ab3d380affa9a3f96728df07969645d2d9", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.11.tgz", "fileCount": 9, "integrity": "sha512-9JKn5vN+hDt0Hdqn1PiJ2guflwP+B6Ga8qbDuoF0PzzVhrzsKIJo8yGqVk6CmMHiMei9w1C1Bp9IMJSIK+HPIQ==", "signatures": [{"sig": "MEQCICDIZ2vFCdqjyaQt58jDpCioARrq76oVGBC5Gqp1sXbWAiBFXMwRwwXBWwAZ96M2tbfF8sFuMdlvKmD12QVktoWXvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1923058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi679zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmfBAAmHNMxj+4v/bkIpKrKIlXhujIlzdVmVI9xA/0O2GGsY3q5hYw\r\nYWJFTB97qVZCMMzlPkaUwJcmGOQNO2Mv/ZAltdPNxwOsj2FkkouE96bmwpBa\r\nQABplp9VntoGy9TmC+3CT8nHQ++hKhXg0iet1rYk3Amm1GtZJkuUzym/Oe04\r\nW43neGYRvoihSgNIbb9PwkCevQLR0v/NMji+foq6BGwNJZE2rhiysTHyf4S3\r\ng/7hejc1Nn369F7jZB4QfNOpHbir6kEDoZn9jBneu0FOkBdoi8Pok98ORoWi\r\nbjHXD2i5LVx306T9YhcWbHdpHuRlwhhH380lCrYv1iQSIr96ptuADsWWUDau\r\nYgY7gk5EWbH2b834KUsomt7xYRN+oINxDPG0MgbzaHdYqSOeNXU1Sz/D7yCD\r\nYGIa6FAxFbCDfjB2P1ztozv9ejb0Kh9DMr9S7xwITkiLPvdU6gGNGqKiaRsr\r\nqvC+dx0t8fVZHNm1NLn+XV+EKTWwFfXOnpIKTW3eL+9G4O7edCUjfbdhV7Q4\r\n4rAxhyQK2SVC3ZezDqUFlTc83VAvIiM1vSVZfq9h8q87DaOGQ3KFWyBZuedR\r\naZzo8zH0x8d5zeVZnwTVQNxQJaCHerY+Lam1VEn6Q6QVbt07oa06/jYgaLDP\r\nr9fLbQo1VDBLpNh4Sdz7PRW4TNTW6gX3+u8=\r\n=Pn6K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.18.13": {"name": "@babel/parser", "version": "7.18.13", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "5b2dd21cae4a2c5145f1fbd8ca103f9313d3b7e4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.18.13.tgz", "fileCount": 9, "integrity": "sha512-dgXcIfMuQ0kgzLB2b9tRZs7TTFFaGM2AbtA4fJgUUYukzGH4jwsS7hzQHEGs67jdehpm22vkgKwvbU+aEflgwg==", "signatures": [{"sig": "MEUCIBXP41V/7fRc5E0u04GXObiTrgVHTEo2FvOXEjJH7l13AiEApfDGuPwDSROhT512WxWcB2AWzVyEjCSf5WD+RXcZszs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1923489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA6k2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOdQ//SLvTSH53Z7J2AVcO8UVQ7KqJO0hTpk4Pp7axwxh6d3T8Wk4k\r\nOQm4wJA0ZN84r/Ya6axHtvdM1eUNJm3rRiv0vudolTnh+KbuHYIjBQNQg8EX\r\nTdNHrgRGd9IarQ93tXQTXqdA4Ak3kLGCiNC1FQUmoaAUo9xcD96oWK1saz+s\r\nzoIIKEjiPYvLO/+PDt2lT4y13Bj2lUAt5qU81jiy1GyPeniyYzKsFn+X7/fn\r\nmy6/SjRdZVt+/WDwvXulR5RIHz0PpOzAPHqlUMdOS5tSlHIZEsT8UtTcAZoo\r\nhVUZMaqAQ/fYbxq6Dqg+DeZGgdInxk0DUFqtMYsPifBgCLSw6F/ZdJOam3kC\r\nsFC2XtowfTIq7+B0KGHWJOD2G3KZUmrv5oG9nY9DXvQfkmZdePtHRQd9/VUA\r\nWqeGMHQ3PcxqljeGfgkl9bTXmFG87kpPJkC+30MHdNmLqXF1nUgGRvT1yHSx\r\n1RU1shTeH6zhzVrCkcdJXfCPI8j0oS3TIb/8P7fPqnzj7xydEQnhE6UjSRfA\r\nr6WH+VS9S3Sk28yiCtTukTO/p3GGvcDnbiSE5XBEwmZXWNujoK5zFLn6BYfd\r\nQ1U400R9Ak1Vby7ae2tp3SwQIDjjX5RVwJj2ybObLE1CICaGgIpAXQdBo5dq\r\nFAgKg38tp6ACoLhkjMHl1vWvNeYnIA/aHq0=\r\n=1dqJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.19.0": {"name": "@babel/parser", "version": "7.19.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.18.6", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "497fcafb1d5b61376959c1c338745ef0577aa02c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.0.tgz", "fileCount": 9, "integrity": "sha512-74bEXKX2h+8rrfQUfsBfuZZHzsEs6Eql4pqy/T4Nn6Y9wNPggQOqD6z6pn5Bl8ZfysKouFZT/UXEH94ummEeQw==", "signatures": [{"sig": "MEUCIQDLyuxYEcwPpnDSd5zj2aROo6jy2I5H16rb6yp5bNvX6wIgIO6qzfHVZrGoqEI+b73UoltOUMNIGX5xfRipuXpJOvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1926947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr75g//fJSB/F+s79C65oFQclZV5zUctAHp2uUZxhBecOQM6CiVDH0X\r\nrhKpS1pvKH/TwknXHNx8dQfqgYB/fHBmHixftlQ6vcPhrX4R2dxTAbgOj/kJ\r\nB9VDiVVK4rKhm9OuuTLA/xRpWtFE3H18Qlo3Zoxh8SIR8udGSXGj7fYXNUgk\r\nGuZEFZqp5FXDY+Cx34BH0nVfcZGe5HUcOfRjpVc9yn1DrRWVzHKTZEGgiBXo\r\nJmbSzA+eUqIUDjmuCTKz66TPfMNC9hFRPDqcg4usP7bwOOkZ+xx7PNEeO4SI\r\nP53pUPx54CgR5fOoW/ue5Y4aWSIUrrf/txRwgeEaf+tYzegDeSfliUKEgS2h\r\nH3+1BdwymdJKuv3cveInyj5DRdepcmhTCUS2WHH/d4NnXw/lsutspw6PPr5h\r\nFnfMucSyyoHchCTluV9dR9RzTjZjbtU8X6Ad/SbYR5YlETMs0JhHu54/eWpF\r\nv+A5EBeacpscE1EnDrZOIYYFpexRCPiwr/wy0dqHIuI1Z2CoZ5+0SPt472pI\r\nWH5DCEsSn4H/LzDW1lepdyDfaScCNRzurxk6t4GBI/kk/62UqW5UoJ/tOC7u\r\n4dHRQETuK/szceMTuvHL9epnmiKcILvCxPMbSH5yxENdB8P8RqxB5qv77e2j\r\nIVyBYqXWg2l54A734Z86VjtCavQuooWavVQ=\r\n=35HW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.19.1": {"name": "@babel/parser", "version": "7.19.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "6f6d6c2e621aad19a92544cc217ed13f1aac5b4c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.1.tgz", "fileCount": 9, "integrity": "sha512-h7RCSorm1DdTVGJf3P2Mhj3kdnkmF/EiysUkzS2TdgAYqyjFdMQJbVuXOBej2SBJaXan/lIVtT6KkGbyyq753A==", "signatures": [{"sig": "MEUCIBn2nvLRWvTmKmB7D2kc1OaaY8cnO93g11pHyJYjo5MgAiEAykNfcdW2SAOucfUOBKb9mv4quKVARpNW/HCvF70Bpxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1932883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfNGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8qg/8Ditv9Y+sE5lvqrWe4E6oaRduTSDE6RjMF0AcGcEtLCSsP+Qk\r\nptucts2uG46r4/Yrzv9nfWR5axlMZ3HFDI1SJSZjveQrIAO20h855XHNXo5K\r\nfIzEwcNPh1UIXSHYC+YaO90zCIqECsyMZdC0eZ804s/46P4+gLyq6uFOBCIV\r\ndF6KM2FHTPamcLv+Y4xjX8i0lVj4Z3Hu8eo00PLwE3kcqVs0J1Bbb5rZBE4C\r\nL37qA0kWyIw/awWpRLZvQq49eGtLRxKA/tCZIZTj6dng3ORsKDGP91oNM1cq\r\ndPHh5QvF+yuFfIKYU7WBDYNX5lv/jstCA2EElBT2mzdnD1V3QUkxyuOupmgU\r\nEKHl3Zs6ewnEfj+BIInBFYlH0vP9vnqfbSDppwAAxrq6tecee+yE8Qp+4yLX\r\nGit8qc5tl1Nw5/3kkVW3Nn55hBhcuTPQ2HLhnbveu/ZbhMB8qlhFjyo76L1x\r\nrYNCY4IIhks0AAb7un3SqhuTvLTiJJ7Ujg6fLGBkg6Nt22K/LT8BrJaR1aY9\r\nS/RjhJsaOUoYm9o8GDy4Q60ouI5OEPWvD8WBq4W5j1yn5uOC07kP4paDuNM7\r\nReIYAGzmi6UzRdcux6IQv880MXsppEuUMbYt7A+yYgsG/U/tbq203nGOftBU\r\nXmie4xPRErz5ByUde+5XGpNKH7L678wZcoo=\r\n=6+xG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.19.3": {"name": "@babel/parser", "version": "7.19.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "8dd36d17c53ff347f9e55c328710321b49479a9a", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.3.tgz", "fileCount": 9, "integrity": "sha512-pJ9xOlNWHiy9+FuFP09DEAFbAn4JskgRsVcc169w2xRBC3FRGuQEwjeIMMND9L2zc0iEhO/tGv4Zq+km+hxNpQ==", "signatures": [{"sig": "MEUCIQCXlz9r2r2S9NzV1HfP7bkNwCbVAKjkUK/bg5INGcF22wIgSUSplzaCZWJJOo9ARgI5h0GXlet44K++RAj2zFcCeSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1933207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0K8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmomwg//QgiGbKCl5EL4O5KGXq3uy4xxaZHZaTUEIXtUSIpy3HWuamAB\r\nG9sf8nTp0N8DLh4lKIP6HzgxDKdzePywlT+ALOZlfXExlQpr8lhcoeP0qXVi\r\ntGMuLhcOC5K5drvFTikbZiLTd0YUhb6a6NP+Ws2fJMdPdZZkHb3VSk7O3m0V\r\nFWC6nCgbY28jFzmD9zFpN5By+E/embilDv2gqm44HqJm8FlmFTpigx2WuBlD\r\n/vpwd0pVnm7U89lKtkyt9S6UEWcdwH3e/+DqT1EG9hF1GbUeZLDmf8/gihID\r\nsSQ8MohiEUifE0dGQlZG98z3QfXQIhBqtPNWnzu2M1F6gRpxmF5dtm2PsWV5\r\nxD1QMWsBwfPPR0UEGrsqpIm1gq1BCz1IwTTvPZovtu4zSuXGV6lKtEDM/13u\r\nR/6wO90dkL8fs/2eNz7uFlxE8gqkMynnJ6/0Ljo7Vid0JZ4b8c5BlIaGMJzb\r\n9KiY/Q/sP28+CvPrus8EjH7XGl5WWPjcikXGCY8A5wEiyAQGKJw3TSWBM1ua\r\nmzRywsWgUk8j2Dx0p5s2uRc49uh+mOrGDzLUL48tDa5FDIfNTK+V40xcdeVh\r\n33mGI+lx3ryU4+VkSAvyUPeyCqSLJPD7zizFSlw3tsunkiyOUo9S+pVvcBMD\r\nLNK1RzHkhmC87kOdEoiE4Tbel/gGNdOGPKU=\r\n=Famm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.19.4": {"name": "@babel/parser", "version": "7.19.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "03c4339d2b8971eb3beca5252bafd9b9f79db3dc", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.4.tgz", "fileCount": 9, "integrity": "sha512-qpVT7gtuOLjWeDTKLkJ6sryqLliBaFpAtGeqw5cs5giLldvh+Ch0plqnUMKoVAUS6ZEueQQiZV+p5pxtPitEsA==", "signatures": [{"sig": "MEUCIBEobIufNZcaTKOAzeFyr6J4FTsAc+So7L+4m2mD86MJAiEA7B9cruYpsKKOvIO2K8NNI+KbKE/8RBRi1NtjxfXkG80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1936046, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/g3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr61xAAg5UNIY86IyWptLpFRREO6Lmx3DEJ2yDprTFtoE+DGinl9nKs\r\nyyxRyBNUBdJ/+thdq0Ul5QVpshwmc2i0aSjHlRqYE4h5KLA6goAznw2lRbwK\r\nzdLCySqMWbZSJo6qdF8PeIs/3+FDAWv5hIg0GwX6T/4HKroJCcyG9gJtyMOI\r\nqDWi7rr1OfUKHp16iZyf6IlfarZy716ByXGvsnaRdBZ0Co/H21qTOXs2f/jZ\r\nl49M3jiF0ayRwk8ShOLiN9htalIz+2o62nLmlDdT1DsBFK1wuBUB0Qtb/EQo\r\nqttKZo/6EZtCFqi5bECPnXHj+nUGiklsPKpF6Gyzueb5V5aR627yI+vLNORL\r\npwVhdopo3KCRHtv1CFrelTCvAPQh7C4SWBP1RC3UHGKLvjCmafC/Uuxy02gm\r\nYYaMul4rFAkwsSIticR/Kiu1Xst18XefgxxZAyY5QrvrLGZGchHL3l/YJF6+\r\nbVd8sYZO0lD9pPn4Oa/7iWihZ5r13rBhV/mMZ0lPMMTLmiBqTp81A1E2F2W+\r\neLve60LLqeE7O/D7ckHzfMFbWwkKK1fzhCPDQRq5TumHmR3nwMcMvRYBmiYq\r\nFufWX2PZFR7jE678SsmtAOQK7faeHxsO/8/RoQ5HQu152ci9UkS2vBD/Wf0p\r\niBaUzVCgmEUi1npjXV8m4jQjNTY/qndtqLE=\r\n=H4cj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.19.6": {"name": "@babel/parser", "version": "7.19.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "b923430cb94f58a7eae8facbffa9efd19130e7f8", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.19.6.tgz", "fileCount": 9, "integrity": "sha512-h1IUp81s2JYJ3mRkdxJgs4UvmSsRvDrx5ICSJbPvtWYv5i1nTBGcBpnog+89rAFMwvvru6E5NUHdBe01UeSzYA==", "signatures": [{"sig": "MEYCIQCyMjniCvg+wBFf3PZp3vOiawrgxLwcx/oNarz/r1GZdQIhAJJdNgYbzrC75LsrfeT2tgVxf+5v9BnOb9LqrL/KvLt9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1924732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUQ7iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5aw//e+GqjTiVVORB3rcCelbMrfm6dMpQaJsPLWWh4632fSvb96gh\r\nOsWD0nPzUUxOWbnXbhavkOKIMUq9mKAIVSoucczdXrbzUiCjXO4hY73Jekr2\r\nBIrYFVoyv+eBnvNPhBOXzpu6PkTG5T9v1U1R2XleIzxoJQmgadNNyl49MDB8\r\nhkKex1ZfnXGwx4ur7GfSUGZJhkZ1+R0rCHmBTbP9z0Mv6LmYHs2ZGoxqLaoy\r\nbLbzdzUyZGNkjcHfO+9ABACpsEpNSljuJ7vF1SD6mpkkxJvRWH0LwunE1YBc\r\n/1MgTTV0MmYkardpF354sd5jfVc0lcI7ImJbxpWUAhmDEYbbnowoUHcEeFwl\r\nt24vI51JuqZDrBVhoy4QKo9GksV7rrvmy7sCkrNAhKi0wHQeukPiFKrhhJRa\r\n0OSWoqmi49HgArapjspZrEb9ixGiF8o1GJzhfgQ+mTKFHTxwU9qm7ebgTRHY\r\nGetVd9eYPgyT/zbyXDqfnPqJWxj3nWlr4vYr14aoTeFK4+WJ/19JOnABDNMR\r\nnPB6WGB9hdTHP1WPHxyHXz4Pikb3aGjjjizDd+c2DP2EqCDlPvETekOlpGcg\r\n/aRJi2wUnkwXw4szH/ymSYW0RToWqthCCjkI2HTOXRPXlQqm8qA9oN6Q02sw\r\nycvvf57Li7jVb/2VXrj06D9vZzoerj808Sg=\r\n=c9kO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.20.0": {"name": "@babel/parser", "version": "7.20.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "b26133c888da4d79b0d3edcf42677bcadc783046", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.0.tgz", "fileCount": 9, "integrity": "sha512-G9VgAhEaICnz8iiJeGJQyVl6J2nTjbW0xeisva0PK6XcKsga7BIaqm4ZF8Rg1Wbaqmy6znspNqhPaPkyukujzg==", "signatures": [{"sig": "MEYCIQDmVVYPXPdojL8ODZMAKOEP+MAxfv2DRUL7+1FJEKDiFgIhAPBGJOWwGiR7++XDI+eYAlEtbn3k/KPk02DSziFiCbaR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1941291, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPFA//dWXjo3JIYNGVwJkySN7p8vtUv083cDaz+8JJialPmmHAOYJ/\r\n+xE+7mnB88N7MBfD537x2aWks0gUAG9J+uy3Ni2r4Jh7hZRF/fdxFPcYkwHE\r\n6CQSSgj2va6WqqwIFedFi0QqJFogGIL3WDq++Rw7tpEVB9trtkInAQ82u6A9\r\nqu7/Ste/3111Rtb6GEcoMMbS4qGARC0ysfDPLhoszJ4Dzd5wyVPWqV5EJzi4\r\noU6TeQsxMNS2BCXx3gbNCl+IU2jiEtQUbpvNQ3wS6QO7KvTULean8R6xfnGf\r\nq0ZyXRV0G361Wdz1j6dD0rXxdWZQUJ7l1UAdBqDP9sRb1XyRscui/SBKeZsY\r\n+ojzQIJKqQcHbWQM+jZmf1FGQwPbo7Yb8Z+bHivayt6qI5wzDer0F+vqSqxm\r\nY4Adzz8f3eM1tRw2KsQJyY9VJg9LCFTvgBAHhrhsO/hNTLaf4QtrZzCg1hPu\r\npPe20jJJpsIIjizw7ObtNv3TahMmpLWXOUtP6jiKkIYB8KMMevnUvHPBnLPu\r\n0n3tuymR7DWGyVcsvh5aZWlgzp4HEhcug6wKsuSDup4mqTGRoN3j5yJeGe1J\r\nZLk637B/4ogzQqvyjS+x0NzS36OGVBUt1/Lw2wY62P8uCpAIb6KTT8egjaL0\r\nCs52v7DuZJqDkDmnHzNWNKz9r6SlMPZvXv8=\r\n=I02J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.20.1": {"name": "@babel/parser", "version": "7.20.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "3e045a92f7b4623cafc2425eddcb8cf2e54f9cc5", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.1.tgz", "fileCount": 9, "integrity": "sha512-hp0AYxaZJhxULfM1zyp7Wgr+pSUKBcP3M+PHnSzWGdXOzg/kHWIgiUWARvubhUKGOEw3xqY4x+lyZ9ytBVcELw==", "signatures": [{"sig": "MEUCIQC1JTIVRB/HJ/ormoUjP8uSiJpA+pxQHL5fzxUfmIXnbgIgVFJhik40VgRACx1tLrTfE1kd0UemDT7ziRc1s0dvyx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1934914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYQI0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC+Q//U75+sFr7LsZfbYR2WWYku4YrD7MxnLh1swXiJt0IeUqQlHK6\r\nonpAJrDUaHlOyvvHiLssQ2djtpf1d6EiXEozBPd8cuM8t40K3hYejI9nFhY8\r\nC4/1iGXzbxsTx8iSZFXY5WcxfADWKKmWI8M/OO5iuISTXRYI5S9EBP7y5N8a\r\nHJnjiPOq2A/ydghKw9xYCvPglxuAo3R1TDN4tHr/qd8aGJNJoY0VQ16BCvyY\r\nzjE2sP+xO+wx6Y0NqdFULDUIox0jjkxhdWMvne2s93DSbKr63865fJE2gBjm\r\n94uUSNNJf6hlV2m1Bws7xigjl4WJWeQzW7FWWOO/iZm1DhZgtCiCDfNF7b2M\r\nUliGu9oUTGz56/k6Q96JGcHx8MH0O43V1l/56cSaKOpNrAMM3iKQD9B1fSvt\r\n+6/PMw0Uk/LGYcGhUZe5EOJNOc6swgeE2nq1aCb0+6+Hw714sDEt1ZAmdk94\r\niiuigfnGs1ERR3nM3rT+bruxXEc7cKmUgf6LX+5LzHmjslPIeh8o+FMTeNAC\r\nBDoMQv4OCrJKn9FEbzlEXmFd1VikBt7xIfp4K5A4mt692MaAbIJRMdDwzkTi\r\nR6CTY3b7I89M946X11iIZ6GUycQlvSjAtC4HnlwC8x07DNDnR1v9k8pWWZBG\r\nZjk8P4EoJXtjOiLjfWgVG8MBlDMrN9S9oRs=\r\n=QrhH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.20.2": {"name": "@babel/parser", "version": "7.20.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "9aeb9b92f64412b5f81064d46f6a1ac0881337f4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.2.tgz", "fileCount": 10, "integrity": "sha512-afk318kh2uKbo7BEj2QtEi8HVCGrwHUffrYDy7dgVcSa2j9lY3LDjPzcyGdpX7xgm35aWqvciZJ4WKmdF/SxYg==", "signatures": [{"sig": "MEQCIE0ofnTTgowhom0fiSebhGBbuPBkHJKs/lpZnV7I6eL7AiBBe46QOXQ+TFfWw1WNXzdJ47lTKKuS9srWkpEEfvgcIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1940126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1DQ//T2/zth08RXNPnrYE3BEN1jwLeMK72b+Jq5J165epvGxvxIgd\r\nqeGvhd65va0NWP0cPpTU1tgkzi6nSyNuhUnmgzikilArM9iSyw40qIBIKPka\r\nHsjxYEBG7kJrQIXE3t0JToCvvs4I8Gxm0zP6J/Bv13h1+SbVPhxGRJbTQ+3B\r\nJcZb0BSp/PJnXno2G6sWcTRcrsIrb0FokWgE1p9ATGmtmIHC7ELEm7OGmZOO\r\n/tRryWVS8Sgyy2wvrLiIEJtW/Zb7QDt1x5IwUi10SoTIX4Z0CiVKEcdn9j6R\r\n74cP+6fhPiLa7so5ziEWhjm66hiWTHGm8LzrRS3YIx/QKmtJbPi9I7VpYQvi\r\nlYreFDTsXoMAHeogjH+S/O0QRkc+GCkItBiSfUEFgq3YXxfRSuijIXtpUHt3\r\nn2GUZ+nY7009dUYspUgeTs4wkqVDdjxFBIcJBoZgT3wYiXze5mHbC0qChG9d\r\npwf/5f0+v/wwuiiFgJ4ZykA+yutQs9omttQEkgSwj6AGzBmMi/HllPW+W3Lw\r\nqtO6VXwE7hq3YTi+hSdIyyzSIK9T+LeKylHBbRJm/3CAyyeCtGVzW/EcXbk3\r\nZ5vtCQxLZ9rg3BaH9HquHglfA5SPATZuFZkLUdjSfbnP+aUlQQjxJFTW/3mu\r\ndutkYrSoSdaw0ruv9UrRWT5x4OaLT1f9oTI=\r\n=9rAp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.20.3": {"name": "@babel/parser", "version": "7.20.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "5358cf62e380cf69efcb87a7bb922ff88bfac6e2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.3.tgz", "fileCount": 10, "integrity": "sha512-OP/s5a94frIPXwjzEcv5S/tpQfc6XhxYUnmWpgdqMWGgYCuErA3SzozaRAMQgSZWKeTJxht9aWAkUY+0UzvOFg==", "signatures": [{"sig": "MEUCIQD0DKk3bxhNfI+Q7XOdyr6UuWn0opMjaTEEuY64CscbZAIgTRy9xpjkhB0PNafXi5gu/qTdECr4Bn30yR6ghoPfDzc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1940300, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaRRRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+jRAAnBRDT04LT5tl4+/mg+SVuMoG92fvzu/aO1Js3Wu7H/MAhvYH\r\nQNZX7dxYZ0HbidSI+Td7crqyRoH8sw1Jq5li4hU3tV5lvZ74CpLSbT5gMils\r\nNZBg9r0l23LqiFxPxOjXuL6ewSM6jRqHQX9TCgAJB3uGrUHrr7twbiME1tWR\r\nRYdrQeIfSxmWV3u9n2vHjh/zXEkhbhDA4Et4jXjgCrLzzzzGJiwLPrrEdUqp\r\nRx3wHzlUiTQqcmgel5ywAHglnqtvXzfGCltrpAm5TxzYZIvwmFEmnM9lnoxh\r\nNct/nTMOPDnpHuhHWo8B4W5fQ4+r69Zf5X/OOrQh/cisdgvKLVdsWWBfa4FQ\r\nY5pwsQed7wzluZC8hzRB/cvURrj/Fd7iw23FjLj7c9GhPN+izIvn7LfrwFtS\r\nSOYninIN/ehwsScCykKI5BuEqpgvTG+uShgPXerE/rxf/7n1e5dLCRjOuO2i\r\n6JHN95aZyCKgHFoFCRjSbAFdUDjAmyogBoLIvpyBra14wEMaDaVoAbmIqGWD\r\nvwif6RZriTeaWlHbXd7o4sFIh0BV4+vImPKKzF+nUIHr6shVGizFtu+DDwE+\r\nH6hgOCLl1K3v3e2ywjUbQCM4fPhoVc3vSIbch+mhiuRMouH1TCKWS9NZ+Z5f\r\nPrZZao3BzOAzMCrWYtZAOMDMjgE+zgyGAVU=\r\n=R77I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.20.5": {"name": "@babel/parser", "version": "7.20.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "7f3c7335fe417665d929f34ae5dceae4c04015e8", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.5.tgz", "fileCount": 9, "integrity": "sha512-r27t/cy/m9uKLXQNWWebeCUHgnAZq0CpG1OwKRxzJMP1vpSU4bSIK2hq+/cp0bQxetkXx38n09rNu8jVkcK/zA==", "signatures": [{"sig": "MEYCIQDWmZlswS6nrGUteC0W162gmaFDtQ6dyR2FKG2jQYjYEwIhAPKLQ1s6JF3mfTo7hHmlRmBjzQawbUrEkCSjqwyfQwTI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1944808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+bg/9Hw9ebCQFq07Iwaq0+f9tasmAh7JPmT9M+B+aLnVcb8QZdRNg\r\nI1RFECvJUKwyTVWO9e8J1vf7B/Cr3NizQuqLR1OsQoNAvlA8EKOm89r36wTC\r\nCT1DKLgk35oMGj4d70nsB5psnzPu1iZ1zCbnIt/Mdsc/hJ5Pu9U0rNpZQJ/S\r\nauNqiGDkoSXBh24mHCU8iukYIvwxU0qBPS4h5WSce0LPQcNq/6kHJhBWynTm\r\n36zGAT56U8r1K+s4lnMEyy6WnL5+mWC5a6fsAWkyxWaJfwhd+S5gYLdCwgEp\r\nv+/m/j100LVlxrXU1dFjHvTxowYgqvTUABgMFUPi0xMe7KhMr9IyQmLBWssY\r\njJNVqGjka2Ej9DrTqAfCZzmRkqnKzokC9CT0JgNnGW+yQAIv4rmXO9xv2Ls3\r\nOMAtxjw1O4ykb8SqdfJSjGdcGSD8W1t3M2fJDK52GRy3YxM95thWm/crZnzq\r\nmydEa+xf2jPpwuyZ3kMex3DFaGUm6EHsCjJWxfkiRAoIFIX/1ea5BPMi+DAN\r\ncMghOuwEXGCMuYiJQu5+UVuKsTnxcYWcu8uVZXalTedGPntcPC4t2GW2GClC\r\negwMziz1tk+xpGc5K00Husi09OCuVNKHx1PUc9brtBQqT3zLM5jsvp8fFAwV\r\nwlrVM9gRuXfiFK9d40oQY633jzbM6ZYZsos=\r\n=FxJH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.20.7": {"name": "@babel/parser", "version": "7.20.7", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "66fe23b3c8569220817d5feb8b9dcdc95bb4f71b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.7.tgz", "fileCount": 9, "integrity": "sha512-T3Z9oHybU+0vZlY9CiDSJQTD5ZapcW18ZctFMi0MOAl/4BjFF4ul7NVSARLdbGO5vDqy9eQiGTV0LtKfvCYvcg==", "signatures": [{"sig": "MEUCIQCTA+R3XAASaVodJLWnZrFyl2dP6i9gMvaZJYECchKfeQIgWbSzBPb4/aDaqbx5tf7ZnQ7WEbf78h1m9JCfvS/t0cY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1943197, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCcwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmnQ/+IudziiBBMqqgQ6FyPKrFg1t7LCxOOrbPH+ccDnOOzqSHoWe0\r\nmQAQpavfZex26CG/CtDT8qtxjtE8tpbva+bRfnrrT6XWjxUzXEgGq6ReARKl\r\nIkKrRIYVAkUL/T7i1fzAxnsH8NnmThyWnrFbKAaCz9D8P3hvLDu6iwUqTlkn\r\nBZRZSIbtUN1sG2nRw8NvdMdxZZR6OqOyyVV3cfKatpAlyR/dkt6MSTTQ4N+C\r\nz1MDnrK3LStqSkzKLXC0PA1MCNe5v0L5sv4qDIEgHu9JZOshdK9C/nvjq94K\r\nSYVrt/Fdu5Xqq9dsOEi4aRayMuiZDjApLOikM7d8j5zrGP2U9cvrsZxEo/mF\r\n367UiS3uygsvYAEursrbWHFo3laJFzgtGWu3/ppP+vNOrgxsTRkW2LyrB7I2\r\nNM3LxxYby/b9MTI+gA6yAqRrQYLwKmdmRQT3AUKlH+em2UYuacetdzKhbMCp\r\nU7LkxKUNvzEPIwnYhPz14pi9Gu9cRmQdDEfQXG5KiJfCw9Y3JSXa0+jxdhd7\r\ngWE5ITULUJEye+YiiWEQHLg1CFKX3avrMh5ERSpm3HRbFgm8nBkkIZ3NjWP7\r\nsq2ieIGPg9X9kAfxDYtQLq2W0VU8PC5fYCh2tU6tfiYgd5Iw1bs00oYb2isx\r\nM9srNicGTjoBlyfX5cCA+hJ5HTan2SfSPQw=\r\n=nSAn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.20.13": {"name": "@babel/parser", "version": "7.20.13", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "ddf1eb5a813588d2fb1692b70c6fce75b945c088", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.13.tgz", "fileCount": 9, "integrity": "sha512-gFDLKMfpiXCsjt4za2JA9oTMn70CeseCehb11kRZgvd7+F67Hih3OHOK24cRrWECJ/ljfPGac6ygXAs/C8kIvw==", "signatures": [{"sig": "MEUCIAzm94KZ4EF+uYwAAM0MIgGSrwQi0sTrilXzLgIQrAwtAiEAqbHGUKWUhxHuAta/wM2zmiKcS+W8TFas+6BPZm3B8lQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1945088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjy/cQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpa1Q//fIoOjdBTYmm7z+aUPEeSXGB+qXw6Tq9z9J5E6IUtVPAK83uP\r\nLpHG4bmRBsmxBEdr/siFjguhN9RH3EjCJJsx4aj3y3npxE743EHoDD4nhH97\r\nZxvOXitLjrGHDBLlqnEuMZgHt5fB2aGb2jQ/3xUrt6k5ztXZx+ukU/3Sj8CQ\r\nJ7qESprmXEAcxnqf0N0ZgFCTiePh5SMA4+AgAKPdHdzi1UNnZz4/O3dQ2y1U\r\nptOtuZeEoDsvEnFRHhSPdEJ4+d+EDHzEoKwNgoGDDcD3CH/0p8EYNCthlKDW\r\nqiG8hZTZQ5ir+wbLEVD0mhmH5+9/87o9odIBhVwW9bTLPx/MmraiQu0DjEhC\r\n4aS4jJ5bWEKHwKrueTuxYz+N3OLrB0AEFs9fuT6x7LdGVg+DkrfK7ybwqYs5\r\nkQlKmhIj/p/WH/Jwq7d0+xvXHZ77PcecPzj2YLOhPngi50J+xnwd6llXNHSr\r\nyc5wBxtu+SLK3f7NWtMSbY+dPjZJ7wi/hW6pJWLkeJ6s4RNaFuxE8SH5iQ3y\r\nVmSMzDLsl2JFLp7tYKJcTj1l0Q/KRWrw5Nh0q724kSJg+yLg/DWUF1llPSJQ\r\nb0a+VytvGb7esdWBOyqd4xNkhLPGwdcAn8y0wydKwPADb7TK0YGnZv0HuWMb\r\nJNQd9rkeGs20VUmQ4E1Tbc0T/m1/ovo+9PE=\r\n=ZqoC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.20.15": {"name": "@babel/parser", "version": "7.20.15", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.19.4", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "eec9f36d8eaf0948bb88c87a46784b5ee9fd0c89", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.15.tgz", "fileCount": 9, "integrity": "sha512-DI4a1oZuf8wC+oAJA9RW6ga3Zbe8RZFt7kD9i4qAspz3I/yHet1VvC3DiSy/fsUvv5pvJuNPh0LPOdCcqinDPg==", "signatures": [{"sig": "MEUCIQCFgcQu9Ae5HHWAlrIy80Cd5nIKISc4916bfF+0NqavEgIgZ0usknHFzdwSNHpYkrBxA+7239lDLIhx/Vg1bOXy2w0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1945652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2/nkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgDg/9GG+Cx51ArhftqM6ojUnO0kfIhzs4QD/5u3jRcSObwmoT7BUD\r\n+TXP4wj3UYLZ+KG7tyHDBuYk2TOkVNHyK5S4jaEOkxTw3nGHEWLeIuBURtBM\r\nj4H6lJqPO2jKuyTQteAtBTTR6AZzIhdHmtyBb5Uexb5AKvbRBn3jQRcj/GYD\r\nWqZho5m/oJLE+PbNnpxJQJ0ypL8z396u0PIYufcAyZib916KXsjBOTAhw6Fe\r\nH387J8EWZ1HLd/luTClOUG/YHADpvikW1hoc6RJBPVIJaPQGPIDamMQgdIQE\r\ng7uI9dcUod67YGEpfr5tZMVFzjXuPe+hoHbTgW5YMQmIRB2uHUng9iwE90ux\r\nqWcvOysUuRcLDGD/sanPDBd1BcObIVWdS3R/KcIdrsIZqQJqivgIQDJckPBo\r\n25m/t7xfRaznWN21UtBcWBcWkH58d2M77B7Yz5ixse2m0Ossqax3Jl672TNj\r\nfPjeYKyRPz/PHOV+TCjsjvnt7xOgc+TRUTA0HOH7+1RVBcUAMU8aG/oLOnf6\r\n2tIV6PdnGolP2gaW5jP/QsAbYEkmSylrwOC9Bww4iVksfqsHSI7oX0qcKuiI\r\n7FgXlM5aERN+ysgmcO+nOr3uqEPzd3UaK4pBTxau46lSd9A/Bn6934CKnhB1\r\nRGn/cZ+hLs1tu0vSNtza+vXTDP71XgfnR2o=\r\n=7rW7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.0": {"name": "@babel/parser", "version": "7.21.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.21.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "cc09288743b867763cb927ba101ccdf0b600b7e4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.0.tgz", "fileCount": 9, "integrity": "sha512-ONjtg4renj14A9pj3iA5T5+r5Eijxbr2eNIkMBTC74occDSsRZUpe8vowmowAjFR1imWlkD8eEmjYXiREZpGZg==", "signatures": [{"sig": "MEUCIQD4BBVtxqRgvqFLytA6mjBKjCnoMGNZpakQx8JGdLveVAIgLs6m3SzapjbUJS8gcNrArl9glz55WxOyc4XvnNRlDt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1954516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85IuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+0Q/+J+KnCa6q1ibyjw2/4bGIvKIaJSYXd277bDrTfkYfBO8ynPi1\r\nxDrORMLz+pVB/6EeH0yCSymnK6coynqy3W0pMpybOyiUyvyZv920hRICyrT8\r\nd6+8+js3RAV/a55jiGOiLC/x04Sl+0D/M4b+LuUvxlTRkqlcUz7ql+q1z5hB\r\nUtjC3I3h0WaCjMIUL9pObImhZBghBbc43MQOahZlicb3ttyGsgtIFDyVPdAi\r\nGf5FgtMRiwcahxGFtSR2hqvPHKqgJB2M5Lyby87z+Oe0xv1tdRh7NRZTbKN7\r\nw4NLKY6M133UNL5DJVF34bTiBksEVPUMjx6TR+bjuPsFkOt5JaV4UxJTGwzk\r\n92s8uZZvsxa4tPIZThTqGjXK3dWBQ2T0Xqn4Iw1PLOX8rON20qAakjNQ8inq\r\nq23BxQw2jeZkzLW/rBm3jdsGcXbbpuQqjCLHgSmqoDOw0GMDGQlgdd22fkNA\r\nqNoYaDSBjKaFUiHU7ZC0CFfbX23ZZGdwtZD0DN9vSP2cpG2RzSUwyoabjRUJ\r\nTuFUR6rmdpL19A61oP6Uhr+FSCtvQL8+8vB52HdFYpE+cQu5i/bFmfqXfSvV\r\no17H1EZNeaQN6vtUtpROUKvrhqbrFSOYgXqfCkY8pzmoVDnbt/oLpNiI0t6b\r\n4oojTO7JPRBA8FfXkhaFG/AMoCtRLxFzh2Y=\r\n=JT3c\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.1": {"name": "@babel/parser", "version": "7.21.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.21.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "a8f81ee2fe872af23faea4b17a08fcc869de7bcc", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.1.tgz", "fileCount": 9, "integrity": "sha512-JzhBFpkuhBNYUY7qs+wTzNmyCWUHEaAFpQQD2YfU1rPL38/L43Wvid0fFkiOCnHvsGncRZgEPyGnltABLcVDTg==", "signatures": [{"sig": "MEUCIEL9EhvNr57erMcmePHlLI5BiPilEWnGRBu/w7iEMQStAiEAr8UPqXYlzNrRKb4hLV2uSUfVv8FEiPZohiJUTPIN3Vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1954853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9AF0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoZw/+NSZFGwgsNr1LE2RGMsZXs2E8Q6jBi0VLJSEt0n0uyjRINQKB\r\n+39kVuQPJV4LGPx2cC4cu9JCVgTlgIkHe4E+w1F594c0QSXln/Tp4P0+50uN\r\n82q71VwUkZIJcYgQ8AthmgMaLYQNz+oeWNQw0n81s/R0MPYrhqTvSbc6PipO\r\n1CplYabr7kJ83+oZJAIngrMl9L0gQTGiZ2+6tOMe0gk9uo03V1yMrzwnL4qR\r\n5yrUHQsonElfzmr9/1Y5NbMRc6QTfx9yqzo3UP62SlilyoycA9ydNoRUynaq\r\nywz1JuxzIfKpW51j5PJpxGgwx4cplyCfqdik2peFLnIJrJIgqD1uq4P0juWx\r\nbzKmL2nQ9QjqF9YWZ1EtZY5vUUMf8qQ9ULDLG2oZPAR2pVQp32e5FvwFH8SU\r\nvYVwXXXEfzwVoLuaNljcuxzCU0IumbYQ6M/XVPvS4zYFwJl6GTe1H0xykuaD\r\nwm0jS35MjzUo2baeAvIXY8G0M01iMxzKwjSiyngIk6HjhyH5yTPdRD02NP1x\r\nnIV33pCigA5ukwas4KhZrFGrUhg4yXDIYTHE/PgnJzeDKDr+UYyXRPOmqANX\r\nizhz/eyjxbl5Gma83KjXvG8/Vg6puXeOrtGoizTxXQyly5Igm/R3pe0/tmac\r\nMYGswTgyllJgQD9LzOnzBTVoN+VpER7xZ0o=\r\n=dfuv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.2": {"name": "@babel/parser", "version": "7.21.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.21.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "dacafadfc6d7654c3051a66d6fe55b6cb2f2a0b3", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.2.tgz", "fileCount": 9, "integrity": "sha512-URpaIJQwEkEC2T9Kn+Ai6Xe/02iNaVCuT/PtoRz3GPVJVDpPd7mLo+VddTbhCRU9TXqW5mSrQfXZyi8kDKOVpQ==", "signatures": [{"sig": "MEUCIFZffjM17stD+C7B5+XY9voKiwATDpfqw4a3/Zhcg/RLAiEAsND1UARJMHUKDFyhu1ZaQz1U+hR3ZGsXxjqpsQQOM9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1953310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9zJ3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNVg/8CB8S/jeJ2bdOQYDufrj5DIq2cgfyFYSPAswR97l6jEQyE6tb\r\ni3KH6/KRcL7HQsIbk6o1AHDc2ET4WVjFqf/G+93CFYFQcm15YlWc9Fj29UkQ\r\nI5u1PBz5TjAFiOXe3QxPuYwjhCMMI8tix4ZvE4sslDfKpKWo8gYhrJwURHvw\r\n6SE0C1MLErlh0Y6BaPq8QBBG+n1B4Mvjb1aSjM2ZiT/iS10nJfJGPnpvGtuw\r\nxzw3cHZBnZjtexfV2iZ9/xsB7mTJqTsJqe/mJ2Nar7JfSKZ8GdgWtJkqbW+f\r\nuSOVQiI6jmLErKBEKLV8asD+VyQ/V1I8kPSEo/HfpfbBDEUVdI1fPLsiMTaO\r\nNrcj5mn66XMSA6qeGpfoUZnUHNFvbmXowa5Y+UO+GF60v13anK4Ztb7EbmHV\r\n+PqrWxy8MyOxtZH5zWaEjIwuNUXUDOEJNbc2zEydc65pi6IYSm/EmygfIcLU\r\ntVQ2UMdSVUc79oDzoOMDA2N7iP/bO1Zjveq27/pf9/My9pCmkJMHUyo/L6Dg\r\nwD+RMmXlNESN2100eeaF0g4KiE6L5aeTeulwWxvUMqLTi6yhiRIRCFrl687C\r\nxnq1h3vMRRrdZ27KFh26jXs7Ksy/nTOBhDvI6Y3PeJc38THwBAULw/xvxOg9\r\nmURrLjynmS6yRpbhu8bWAuWpxff/HbMV1/4=\r\n=IuGC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.3": {"name": "@babel/parser", "version": "7.21.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.18.6", "@babel/helper-fixtures": "^7.21.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "1d285d67a19162ff9daa358d4cb41d50c06220b3", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.3.tgz", "fileCount": 9, "integrity": "sha512-lobG0d7aOfQRXh8AyklEAgZGvA4FShxo6xQbUrrT/cNBPUdIDojlokwJsQyCC/eKia7ifqM0yP+2DRZ4WKw2RQ==", "signatures": [{"sig": "MEYCIQCfw2S3oB2wUe3OwatuP4oF/4FBR8miVEGo2WhTMAXLGQIhAKlgEuPdWmqpsdf2C3oYbcncTFpXBYZCa64sMdWryBZx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1871919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEIvUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgEg//ZOKGdHGPjzGTrBV1/dV5AJg32M93VKDvSGrZcV37nN9GpYUx\r\nsft0ssmQqLJiBr9ljyvXRjhEhBWM4LOoVWm+Qecq/QOyHgpbXuOLH+B87DGY\r\n5Xhk9X06AJov302F6BI46v8Ba7EQM6LlB+04gZixQGv+uPqPU6pnuuFoqBAK\r\njyykT+gIeS/LS2XRAqijeo3IYJMWWz4dbvs9H6IDK5MQNYuu5aBks9xu1IzR\r\n1Tpynj0k4vHyTzyVqaWWOdVKEsS0S2Hbb/KU6ygQ8+fu3oFeIcnA0WqCJUHu\r\ndw132tYomSK7RohT7fQqxy0FqQ/0+TK2n6wEWac/moflK+pDg/yypZxDKyVP\r\neHgdQUUT0IKeOWFTXeSOVuCKCk508vXhl7i/3gAgPH6xvI6hiT0559q8Ws+n\r\nfadpLSnM7f4Z3oW3SCllaL2C9lI46ty04zNSPNZyvB1h313XmgTuj14Rbqk7\r\nAHpau1yAbJD7c20HSB4l/HbTsVA6YW7mYwWBJvUcyBkOW4FxC0qTkAA5NpQH\r\nftFHD8vEjW427eIniBAhAjvzH53PpvxbnVfb1zCuVhEVBBV+52ufGW3T9+sa\r\nNQ+ZmQl09qrsTqi7w7y38wuTP9AE8tt/cml4Krboz8LvVYn4kl5BBT0qVcvF\r\nH5be5N3miuUPrFrBfYiRqb3oThtdkjJurTc=\r\n=GuLd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.4": {"name": "@babel/parser", "version": "7.21.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4", "@babel/helper-fixtures": "^7.21.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "94003fdfc520bbe2875d4ae557b43ddb6d880f17", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.4.tgz", "fileCount": 9, "integrity": "sha512-alVJj7k7zIxqBZ7BTRhz0IqJFxW1VJbm6N8JbcYhQ186df9ZBPbZBmWSqAMXwHGsCJdYks7z/voa3ibiS5bCIw==", "signatures": [{"sig": "MEUCIAXQJtx6OGY2VzX5Ylq7lZDujUH7RmCTfYNyGkwf2mJ0AiEAzDSzUB2TlqcCWpk0dA+LDcuT/HbmkE2s+7TV4Le6Atk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1873540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKhQ/+K1wazbwH9SVw3qGtuZ8Kda7PlaonJm4D0pnjnaO9jyc2oOaK\r\nMJU4SHg1WyYApousMRekSMrxQiKSOfTzuB5FvxvXe/La09Hej1RaDcEkbIMD\r\nYtMY//PqhVowUsiro4x+t8RayZlffnWcES23dysv97ESqNsoEqVPKtP/HUd8\r\nERECistlI+M7Geag5mso3nsoxuIcjGqDL0Ukr/3jn4pttXB6SyjuowasXNQ0\r\nredlrfKVSIhTMQOcMjWrhHtLDPzBEgCtG3lFR8Aaw02wTh3H5Y/N9p8it8fu\r\nAe6kgZcMBOy51/zDfulcokypcQqwxhlKmfR0iqw7sHHUEqcScdv+5ImDGrbJ\r\n7rbtr2pHYxOgoxBg44obaEizjEOtC2o0aooaxzt/3fhw2wWLdLy81BFaP6W2\r\nApD0NhDOqwRJJmrHLjz724RKGW55vSTF2A8cWp08RK9zrz0+Huaps0qNEnZn\r\nUVUJuxGaCJR+fCOXTXdxGORyTFgIv++tB6EjOtNTWl/XySGsxf5sJpaKtQGq\r\nbpWtkjxhPm9Yre5E7nb/7hzxXMm7P75vI8rw8M83+6+fNPX6y/HPin7yu8b7\r\nDnMZuZ+J0gj1chcTZKEeVYbTXB2h6Xz2tfw34PqJBzZ2vkc5V35kIs0q1J8B\r\nuCaViNmeLu/4Wfb8l1dv4CjGARoOQXs1cfU=\r\n=mzRV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.4-esm": {"name": "@babel/parser", "version": "7.21.4-esm", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4-esm", "@babel/helper-fixtures": "^7.21.4-esm", "@babel/helper-string-parser": "^7.21.4-esm", "@babel/helper-validator-identifier": "^7.21.4-esm", "@babel/helper-check-duplicate-nodes": "^7.21.4-esm"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "b35d950205349a3e5327b228d9cae871921187b7", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.4-esm.tgz", "fileCount": 10, "integrity": "sha512-MK9egyIALgCAHh/t4WKySWKi+lZ/5ddI7srJPzxIbuMo2kgLgpwm/In7qeyk+lLELuerovzIk19oOXEHzTF+eg==", "signatures": [{"sig": "MEYCIQDBofTs5Qog9Mw0EfsqGuzv2UI3qUYO2OWqxdM8u+rj5QIhAN+g5bOi6oxjXRrgQngjXfYkTUgn5CY/RqL6mQlHPv50", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1873584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC9+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8Zw//QMKby7Vr9+w5TcQNvWNSL/aTs/DrIj62ghbpzGZnLv6K6qC7\r\ngdBiCPSmNd+1AXSxisyEkYf9hRNRctHODJJFiSz+V7ZCPcwxPEkXp4/DNswz\r\nbPTroEIgvC/+ukn2BMhgyq69noNg4xA+SQcSZCHmoOgXM0xdD86E24K691s2\r\nHncf5B2nSRcC7py2f6TfjhN9JwrOkK5ZivHGRD3CvwOwa+vkJFZ5U4ot8IRE\r\nw+t74pa1q3+eCGoi7xkjF4eBZv4pLoQE9FgegYBXS72+uVgz5CN/W9iHRjd8\r\nUsCGzxA0I3dED83qt8cFGx32zxm3s6lLCzEUsKQlMy97HIF3buGJTouUqfS2\r\n8U0+PLAaAkUNiQAM0fYtpsLigA3ii+tenlwD7z5FOpzIPvbybnYivav3/YgI\r\nSdLdpxY3nkC5fDrI4E37UYuEw4AkWvOBCjKt+t+EThPP9SyU6uTqdOiXNKQF\r\nQ4LLMqZrlfpcYE0rgbhb+Ul1MzPSD3NFHHJRHR0hw3VVmCr7zsNWhE01Z18E\r\nD7+rAtYhM51XI7GBWVzvWMSgu9Gnvg29Z0DTv65Ce7blzErFGSa5zzNVwPF1\r\n/FthwWyI8TdfNofO7dHrP5/i+olQUb24nG1BzfZhU9ubjmOnj1pri34GZ8qV\r\n07GNCu01NiJ7GRGZGUhDhvqhc7b5ru7PmEY=\r\n=0vaN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.4-esm.1": {"name": "@babel/parser", "version": "7.21.4-esm.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4-esm.1", "@babel/helper-fixtures": "^7.21.4-esm.1", "@babel/helper-string-parser": "^7.21.4-esm.1", "@babel/helper-validator-identifier": "^7.21.4-esm.1", "@babel/helper-check-duplicate-nodes": "^7.21.4-esm.1"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "2b85a38bcfdfe8f17f3a9d76e8fed83e57cb911c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.4-esm.1.tgz", "fileCount": 11, "integrity": "sha512-8zbCWPI4MRwh4rJYmg33d4KpXSVoqld1XyDlavhu9BTeYMZtBcSm0CUvkM4X5paIv/nzGyZ4gUDMATcQeWIHGA==", "signatures": [{"sig": "MEQCIDmklAJlvLAJFvu8XBQUFejxufwpQAVkIkMFGVVUmZmiAiAI0NOjVtBrSDDK0/A/cWvNNLMAVci09OSJxDCc870z9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2361122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouEA/9EIErsG65Tg/5xye99wL2NwNd/LjYFyZf9lekpVInV3OQUGqI\r\nyXcLiVXiosUnZt8vcDae+amz5RzvSMJx6k83dd+y+P7jD1ASmp9xOyiudREU\r\nK2h0v1FtflWe0XyJbFbMiIbCtJLmvC+IBixbdLUA53soHAKEGOBmB67N5Ygm\r\notxZFUegaJJDu/2wmXVhXZsU050Jvv6/wd3FeNoCF67dSgy9F3ZCca5SwjRy\r\nbJIDLoGzDYsiZKERCt5BELJugsbVO0MBgsOK0Cs9Tsdt54caS/HndPWB1aY8\r\nzzdXHZATS8ZJUp63KRLlF1q78trKFzxcV0QYHHv99TL0qM6wVRXdzl9OOZVI\r\n2l3XlOofE7cz1qjruRDlH4b9rj6Xtm5Ma7vqoF26M58J2BdF2CBPR2IinlbV\r\npCi+KhfZILAxuPI3FtxRk7mhDs4FRp0cU8hmkJ77dbLR/r7r5sK7fc3Vqvb9\r\nac+HfixCILrPEmo7jgti2o8YgCC/NHz7fR4UuW0G/EHU7WwglmsmCwzag+Cv\r\nXmguOGO1G9rKB04gJoaCE56ryIWlC6DpjJzuEdWKdU8J7J9lLLm6m0G6UZyC\r\nFB+5ddtE2bE/k1fjcRYoZA1g3tkdk7WyKWQKeyoV6YApks+dFjGBH779VZYY\r\n9ImpIdfPpPI3QHaUQxp0e1a6j+tBLkbhQ9c=\r\n=haPb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.4-esm.2": {"name": "@babel/parser", "version": "7.21.4-esm.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.21.4-esm.2", "@babel/helper-fixtures": "7.21.4-esm.2", "@babel/helper-string-parser": "7.21.4-esm.2", "@babel/helper-validator-identifier": "7.21.4-esm.2", "@babel/helper-check-duplicate-nodes": "7.21.4-esm.2"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "0fc581d4a70b1af736e298028a4ef574f8756480", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.4-esm.2.tgz", "fileCount": 10, "integrity": "sha512-3F/AlC/TggcCwYpY370ZH0uyKAxvtOEi9z9vT9tL9QRZmCT03RIziz6OxB+SyzgMgxaR9MCNinRXar5H1n49xQ==", "signatures": [{"sig": "MEUCIQCFgalhTsuvmW/Ggkd4JX+YC/ZxkBZJV0iL0PqW9Z8fPAIgE+fz8VaGpyKyogggZCUAf3L7e07xNXxLxrwelMS51AU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2361097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYQxAAgF7oWMv2KNkmjXg11akPo5dz19J9EcmzxlVy0qmoAofliy3z\r\nWib952n7Etpt7B+g0uwgIYKgcKAdg7Zaz1w8bYp/1eFTZQmDTbY32F2blkW7\r\nZF+jTXYVQXOZjY/5iYx7BoVdZKzq/WEYDXrHwbV4TBhi3fRYTqMqx4zFndq2\r\n9MWk+sbGyXJgvOkKjuwYAjXExGCFtx4Xm07K5RITVXpK9aDoqefsn64v5nlE\r\nNci/oPFam3+LK8HrPZ0qZG7ScmlXvvfCN2AtoGss092mYAcThLC4Ajn6Cy5W\r\nnSIYxxzvRPw31mDXDt5AdlrM1KXw+u7Mc0Ok7G2Xy0h8HxQ5mZcPCRlA+D7d\r\nEquSvwhoKFdWi28KBbOOrJjQQXf2Dd+fl/k2aAZJ4xdKl0icKzMwkBIuyXvH\r\nrK24QpKm4rM9gb3VvKjykD36jiwof2+RozhCjLbeecHpMipuYMRjQhE1WLC6\r\nlKcR9KzSSDXa+ZuY2e4Su+PS099cnEoGpfWzw769nf+VIkvwiFaAKFtEuApy\r\nQyhSAKcw+NFfIk94TGu1xinrGTQNbvZbqPxg+jzD7RpsB4XxQOxqAd4opr0L\r\n+DrzyrjQLpoOuVj54dGqiyy2ZFSoKeupuehEdrsFCZuofSUnO6N79Cc6kEu1\r\nrzwNJsYpiGAi4CDz5VGqdBk/eQjQ5al8SXk=\r\n=tclY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.4-esm.3": {"name": "@babel/parser", "version": "7.21.4-esm.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.21.4-esm.3", "@babel/helper-fixtures": "7.21.4-esm.3", "@babel/helper-string-parser": "7.21.4-esm.3", "@babel/helper-validator-identifier": "7.21.4-esm.3", "@babel/helper-check-duplicate-nodes": "7.21.4-esm.3"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "945632f947bf0f140b0807d5ef51b24d67299624", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.4-esm.3.tgz", "fileCount": 9, "integrity": "sha512-BT3kbwR5Ioxwu3TJ2rr4rzgBLNqADAXCGUhkMwjYA7L92NdCWg66cSYv31khC5cqVg46QQKwrvd71k7+9U5w8Q==", "signatures": [{"sig": "MEQCIDnGmt+6slFdMaaeWQFx0M/6kyRl8gAws36o87q1ApagAiBUBMaOcl60eOqUlw3W6wP/CaSDBdHzRneNSrc6WQUg+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1873762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDp0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqwig/+OgWTz4j4tfSbA9EJ8ToH3B6+chwEA/WT4LKvHbpHzKN2XkL0\r\nj/EsuIQVf8mmncVkYCG+668nNTgc5pMN6UGEA5A15NqyIBm6TEcvXryI0BC2\r\nbF0mkiD25Ivu7lj88g6Zm94UIQX63wXLstWSE2l0+o7dZDwjPTz3TEe13EDb\r\nVFwGcToT0eCoMqi0BDFafnoRGYR97br5iB9U9B3z61hjJdNCUauu5Pe+7RRj\r\nNHdkYB0s5/xnsXAyttPX02Ja/mersq5uSYLDi3ui0Cut39BXsexRLfqfLr7F\r\nR0x0v1wfF7xgnbZUtmkHtIaU9U31NpDDxk7VoxAzV/NdPONxb3ZCVAknr9bi\r\ni1O7gfFnemOl8qaLmOdjJcrBfSR5TwY5DwLkdOtQ+L2tjubT8vL5tAgXE71A\r\nzVj6/A0/drES7mu0EwZR6Sdc+fz7c6Vx1PTkRL+qDxbByVUsPEYsiJTpiEST\r\nEd+X1tm3NmPiyOwHRWrhwdRnriXzUdZhBL/goEEkXudK7q/idj1W8DK55XT9\r\n2/PjhX+3Tk9QA/P4pWezQxZ//57ogTNaSd+PgW+D1ofv+Kelv7loo1X59xNv\r\nKWqNfFkWkoKCugLOulX/27/vy1SHoh6ygCrhF8b8WCVDnwklJFVVJ37LJcW0\r\n2OWlXLNjpIK+Jp+Vz+DOF4EZTWoVJakPsNI=\r\n=SFEK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.4-esm.4": {"name": "@babel/parser", "version": "7.21.4-esm.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "7.21.4-esm.4", "@babel/helper-fixtures": "7.21.4-esm.4", "@babel/helper-string-parser": "7.21.4-esm.4", "@babel/helper-validator-identifier": "7.21.4-esm.4", "@babel/helper-check-duplicate-nodes": "7.21.4-esm.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "36849717288921ffe4a37205120ccc03c6d45a5c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.4-esm.4.tgz", "fileCount": 11, "integrity": "sha512-vhp82JhMLIz2djtPV8lGfwTCHdlfTYdvFKntgoR5dpji3twb/lEJJOFlgXaRX+zeyNWzssmz/weEt5jfEa0z4g==", "signatures": [{"sig": "MEQCIGxAbra/tzeKMuUB+Kp9JTkJqyAHqDIUaCCUwvYWEprDAiB07YdpCu4kp42gGwKSyOPd9DY1EQTZuKc8H0m7N65+mQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2361117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD5/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAiw//ZV4wrKYP9AlY3AnmkQ5oO6PcTnvbOZhnxHR1xOW4dRE+otah\r\naJWrR78DHcby76ElGzODaTDoEqIyv0et8T8G13xr/tcJPjTgLh9cpPVjv6UB\r\nSaQvljPLzVcgQ4d88Ond+HW385zBZhFYscR3mu+d7C5yjVLzkERsakzbs5zf\r\nLRTNQb6TtZgbPUAEJgmvh5HPltm+8v4TsdLjIF+I12JRC8xeA5RecF10txQx\r\nXKySw0yJ+4oo0tZ5YrB2GKJZc5Ed3XLc7+dYU+7qv6iDCXJR7u/rVatwjRLP\r\nEBna7t2YzpMl1KT1YNGZLRfKdUi8FGRyadHjXt2ACO+bSUk0pY9YbjHE/Lb/\r\nNukAkXYRMY4Nfl53yTExRui4SDjJsn9ZCpEzHy7CGWjCduI7l5PW0NZTI32P\r\nGkkAH3IQHTKBdTzD2DXV148aMKGjVerHeEF4pyse7PBd9GLcBRNcOhlmWDoR\r\nzrYrGrlMcmHPbnOyMqcrD/fZL+9TcJ5Zz2Ki31fiK5Ap8/OIy5LXSK/8Yu6i\r\nAD1LlcOTJNprA5epNb3sIa29a01HDIqUySkrsMis04pkBn8VrhV7FGwKHKnB\r\neqnAgHAhYDn+YwojjdKU7vOiEp8GJZj6PJpLpxca14Ca8G/GFCfkARQawm6A\r\nsWVd2cooNEfsBWcsKuE/5DUTpXuPMmzmpxo=\r\n=Y8AP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.5": {"name": "@babel/parser", "version": "7.21.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4", "@babel/helper-fixtures": "^7.21.5", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "821bb520118fd25b982eaf8d37421cf5c64a312b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.5.tgz", "fileCount": 9, "integrity": "sha512-J+IxH2IsxV4HbnTrSWgMAQj0UEo61hDA4Ny8h8PCX0MLXiibqHbqIOVneqdocemSBc22VpBKxt4J6FQzy9HarQ==", "signatures": [{"sig": "MEQCID/BYP2b6TELqMx7l5dcb1HeXls47Xp7PeyXyrre8H3aAiBe3/Als7cAvfmoXGRmNR+DkOeXruypWk0Qj4ltUs6HVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1871698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjPA/+IFujrspStM73dH4WCPWcd1rtArkCYCZ3UhqxBq4DVCFm0ySz\r\n/329VBdLZQoqEJd9SlvteUDnVgzNdn/+gin8RvQGg3pGLr/AZ7lLdng6wbg9\r\nZgNpCSrx5oQaj5OsPNeRItqFfVS369oRGwd3n8D+KuJqGW1LTyZC7WxRTjhH\r\nwJtnpspAJR1qeLXtWniRjeLn4h7iNvkvnzNXVuE5kj7O4/oPXh8HxeD9ezhA\r\n221m0mWjLYWkXrdPM1wKM+K6YZc1LcA61k7RLCAT7d9e/twkubbC0dEl4ifQ\r\nIHR89RBoG1wS8OqSyCeNCVqHf49v7kgCFao7XmSC2x4gCUDMyGV32Way3j6o\r\nyG0M22jTKN3udau1gKRgdNj+qFNfTMLZZQdRu+7SQBpr83NPJ0UCkt9eJ1Cl\r\np6w4EHk95D5APhaBFJOvSkDyYJoQMKGpjgUXvc8kh7ffF48Hp/dPhJcUnQRw\r\n0X+iBDnbPoqqzkdrDd4jo8XhblNEz+chp7N/5TYXFZc3n5w++d1ondq0UVus\r\niYbszxrzYJ1jDQheEAitvnRMjLMhGrAM9Tck+2yKnfETx5p7PJZfJTUnXVrZ\r\nmj2c2bSOvxQzdXa3vmPB8AMXqmMaMo9vdG2IX5+hZpDdMmmM65gcDL9HYi4Y\r\n9yuVf6d9ImrOLi9YZdeNnS0x1/Y/aehhpYg=\r\n=FXJa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.21.8": {"name": "@babel/parser", "version": "7.21.8", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4", "@babel/helper-fixtures": "^7.21.5", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "642af7d0333eab9c0ad70b14ac5e76dbde7bfdf8", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.8.tgz", "fileCount": 9, "integrity": "sha512-6zavDGdzG3gUqAdWvlLFfk+36RilI+Pwyuuh7HItyeScCWP3k6i8vKclAQ0bM/0y/Kz/xiwvxhMv9MgTJP5gmA==", "signatures": [{"sig": "MEUCIFXOOxzfohFSASf63AcVRy2UVK1tL7U0KmoAu3M3QPICAiEAozK7hSGBV6Y2riUrx4/lY5/PG2dAimevptJR28unEUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1870760}, "engines": {"node": ">=6.0.0"}}, "7.21.9": {"name": "@babel/parser", "version": "7.21.9", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4", "@babel/helper-fixtures": "^7.21.5", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "ab18ea3b85b4bc33ba98a8d4c2032c557d23cf14", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.21.9.tgz", "fileCount": 85, "integrity": "sha512-q5PNg/Bi1OpGgx5jYlvWZwAorZepEudDMCLtj967aeS7WMont7dUZI46M2XwcIQqvUlMxWfdLFu4S/qSxeUu5g==", "signatures": [{"sig": "MEYCIQDXU//Phr6Pa0uyo3YfxQkSYPqY8oKQxZ7SeZBLblyeXQIhALxeEGk1q6OBNW49/dd8rBDoPwMeeIUZOmRDQNB/GOcq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3669408}, "engines": {"node": ">=6.0.0"}}, "7.22.0": {"name": "@babel/parser", "version": "7.22.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4", "@babel/helper-fixtures": "^7.21.5", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "b23786d26c2fd2ee07ec7384a96a398c3e3866f9", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.0.tgz", "fileCount": 86, "integrity": "sha512-DA65VCJRetcFmJnt9/hEmRvXNCwk0V86dxG6p6N13hzDazaLRjGdTGPGgjxZOtLuFgWzOSRX4grybmRXwQ9bSg==", "signatures": [{"sig": "MEUCIAJu2YczBc53No4WCr1IcwmzUhzvve5nCmPM716UG/qaAiEAw8zmhlF+upPpJPefHavaqsUiakqGwKdn04jvGIlnw3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3708595}, "engines": {"node": ">=6.0.0"}}, "7.22.3": {"name": "@babel/parser", "version": "7.22.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4", "@babel/helper-fixtures": "^7.21.5", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "838ae31893373222cd9062568e2192c670037e00", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.3.tgz", "fileCount": 85, "integrity": "sha512-vrukxyW/ep8UD1UDzOYpTKQ6abgjFoeG6L+4ar9+c5TN9QnlqiOi6QK7LSR5ewm/ERyGkT/Ai6VboNrxhbr9Uw==", "signatures": [{"sig": "MEUCIQD0ipMnBDd8u0lz8MZcdoYYbwtPcfqMqZkOrnjexeXENgIgOKUtLLCRLWsmHPQj5EHTsvZBG+lb46A5EL9KAogoGjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3721098}, "engines": {"node": ">=6.0.0"}}, "7.22.4": {"name": "@babel/parser", "version": "7.22.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.21.4", "@babel/helper-fixtures": "^7.21.5", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1", "@babel/helper-check-duplicate-nodes": "^7.18.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "a770e98fd785c231af9d93f6459d36770993fb32", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.4.tgz", "fileCount": 85, "integrity": "sha512-VLLsx06XkEYqBtE5YGPwfSGwfrjnyPP5oiGty3S8pQLFDFLaS8VwWSIxkTXpcvr5zeYLE6+MBNl2npl/YnfofA==", "signatures": [{"sig": "MEUCIAwTo4toJtMd1fLQOChQkSLakNSRd3AHua9F/eyVqCmsAiEA1C8Oi05ZEq7HZ6ZV6r1MtmmqaBbwIvlgjih0rsTSiTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3721473}, "engines": {"node": ">=6.0.0"}}, "7.22.5": {"name": "@babel/parser", "version": "7.22.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.5", "@babel/helper-fixtures": "^7.22.5", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "721fd042f3ce1896238cf1b341c77eb7dee7dbea", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.5.tgz", "fileCount": 85, "integrity": "sha512-DFZMC9LJUG9PLOclRC32G63UXwzqS2koQC8dkx+PLdmt1xSePYpbT/NbsrJy8Q/muXz7o/h/d4A7Fuyixm559Q==", "signatures": [{"sig": "MEQCIHvfbSa9LzIJBC50T+rumtG6jd5zbvHxchfEclQm2zZaAiBlQy0JgHPdYac1QuvYVC6ekOCL1g1F8iGVOB2kn0y4lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3721473}, "engines": {"node": ">=6.0.0"}}, "7.22.6": {"name": "@babel/parser", "version": "7.22.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.5", "@babel/helper-fixtures": "^7.22.6", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "201f8b47be20c76c7c5743b9c16129760bf9a975", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.6.tgz", "fileCount": 85, "integrity": "sha512-EIQu22vNkceq3LbjAq7knDf/UmtI2qbcNI8GRBlijez6TpQLvSodJPYfydQmNA5buwkxxxa/PVI44jjYZ+/cLw==", "signatures": [{"sig": "MEYCIQCywN36aNZLfwRGcB8f3UKxnVBpkktOMUP5a2DauJDJ4QIhAP6ygDIE6PO1MZR7jMWnhCo/hr3i2Wbp30JrzGocpJpg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3721380}, "engines": {"node": ">=6.0.0"}}, "7.22.7": {"name": "@babel/parser", "version": "7.22.7", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.5", "@babel/helper-fixtures": "^7.22.6", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "df8cf085ce92ddbdbf668a7f186ce848c9036cae", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.7.tgz", "fileCount": 85, "integrity": "sha512-7NF8pOkHP5o2vpmGgNGcfAeCvOYhGLyA3Z4eBQkT1RJlWu47n63bCs93QfJ2hIAFCil7L5P2IWhs1oToVgrL0Q==", "signatures": [{"sig": "MEYCIQD1X8EjSjLrNo+sCXus3K1KptczZ9tBYCxGLNPkje5kUAIhAOTYuMKSWfRq2xvpr7i4dzKvULKhYiaIjF6A8omNhsyp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3721470}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.0": {"name": "@babel/parser", "version": "8.0.0-alpha.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.0", "@babel/helper-fixtures": "^8.0.0-alpha.0", "@babel/helper-string-parser": "^8.0.0-alpha.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.0", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.0"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "227737d23734282443bfb20994cf4d6291b6ab22", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.0.tgz", "fileCount": 86, "integrity": "sha512-l/waMp+mFP9kl0FVK1r/Qw+9zFd4I6cGDx0NnM9d/kRVuPGXA4cm3fO+tJLzqWlH+Th6egoi6VyYMdHUzLII9A==", "signatures": [{"sig": "MEQCIHlGToIOQhmfJO7PmrdUs7mRlMbfQgGKYAZNRkqoG+5KAiAktqVtBAo9s1WYXVevySW0dGbL/s+wuMDs/JNa1P3wsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4121013}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.1": {"name": "@babel/parser", "version": "8.0.0-alpha.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.1", "@babel/helper-fixtures": "^8.0.0-alpha.1", "@babel/helper-string-parser": "^8.0.0-alpha.1", "@babel/helper-validator-identifier": "^8.0.0-alpha.1", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.1"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "ea3ad4264447202a49d29d2d596eb78427bf0ed2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.1.tgz", "fileCount": 86, "integrity": "sha512-ubuaD0I7m6dlXE2UeMW4Zx16FO6l8sHhztQbg1IPspoxItFvc8F8VCnnnlOBh22COx7pAeEt4HatBPt8nFv1pA==", "signatures": [{"sig": "MEQCIAmjzg9s8S8LGkT8iVVtaJA9HmnQCiwpm69jyCEB4xaNAiB2U5to2TX5JWD3NcKu4jzvx+GKx+slObBjpnePbcIVfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4121013}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.22.10": {"name": "@babel/parser", "version": "7.22.10", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.10", "@babel/helper-fixtures": "^7.22.9", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "e37634f9a12a1716136c44624ef54283cabd3f55", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.10.tgz", "fileCount": 85, "integrity": "sha512-lNbdGsQb9ekfsnjFGhEiF4hfFqGgfOP3H3d27re3n+CGhNuTSUEQdfWk556sTLNTloczcdM5TYF2LhzmDQKyvQ==", "signatures": [{"sig": "MEYCIQD6OFNlo5aqtn5X2gTUmCgOXCaT6MpP76Dn0KKZ5s+GVQIhAKqpNnnQSCLf32vNmybLPbEHaZQ/eEA4eGE2jz0lWw2C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3700407}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.2": {"name": "@babel/parser", "version": "8.0.0-alpha.2", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.2", "@babel/helper-fixtures": "^8.0.0-alpha.2", "@babel/helper-string-parser": "^8.0.0-alpha.2", "@babel/helper-validator-identifier": "^8.0.0-alpha.2", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.2"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "a7b3d4ba401e362339fe432cb67a5ce72331e054", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.2.tgz", "fileCount": 86, "integrity": "sha512-XJruaQb5irakFnM0ZZIJ64PXmRu0SXqOBtqppTqVfHINKM97CFNwTJOFCAmV/p6l8S57vqgP6ZQmvD/zVACGMg==", "signatures": [{"sig": "MEUCIG+cCAsNq1zui1DQk9blmC8CnC3R9Ayj1LzDjbbpOsplAiEAjocEsg6rZwmFy7erJX2/OA27X750/4+l+dVQj8DYinM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4121013}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.22.11": {"name": "@babel/parser", "version": "7.22.11", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.10", "@babel/helper-fixtures": "^7.22.9", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "becf8ee33aad2a35ed5607f521fe6e72a615f905", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.11.tgz", "fileCount": 9, "integrity": "sha512-R5zb8eJIBPJriQtbH/htEQy4k7E2dHWlD2Y2VT07JCzwYZHBxV5ZYtM0UhXSNMT74LyxuM+b1jdL7pSesXbC/g==", "signatures": [{"sig": "MEUCIQDDo5vi8qKefiImsRC86DwzhsA4vDBxFmLXrDhSqOM34QIgOK8S1BtdxPwFrQgjvfpytD6ERqqyQxuItBakDCl88js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1880760}, "engines": {"node": ">=6.0.0"}}, "7.22.13": {"name": "@babel/parser", "version": "7.22.13", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.13", "@babel/helper-fixtures": "^7.22.13", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "23fb17892b2be7afef94f573031c2f4b42839a2b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.13.tgz", "fileCount": 9, "integrity": "sha512-3l6+4YOvc9wx7VlCSw4yQfcBo01ECA8TicQfbnCPuCEpRQrf+gTUyGdxNw+pyTUyywp6JRD1w0YQs9TpBXYlkw==", "signatures": [{"sig": "MEUCIFQUtYF2tRj8KHNkLFgCKFHKlg22Zh3S4cWFOy7vZ/pMAiEAlksLQj58cJsYForsE0Pdd+hVdlf7NiEMGtsomc2/hys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1880807}, "engines": {"node": ">=6.0.0"}}, "7.22.14": {"name": "@babel/parser", "version": "7.22.14", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.13", "@babel/helper-fixtures": "^7.22.13", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "c7de58e8de106e88efca42ce17f0033209dfd245", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.14.tgz", "fileCount": 9, "integrity": "sha512-1KucTHgOvaw/LzCVrEOAyXkr9rQlp0A1HiHRYnSUE9dmb8PvPW7o5sscg+5169r54n3vGlbx6GevTE/Iw/P3AQ==", "signatures": [{"sig": "MEUCIE4JZbpJQzcWmo3oX85HGDr7tNK16eObGYcCPNvXCrFjAiEAhlx3lI/x9sv1h/GAT0yKUX2i6v9UIQHmRM2XKxcU188=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1877467}, "engines": {"node": ">=6.0.0"}}, "7.22.15": {"name": "@babel/parser", "version": "7.22.15", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.13", "@babel/helper-fixtures": "^7.22.13", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.15", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "d34592bfe288a32e741aa0663dbc4829fcd55160", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.15.tgz", "fileCount": 9, "integrity": "sha512-RWmQ/sklUN9BvGGpCDgSubhHWfAx24XDTDObup4ffvxaYsptOg2P3KG0j+1eWKLxpkX0j0uHxmpq2Z1SP/VhxA==", "signatures": [{"sig": "MEQCIFyM3nBj9nc8ossyYND4HlP4eMhbNgLxyNKI2KjWWOaVAiATmW/2A/xZl5isO05DFV700p0MndbGpgrtSpLCigecdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1878159}, "engines": {"node": ">=6.0.0"}}, "7.22.16": {"name": "@babel/parser", "version": "7.22.16", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.13", "@babel/helper-fixtures": "^7.22.13", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.15", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "180aead7f247305cce6551bea2720934e2fa2c95", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.22.16.tgz", "fileCount": 9, "integrity": "sha512-+gPfKv8UWeKKeJTUxe59+OobVcrYHETCsORl61EmSkmgymguYk/X5bp7GuUIXaFsc6y++v8ZxPsLSSuujqDphA==", "signatures": [{"sig": "MEUCIQCVdNbK3T8BPHndrpNM9Y8zNN7ZiSUICg0E5lgMgxGbSwIgUEVCVhvI4kgiPOAg4MSUe5r2YyNNP6JO9UUWHHKGYg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1878191}, "engines": {"node": ">=6.0.0"}}, "7.23.0": {"name": "@babel/parser", "version": "7.23.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.13", "@babel/helper-fixtures": "^7.22.19", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "da950e622420bf96ca0d0f2909cdddac3acd8719", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.23.0.tgz", "fileCount": 9, "integrity": "sha512-vvPKKdMemU85V9WE/l5wZEmImpCtLqbnTvqDS2U1fJ96KrxoW7KrXhNsNCblQlg8Ck4b85yxdTyelsMUgFUXiw==", "signatures": [{"sig": "MEQCIFmPjfALEfXmCLZRAZuohTFuy0mNlU6JH0bVSeQsc8odAiBLUN4iU+a0TD7YMsLIikt3Hcfmxz0RLOjgJDfjGYAgMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1890329}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.3": {"name": "@babel/parser", "version": "8.0.0-alpha.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.3", "@babel/helper-fixtures": "^8.0.0-alpha.3", "@babel/helper-string-parser": "^8.0.0-alpha.3", "@babel/helper-validator-identifier": "^8.0.0-alpha.3", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.3"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "f8a56f73c021c89e283382ba212a8e00478dba11", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.3.tgz", "fileCount": 10, "integrity": "sha512-oMdbCIk1T1F86INoRQf61wFAjfq65CoFKLAP635FHnt7L+FwNDnDG6FIx9eFa8lWUZzlnwX6bbEbVXuiynIFvQ==", "signatures": [{"sig": "MEYCIQDQNlnNEeK/0j1w8eWAnFxilmNCyrWh0mnGgRp+oONBlQIhAN80sMHxhXFQ3ThaKZ567kCHFv+V6VCzT+pjf6ArO9za", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2351062}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.4": {"name": "@babel/parser", "version": "8.0.0-alpha.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.4", "@babel/helper-fixtures": "^8.0.0-alpha.4", "@babel/helper-string-parser": "^8.0.0-alpha.4", "@babel/helper-validator-identifier": "^8.0.0-alpha.4", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.4"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "0e644d92cc3e4808126775e9166639796432516a", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.4.tgz", "fileCount": 10, "integrity": "sha512-S/D/oNljFtjHe82Mszj+VpIecJmSypBXsoE2az4fmac4fXYf7CO/8V1TInVFji7Nhw6CkvBAQslfKDzy5XqlNA==", "signatures": [{"sig": "MEQCIEHXoOocSIALpM6t86aynscekxYVVTtzjTs9XTqeNVKHAiBRtkzUiqrGUdZeg9e8vq+6UqbVU+assNnxZnR+vaE8Xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2351062}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.23.3": {"name": "@babel/parser", "version": "7.23.3", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.22.13", "@babel/helper-fixtures": "^7.22.19", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "0ce0be31a4ca4f1884b5786057cadcb6c3be58f9", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.23.3.tgz", "fileCount": 9, "integrity": "sha512-uVsWNvlVsIninV2prNz/3lHCb+5CJ+e+IUBfbjToAHODtfGYLfCFuY4AU7TskI+dAKk+njsPiBjq1gKTvZOBaw==", "signatures": [{"sig": "MEQCIEfXCyNuI3PNpJJ4CK44yIhTBnAKQiaRDI0rqcHy+DY+AiBhrUVHik67uvUU4q3b63KUL7F0WXqb9ecgZ1ul5UaXOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1889957}, "engines": {"node": ">=6.0.0"}}, "7.23.4": {"name": "@babel/parser", "version": "7.23.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.23.4", "@babel/helper-fixtures": "^7.23.4", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "409fbe690c333bb70187e2de4021e1e47a026661", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.23.4.tgz", "fileCount": 9, "integrity": "sha512-vf3Xna6UEprW+7t6EtOmFpHNAuxw3xqPZghy+brsnusscJRW5BMUzzHZc5ICjULee81WeUV2jjakG09MDglJXQ==", "signatures": [{"sig": "MEYCIQC5zJYTOy/P1uHrwSy+4jeBToCM5/IFbLvXx7eb6BpexQIhAKbjOxJx7ui1UKfLrDd0g6XvSbBbji0Is3gymj9z8dou", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1890027}, "engines": {"node": ">=6.0.0"}}, "7.23.5": {"name": "@babel/parser", "version": "7.23.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.23.5", "@babel/helper-fixtures": "^7.23.4", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "37dee97c4752af148e1d38c34b856b2507660563", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.23.5.tgz", "fileCount": 9, "integrity": "sha512-hOOqoiNXrmGdFbhgCzu6GiURxUgM27Xwd/aPuu8RfHEZPBzL1Z54okAHAQjXfcQNwvrlkAmAp4SlRTZ45vlthQ==", "signatures": [{"sig": "MEQCIGFLUFotBZdci6yvxJIXL6zSx3ugd5VeTTshDqMv7otBAiAksiclnpbiptQDrGrHqSxJ6FJZYtnz1T3L+X4+WZkzWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1889915}, "engines": {"node": ">=6.0.0"}}, "7.23.6": {"name": "@babel/parser", "version": "7.23.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.23.5", "@babel/helper-fixtures": "^7.23.4", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "ba1c9e512bda72a47e285ae42aff9d2a635a9e3b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.23.6.tgz", "fileCount": 9, "integrity": "sha512-Z2uID7YJ7oNvAI20O9X0bblw7Qqs8Q2hFy0R9tAfnfLkp5MW0UH9eUvnDSnFwKZ0AvgS1ucqR4KzvVHgnke1VQ==", "signatures": [{"sig": "MEUCIFrp8+QvzrVi0P2LJxqE6NPpZP3QiY4xzSh1fMeVP+hoAiEA8NAoFefpw+mErmRc/lCXllYf3DyLbY88y6ySFIWX40I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1889915}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.5": {"name": "@babel/parser", "version": "8.0.0-alpha.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.5", "@babel/helper-fixtures": "^8.0.0-alpha.5", "@babel/helper-string-parser": "^8.0.0-alpha.5", "@babel/helper-validator-identifier": "^8.0.0-alpha.5", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "06d51d18d8ecca60118fadd2f83fa52b8a5693bf", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.5.tgz", "fileCount": 10, "integrity": "sha512-qyZ5iQvUA/oh2N+h8VRswZ4WoWQ0x0pMDMPFp+9Muky1s3DpvXAjiPEH2P8iTQwgHc14/bixrCFqykuIuMtwRQ==", "signatures": [{"sig": "MEUCIEtbWKKsCEK36yqQt8imonk/vIjoGF/TO552kt7GmOWMAiEAk6zmAOufhEy964JgApAgXCBy9XxJEn2+QCYQSX9yU6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2350404}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.23.9": {"name": "@babel/parser", "version": "7.23.9", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.23.5", "@babel/helper-fixtures": "^7.23.4", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "7b903b6149b0f8fa7ad564af646c4c38a77fc44b", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.23.9.tgz", "fileCount": 9, "integrity": "sha512-9tcKgqKbs3xGJ+NtKF2ndOBBLVwPjl1SHxPQkd36r3Dlirw3xWUeGaTbqr7uGZcTaxkVNwc+03SVP7aCdWrTlA==", "signatures": [{"sig": "MEUCIQDieas25ZVj7ENTtvhBPtSJNzvrAfCJoviKS06RwaAn5AIgK3t8JwyFzFzjJXYc2tJQCr8TrP1KNR2u3bH2ev+HHA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1882299}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.6": {"name": "@babel/parser", "version": "8.0.0-alpha.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.6", "@babel/helper-fixtures": "^8.0.0-alpha.6", "@babel/helper-string-parser": "^8.0.0-alpha.6", "@babel/helper-validator-identifier": "^8.0.0-alpha.6", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "8fbbae764253efd6b7c84b697978b16d573f4cf7", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.6.tgz", "fileCount": 10, "integrity": "sha512-AJ6gWeO6hR2NXitqoRykzmDACsSWHWWDIsKXMTr8FOAte0hWrPeinjftra5iKT3R6pcTOb1Z1UKxlUn6yWHAmg==", "signatures": [{"sig": "MEQCIFkcb3JDrDjFql9yHbhnqRAaU+4zFZyrVDk0tcapxDxzAiAb7Yxp6xEJB+KjAdL3MayExcyOHjNpfWnLNZ5dEFu85Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2337658}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.24.0": {"name": "@babel/parser", "version": "7.24.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.23.5", "@babel/helper-fixtures": "^7.24.0", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "26a3d1ff49031c53a97d03b604375f028746a9ac", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.0.tgz", "fileCount": 9, "integrity": "sha512-QuP/FxEAzMSjXygs8v4N9dvdXzEHN4W1oF3PxuWAtPo08UdM17u89RDMgjLn/mlc56iM0HlLmVkO/wgR+rDgHg==", "signatures": [{"sig": "MEQCICacaWyPK9panQJqiE/1/t5JdcOuivL6/+1eYLOGtsRcAiBtOKxdjDEgFiKpeCK4Pe0y0HBGuQTQEFk2Mo2Dc33C1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1883263}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.7": {"name": "@babel/parser", "version": "8.0.0-alpha.7", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.7", "@babel/helper-fixtures": "^8.0.0-alpha.7", "@babel/helper-string-parser": "^8.0.0-alpha.7", "@babel/helper-validator-identifier": "^8.0.0-alpha.7", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "1282dffac58f8e318eff0c82d6fddf0a4a35c558", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.7.tgz", "fileCount": 10, "integrity": "sha512-/213QUyFixQhrhljRWu7ZdDSCSyOaPJFkH1nZtfsCCff1yfXRpfOAreJSleVKj9jv+WGAHIgh2oAH/gIawvDgw==", "signatures": [{"sig": "MEYCIQC0Buq+0jYlnJr4N4z1WnTgnQzNI0dRRVxsO367hsZImgIhAM1eEZG2R66ggLx1OtK+E4nVdXlNa+9Sw4vIaiic53La", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2338862}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.24.1": {"name": "@babel/parser", "version": "7.24.1", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.24.1", "@babel/helper-fixtures": "^7.24.1", "@babel/helper-string-parser": "^7.24.1", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "1e416d3627393fab1cb5b0f2f1796a100ae9133a", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.1.tgz", "fileCount": 9, "integrity": "sha512-Zo9c7N3xdOIQrNip7Lc9wvRPzlRtovHVE4lkz8WEDr7uYh/GMQhSiIgFxGIArRHYdJE5kxtZjAf8rT0xhdLCzg==", "signatures": [{"sig": "MEYCIQDO11OIWb1QEPA/MWrSKHA3Og78VYFMPPK+8ZyB0L7qIgIhAOgqAY2qD5rgyi6bKmnWxjhqTswwrqT51zbLDzLhBIQY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1883259}, "engines": {"node": ">=6.0.0"}}, "7.24.4": {"name": "@babel/parser", "version": "7.24.4", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.24.2", "@babel/helper-fixtures": "^7.24.4", "@babel/helper-string-parser": "^7.24.1", "@babel/helper-validator-identifier": "^7.22.20", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "234487a110d89ad5a3ed4a8a566c36b9453e8c88", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.4.tgz", "fileCount": 9, "integrity": "sha512-zTvEBcghmeBma9QIGunWevvBAp4/Qu9Bdq+2k0Ot4fVMD6v3dsC9WOcRSKk7tRRyBM/53yKMJko9xOatGQAwSg==", "signatures": [{"sig": "MEQCIB+3VZFC9WUyMy4oXlmG+I6O1NDGKEsE3MCxyj6A8a+IAiBs//RMN64wlyCeorDI68L27RkhIycVVParwPP9yiDNYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1883377}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.8": {"name": "@babel/parser", "version": "8.0.0-alpha.8", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.8", "@babel/helper-fixtures": "^8.0.0-alpha.8", "@babel/helper-string-parser": "^8.0.0-alpha.8", "@babel/helper-validator-identifier": "^8.0.0-alpha.8", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.8"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "f7a7af75ae88ddffe2253a672463d230db5d2219", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.8.tgz", "fileCount": 10, "integrity": "sha512-OjPHm4sBZ5qLOyOc5y1qBvEk8r558J7CyXAwtj3v3FqPXMfelhM7K5ohkgLiFT/yevQdhPe08jKLHdD64CcnGg==", "signatures": [{"sig": "MEUCIQDlHnFnML5/WeGOzVNrQDZ0ixJVlJzpNa9+VM5HTflh0QIgCiy5RstholsXkPJ9kHlACHmoplpyLRzfO4kC/nLiGbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2339015}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.24.5": {"name": "@babel/parser", "version": "7.24.5", "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.24.2", "@babel/helper-fixtures": "^7.24.4", "@babel/helper-string-parser": "^7.24.1", "@babel/helper-validator-identifier": "^7.24.5", "@babel/helper-check-duplicate-nodes": "^7.22.5"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "4a4d5ab4315579e5398a82dcf636ca80c3392790", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.5.tgz", "fileCount": 9, "integrity": "sha512-EOv5IK8arwh3LI47dz1b0tKUb/1uhHAnHJOrjgtQMIpu1uXd9mlFrJg9IUgGUgZ41Ch0K8REPTYpO7B76b4vJg==", "signatures": [{"sig": "MEYCIQC/8Hy7a+I5rRjFdPlN8Kum+DL/z9JDo0hLPDiEXLUywwIhAP5cT9CvG0MNqhfy3ZEvisHErf3CoBk3CclpXtxGcv8b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1891765}, "engines": {"node": ">=6.0.0"}}, "7.24.6": {"name": "@babel/parser", "version": "7.24.6", "devDependencies": {"charcodes": "^0.2.0", "@babel/types": "^7.24.6", "@babel/code-frame": "^7.24.6", "@babel/helper-fixtures": "^7.24.6", "@babel/helper-string-parser": "^7.24.6", "@babel/helper-validator-identifier": "^7.24.6", "@babel/helper-check-duplicate-nodes": "^7.24.6"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "5e030f440c3c6c78d195528c3b688b101a365328", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.6.tgz", "fileCount": 9, "integrity": "sha512-eNZXdfU35nJC2h24RznROuOpO94h6x8sg9ju0tT9biNtLZ2vuP8SduLqqV+/8+cebSLV9SJEAN5Z3zQbJG/M+Q==", "signatures": [{"sig": "MEUCIHN34zlgKbmnDXCaZR4/FJwzsUj3DAWRInkSwEu305QkAiEA/W7emWFRva9dyJpuZiMBSPCMHCtHNTqRIWgdizJJ5bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1889015}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.9": {"name": "@babel/parser", "version": "8.0.0-alpha.9", "devDependencies": {"charcodes": "^0.2.0", "@babel/types": "^8.0.0-alpha.9", "@babel/code-frame": "^8.0.0-alpha.9", "@babel/helper-fixtures": "^8.0.0-alpha.9", "@babel/helper-string-parser": "^8.0.0-alpha.9", "@babel/helper-validator-identifier": "^8.0.0-alpha.9", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "42fcdb85fae89205d10e22308279d57f5ddd78af", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.9.tgz", "fileCount": 11, "integrity": "sha512-BDyqKyXEMePKZAEf6PUTF+YfQW5x+FRyvpBrYc4O96xWz7qU3kttroibK33aYi63IViue7FE7uvM+gNSgpCm/A==", "signatures": [{"sig": "MEUCIQCQEJXQS3zuToIbN7S1EzvU3mgKFFKw7k1tCWnf/+LOugIgOmS+PJ39TwYFb6SKZnhFTu/aipbb+gvtprxkIRw6nqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2403194}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "8.0.0-alpha.10": {"name": "@babel/parser", "version": "8.0.0-alpha.10", "devDependencies": {"charcodes": "^0.2.0", "@babel/types": "^8.0.0-alpha.10", "@babel/code-frame": "^8.0.0-alpha.10", "@babel/helper-fixtures": "^8.0.0-alpha.10", "@babel/helper-string-parser": "^8.0.0-alpha.10", "@babel/helper-validator-identifier": "^8.0.0-alpha.10", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.10"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "cd7f220c5eee156ae9f2df5d51800a30ec0eb52d", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.10.tgz", "fileCount": 11, "integrity": "sha512-7VAJvVeH9tJMyEpPrr/YxbO9f4xFt4Mdfa/kCnm2glKwZsiVd4prGzOVjLlz03iVDEOpGlgL7e4M4cz6NjUB9A==", "signatures": [{"sig": "MEYCIQDzzZXJiI4MPrzbFs+v8qNS2OT7Kc8BtkP9h2SjIaotGgIhAMlrLVu88ASPPlVUzAQEdsCTMRz2DINacjaoIfYA5cDR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2403201}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.24.7": {"name": "@babel/parser", "version": "7.24.7", "devDependencies": {"charcodes": "^0.2.0", "@babel/types": "^7.24.7", "@babel/code-frame": "^7.24.7", "@babel/helper-fixtures": "^7.24.7", "@babel/helper-string-parser": "^7.24.7", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-check-duplicate-nodes": "^7.24.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "9a5226f92f0c5c8ead550b750f5608e766c8ce85", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.7.tgz", "fileCount": 9, "integrity": "sha512-9uUYRm6OqQrCqQdG1iCBwBPZgN8ciDBro2nIOFaiRz1/BCxaI7CNvQbDHvsArAC7Tw9Hda/B3U+6ui9u4HWXPw==", "signatures": [{"sig": "MEUCIQDXXe6SoEUCwM09EYevJDqIOHeYcVTIf5T4p5ZF+03UZAIgWtwYWjMFvzIMX5c2YcaL6cNV3/0AHZm9CuYTO+3dLWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1889255}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.11": {"name": "@babel/parser", "version": "8.0.0-alpha.11", "devDependencies": {"charcodes": "^0.2.0", "@babel/types": "^8.0.0-alpha.11", "@babel/code-frame": "^8.0.0-alpha.11", "@babel/helper-fixtures": "^8.0.0-alpha.11", "@babel/helper-string-parser": "^8.0.0-alpha.11", "@babel/helper-validator-identifier": "^8.0.0-alpha.11", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.11"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "f9a646aa118b64fd8cb222bbf1a4c11695703683", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.11.tgz", "fileCount": 11, "integrity": "sha512-H26G0jcHMYiKcvdeUrvrAuKgFvJKToXLBBhQ067T0KI30S2Ow7zwR6+6u8kV4KjWVLk8YEbH8EIBhGayRF+8WA==", "signatures": [{"sig": "MEYCIQCt3pan5yW1q1VBQOSODdrSaW5mJqZ5wpTJzMR2Svx1qQIhAPEAolE3651r515zlIMP5veKDsclGez03T/rPsxD9NMu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2403666}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.24.8": {"name": "@babel/parser", "version": "7.24.8", "devDependencies": {"charcodes": "^0.2.0", "@babel/types": "^7.24.8", "@babel/code-frame": "^7.24.7", "@babel/helper-fixtures": "^7.24.8", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-check-duplicate-nodes": "^7.24.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "58a4dbbcad7eb1d48930524a3fd93d93e9084c6f", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.24.8.tgz", "fileCount": 9, "integrity": "sha512-WzfbgXOkGzZiXXCqk43kKwZjzwx4oulxZi3nq2TYL9mOjQv6kYwul9mz6ID36njuL7Xkp6nJEfok848Zj10j/w==", "signatures": [{"sig": "MEQCIAO8nJjMv7FY37ss8+ZvQIQ4Y7wjeiFCpHQogrT2wUqxAiBYIh/9jd7E4XyIAfobNYVERmPgs74Y8+qdtAq04Q67fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1890764}, "engines": {"node": ">=6.0.0"}}, "7.25.0": {"name": "@babel/parser", "version": "7.25.0", "devDependencies": {"charcodes": "^0.2.0", "@babel/types": "^7.25.0", "@babel/code-frame": "^7.24.7", "@babel/helper-fixtures": "^7.24.8", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-check-duplicate-nodes": "^7.24.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "9fdc9237504d797b6e7b8f66e78ea7f570d256ad", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.25.0.tgz", "fileCount": 9, "integrity": "sha512-CzdIU9jdP0dg7HdyB+bHvDJGagUv+qtzZt5rYCWwW6tITNqV9odjp6Qu41gkG0ca5UfdDUWrKkiAnHHdGRnOrA==", "signatures": [{"sig": "MEYCIQDAwpWcN+Zzp/L0hJyppM5aurEBoKCiq6f86C3N0sv4sQIhAJ8nj8DMWhQnxZ683IIOLDw2WS4nWBit8KSok3XAX6uv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1886520}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.12": {"name": "@babel/parser", "version": "8.0.0-alpha.12", "devDependencies": {"charcodes": "^0.2.0", "@babel/types": "^8.0.0-alpha.12", "@babel/code-frame": "^8.0.0-alpha.12", "@babel/helper-fixtures": "^8.0.0-alpha.12", "@babel/helper-string-parser": "^8.0.0-alpha.12", "@babel/helper-validator-identifier": "^8.0.0-alpha.12", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.12"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "a72b15da8cf201e5e9984ab6ae1a5b63f32af6bc", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.12.tgz", "fileCount": 11, "integrity": "sha512-AzWmrp4uJ+DcXVH0uoUpJVhRqxNirC0BbXsZ82AQuVod41CoaV5G+cwcvtYusrIIxv7BIJb6ce0dQ9L0wAl1iA==", "signatures": [{"sig": "MEUCIFMTod3GXfVr9jzBl43Tks7ghzcgEKDZiOzbdMuS5t4EAiEA1CoDHNYe8RJLxew9sXvsvZiUXNGxXOaVQEmLPfrx/U8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2400925}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.25.3": {"name": "@babel/parser", "version": "7.25.3", "dependencies": {"@babel/types": "^7.25.2"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.24.7", "@babel/helper-fixtures": "^7.24.8", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-check-duplicate-nodes": "^7.24.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "91fb126768d944966263f0657ab222a642b82065", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.25.3.tgz", "fileCount": 9, "integrity": "sha512-iLTJKDbJ4hMvFPgQwwsVoxtHyWpKKPBrxkANrSYewDPaPpT5py5yeVkgPIJ7XYXhndxJpaA3PyALSXQ7u8e/Dw==", "signatures": [{"sig": "MEUCIQD5HdS2SL7HRymzXkGosbyuxniyzbpc179p+GH7flL0xwIgb9LFy+ZGlIX+1NYmd8X0cmAhJM3+KeojPxz8q0sXLcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1886549}, "engines": {"node": ">=6.0.0"}}, "7.25.4": {"name": "@babel/parser", "version": "7.25.4", "dependencies": {"@babel/types": "^7.25.4"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.24.7", "@babel/helper-fixtures": "^7.24.8", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-check-duplicate-nodes": "^7.24.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "af4f2df7d02440286b7de57b1c21acfb2a6f257a", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.25.4.tgz", "fileCount": 9, "integrity": "sha512-nq+eWrOgdtu3jG5Os4TQP3x3cLA8hR8TvJNjD8vnPa20WGycimcparWnLK4jJhElTK6SDyuJo1weMKO/5LpmLA==", "signatures": [{"sig": "MEUCIQDumpR3imMhOkQv2R1iONqhJ5EChPP77A+yct3Q9CtxVwIgOIEA4RtBmZcasQslN7KovOwuauZT1U82z+3/RojtlIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1886828}, "engines": {"node": ">=6.0.0"}}, "7.25.6": {"name": "@babel/parser", "version": "7.25.6", "dependencies": {"@babel/types": "^7.25.6"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.24.7", "@babel/helper-fixtures": "^7.24.8", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "@babel/helper-check-duplicate-nodes": "^7.24.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "85660c5ef388cbbf6e3d2a694ee97a38f18afe2f", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.25.6.tgz", "fileCount": 9, "integrity": "sha512-trGdfBdbD0l1ZPmcJ83eNxB9rbEax4ALFTF7fN386TMYbeCQbyme5cOEXQhbGXKebwGaB/J52w1mrklMcbgy6Q==", "signatures": [{"sig": "MEQCIFjjx+01V1f4md5Ugat/on1vDmX8qW2lAcOfhdWLAakxAiAXcUKrRj4OAUAP/4tLPea69ZYVCEgUwUGiKOa50P2Sgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1889270}, "engines": {"node": ">=6.0.0"}}, "7.25.7": {"name": "@babel/parser", "version": "7.25.7", "dependencies": {"@babel/types": "^7.25.7"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.25.7", "@babel/helper-fixtures": "^7.25.7", "@babel/helper-string-parser": "^7.25.7", "@babel/helper-validator-identifier": "^7.25.7", "@babel/helper-check-duplicate-nodes": "^7.25.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "99b927720f4ddbfeb8cd195a363ed4532f87c590", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.25.7.tgz", "fileCount": 9, "integrity": "sha512-aZn7ETtQsjjGG5HruveUK06cU3Hljuhd9Iojm4M8WWv3wLE6OkE5PWbDUkItmMgegmccaITudyuW5RPYrYlgWw==", "signatures": [{"sig": "MEQCIGci6dM/X1FfAZqfzRLOi+kmp77AIrsK6C5bCN88wgWvAiATzljxyE8pKVMg+DMLgNYTlcJz73A53xL/3dgnUrrlWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1894175}, "engines": {"node": ">=6.0.0"}}, "7.25.8": {"name": "@babel/parser", "version": "7.25.8", "dependencies": {"@babel/types": "^7.25.8"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.25.7", "@babel/helper-fixtures": "^7.25.7", "@babel/helper-string-parser": "^7.25.7", "@babel/helper-validator-identifier": "^7.25.7", "@babel/helper-check-duplicate-nodes": "^7.25.7"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "f6aaf38e80c36129460c1657c0762db584c9d5e2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.25.8.tgz", "fileCount": 9, "integrity": "sha512-HcttkxzdPucv3nNFmfOOMfFf64KgdJVqm1KaCm25dPGMLElo9nsLvXeJECQg8UzPuBGLyTSA0ZzqCtDSzKTEoQ==", "signatures": [{"sig": "MEYCIQD+thhxzzvTl62ov0WUZVkd2RBYWrGdGSExmh57VYivggIhAOtcHTbVp6qOjcSSmqtaULiqeI4lBAYDTlKXo9fIf50G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1894157}, "engines": {"node": ">=6.0.0"}}, "7.25.9": {"name": "@babel/parser", "version": "7.25.9", "dependencies": {"@babel/types": "^7.25.9"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.25.9", "@babel/helper-fixtures": "^7.25.9", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "8fcaa079ac7458facfddc5cd705cc8005e4d3817", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.25.9.tgz", "fileCount": 9, "integrity": "sha512-aI3jjAAO1fh7vY/pBGsn1i9LDbRP43+asrRlkPuTXW5yHXtd1NgTEMudbBoDDxrf1daEEfPJqR+JBMakzrR4Dg==", "signatures": [{"sig": "MEUCIQDG0VyvFFWNrXcOLcXJux85Nhvlm35F1dMTbO/EOW8V2gIgagHAubKTT/rqFibdaPs70m0IJdA/03DnNN5vq34C3Vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1894467}, "engines": {"node": ">=6.0.0"}}, "7.26.0": {"name": "@babel/parser", "version": "7.26.0", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.0", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "710a75a7d805a8f72753154e451474e9795b121c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.0.tgz", "fileCount": 9, "integrity": "sha512-aP8x5pIw3xvYr/sXT+SEUwyhrXT8rUJRZltK/qN3Db80dcKpTett8cJxHyjk+xYSVXvNnl2SfcJVjbwxpOSscA==", "signatures": [{"sig": "MEUCID7raq/6geY9Wtkl9FOv8oWbrCvV2b9A1utFsIwYVE9MAiEAwlB01SAzL028NKa0LGADiHZW7o7THDWCMyDygJgbaGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1891501}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.13": {"name": "@babel/parser", "version": "8.0.0-alpha.13", "dependencies": {"@babel/types": "^8.0.0-alpha.13"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.13", "@babel/helper-fixtures": "^8.0.0-alpha.13", "@babel/helper-string-parser": "^8.0.0-alpha.13", "@babel/helper-validator-identifier": "^8.0.0-alpha.13", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.13"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "89765fac52b83927119bee37ed9d6b4d0d89634d", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.13.tgz", "fileCount": 11, "integrity": "sha512-b8jxzP/fDYLjByadJgnXutKZvjXJUIyf0bIWMUIX47jhtbgA3ZFPC0PByLlqD6rKhTbgvuGHZ0GQilMFL5EaOQ==", "signatures": [{"sig": "MEUCIQDKisaoGORJyrOcH0hG1ZC34rTWakHBjyH1dFQ+b5izEAIgDOPDisZoSGFYK1bo5cDieovoC73neU9SwYBkL8gjjdM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2402997}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "7.26.1": {"name": "@babel/parser", "version": "7.26.1", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.0", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "44e02499960df2cdce2c456372a3e8e0c3c5c975", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.1.tgz", "fileCount": 9, "integrity": "sha512-reoQYNiAJreZNsJzyrDNzFQ+IQ5JFiIzAHJg9bn94S3l+4++J7RsIhNMoB+lgP/9tpmiAQqspv+xfdxTSzREOw==", "signatures": [{"sig": "MEYCIQCOoIQJ7IZv8FQwZ673egm0+fqteX5e3xvqFfcSpsLAFQIhAJSRQ7Ls8nP0EAkVBpcQa+1JAn5OPdbDGsLQy6XWtqZT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1894220}, "engines": {"node": ">=6.0.0"}}, "7.26.2": {"name": "@babel/parser", "version": "7.26.2", "dependencies": {"@babel/types": "^7.26.0"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.2", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "fd7b6f487cfea09889557ef5d4eeb9ff9a5abd11", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.2.tgz", "fileCount": 9, "integrity": "sha512-DWMCZH9WA4Maitz2q21SRKHo9QXZxkDsbNZoVD62gusNtNBBqDg9i7uOhASfTfIGNzW+O+r7+jAlM8dwphcJKQ==", "signatures": [{"sig": "MEQCIBZR7Ur9PXllI4ZsU8fmKyflo9IW4YPRHSpE9ZgfagB/AiBb2D6K7e2vfKnlQMl3jcBjeChNmsVEMOSTyAPKuzs0Rw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1895095}, "engines": {"node": ">=6.0.0"}}, "7.26.3": {"name": "@babel/parser", "version": "7.26.3", "dependencies": {"@babel/types": "^7.26.3"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.2", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "8c51c5db6ddf08134af1ddbacf16aaab48bac234", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.3.tgz", "fileCount": 9, "integrity": "sha512-WJ/CvmY8Mea8iDXo6a7RK2wbmJITT5fN3BEkRuFlxVyNx8jOKIIhmC4fSkTcPcf8JyavbBwIe6OpiCOBXt/IcA==", "signatures": [{"sig": "MEUCIQDZvkmFQ9uPRlIPSLHIu1LqyeQwkyM+zHftH+coqOwQJQIgLk2r2G3woWE2oDugsn4Qho9B02X1KVdThlVbvwEdaww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1895525}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.14": {"name": "@babel/parser", "version": "8.0.0-alpha.14", "dependencies": {"@babel/types": "^8.0.0-alpha.14"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.14", "@babel/helper-fixtures": "^8.0.0-alpha.14", "@babel/helper-string-parser": "^8.0.0-alpha.14", "@babel/helper-validator-identifier": "^8.0.0-alpha.14", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.14"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "ec46adbe2d93d1c069fa66363bcd66a866400ee2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.14.tgz", "fileCount": 11, "integrity": "sha512-votW6ygeXEluW2yk3sZIh5pr4TfiKA0T+IsAYGSSFdgey2sjdW4ujjnDbnAiXyaKwNx4Dy9MZXjTBCZ7QqkGSg==", "signatures": [{"sig": "MEUCIQCqAFgIYfWKbl708Tw3RmUeCsS2weRND0jc16SnDDj1RAIgclmY3M0sQ7imfddJY1IlFhUnXdAv4Zj/7lzux+qBRxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2408282}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "7.26.5": {"name": "@babel/parser", "version": "7.26.5", "dependencies": {"@babel/types": "^7.26.5"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.2", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "6fec9aebddef25ca57a935c86dbb915ae2da3e1f", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.5.tgz", "fileCount": 9, "integrity": "sha512-SRJ4jYmXRqV1/Xc+TIVG84WjHBXKlxO9sHQnA2Pf12QQEAp1LOh6kDzNHXcUnbH1QI0FDoPPVOt+vyUDucxpaw==", "signatures": [{"sig": "MEUCIEOvJqsFZdxr9Zza0cDCA9NDEjb7Dlbxog6ExdcN3HtvAiEAm+ZMW1ZAq8y3s/8VM2IYWC3I5Pfqqm1UZ59wjdH0TVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1911393}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.15": {"name": "@babel/parser", "version": "8.0.0-alpha.15", "dependencies": {"@babel/types": "^8.0.0-alpha.15"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.15", "@babel/helper-fixtures": "^8.0.0-alpha.15", "@babel/helper-string-parser": "^8.0.0-alpha.15", "@babel/helper-validator-identifier": "^8.0.0-alpha.15", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.15"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "17f9b6f3d7820e39c0d3993074349cb0ac78396f", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.15.tgz", "fileCount": 11, "integrity": "sha512-AxmJtwfTyydMezVr3ibPzxoo7y1Fm/BHLp7fWBxfIWomc5wV1ELseQM7GIt2CVsjhfTKsKlWNP9xpETerOHN4w==", "signatures": [{"sig": "MEUCIQDhYggWDttB4h1rZXRdGLvNVEdVTB+6hNNxLgbQz6+hrgIgCQoGz+EawGj0cTFUJqYHeVXLclZ+YI4X4bZBSOzrVFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2428318}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "7.26.7": {"name": "@babel/parser", "version": "7.26.7", "dependencies": {"@babel/types": "^7.26.7"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.2", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "e114cd099e5f7d17b05368678da0fb9f69b3385c", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.7.tgz", "fileCount": 9, "integrity": "sha512-kEvgGGgEjRUutvdVvZhbn/BxVt+5VSpwXz1j3WYXQbXDo8KzFOPNG2GQbdAiNq8g6wn1yKk7C/qrke03a84V+w==", "signatures": [{"sig": "MEUCIQDXqvn05eEuPrh6nfGxkIBcnRpHWff49tQHWC43KNJVpgIgCUx3KnzsmgAnB00ZPhQ3AX0a2wkJJTx5GcAN416TJBQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1914316}, "engines": {"node": ">=6.0.0"}}, "7.26.8": {"name": "@babel/parser", "version": "7.26.8", "dependencies": {"@babel/types": "^7.26.8"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.2", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "deca2b4d99e5e1b1553843b99823f118da6107c2", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.8.tgz", "fileCount": 9, "integrity": "sha512-TZIQ25pkSoaKEYYaHbbxkfL36GNsQ6iFiBbeuzAkLnXayKR1yP1zFe+NxuZWWsUyvt8icPU9CCq0sgWGXR1GEw==", "signatures": [{"sig": "MEYCIQD6kQc05nOtvEY+shZ1qXvtYKiQCgktFXj1HqK59Cnp2AIhAL0NjG9I5GTfwwf9jW3YMaL/aGGMeEYvE9wJY45DPn6+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1916324}, "engines": {"node": ">=6.0.0"}}, "7.26.9": {"name": "@babel/parser", "version": "7.26.9", "dependencies": {"@babel/types": "^7.26.9"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.2", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "d9e78bee6dc80f9efd8f2349dcfbbcdace280fd5", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.9.tgz", "fileCount": 9, "integrity": "sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==", "signatures": [{"sig": "MEUCIQDm8YJtz4s7U4sOH+mizyjaf4N2Nbj5V1kp7uhoHkZbLwIgPX6qJddyGryCI3r8ERSdT+U5N/j8wb9wKW4R2s/HvkI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1920519}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.16": {"name": "@babel/parser", "version": "8.0.0-alpha.16", "dependencies": {"@babel/types": "^8.0.0-alpha.16"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.16", "@babel/helper-fixtures": "^8.0.0-alpha.16", "@babel/helper-string-parser": "^8.0.0-alpha.16", "@babel/helper-validator-identifier": "^8.0.0-alpha.16", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.16"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "cbfd177b9e21dfd83a012ffeba55f2f17554bb85", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.16.tgz", "fileCount": 11, "integrity": "sha512-MAkapLgjL4Kt7m2KVCnegQS/KVxJLmv04ChvHe8bUiH4IBjkaHKvgGPTnITNeh5mzeCdvfW57Yzp1NmAgsZgBg==", "signatures": [{"sig": "MEUCIQD96PpD0iXvSG7BMSMhfmslEpzZ7b2K333z/c9YmPFBDgIgCW69+OtaU/tIAx2CgX5cIVzhCTvXBtRQi8ZBkkzuPv0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2438638}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "7.26.10": {"name": "@babel/parser", "version": "7.26.10", "dependencies": {"@babel/types": "^7.26.10"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.2", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "e9bdb82f14b97df6569b0b038edd436839c57749", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.26.10.tgz", "fileCount": 9, "integrity": "sha512-6aQR2zGE/QFi8JpDLjUZEPYOs7+mhKXm86VaKFiLP35JQwQb6bwUE+XbvkH0EptsYhbNBSUGaUBLKqxH1xSgsA==", "signatures": [{"sig": "MEYCIQDy9hue0Q7i+l1dSzAMb5Amo6fedOzXtpPwGEv/TaBYSgIhAMMK3ke/44JYPGe4tVn4gSIJvYyYtZy8CP3jBf8SEpAt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1921044}, "engines": {"node": ">=6.0.0"}}, "8.0.0-alpha.17": {"name": "@babel/parser", "version": "8.0.0-alpha.17", "dependencies": {"@babel/types": "^8.0.0-alpha.17"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-alpha.17", "@babel/helper-fixtures": "^8.0.0-alpha.17", "@babel/helper-string-parser": "^8.0.0-alpha.17", "@babel/helper-validator-identifier": "^8.0.0-alpha.17", "@babel/helper-check-duplicate-nodes": "^8.0.0-alpha.17"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "a84abb532d0769a445881f799f526c08d0118fac", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-alpha.17.tgz", "fileCount": 11, "integrity": "sha512-TNExtmtwMQ9Ng5C/TENhbhNyU4qOOkLLVhkYsAEhLrEhrBDmIW8XoWO909E38rStZ1Hrz9d7YUZl3QWLqiv/sA==", "signatures": [{"sig": "MEYCIQDCLC9tJAz1IK/QAGNmJub1EdxKBD3o9shhKE4NkusuEAIhAJ+ywfyw5bcMU08aJOtFJC+gSSJL/Tg+zsLRupysoA/v", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2441832}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "7.27.0": {"name": "@babel/parser", "version": "7.27.0", "dependencies": {"@babel/types": "^7.27.0"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.26.2", "@babel/helper-fixtures": "^7.26.0", "@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/helper-check-duplicate-nodes": "^7.25.9"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "3d7d6ee268e41d2600091cbd4e145ffee85a44ec", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.0.tgz", "fileCount": 9, "integrity": "sha512-iaepho73/2Pz7w2eMS0Q5f83+0RKI7i4xmiYeBmDzfRVbQtTOG7Ts0S4HzJVsTMGI9keU8rNfuZr8DKfSt7Yyg==", "signatures": [{"sig": "MEUCIG7aNY8H5LuklgiV1J20IhWKGKDvw0J+BeOzIWbapCeAAiEA9LH5pyz9srACYXjKpXN8EnsxjSd67T4rMFVPjciP41M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1924962}, "engines": {"node": ">=6.0.0"}}, "7.27.1": {"name": "@babel/parser", "version": "7.27.1", "dependencies": {"@babel/types": "^7.27.1"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.27.1", "@babel/helper-fixtures": "^7.27.1", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "c55d5bed74449d1223701f1869b9ee345cc94cc9", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.1.tgz", "fileCount": 8, "integrity": "sha512-I0dZ3ZpCrJ1c04OqlNsQcKiZlsrXf/kkE4FXzID9rIOYICsAbA8mMDzhW/luRNAHdCNt7os/u8wenklZDlUVUQ==", "signatures": [{"sig": "MEUCIQDIHyKgW4CqPhYL85Yg2MFPA9HHho2oCAjeSBSGklFI6wIgCpgFUDieW3l6zr1ZzWMGhUfVYzuAYkYNtULqTnaR/I4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1859876}, "engines": {"node": ">=6.0.0"}}, "7.27.2": {"name": "@babel/parser", "version": "7.27.2", "dependencies": {"@babel/types": "^7.27.1"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.27.1", "@babel/helper-fixtures": "^7.27.1", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "577518bedb17a2ce4212afd052e01f7df0941127", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.2.tgz", "fileCount": 8, "integrity": "sha512-QYLs8299NA7WM/bZAdp+CviYYkVoYXlDW2rzliy3chxd1PQjej7JORuMJDJXJUb9g0TT+B99EwaVLKmX+sPXWw==", "signatures": [{"sig": "MEYCIQCDd5J/lrN2qZI5P1nbsIRgWSswMEGO7yLat3TnIhEFwQIhAMMyVbchI17UAFkaArhl3hn2w5yuRIh0vCee+yMO50I2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1958006}, "engines": {"node": ">=6.0.0"}}, "7.27.3": {"name": "@babel/parser", "version": "7.27.3", "dependencies": {"@babel/types": "^7.27.3"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.27.1", "@babel/helper-fixtures": "^7.27.1", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "1b7533f0d908ad2ac545c4d05cbe2fb6dc8cfaaf", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.3.tgz", "fileCount": 8, "integrity": "sha512-xyYxRj6+tLNDTWi0KCBcZ9V7yg3/lwL9DWh9Uwh/RIVlIfFidggcgxKX3GCXwCiswwcGRawBKbEg2LG/Y8eJhw==", "signatures": [{"sig": "MEUCIE+bgARK392Mecv5SgiJbc0Glq854l2aMZBVAdS3MIIJAiEAg2AlpLMxMCn5hfbl/hob6rggSlCnktQOMrbSue17Qm0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1957295}, "engines": {"node": ">=6.0.0"}}, "7.27.4": {"name": "@babel/parser", "version": "7.27.4", "dependencies": {"@babel/types": "^7.27.3"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^7.27.1", "@babel/helper-fixtures": "^7.27.1", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "f92e89e4f51847be05427285836fc88341c956df", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.4.tgz", "fileCount": 8, "integrity": "sha512-BRmLHGwpUqLFR2jzx9orBuX/ABDkj2jLKOXrHDTN2aOKL+jFDDKaRNo9nyYsIl9h/UE/7lMKdDjKQQyxKKDZ7g==", "signatures": [{"sig": "MEQCID88sXXEW090CdqCp2CvnoUdrFzNetdOzQYHLbg8vprqAiAI7KRdhtBwdw5RczpZTLBZ+A3UgEicvRcElFr2z+4VXA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1960542}, "engines": {"node": ">=6.0.0"}}, "8.0.0-beta.0": {"name": "@babel/parser", "version": "8.0.0-beta.0", "dependencies": {"@babel/types": "^8.0.0-beta.0"}, "devDependencies": {"charcodes": "^0.2.0", "@babel/code-frame": "^8.0.0-beta.0", "@babel/helper-fixtures": "^8.0.0-beta.0", "@babel/helper-string-parser": "^8.0.0-beta.0", "@babel/helper-validator-identifier": "^8.0.0-beta.0", "@babel/helper-check-duplicate-nodes": "^8.0.0-beta.0"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "3557f4a9c8999c6510d588dbf0bdfccacab772a4", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-8.0.0-beta.0.tgz", "fileCount": 9, "integrity": "sha512-qtov8PDRzRsTolcpLMs9Xn9nc/0R0fXI+YxxKaM9vZUCeYDt+lsMUCFCW1vEjOuXwX83xz8sapts31GIsbEFpg==", "signatures": [{"sig": "MEQCIHDfhoGxP/7dNxKxQa0ha+yKZ6M5+IM0Kz2UeXOtkxG7AiBFRR7JlWtCK7msdzyaieeQ2ETYZb2Ihh2D4INhhGdWPw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1994387}, "engines": {"node": "^20.19.0 || >=22.12.0"}}, "7.27.5": {"name": "@babel/parser", "version": "7.27.5", "dependencies": {"@babel/types": "^7.27.3"}, "devDependencies": {"@babel/code-frame": "^7.27.1", "@babel/helper-check-duplicate-nodes": "^7.27.1", "@babel/helper-fixtures": "^7.27.1", "@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "charcodes": "^0.2.0"}, "bin": "./bin/babel-parser.js", "dist": {"shasum": "ed22f871f110aa285a6fd934a0efed621d118826", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "tarball": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "fileCount": 8, "unpackedSize": 1964592, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCD1L8XxL95/qOQ0eUdVKB60B1akFD+hMeJMIgWWzxlXAIgR+TYs9yyQKsPQUkEgtxwib/El/t8orEHhW2qoKpWITM="}]}, "engines": {"node": ">=6.0.0"}}}, "modified": "2025-06-03T11:27:04.355Z", "cachedAt": 1750172041163}