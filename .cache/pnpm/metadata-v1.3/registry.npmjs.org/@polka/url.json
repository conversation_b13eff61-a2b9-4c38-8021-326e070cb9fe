{"name": "@polka/url", "dist-tags": {"latest": "0.5.0", "next": "1.0.0-next.29"}, "versions": {"0.5.0": {"name": "@polka/url", "version": "0.5.0", "dist": {"shasum": "b21510597fd601e5d7c95008b76bf0d254ebfd31", "tarball": "https://registry.npmjs.org/@polka/url/-/url-0.5.0.tgz", "fileCount": 4, "integrity": "sha512-oZLYFEAzUKyi3SKnXvj32ZCEGH6RDnao7COuCVhDydMS9NrCSVXhM79VaKyP5+Zc33m0QXEd2DN3UkU7OsHcfw==", "signatures": [{"sig": "MEUCIDnWS+2kAXBM0A8ih8nrd4Euz5M+jXMjRWAgNsvHe/6XAiEAoUblumF0rgH1lx6ELW8nqFXaluXCufGUolXnQoKlqoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2Ww9CRA9TVsSAnZWagAAr6sQAIHHnsZsVdjKrI1ss1kw\nxpm/SvsiTrdn7MK+ReY8QYksLLYeYNP/IsVYh+0h9Ok7M8xK0UryG2GC2J4a\nW5gQNz15SW1GSKMdxZoZuV5IOgq344RZawCyHH1mfxNb+ozVYQyhGK7wP2Hq\nelE1RGiOPwSLeaABXGqwWq07MVqb9rP4k7d4Xw8bu2LteKhKuret/IK3S1D3\n73+oX/6FA3G/+FfDFcXm1z9V5VHUiYtOllAU/xInx2ZRTnodrfm2wlPKXhTH\nLTYbV4Vdt+LY7haf2RgdbJ+TS0MH3MesejsQjNtjYHoJHN2Xp6/78fNLyax/\n5nx4AMVby6k7uuRNrTkoE4YHnrWX7Il8YZe8LujBMbrsLXpoWvp4lJWppFyi\nQbrQMlinLPB+3EpX2+38NHbY6hPwvU2fyaqoPBaR70LbBUBKrH2ldCB5VmZH\ndIfvVm0ptBphArD5yO196fBV4b+s0rWioexQys/txYzN1kZGAxzPUKt4LFBP\nlgnOnPC2iVXdr7qqF5UQpk9FtzHOKpsgOlX330UYrU77TJmKBab4NhMhromD\nj8DpUsBwxpSqFPYCMxYjTNIdeIlYaAFR/UWQ77l778jw19ssabBYcrqXfsoi\njhNf2FEhfAgJ58SJuFMAfU4SPW5zTb2U0teHN2546cjX/qubkOpEieIhsviU\nKHiC\r\n=5ii4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.0": {"name": "@polka/url", "version": "1.0.0-next.0", "dist": {"shasum": "818f693dd4ee2a742ff43dc789eccbcb6ba6efba", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.0.tgz", "fileCount": 4, "integrity": "sha512-khlIazYuw74cnv6yK5iA4Snggo+ICeeqtWVZbzuHFdvEbVIf2veenitVjvTcZ4S9WD8NTd3Rl+HrVOATAA/qWw==", "signatures": [{"sig": "MEYCIQCQ/yTzIrSmjRLg6eu9mSjTUH47W9cAX0JxvydFcxNtywIhALPmKO4eLZZgSy1kgRc03g8e6xZ0DmzEQPG0A3xT7Jb5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchL3sCRA9TVsSAnZWagAA0DAP/0Kumyh+lVIqGSwUpQxj\neRptsjCJ0254Egej1uoDACDbDtRZF0ho3Y4fSzEZFM+5/eQSoIcbK1Ij83be\nUZNp0ZmfBhpH+wa3ZBjL9Gfhe5q7ZBHpFAbsSaOtMWCiiyA2XoVdv0bphZtP\nMCDw7Klslt14EO9rCzXQPJ46rEfcdiYqMELmj4G4KBVRdBJsMNsM8R7Zf3UY\nIKIF/7f66fJzhM5li7D+mizE3mG/gEz28UL/c1tOVkWyU2TZKW6YtYg4X6lT\nHdRQBvXBZnw4A8pQ/wzGLGt/87xKYP3sYfptG74gc8WTPdSlAgv/7DvBKIlu\nBKwvKYD0sslGfwyfy7kKc0jjwP0prIHrdBOcNPHb5kpGf7Fg2WsdIaWWqyx4\nSUuI6SSFF1L+u1yw4X+qLi33jwZXyL2F9uv+i/5HfeIgu/Y7D5FPuiUhzBXN\nAi09c3ar+zUxpEWF/Te4hX8eWCnJwPmSrdzEM0t9xS9Tfk2FVHKS7bl1atC3\nKGJtLMgWwkxlUiNGZN2I0G+Spcunsz2JYBBNLWQZxO7oeQfOSRS2npPPKnaq\nOqgJnpCNVpz0sTdyb4JHZf9RPOqYtkh/dJEdzs7dPNiMH2bDK1dSClpL3PnY\nG2M4l1LsIKmE+nfUWI2CNvIEGTYwradzVgz5sd65pEl0MZ3atmNiWU1XJ4ma\nF02P\r\n=inoO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.1": {"name": "@polka/url", "version": "1.0.0-next.1", "dist": {"shasum": "e121c79b30b0873e4701390d3274245f81cfae77", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.1.tgz", "fileCount": 4, "integrity": "sha512-6d8YbKW4hjJMnU6ZJSDLtALWiB4J//OIPaP885ruf5U8MLZHigocDxhjgvLwbV6bGkikhllgTjD9eWioKWAQdA==", "signatures": [{"sig": "MEUCIQCpBTJJmoiIgOhF6zpBfg2hOmF+sBz1dZ0MJfHNvMeQ0wIgIvXkHUqdmoyGK3L6u9FRRjYKYNKthWdFoWvLyBraivo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcoAY/CRA9TVsSAnZWagAACwoP/j1FFwNwcdX6dtXGaI07\nlD3ShDg+r78bg2XW2W+zKo9oRLjGbshgADUc++wNRvHn3IUQWZ0NHzewOBgT\nyi391PD/uvTeyYk74gNgmssRNnDvrUTWRkxlDKbiGeJXeIs4DY7ajhjzPrgb\ny0+BUN+zjT1mmhZTO/x0uqVk/pQtvj1/dASdLn3VaoXCDJjhhUe3nunNTnIF\n64TE9QFzKMPoaW3wREMESLSdIgRZCg+E3tFzRz8vjgqu/+MWn88W4HJDcEEB\nDjDNRXbHDnsui9KfQ09Zouf1WFMh5/I9RjnvzSTDmDCYghhhaH6zMi9a8QHu\nNSM+gPhW7gf0PhH15ZOtNpsx2dtIGB9qirD17dFr7Al+5DfZ6kht6Mzjdilg\n7Dr0wA1dmcBANI9eq8ITa/LyjP+/z0YCz7azaRzfOU8W5O6Yjp6nPFqovuU4\nTGEpFZB9ovJw845/cIlNhsHzmLq60zI5eLh8MY8WmETQWC++gFxNW5TXcGOm\n2mCQNiqvySqjiqpcA34kBpvDIfpD8wt5OUTNtL1Ird6Gr3QTn2Mso/5gS8om\njpmoTT1b8G/8EBOMvdm20u3dTTRs21H/ipLUCrZ7ciQC+CuHr7pxJKtMHYDL\nuRwHp3yfhIVTYZo54GV3ESzldrP7XAcfiVj5txhaQUcoZQ8oCmAOsoVDOhN4\nAjUU\r\n=cGXH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.3": {"name": "@polka/url", "version": "1.0.0-next.3", "dist": {"shasum": "7a56e747ec94622e8513b406c53cc1729799de09", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.3.tgz", "fileCount": 5, "integrity": "sha512-Uom7l6OeP6vcf85lMImelYu5WKVWjXyhkpi9WsRdRzlJFJFPVhjBtBCktgDUj7dk1N5FURUdegSZ5XOjxf8JZg==", "signatures": [{"sig": "MEUCIQDQuW2UcFI5XMFL8rUVE1vn+cmuWfMD9Fi/ZKTuEJmnrQIgKN3ekL5lCvWzLrxD46fzqXhXH5cMd5ZU8fo6VkwUCQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyzcsCRA9TVsSAnZWagAAacAP/1I/Dl4z34fciO6y8Mar\nYlpSkCnbTQWXxjzv/7QpCzw2X9LhoxkyxCo2Th+eRtoygC6mmCUfXCsmgwtA\n1/F2mbF36ZXr1lYdsWXmjxupECXlE76WibaExMaOv1trjSdBWGoEgbHbt//C\nmVjKMP+P78akwqZeqkBjPKn/aZZsZorwmx00qLRHB64jx7IqkQoCMcASAjPi\nU8o0XSsyolMC0X02dZS7RpVaQLAAvtYQ6RwdpBRbS+mhhtR4HH8s+Tv+Eg02\nUBEfey/mCrmTQowNMe9I9IWF0grMcBHcbynhDTVs6ccdl3ood26SA3t4dpzK\nM+YNxxi7bHVzzaopb1ZyXX7wRbGXZkrL9lu5+UMbATbPq6Plv9F1c/PhudYE\nBr2jR3dT8tO72eZbym5z+mSKi7E1A+3lDV6ax4sxCoywt4dLB8v7I3/xuQxL\n1PczpGddNKTAWre10cGBJG/vllyCznFhnm2O5cnu+3wCTicxnueawIJHObn9\nOzTHf6rNr1D48/PRQFw/CUe5PgMmhaZ1Ygvc5YWNSGjAXJZy0p0/ZiIRN8ZF\nkav9cZSwviopns69Q84GCqYUxra/eTFXe/QUyUnVvK3R5pHai4/JlsVZFecV\npqCej8tGaSRGVnYg0bhdXBXxBxO7sgXyA3lR7nkKNmXPkwf2owmtf/YXU9dU\nDrZd\r\n=bvgj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.7": {"name": "@polka/url", "version": "1.0.0-next.7", "dist": {"shasum": "e0f307ab768b799aa8668343ce0df6601f29b9b1", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.7.tgz", "fileCount": 5, "integrity": "sha512-DOQhemlNfLQ7FZ6gdThARbeIjfhdM5fIqoPtgOJkO27r8A4njwiQSqx+qbReuIHEtx3fJyjSn2spMnsAHc0rFQ==", "signatures": [{"sig": "MEUCIHDN3ewIwFIAHSqjTW3+ZchhdFUerzkiVxHI10NWb52CAiEA2OczEIfzWqe+a5+EWN6mJuRRuM/0aJV6piuyASRq8Io=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdt9tWCRA9TVsSAnZWagAAnLkQAJqVkZYcfU6OJMOKRUQ0\nUVg/xVdw3xjs7CXB+qXzT9+5v4YInnmcS7pURfftuY809Yp/Wmx4tyN2zHnw\naFaOOPFhIsBSoSlV78kj7UcHKIF6o+QxScHmu0JFn7TEBSmX+1vt3ts40j/1\nl64lubemgoBINSfDy6xpcrkG3suEiM5+mPtCSbM6rZ87trs76Chjnn3ngSFT\ncAIVQfJGcI7jWNpxhQJgiP8YX5QmJIQjNCjmTXHF6lCGJrxEVdQYbl/6RzP8\nT60gQWYtUrem6CM0yYTzKTJHIAv+6dHNhSBrQguV10fyvv+g6uTPxRlMSH0p\nb3YZvWooT4WQ8Hx1FiWbDSMoCtV9wKK5tx0t07J68s2tUh17/QwdGhfPx8Xc\nf23HyejP7H3G1ue28QKsdvskvq3MaeTOSoH7cLMfe30C+qxD+U3zjCGaa9G0\nXUSMFax/vaSOHh8+xH67CC5DPByp8Gyfv1IVlkwxuyrEfHj8hId3UijfIQGC\nyil4uGCuV1zzW+qko4sJlDwMC8KzrVO6U/jQumf4Xt6BQ7zB8H3aI007kkz2\nBxx0MzgcKQgAeZXifapsxj43u9WRC5u4Y9c8VvW87bRBlkuKfPVimQjArwLf\nCRtpkFMohMIkM1Jd9fEeqxE22+1bL/w0YA21+T37FTyvpU5J8c6sK2ElIgQ0\nb/Nt\r\n=CcA9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.9": {"name": "@polka/url", "version": "1.0.0-next.9", "dist": {"shasum": "9abddbf31c15548f9615a3275f66ac5c100f296d", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.9.tgz", "fileCount": 5, "integrity": "sha512-VZqSaulg2kVQYMulmuZcvapPwH5/y81YHANiFIKz1GNZoG/F4o1JSeLlrvXJ8tC+RPUjxdrebfT3Qn+bnMi0bA==", "signatures": [{"sig": "MEQCICoi3YEUY1ehUgQ44hPmgfn58lVIjiR+c4Z7EKJFkwBJAiBYrggsY+PQ+fpUfGH/+M2KIDxenRtAO6aP7lZKE8JdQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1f/nCRA9TVsSAnZWagAAle0QAJ3Ic+nYfJHilvuCtShD\nqpWcbRka4iVatmuATxSPUFUpXDIakWJvMLJ60eB/z/I0rHYpyTnLYR+9dd8p\ntrg3INJ+Yk9btKU3l+XLXwjSapB+9pF6aGLta5qBI5Idfaq3gG5V+ptkoQ0j\nz9sOYYwva73aaV6TZvImh65AflaVLM1YJDG17qOphOEXRtlTT5u6Ah8jQRmY\na2nDbyQtL2/Zthxy2Uzo40+eemlaK/7O872ZDRI48V2GXOIkTR/zVXvXHBDc\ncHCKJt0OM1XKTp7tD09j5j5ql0P0qi99+npFIpBISefQsjpqwDh4ufHhdt2M\nrO6CpCAXtXFnB6kcNokP8QrfyLUJUx0kOn1/wPLXmItmvDsf4BjTIdOn3h7l\n75pcDAkfQLDnTYbb5CUWFr0+tBItZEd6pTgKS6qjnYcfxhgwUuBblXo5pQbI\n4viQmnvI0z/J+sY4juP3SutrtBydDEoGEHlGw1PYjSIgIyWiJY4ixEdn7fzp\nbgoGcCDOzNa8PvKe8FMDaP3eCYg9XLtDfA/KBag3+RCTk/cTK+hTMjN8tu4P\ny5dgboYELLmWzDJVh1UG9yUcihpgWnqI5yarTPEO/kkaVn1dQfEVNvY7kXid\nIZzw2NuahqVLnVegp8HRE1SnRY4ca4E8D5ndQHGmavVU58q+6kHKol0sxu10\n8rFk\r\n=oJu9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.11": {"name": "@polka/url", "version": "1.0.0-next.11", "dist": {"shasum": "aeb16f50649a91af79dbe36574b66d0f9e4d9f71", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.11.tgz", "fileCount": 5, "integrity": "sha512-3NsZsJIA/22P3QUyrEDNA2D133H4j224twJrdipXN38dpnIOzAbUDtOwkcJ5pXmn75w7LSQDjA4tO9dm1XlqlA==", "signatures": [{"sig": "MEYCIQDMvc3jxgDEgEMG9TOjvW0Hitp+4gjifR7K9c3jCk9N8QIhAObVb5X1uI1OdbWK/eMJ5TlwteDYnFLlfs38LnGgZxxp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHIN2CRA9TVsSAnZWagAAPYgP/R6FHMunm/xNwk6D/XY6\nIJmkhlqywyIETLSBpkEP83B7CcXlWR0ITO8Veqo1xfDRqCvpHf4n1BWSvK9x\nIj4zyXBQLLGn4Ee3em05CHgAAzZD79BrBMYkhviVVpN9BtAo/fDNW41CUeI6\nNPuSKXZDjklTZO6GGR0XsRMuBX2I0zK4kQ3pw4a9JpffDcVnL40PpvFPFqvR\nTe0nLhMAR7dtwv0eHJF/xrmCGbGo6Dh8vpixEwAE3npX6ITR0RLxI6kMUPsX\npEhIx6terN1TmvfKHBCuimubvlKlP0W2GozNi6xMSGs6g5HzLVaqigjcI0zC\nLL1Rkk7zNZG4JYS8aOKyz+ca/AwhzBrU5nNpH/Cg7DHitwnFlgHLDJ7mzZHY\n6GIzWHXRf0VUhFG4iL8nJ+tKcBLaTvwDToj3CzfaO19XxgPW2mX4eoIL5Wxt\nZ2lRdj1BVAjlX2+NrRXjyXxbFl3+oSbjIXTu6FXZ30yytKSbkdhH4H8Lk/lf\nyMhblkMeThrNNm3J2QyybFCKrCWqJu80Zd/RByBIMLqMt3rnSBigYfgJ/mv+\nxjjRoJGRmjygyb60D4g1jANguQllBgk485U4ohTX9wLbBBpSwmWDwqBvMWKb\nUq4eU9mSZPEFE69gXnQ2HO3vjfDdzcRAxngD0aNJhkg6hZnFMajg6ASP5w0P\nJwGM\r\n=3A5B\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.12": {"name": "@polka/url", "version": "1.0.0-next.12", "dist": {"shasum": "431ec342a7195622f86688bbda82e3166ce8cb28", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.12.tgz", "fileCount": 6, "integrity": "sha512-6RglhutqrGFMO1MNUXp95RBuYIuc8wTnMAV5MUhLmjTOy78ncwOw7RgeQ/HeymkKXRhZd0s2DNrM1rL7unk3MQ==", "signatures": [{"sig": "MEQCIDcZlQwujEY7L6MgZyJ60ZVojylNj0mkBaRYSVCMBeFKAiBc4P5wpC+9T7eGgP/w3Gr8LUb4SM6JEa9uZbPPDqZv6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYQD5CRA9TVsSAnZWagAAuVsP/1i4ug28Qen9hwiF2wSb\nyyk3LlbZXdLVi/kkGf3NAQNkMihWYmD0wZW4FSMlnCad/R9GdsBzZAwNOw8f\n9l4yqgm9TbYiRNDpIFaW+mVqFB/5bHkMGaP7OBba8F45x5QYi6jy5Qc4I+dA\n1LJgHhuW+mrkK2AqNi9H6rshTbHyCT0b6e82XvHREU8mQGhj5HNxZMeqwgt/\niMqE3umNtN3xb65KCG7h7GFSQko7owAUVFAWD/RuBmqh/QF5ukhYZD8hmGBS\nquxey2gn5DQ8nqOB24hUM0nqzUWWPu4Srnl/OLbl9Q/JYaGaVT3hT3rjRuYz\nbjqJCbpQJlQ61aP7iEZhHIeaSWxbazzFoo/mVdOBeMNuOv7hAx0+rHrCdx6h\nYmEMKx6NhXoCY5mCREjBT5PLrlEvY/TwgPLnoG49VRI98ZeblHVU3brnTqVL\n2kxOF5Aczva3LaxmEdgu795m0+o+R/NfuCKipF1NpUoGLzys1wYccvaCioQI\nOaenacsGF3GQio5WXl0DQfzpzy350Y4Q3LKq2TVbKgZ0EWufrVdaBe+8ONPL\nHx18/cM4WAyQbHlEafTGwQHeL9UfSdWaWAXmFWagDkDuukq73wata+C0dS3D\nC4ZymG9sY/m7oW78LFOjeGqCStK+mwkAz6PWs0oWXog9iQo9rPH6+zvETFUR\n5S6d\r\n=I42H\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.15": {"name": "@polka/url", "version": "1.0.0-next.15", "dist": {"shasum": "6a9d143f7f4f49db2d782f9e1c8839a29b43ae23", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.15.tgz", "fileCount": 6, "integrity": "sha512-15spi3V28QdevleWBNXE4pIls3nFZmBbUGrW9IVPwiQczuSb9n76TCB4bsk8TSel+I1OkHEdPhu5QKMfY6rQHA==", "signatures": [{"sig": "MEYCIQCRapcZ2xdvG2s78vTB5dYhPhtYt5PvCgfQf2pA+lkzGgIhAP74QFhhxX0gClU7X2aRJ+P6DCdk3DRonjbldYiZXda+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrCIlCRA9TVsSAnZWagAAGYcP/RfR9OvhVwDRvwapCpZi\nD6rrAtxaZvsxMc7CokZckGkEd/8Zt0b1U8d6w0s62T6qeQbD71BLbGT+uxda\nRm3hs0Ga7Bug+BdI6Hp6HlRmhmyeYceSW2QDoNz/ENiEL9G8PfuTe+dlwVBL\njU+JiVzgoGCPyeUP+/5BlVHQnorWl0Fkeq3PlJIES3Adu5J4yui8mNPBIe0/\nYMcRUOwqlydsxwNrWgXtdHgjkdxVIP+V+e+R8QBPhrint42oVWCXpiZXyfUM\niBH2oF7hWDB51oEFT+YaEc8hxhyoGoWHcCeWwBS2PvpRls1ASzIkkZ+SIZ/X\nAaYO+xeK5ZIbaZzgNqVhqV355K0KKJyeqMtptm8VCulVaX+/IcxVgxr3omDx\nqkbfDobeqDMry0xpoCjk8DVfgWjZSyv+yfjXPHXV/bS0YbG/YzRNF0o4gggk\nsOGOEdzfdwFvZZzmdKWROWGtlG/Mr4eSebhRquAoKhWO4+X2fIR9vm1qcgAs\nWtqw3juuAWGMCpYOnNJZuFDyO1GACnw6sivaWeCQ8m03Rucn3PoysHd87fWL\nx4asmVHbRJMu/Jvdw2y0o09/ciOE/Z9MDJHc6d2GhmzFpOAKG58QEBHeIwb+\nZZ47Rdii4Z9yTqSeP+F6DNR/FXYklZx7Hnzp5MHAcj2l6OamiI+K+12dJ/sh\nLqdg\r\n=dD5Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.17": {"name": "@polka/url", "version": "1.0.0-next.17", "dist": {"shasum": "25fdbdfd282c2f86ddf3fcefbd98be99cd2627e2", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.17.tgz", "fileCount": 6, "integrity": "sha512-0p1rCgM3LLbAdwBnc7gqgnvjHg9KpbhcSphergHShlkWz8EdPawoMJ3/VbezI0mGC5eKCDzMaPgF9Yca6cKvrg==", "signatures": [{"sig": "MEUCIQCUQm9j+OfkONMEgAp8EF7x+RkW15YfEoL9p2GhQJ3kKgIgaNrRhB2s/ZTLn8qh+0xH3gDzVG7NNl+ukKv8ZB6XLXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7278, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFw5JCRA9TVsSAnZWagAAFUUP+QC4PxGm8x6Rn/1jCyHt\nSIYuzb9BX+X/oy4DqsQquDtZd6JoaA9sUkqhQxFgGS4MxSIxVm/AIT4GLocx\nmRyvKkWyPacqPS6VX6tAA22/q0MftiIxi/7Wcq8Jy4i8HAcQaSTrLXSUCARa\nMze/Pre1pv8fU39ZrMfCLbyFFxxftZ8q4bwQMW7C3stAN29TyYYF1uFbcrtJ\nvXqT5prnuE+NYdRQWzcOgB1wKJkWhO5yXjIBDIGdNc8jaV+iRTHi0w04wznI\nurOATSZecKnJsEuOUXRTTRRPDOJyUt4WdxWjTAovkTJa9mjgHdrT2FYHwCHD\n7ZmhkjeEvMiYV7wRpTC6coMSPFNiCQilzVcflo0NIteOZ+d2PSfP1wJNjAIA\nDgDYvqQLhTYAcS6ZL6QN8MbQstS/P60iD1W8REk8h4SU5g5+T94Dtlas2HoK\nefu0IgaQUKXlnGv5xgTjdMixkYqcfJNhygL3MuXOchrWJVEO6k6+1kdbb+sA\n0BPFvD/s67afV8fCfr4A3IJVTiYKjcYA5tM3DklJiU+Wec2ojeM0Znx7WAoq\nFlSABVnZ3NKqB58CCZGZrNihr2d+BCimOpcRNT5muB40L5xUQcr7pBhhpxto\nPfRhjn7i0XqeVmOM4ILOht5BAaLeCyo6mDEMNirca7kTiDpQg5PwS8F5u/XI\nK15m\r\n=Ih4V\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.18": {"name": "@polka/url", "version": "1.0.0-next.18", "dist": {"shasum": "c8d8db7f3d38092c90ebc5e825c2c6b0cc5b1042", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.18.tgz", "fileCount": 6, "integrity": "sha512-rEAwa2i6cLiGROES86oNNsSV8DlXXnv34+G/e3lnEcmkP9Y2YVuEGcqv09dHQuvzQCDAqRGlJVjeqlawQb2qew==", "signatures": [{"sig": "MEYCIQDZ3h194g+Tj/AkWlRKly2kav92BcNcRoNNZ8NRTFfccQIhAIW0k3wAeb+dC5+EhXRAME+qCjiBJaiSt8Ns+uhCiUw1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJwliCRA9TVsSAnZWagAA6psQAKTRr3uCeT1SiJWgW/6s\nl4yMsVyvHIsMK9CNeCdIboKn/71ziP/JfJdBcPWT0LXDjz8Wl3CIoOdUwqr4\nabjSJdZe4lmQyFrFExFct4qLKd6PLkUwjpiL7QPx2dJ19NHsFalIPL9IkuvE\nn/U58ciuTwGIDmtxHKzXWEYCwFcvOekDgj8cFidwhHsY6A83+NfDpBMEk1Yp\nTjS36jmEzcNwEuah9GgnlNiFLlfPUAI2CrffYz83c7MIDusqMOKtuB60ubcz\nqTtCyspQTnXRxaSLCJWN+NNalo+VPpy6MecUVYLwWjkZpUeoaSEe8pbv6Rnm\nKHxhXCLBUFEjoGWqKVJY2Dt2T3OeAw5WNJieaCByd7GTmmiwbxdzj0TcS9dk\nOXSYrJPPwezwtrDFsEy4Z+zOoT3yuh+V86JVWAqY7fBysdWGKXc8OZQr1gUz\nAk1zimZM+8LPJCmu35Q9yqPKBU0W5NRp+yH3m0f1mPWjDtvItT9l2DxW0+wz\nimCkmh4i3tQUlAe1YpjM+vDOsUAeMlTG3o6uS1CIShS0cqTZ+CjPPy9EppxG\nrHqVhSBHLRCEpl+u5/8ex7ao0f/yGl0F3k3YdZ+8PmCGru+RwUWSwJIBXnT+\nBsz8bem8QTjL8jb6Ng7xkbBaLx6d1b5Zu7xYboFabZo3a7a4lXT7cqEy2JYY\nGyYk\r\n=U0gw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.19": {"name": "@polka/url", "version": "1.0.0-next.19", "dist": {"shasum": "2c94db828794aa53e7a420809dac870348819233", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.19.tgz", "fileCount": 6, "integrity": "sha512-kHR9OHwP9WLpyC0i/WCAQCgf5hXkR9C+/21qxmrn+YwRlDRnBlqrcrFpXxhJTA9LDHJWa/FjoO2LJ12q8iWlEQ==", "signatures": [{"sig": "MEQCIB4ZqVvCOZNNmN7OXYG42+Z/XBZb54FrZu6ZtxFc3+pnAiATL9ANZzFum1OiKZyx7sA2jNvJo9URvH2pqorHYV1BSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJwnbCRA9TVsSAnZWagAAlhAQAI9K0oNM2dhWuCJr6MVy\njaYIvo6E6+YF3olml2doaWdpSYH4P5BYxqXdmP0Ratl8J1GFQZC7FEExCaDX\nhNNNjcR4LFI6cTe0ZCa6BoA91TqCO3vmR6KRFV4s8AbR74IO9mGS3op0XnRZ\nQcUxI6xuEpCtnJZYtU7bBTIOZHrlypj7St1rhlBq71jdGBgPjTPvUn0gSoGP\neXCKCP4Qg1mrPAS3NpucauqxJ3TarB/euxaKvlL7EI/QqatXHeK76ebnhWlD\nfkvXhtFCwY7d0JlPwMFNJEB0aumTiaIfG0oo0Fba11yna0hNrLZq+1Ts4JYY\nLU72u8EW745FcFCEUaXvB7/b4xfKi0JNTNmeLu/xOiWM+gjcUkyV5ss7Is+0\nF5tAzTNr35VQ7THG+uE0fdufU+QEKfwgq6Extics5w1l+kyrj5kQx14KslPw\nK1wTY9m5ptrJjCmMz77405OuAROgpWlFOjSA0TCnwP6AcfpfklUy4J3DOrj8\nx6wE6CQGGPY2kmI3yX2tm6uVf8cJxfR/YN/vr3n+fMyFS1wLVFbNKgPVzldV\nAb5c2M+PBk+Cs8g80ZeHuVrespxYgEnSW3Tmmg581Wa5NXktmOAxH5RQyqu+\nQTfokHSyYi+CCXqzMec7NIK136aJu68bwkKKbUr1GSbs/JuvCP9kiHRIlDD4\npokT\r\n=yIpd\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.20": {"name": "@polka/url", "version": "1.0.0-next.20", "dist": {"shasum": "111b5db0f501aa89b05076fa31f0ea0e0c292cd3", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.20.tgz", "fileCount": 6, "integrity": "sha512-88p7+M0QGxKpmnkfXjS4V26AnoC/eiqZutE8GLdaI5X12NY75bXSdTY9NkmYb2Xyk1O+MmkuO6Frmsj84V6I8Q==", "signatures": [{"sig": "MEUCIFr/kM+hV5mYQ6YSdnwZk/TOSsVxL3BGCdwiN73sQFHSAiEAycQc0u9gMT0qusE4lJT2OdmGCh5G6WnOdZqwJLrXfhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKI7BCRA9TVsSAnZWagAAzuIQAIhqkPeEIBhi/vk3Syth\n5S9ms/0mL/ABvGUjQVG2CGuRLSQTChj2+22cBc9NRw5vB28Jwhzpm36kgKPM\nIg/BY8WPFBP1CHc6U0mo7JnGP6b8UUHktAzdQiSt0/zp/aqZZCNmm2bDCyvh\ndZ3/HOwSArCoV2YvLkvZnBOjYB496gc40UQUTZu671hU79DVMVjFv7ArLC2/\nDDKqKN/+klQoaJ2EbtW8ap19WHalB4jihHdB2QRs1FNO2D7JNIznQTB+xEVw\nVLBRCxjCM/VdPr0VV/EGeC8tIS7GgKvjP0OiFo9vlQkafrY/3Ms/BWha0yC6\nc0ursIHbXScvb6svNeR/VrDWixEFhzC8rJsOWnLFDkq8melhinws93JEbJcA\nYyq01BtXyhT4bHMuEBZGUEit8dQ+OB8GRSjvR8x6L0h7XEFouPdR65kG1yeq\nn13E4xAHY+1hyAiAf0vVsO/bOs7NyCwgr8yCLbqMhFwisO69lfnl7MxY0NSt\nQ0dnjbpLvMuROhUxLar3xRG2WVvxmVMcN0tPjHEEYm2xx7y4QZ5nc8XhKkux\n9kwAdvoBCWfl2mAij67bCxbw9q2o5vzCEToOs7KafBo/7XZatGVZiWP2zVQ9\n+ioDPf3UIIzEOfL/0WbIggSEf6DH1HHJO+PyyZjwcqu+ZLpLpvM0btCUaypv\nBx5E\r\n=FYOf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-next.21": {"name": "@polka/url", "version": "1.0.0-next.21", "dist": {"shasum": "5de5a2385a35309427f6011992b544514d559aa1", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.21.tgz", "fileCount": 6, "integrity": "sha512-a5Sab1C4/icpTZVzZc5Ghpz88yQtGOyNqYXcZgOssB2uuAr+wF/MvN6bgtW32q7HHrvBki+BsZ0OuNv6EV3K9g==", "signatures": [{"sig": "MEYCIQDljFfE0GHmwFJbs8mWFKgfQsD0KrvK0GHvofvPXbUg8QIhAMLUv5jJWPWlENsBPrWjO1xgHeSPvBlG4qsmMEka17yr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5528}}, "1.0.0-next.23": {"name": "@polka/url", "version": "1.0.0-next.23", "dist": {"shasum": "498e41218ab3b6a1419c735e5c6ae2c5ed609b6c", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.23.tgz", "fileCount": 5, "integrity": "sha512-C16M+IYz0rgRhWZdCmK+h58JMv8vijAA61gmz2rspCSwKwzBebpdcsiUmwrtJRdphuY30i6BSLEOP8ppbNLyLg==", "signatures": [{"sig": "MEQCIAJmeO3WhzMJYQazg1qdjhStpCWXnnRJQcFzxLDCQ5ycAiB3DFU/B68i6VPyKGxH+/1Pm5Za3py6ylwo1iYef1jVNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4379}}, "1.0.0-next.24": {"name": "@polka/url", "version": "1.0.0-next.24", "dist": {"shasum": "58601079e11784d20f82d0585865bb42305c4df3", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.24.tgz", "fileCount": 5, "integrity": "sha512-2LuNTFBIO0m7kKIQvvPHN6UE63VjpmL9rnEEaOOaiSPbZK+zUOYIzBAWcED+3XYzhYsd/0mD57VdxAEqqV52CQ==", "signatures": [{"sig": "MEQCIF6qF0pOv6Y8PNYs6BBORXMFXYSy47CTfzlNj7EhlkwFAiBDyrO0XGTi+0h884zyTJk3xswpDx0nqvXbf4iVjKY09Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4382}}, "1.0.0-next.25": {"name": "@polka/url", "version": "1.0.0-next.25", "dist": {"shasum": "f077fdc0b5d0078d30893396ff4827a13f99e817", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.25.tgz", "fileCount": 5, "integrity": "sha512-j7P6Rgr3mmtdkeDGTe0E/aYyWEWVtc5yFXtHCRHs28/jptDEWfaVOc5T7cblqy1XKPPfCxJc/8DwQ5YgLOZOVQ==", "signatures": [{"sig": "MEUCIFrh7FzuSJ6LJWVLhrtmPMxjU/PcnFUUZqPXtAEGygotAiEA920/YHZ0/k8jrajtS4nEo9TiyOQPqSP8gpJGVtJGkCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4480}}, "1.0.0-next.27": {"name": "@polka/url", "version": "1.0.0-next.27", "dist": {"shasum": "527e8df13dea13ab68d347d20ba9432cadb867a8", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.27.tgz", "fileCount": 5, "integrity": "sha512-MU0SYgcrBdSVLu7Tfow3VY4z1odzlaTYRjt3WQ0z8XbjDWReuy+EALt2HdjhrwD2HPiW2GY+KTSw4HLv4C/EOA==", "signatures": [{"sig": "MEUCIQCkwazbOlwlkM7gezrM547ZNIvpIVD1TFvy2lnpSZJbewIgbLiLtg/nF8aQK2c08z3H4bNSDnyLW5MiOC9UeyopZWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4490}}, "1.0.0-next.28": {"name": "@polka/url", "version": "1.0.0-next.28", "dist": {"shasum": "d45e01c4a56f143ee69c54dd6b12eade9e270a73", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.28.tgz", "fileCount": 5, "integrity": "sha512-8LduaNlMZGwdZ6qWrKlfa+2M4gahzFkprZiAt2TF8uS0qQgBizKXpXURqvTJ4WtmupWxaLqjRb2UCTe72mu+Aw==", "signatures": [{"sig": "MEUCICwtpQqfFYOtqEk/r5x69RvllD/SAbsWNQTn34xcERHdAiEAooN+imrEV7qGzuqcPlDgm38fawZ5UHk5HcoLvLMLfd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4485}}, "1.0.0-next.29": {"name": "@polka/url", "version": "1.0.0-next.29", "dist": {"integrity": "sha512-wwQAWhWSuHaag8c4q/KN/vCoeOJYshAIvMQwD4GpSb3OiZklFfvAgmj0VCBBImRpuF/aFgIRzllXlVX93Jevww==", "shasum": "5a40109a1ab5f84d6fd8fc928b19f367cbe7e7b1", "tarball": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.29.tgz", "fileCount": 5, "unpackedSize": 4815, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDF1ggIE9corx9OrD9dpkiSUn36AGHZkLhxzAOhQkLuZwIhAPaZVC9XMKDDNowpiKUR/Mx0sNe+ZjtUzce9b1HN0ofL"}]}}}, "modified": "2025-04-09T18:41:21.608Z", "cachedAt": 1750172041228}