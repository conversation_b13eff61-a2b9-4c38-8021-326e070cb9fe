{"name": "@asamuzakjp/css-color", "dist-tags": {"next": "2.8.3-b.2", "legacy": "2.8.3", "latest": "3.2.0"}, "versions": {"1.0.0": {"name": "@asamuzakjp/css-color", "version": "1.0.0", "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "f8e0b297820a2d660037795e8107c362946530b5", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.0.tgz", "fileCount": 16, "integrity": "sha512-lo7k8F33cu01rzneWv2Pj0cBw1L4B/VX+V9XLJscyIMjvVuSM7jBUsRDR+Z57/c1DMjj4QHm0IBN/PG2Au14aw==", "signatures": [{"sig": "MEUCICUf1KMGIfeAAI7snPpBkJgQ0jWhieHNu2KQ70hpVSgzAiEA3xwxeOHVdNFS5nob3RZCLZHb8CzFRykYPzUys3UWOro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257579}}, "1.0.1": {"name": "@asamuzakjp/css-color", "version": "1.0.1", "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "61cfd9651604ae21f2a389c7a6166cc366fa5652", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.1.tgz", "fileCount": 16, "integrity": "sha512-HRE89jJMyiCdYbgOrozBz81oYVG8WSegy7jLrCAQ8JWQdH5lyz4Ax6t1Q7ja5hjdxcWSHs/HEYvzC1qwEfTmKw==", "signatures": [{"sig": "MEYCIQDnTzVYLBk7HmBTq/izF5ZwEVMvDq7hchvatk/EaTgTEwIhAJtJDjMY8plVaKazToyqsDUnoAMq94jB5dVvTPjUDFFc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 259583}}, "1.0.2": {"name": "@asamuzakjp/css-color", "version": "1.0.2", "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "npm-run-all": "^4.1.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "ac792411f94e4006befd4068feabc1bae5f545cd", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.2.tgz", "fileCount": 18, "integrity": "sha512-dmyJMjEtN/cJU9QLjpFrtkJGucffWOCJD8tqolXJUfSKe+1jyjr23yJZFZjfvvKqIkFO29e7NX24dxkBU8BFxQ==", "signatures": [{"sig": "MEQCIBpftDPH937dQ/WeHOmWtZZpDCdGGrNNBhAYNm0BsDA9AiBR8bslRNTNUMi5HgFU4uINL1IJKFODjXw0Bq0HP2KT4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 426503}}, "1.0.3": {"name": "@asamuzakjp/css-color", "version": "1.0.3", "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "npm-run-all": "^4.1.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "4fe0fd729b9c1163456005c5230dfefb00936bd6", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.3.tgz", "fileCount": 18, "integrity": "sha512-0b5QM+7CT4nLTfVh+v5kflqhK0KLUwawJhymiAWsvS0P9t6qwqoRnwnmnqnPRRoh2rSgi+dGKwWjnCtABJNTnw==", "signatures": [{"sig": "MEUCIA9mYwP6eIkw7n64Dk5kSwA790e28BbHZL7aGEzcwBdYAiEA/PWbYQLnJgquO54d6JH6iY1xvL57EV8s1GbI7LrcdPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427100}}, "1.0.4": {"name": "@asamuzakjp/css-color", "version": "1.0.4", "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "commander": "^11.1.0", "typescript": "^5.3.3", "npm-run-all": "^4.1.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "a99fdfec05542dbdfe362414f3a166c9078464a6", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.0.4.tgz", "fileCount": 18, "integrity": "sha512-1F6N0WjGsL4ZmQ7PS+hVl2Bb6yaH3wvXpGLJHjaCoWqsowkI04e9I9KqU2Bbdv+O6RBYv8XrjkPObQixlQHveA==", "signatures": [{"sig": "MEQCICz/Us/AkwHYKx1/JsMxyr6l8E7OIKewZaJBt5LFiSR6AiBeWkCVc7mikTUsQSnQfwSTXI4ZDHbXeXCNdMFo5pCLvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428140}}, "1.1.0": {"name": "@asamuzakjp/css-color", "version": "1.1.0", "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.57.0", "esbuild": "^0.20.1", "typescript": "^5.3.3", "npm-run-all": "^4.1.5", "eslint-plugin-jsdoc": "^48.2.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "2df51aa4d61076faba8b97e4f9506b11a5b9bfd4", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.1.0.tgz", "fileCount": 14, "integrity": "sha512-wWXyb6PPgTkfqfA+pRAr0fIKdUMwGxt0gPpnVuQ4ArcRcXWg3VQu+qHCFuZ4DaxNCu4pB0OYoj7FC3pxEtq55A==", "signatures": [{"sig": "MEYCIQDCfHMm7FAfqQeup6hxh9IrdpXu3aoWXH3GO6unW58zygIhAI3ketNBeHmutp/nuqVQ9g8skkHKB5Iy5iOZ1/4XEom1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 423596}}, "1.1.1": {"name": "@asamuzakjp/css-color", "version": "1.1.1", "dependencies": {"lru-cache": "^11.0.2"}, "devDependencies": {"c8": "^10.1.2", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.15.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "typescript": "^5.6.3", "neostandard": "^0.11.8", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.5.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.4.2"}, "dist": {"shasum": "0e8e4ffefaac6e7275aefebcbf18f62be0c9836d", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.1.1.tgz", "fileCount": 14, "integrity": "sha512-wfKjiBrlQC2bexTisWDyYrZToSGKV3AYZ44MTdYb2bJiQmt29VtW43MDSax8pAghfmDKuNg08tNXBGcy96b9jQ==", "signatures": [{"sig": "MEYCIQCVIfNI75u8y79VEJxPlBFaSaOYnQ5hKnxHIQdy6mYxrQIhAP8Cn/HV22T1NrvjeN/tQEhayHfAKH+xkzXMQkBbr+f6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 695665}}, "1.1.2": {"name": "@asamuzakjp/css-color", "version": "1.1.2", "dependencies": {"lru-cache": "^11.0.2"}, "devDependencies": {"c8": "^10.1.2", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.15.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "typescript": "^5.6.3", "neostandard": "^0.11.8", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.5.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.4.2"}, "dist": {"shasum": "da5b7ac6cc51b08e08f2259d7306802a3cd29d92", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-1.1.2.tgz", "fileCount": 18, "integrity": "sha512-Pj8hBJj1tKHxVs79CGzj8iMzLXswLXrp/X8Yk+uxIC3wzeNolRLHBUi4UV+bAFL3+Y4cleSgDaC/dwoeAvjiqw==", "signatures": [{"sig": "MEYCIQDdBqI18OVVj4a+k+ImqKs/ATx0l45Oo7tWs1gJrY6fngIhAOU18uFCtAdeEXRk9BNKyGGPVhCLUu1ZsxdTewN/yLt/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 720229}}, "2.0.0": {"name": "@asamuzakjp/css-color", "version": "2.0.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0"}, "devDependencies": {"c8": "^10.1.2", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.15.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.4.3"}, "dist": {"shasum": "f7c85c3d0a32ed75d0f9008a196da96ebc5d3895", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.0.0.tgz", "fileCount": 18, "integrity": "sha512-y038hFi18iBVsHz1lZZTDhMyXIp6DXdZFhJwQvH5i+pKfEG138z0UDBi1pvz6qFWWMZMVhksQnfps5W70MTN+A==", "signatures": [{"sig": "MEYCIQCmj9fgQrHIVL7R6o902sXGXwztlr8eBxcVOwOX3GyHTwIhANRPPBhsEaTJJNgnrAzJxvYOHQ3NN7gdOx8bUK/Yg9Ki", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1171268}}, "2.0.1": {"name": "@asamuzakjp/css-color", "version": "2.0.1", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0"}, "devDependencies": {"c8": "^10.1.2", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.4.3"}, "dist": {"shasum": "034ca9dd6f3fbeb24fb162a04d1e8b51f4071a45", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.0.1.tgz", "fileCount": 17, "integrity": "sha512-q9rufhO6u+1Se6ZOqflgTnzk2b6qWDCCtxzSTZTyx4gYmx125Ek58IyGpw9of04wGqVELg/WAEp/kRx8KolNbA==", "signatures": [{"sig": "MEQCIFAGiVf+MkrohwwsaizdXLL1Msi73TeOtqhklg9EBKY4AiAzHYoJl6uH27h+Xr6qw8U6cDXwmxMHOv0bu4iDqO6Ypg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1169244}}, "2.2.0": {"name": "@asamuzakjp/css-color", "version": "2.2.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0"}, "devDependencies": {"c8": "^10.1.2", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "8d7d095558927e27f346349a5fd15127474ee041", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.2.0.tgz", "fileCount": 17, "integrity": "sha512-3YFjdkmcADmBBz1ve1T++UgnbjJUFnssl6cRHaTY34oKjDbHy65T/kTHpO6oph2Z1wdZkeFt9PvQRjXccAufUQ==", "signatures": [{"sig": "MEQCIHJgZN5tZU7tEN5a77kZZOgqGeXP3cLVOv2hBPh/QaCgAiByZS3LPRZuQ61n7xkjoKR2RByGeFVXn1S7xazjodWnKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1253261}}, "2.2.1": {"name": "@asamuzakjp/css-color", "version": "2.2.1", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0"}, "devDependencies": {"c8": "^10.1.2", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "9cd258617279c91bd183f92a4e0bfbd3a5b6daab", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.2.1.tgz", "fileCount": 17, "integrity": "sha512-4/RoWIs1GTq3Bm7Xyh1cjRC1h+nyMqoB23Twr5vnbyNqWx5FTbBq7Pb70IovyL6vZbi8vlsoyx03bIBl0U61FA==", "signatures": [{"sig": "MEQCIGTPaY2EWWyM7/KNCFKD+QnHxT25nu4kbpytjBesm3NTAiAWq9EeYrpPBAof7si8Gx/zAnY19KYAjcLaQOlpmCr8AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1255435}}, "2.3.0": {"name": "@asamuzakjp/css-color", "version": "2.3.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.2", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.11.9", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "c746b8b9c97642caa70cffa0d521a4b03f42bcd9", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.3.0.tgz", "fileCount": 21, "integrity": "sha512-vDI1tl26mhkepjjDW6th+vM8p6kEqUsdQy+GwYkAWIS7Uo85nV9Zf8FZLb4RGzWjKeys3KOHEyUwRd20TxdXBw==", "signatures": [{"sig": "MEQCIGt+wWBVJbMjcUXlV4K9rtCCdWlWlqW9Y4NJxSiJhAT7AiBNc7q1l2vWdXEnAL4j4Wh28CwACYXsg4VUB04WXtQh9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1325841}}, "2.4.0": {"name": "@asamuzakjp/css-color", "version": "2.4.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "ca5d4646b5409dc01af2034fc830477e053b0b65", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.4.0.tgz", "fileCount": 23, "integrity": "sha512-7UjwVHh1yFoJS8phGqWJH/spSaPrP6hO1WTnwlEM33gISpFruOKiaD4TI4411HsQAkkRA8neHZaqj+aHcTlaUQ==", "signatures": [{"sig": "MEUCIQDxS9lLGPHlOGVeDYh9McpZdwHtVwqRHeGFd2GylQgg9gIgMRDCRwt/XaLQv6yooiBieV5Vy5b9SVDt6ubMKLhr5qw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1310382}}, "2.5.0": {"name": "@asamuzakjp/css-color", "version": "2.5.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.16.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "11bec53b218ed231f042eda5fc82a788d373f99b", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.5.0.tgz", "fileCount": 23, "integrity": "sha512-LuUSDuz8MPTAPOb9Zvd6b/D2nUyG7NZaI8OGZhu45xgQTqLQOC29HLfadPMrs5C7JY784WRtLMvLmQhTpIY0Pw==", "signatures": [{"sig": "MEYCIQDHrkkV6fK5qCrIlN/6AoZpfoptkCDL8icghOeaWWyhqQIhAIMTIiORpUc/FYEzDFeDGSf8Ov+ff2VkhwXvkJnPqi9n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1311190}}, "2.6.0": {"name": "@asamuzakjp/css-color", "version": "2.6.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "d34c0e449de976ba1c403b258c413a6b660b864e", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.0.tgz", "fileCount": 25, "integrity": "sha512-/S8Z9048cqsR8xpzLnky48Xw0HTPus+TeO8GS8eru3RUAT0YwxCmLuKxU+6iUJCFthiNshvHRTn2cY4nCfoauQ==", "signatures": [{"sig": "MEYCIQDCOCAHgAgWDX0QMvsi0QTAt52BHB2jftDih+eMjKl6VQIhALIsDVWUgRwv0UxAC8W1YzezYxi4dgJ/nT1qaU1RTV1L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338050}}, "2.6.1": {"name": "@asamuzakjp/css-color", "version": "2.6.1", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "1a36dd3edc0980b24b1723b7f53700884f677d37", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.1.tgz", "fileCount": 25, "integrity": "sha512-QRBdygAtbi9H2SUb8p5xU+TjHfBIidoSUkmRhbMG8UoTGucdXblcP9T4JnzKKvv+FaiQ86SynsECTQ8SDYP5HQ==", "signatures": [{"sig": "MEQCIGLVLj3Dzy0uoI9ezR3fOyrqZXWydYYPjSJiKpYKwjeuAiAU29JAc/fBrIRG/11jhowJEQQH1MIhyivp89Ub9FCb+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338235}}, "2.6.2": {"name": "@asamuzakjp/css-color", "version": "2.6.2", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.1", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "55f6a7101657e66047bfe244bd942aa5a75c0c44", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.2.tgz", "fileCount": 25, "integrity": "sha512-KDnElUF8LWAFGLqaJBYbgOXS0RIhOHHFLWD2DV3jW5EPrCrOD8Hfco+qn8McBFnfSss33WUarKW9PTyqeXDy3Q==", "signatures": [{"sig": "MEQCID4kjZgVr1N/a4pGImD0QriK9Ee4hjcw6lcTzxbjMiLuAiA5p64pnTbwa4njrTpaVm1hqgn6Gy0ZvahsoPImHqi2YQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1344743}}, "2.6.3": {"name": "@asamuzakjp/css-color", "version": "2.6.3", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "68d12a64f9fb356460a17b7f7ef55591d49160f4", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.3.tgz", "fileCount": 25, "integrity": "sha512-zrMP1u8F3U92LSsuys9jV2WwhidRVfXX5Jyqf533h9Cw9y/VuKToUMjapv3ZKC25p0KkXx75wMGKD7UIkl8MCg==", "signatures": [{"sig": "MEYCIQCYQNscP1DKrm3RhPtVxqXa0bymukYAC86C6ANdF4RGIwIhAOfILE2oz5ZGxEB6EHf7Gvm0mDDjXEd6zq72OELfvHi8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1345324}}, "2.6.4": {"name": "@asamuzakjp/css-color", "version": "2.6.4", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "47126da705a9702c95173abce8bbf1bc004ae7ea", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.4.tgz", "fileCount": 25, "integrity": "sha512-4qX/Y3Hr+Z3B93fY91GMCL4Dm+nvl1a/7iq0JG5SfmblbqW/l8esXlxKTHsOeXIiROIY9uuu83I3eXpTyIMMtw==", "signatures": [{"sig": "MEYCIQC2sPgh2x4ewGEWnj5B25G7vK78DWMJZsPJC2XyNRx/GAIhAL3PWc4d59I91jOUWI1EOlDCgxCaEEq0t++B9cNE68o6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1350017}}, "2.6.5": {"name": "@asamuzakjp/css-color", "version": "2.6.5", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "9420b664e6555b7a966a2a6b3ba80fcf4a8145ac", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.5.tgz", "fileCount": 25, "integrity": "sha512-MIF+O+I0TadFOcvMTxAKvxz5s/2AeqX0Zk546llW1Sc62rH7zBTF5i21r3sJYhoFEZvHb76tNQfnWqesn29h8w==", "signatures": [{"sig": "MEUCIAJzY+1639md54KEAtBJHpbzSRxc1SZBHzQ+kHeZw9swAiEA2KkO3BMRcHXZZpnBXRTffijqCPqpEx4CAPzzBdzphOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1350244}}, "2.6.6": {"name": "@asamuzakjp/css-color", "version": "2.6.6", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "8df73ac98812d710197aa8cc6f14ec28c5ad9b96", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.6.tgz", "fileCount": 25, "integrity": "sha512-6kThlAl+6+YXKwArRfkyptBK6JbpBqGfTYlpMOINha4OIU/gIem0GPQ1a77OusA3N/1lWvVgL21GRGlvz7UbUg==", "signatures": [{"sig": "MEQCIF7/iEv5wsyWtkFxCFO62qw+2q0hfgOBpGCZvYrKqMiXAiADrYxVaCDznZQyzvHnFV39ohXUcSNmHlBh3XFsN8bTWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1347776}}, "2.6.7": {"name": "@asamuzakjp/css-color", "version": "2.6.7", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "35cb3c9c13bf1a189faaaf0cbfd9ba0d38ca08e3", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.6.7.tgz", "fileCount": 25, "integrity": "sha512-d95sStIjZzGoAYPHHT7A6oyU8Tr+BMYOebog/U9kVkRonH2AUA0QmDOqEVQvpNbLuj0YqesS08RRW+bhrR1/jQ==", "signatures": [{"sig": "MEQCIA795943YEIvg2qqZm6oJVLNgHZUoqJqEqa06lKSTk9oAiBihk7DH9JFXpLJDl2BVx+5NOfDyIUKdMeOMqYsQaLJqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1345233}}, "2.7.0": {"name": "@asamuzakjp/css-color", "version": "2.7.0", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.0", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "4a831859876778b7d4c731a18bea6d3481f31d88", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.7.0.tgz", "fileCount": 25, "integrity": "sha512-Q8vPhtSooMPXAMif1gSk2TorlnlT+1hkgryGayGNKdxqdptj0o21W9q9JYKCJMMKa5hVfOgsru6zI7fhXzv/vQ==", "signatures": [{"sig": "MEUCIBD+IBLBGZ9wwzUrZpSxb2H4kkAoGjtGXw0llZ+jSOfhAiEApbbJJGP1umDi50NxeTArIZ/6hcLDO6sczJbNAbopru8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1353352}}, "2.7.1": {"name": "@asamuzakjp/css-color", "version": "2.7.1", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "ee1a577530c377cc9fe565cbfb44d200f8b8a5c2", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.7.1.tgz", "fileCount": 25, "integrity": "sha512-FrLFymoHF4ygxluMLQFH6TNaOFvVaMj28//YmfQBrDieEc4gvcCF6mJyI4Bdw7ggKWbD3o/vlo4aCI8I5NEtgQ==", "signatures": [{"sig": "MEYCIQDOiKs63CEGgt48wy83tsMfbj8NoyhYjAvzH9ffN4IkxAIhAM7RA5WmaS9XBtMC7SflfrhPi+IRb0EKdSEaTr+lhP77", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1352875}}, "2.8.1": {"name": "@asamuzakjp/css-color", "version": "2.8.1", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "2f72de2efaf7fa6545b0c37c9394b4cf705a7a2a", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.1.tgz", "fileCount": 27, "integrity": "sha512-dnEjj0gnL1+rquxI2MIS661jWefxu5XF2Hya7dHvl2jK+MMg2zie4kNxHCmTTyi63tuAiKY4hCXa2rT2/rG/2A==", "signatures": [{"sig": "MEUCIQCypCiQkmGvp0MhSb5SMMwkT2xY4HaivAYoa9TIRcG68QIgF6URH39stvkQLBn7+8092kjQftzSHTiYUekK45ovqtk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1797897}}, "2.8.2": {"name": "@asamuzakjp/css-color", "version": "2.8.2", "dependencies": {"lru-cache": "^11.0.2", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "817e84b0cc9f426379f4b549836f32b670c43649", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.2.tgz", "fileCount": 27, "integrity": "sha512-RtWv9jFN2/bLExuZgFFZ0I3pWWeezAHGgrmjqGGWclATl1aDe3yhCUaI0Ilkp6OCk9zX7+FjvDasEX8Q9Rxc5w==", "signatures": [{"sig": "MEYCIQDJBZTAVq7pUKrhRIy2CL6FYxJQZSDkUBpJLS0vmEQF9gIhAJkuahfP34ztR9074RfppzWwJFI+Y3jfyqQFG3eQQqUf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1882366}}, "2.8.3-b.1": {"name": "@asamuzakjp/css-color", "version": "2.8.3-b.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"c8": "^10.1.3", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "529a9361048537159a126d85be9a57a0a2bf255c", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.3-b.1.tgz", "fileCount": 25, "integrity": "sha512-H2D+f+wwFx+4KpPCwKYPye3OWGCV6Pc0dzDkPvbq3l54IK9z5HFCkqBVeCO0GtaWaSMxnic6HCWHQYP2HdnrZg==", "signatures": [{"sig": "MEUCIQDvWgEi3P112iLdeamJecE1SPzWyWk4hsc/OBnsHaLEeQIgcaPz+po/jOIRhzbUBl2shU9t+PF3WakHSMG2w1TiQJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1044371}}, "2.8.3-b.2": {"name": "@asamuzakjp/css-color", "version": "2.8.3-b.2", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"c8": "^10.1.3", "tsup": "^8.3.5", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "typescript": "^5.7.2", "neostandard": "^0.12.0", "npm-run-all2": "^7.0.2", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "e98f6d8577f0d6c5c3b6d9afca1a012ba7db3683", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.3-b.2.tgz", "fileCount": 28, "integrity": "sha512-caREANPZtL+FpyN5cJMonkm4jJ0EMarV9PdGG8HkkqmPJIbnXZXn1h78WdorZKTgOt379aGkt1CRMd2YNV9ycg==", "signatures": [{"sig": "MEQCID8o8HR22QuHEo78tLZupk/pOseZ+o3D5d3/V85zPxiyAiB75j0SSHdY6eWuL3LXfHsKWXym824E7aTWu1ykM785nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1375967}}, "3.0.0": {"name": "@asamuzakjp/css-color", "version": "3.0.0", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.42.0", "vite": "^6.0.7", "eslint": "^9.18.0", "rimraf": "^6.0.1", "vitest": "^2.1.8", "esbuild": "^0.24.2", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.15.1", "@vitest/coverage-istanbul": "^2.1.8"}, "dist": {"shasum": "9db2a28c819b9903bbda017915b3081f76fa1df5", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.0.tgz", "fileCount": 74, "integrity": "sha512-APrXjl8zFbxMw8N8sUciUcK24sMTu1FJhBMM418sXbuUQ4dzVWHBAU7JpX5u9venGSpoaPA39jEB7zOskTsXtg==", "signatures": [{"sig": "MEUCIQDpt5sJs7c81AkVT3zougxXfCicmOTgl4N2CPySo6v3WwIgIltFPsu5y911xejU87Gb+bu5lXiiwDyLE5O7D/VacKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1403394}}, "2.8.3": {"name": "@asamuzakjp/css-color", "version": "2.8.3", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.42.0", "vite": "^6.0.7", "eslint": "^9.18.0", "rimraf": "^6.0.1", "vitest": "^2.1.8", "esbuild": "^0.24.2", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.15.1", "@vitest/coverage-istanbul": "^2.1.8"}, "dist": {"shasum": "665f0f5e8edb95d8f543847529e30fe5cc437ef7", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-2.8.3.tgz", "fileCount": 74, "integrity": "sha512-GIc76d9UI1hCvOATjZPyHFmE5qhRccp3/zGfMPapK3jBi+yocEzp6BBB0UnfRYP9NP4FANqUZYb0hnfs3TM3hw==", "signatures": [{"sig": "MEUCIQCYH6PeqLrQUcldhBWzBdLYrLavVTaDECpMNwGMO5b8QwIgAmPrAcmJLOkuOwkT7bIV7LowMZ49vjiZ7ynSmU9qXAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1403394}}, "3.0.1": {"name": "@asamuzakjp/css-color", "version": "3.0.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.43.3", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "dist": {"shasum": "7a2616c08be9658963d85d3e00f2b951bd871f61", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.1.tgz", "fileCount": 78, "integrity": "sha512-qaU3hPi2DOcuPq8U1jRJ8f8LLIj14U1JR34V32qODhg72CekXHjdAJV+CGQMd+Br5OusfyYaxGLbUbIWDWoT7w==", "signatures": [{"sig": "MEUCIEUlsCsMeKyHAMSE/dJYspT2LYm/sVwJiWsO4sDxp4Z4AiEArL/XABWwQ2fjV+IDeZ67dPiKE48aCma8O5vz/5IK8RE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2000368}}, "3.0.2": {"name": "@asamuzakjp/css-color", "version": "3.0.2", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.43.3", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "dist": {"shasum": "96a2c03467342fefe78654439b2151db64615ccb", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.2.tgz", "fileCount": 78, "integrity": "sha512-a8puD5V+O2iGfm/Y9Ow+6kGLfdGB5Y32Mg7LRLdhLTk7fhTdBlRJzN4kSMZtytjywVgZxH9IInJjQ0aVT7L3xg==", "signatures": [{"sig": "MEUCIAHHRLv6hA1mcikqYrPxGvxGSUHwukeo1yOo4tO+GlpaAiEA2g7L0p02uD2SLur2zVgaGH08fvBRMvN19jdd9UKTRfA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2000375}}, "3.0.4": {"name": "@asamuzakjp/css-color", "version": "3.0.4", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.43.3", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "dist": {"shasum": "0ac250957aa7a1c13651d735f34bfacc8c6b032e", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.4.tgz", "fileCount": 78, "integrity": "sha512-JvJJWj+qfjCf4zoYLoNANTm7ZnYlUS9pltsS377qjW3o0ry/FrbgOvSW+VGNfyOvD95uldikfsi3oem2vc6tsQ==", "signatures": [{"sig": "MEUCIEFZ8rWv5cVjq3F23dT684Hmbl7Cf2YLWQrlntjUPON0AiEAupXV6pSwvlIJ2SOmej0ShUbmCtDyXEMsp/+3rjwAYY0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2001698}}, "3.0.5": {"name": "@asamuzakjp/css-color", "version": "3.0.5", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.43.3", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.0", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "dist": {"shasum": "ad21e767f20d799547238275ce30ed4e47c18716", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.5.tgz", "fileCount": 85, "integrity": "sha512-rN/3RCmcRYYHWW+hM0ZQDFSTLyn4F3kbrh+jqfeMZl540fO7jbjHv0GFv53atYOY6yjpOIcMkAzuuyntfuTH9A==", "signatures": [{"sig": "MEUCIQDN0ffMr9YvFNJ5I4xYvINRwA/aiAFKo2KaECAjHwmvKQIgSfIw2NMJ50iou0BjLirv2Dkds/UBWaS+SIKadnT9Zvg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2042522}}, "3.0.6": {"name": "@asamuzakjp/css-color", "version": "3.0.6", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.43.6", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "dist": {"shasum": "182d9fa00160f79b23e4907e9907bb7305ecefe8", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.6.tgz", "fileCount": 85, "integrity": "sha512-AP2gkyJFU5qhmBgRZCoKfl8jqQLawJTGC6GTvgy5fn7LMIxDUKo9pg6x9+8pmv+jmXo+v70vQSq3FI3jfUP7Kg==", "signatures": [{"sig": "MEYCIQCr+0T7HxffqK0DZdPYO85r3hWJVDW4UpBQbbhq846/OAIhAPdQn7CEhwDTxpve493uyGYmtMa2kg77jpx16Nf3w1aI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2041710}}, "3.0.7": {"name": "@asamuzakjp/css-color", "version": "3.0.7", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.43.6", "vite": "^6.0.11", "eslint": "^9.19.0", "rimraf": "^6.0.1", "vitest": "^3.0.4", "esbuild": "^0.24.2", "globals": "^15.14.0", "publint": "^0.3.2", "prettier": "^3.4.2", "typescript": "^5.7.3", "neostandard": "^0.12.0", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.4"}, "dist": {"shasum": "c1bbee8030ba8cabab0a82484e86e537072776d8", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.7.tgz", "fileCount": 85, "integrity": "sha512-hsROoNn4Y6DU9b/Og9bTCu7nWDjHNTHGqjKZdS/lZZ0JM9NVrKyMjp5LqMHL5dUOvwF1EbVmuG6q2FIWBysfEA==", "signatures": [{"sig": "MEUCIQC92ILttAfjausvxvfjvxZQccNEKo28jmMc36+Mu8FNHQIgRdBaiqYM60ebWMHLUX6ynsEfFOFx9ccgNeRJfEPqryQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2045587}}, "3.0.8": {"name": "@asamuzakjp/css-color", "version": "3.0.8", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.43.6", "vite": "^6.1.0", "eslint": "^9.20.0", "rimraf": "^6.0.1", "vitest": "^3.0.5", "esbuild": "^0.25.0", "globals": "^15.14.0", "publint": "^0.3.4", "prettier": "^3.5.0", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.5"}, "dist": {"shasum": "bec188c3b258483a8957df889f7179d736ec1599", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.8.tgz", "fileCount": 92, "integrity": "sha512-cVcivPZ0aKWTE5PuaY+/x7yPOJlA5nlhz44VQKlWMnKyLe7qCpuol5ilYvAQSw/KxmDq12rZ9kPihHdkrpvldQ==", "signatures": [{"sig": "MEQCIBvNGSkJhM8w+PSBDz7/GBcGPE2dNOv5oFsa+qbBCf99AiAtaKrqxw2FuvJsTKe4TrU0DsDf7Ck+jgdzlc+FSwsceA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2128338}}, "3.0.9": {"name": "@asamuzakjp/css-color", "version": "3.0.9", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.44.1", "vite": "^6.1.0", "eslint": "^9.20.1", "rimraf": "^6.0.1", "vitest": "^3.0.5", "esbuild": "^0.25.0", "globals": "^15.15.0", "publint": "^0.3.5", "prettier": "^3.5.1", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.5"}, "dist": {"shasum": "420ac9d7b7bd43008e9b36c03525642a071685f7", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.9.tgz", "fileCount": 92, "integrity": "sha512-u/vJXVMQajT/+MBmokhdSAwI2Loa1bdKp/bTCXziGD+oE1m9aRnltvieAvpYR7WHiLs4IIWikcq2l3k6C6GdYw==", "signatures": [{"sig": "MEUCIEGopDTw6GP8A41G8pn2b027JmCyEvC5EqWwEx2s1qpUAiEA7YFDrI06uDsomvzlAIvyOf4T1pCyXUJmsiatMOg79Y8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2136164}}, "3.0.10": {"name": "@asamuzakjp/css-color", "version": "3.0.10", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.44.1", "vite": "^6.1.0", "eslint": "^9.20.1", "rimraf": "^6.0.1", "vitest": "^3.0.5", "esbuild": "^0.25.0", "globals": "^15.15.0", "publint": "^0.3.5", "prettier": "^3.5.1", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.5"}, "dist": {"shasum": "170aee94d4557842bc4804d1125f18ff5c43be53", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.10.tgz", "fileCount": 92, "integrity": "sha512-LpMJfPAVp6eCMabw4UxojJ7dnjcZR2RVFBM7YWywk3KAeSF0IZ7FXKOFcmurWgpRCLz5PnE8SwedagzLR5BCPQ==", "signatures": [{"sig": "MEQCIGuz95dN/KOEh/oPggy9PTEIGduIY9e/k/hXaEecMeJZAiB1XdnGW4HewZZ/IqneAJ4IDHT3vyy4C0/5eEhFNTvKFQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2136477}}, "3.0.12": {"name": "@asamuzakjp/css-color", "version": "3.0.12", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.1", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.7", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.44.1", "vite": "^6.1.0", "eslint": "^9.20.1", "rimraf": "^6.0.1", "vitest": "^3.0.5", "esbuild": "^0.25.0", "globals": "^15.15.0", "publint": "^0.3.5", "prettier": "^3.5.1", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.2", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.5"}, "dist": {"shasum": "d15dc8f4ad6663ea211f9361280f5413201efbf5", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.12.tgz", "fileCount": 92, "integrity": "sha512-pjiqCzOm3L2YbDGrCwZm3fiank8DFsu0Qidi70sx6XqAob/UvE14bd80BDsy3z9/e+BupDzu052K0Qb6/LcYKQ==", "signatures": [{"sig": "MEQCIENQee0qRaJ09EyeAI/iwzTwkOsPTSOFc/NLx0suLaQuAiA4wEt3bMFA5kzc3j0A3gA5foFDNcPsRTceHDADduESIg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2146473}}, "3.0.13": {"name": "@asamuzakjp/css-color", "version": "3.0.13", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.44.5", "tsup": "^8.3.6", "vite": "^6.1.1", "eslint": "^9.21.0", "rimraf": "^6.0.1", "vitest": "^3.0.6", "esbuild": "^0.25.0", "globals": "^16.0.0", "publint": "^0.3.6", "prettier": "^3.5.2", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.3", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.6"}, "dist": {"shasum": "aab016a736a370ca8fe66076f6b01dab3da602fb", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.13.tgz", "fileCount": 58, "integrity": "sha512-ENP7EA1x67tMLaGu15rLZ52kKDvjT92OQyFXvStiCOZSOn8N7x4I38CJs4+vGQ7hjqdxhMVBZiQLQIktTAVgGQ==", "signatures": [{"sig": "MEUCIQCaGdRkebhHm0ayY+sLCA5XziZh8cie12GdJjtDj1Zd3QIgNkW+uaVrEqDfW9Kh6WCDdFzujS9G/uo+3FyFshF5L54=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2072221}}, "3.0.14": {"name": "@asamuzakjp/css-color", "version": "3.0.14", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.45.0", "tsup": "^8.4.0", "vite": "^6.2.0", "eslint": "^9.21.0", "rimraf": "^6.0.1", "vitest": "^3.0.7", "esbuild": "^0.25.0", "globals": "^16.0.0", "publint": "^0.3.6", "prettier": "^3.5.2", "typescript": "^5.7.3", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.3", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.7"}, "dist": {"shasum": "9a1bd47ffe9184307e9fa826e05e6540a5a2921c", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.14.tgz", "fileCount": 58, "integrity": "sha512-5oV4rn3v1VfdRB07eo1I6TIFxTPUNxoqXjNEaHmksTX+fqnoyEQve/XttPlUd9c/6Zxz4bi4tr33e7xqsVQUXg==", "signatures": [{"sig": "MEYCIQD/E6ja03okRMQrXkqfiKGIIOyZIMgqxMr21QeqLZsbOAIhANNhCnrkVlJqGpM1rclPJvNtT+dxOtjVEdy/YzOCc0JJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2072152}}, "3.0.15": {"name": "@asamuzakjp/css-color", "version": "3.0.15", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.45.0", "tsup": "^8.4.0", "vite": "^6.2.0", "eslint": "^9.21.0", "rimraf": "^6.0.1", "vitest": "^3.0.7", "esbuild": "^0.25.0", "globals": "^16.0.0", "publint": "^0.3.7", "prettier": "^3.5.2", "typescript": "^5.8.2", "neostandard": "^0.12.1", "@tanstack/config": "^0.16.3", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.7"}, "dist": {"shasum": "9e39fe39a73e511b6010968b184ad96b9e40c5e9", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.0.15.tgz", "fileCount": 58, "integrity": "sha512-6Rvemf1V1EZbeu7z4zgjdWl2oOWyX2r2njwUBAtlp09t1uwIwAfjc0Fsr18Ij7WbM09GEers/ZYqGNDUAV+udw==", "signatures": [{"sig": "MEUCIAuv3F73CuLsNIR5uMJhVGUzDMy1lV1Iv+AC5rZVlx/vAiEAzzumnKQPI3RYyHC3qpOUKPzbvQF1dxA70ygygIpLf30=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2079971}}, "3.1.1": {"name": "@asamuzakjp/css-color", "version": "3.1.1", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.45.0", "tsup": "^8.4.0", "vite": "^6.2.1", "eslint": "^9.22.0", "rimraf": "^6.0.1", "vitest": "^3.0.8", "esbuild": "^0.25.0", "globals": "^16.0.0", "publint": "^0.3.8", "prettier": "^3.5.3", "typescript": "^5.8.2", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.1.0", "eslint-plugin-import-x": "^4.6.1", "@vitest/coverage-istanbul": "^3.0.8"}, "dist": {"shasum": "41a612834dafd9353b89855b37baa8a03fb67bf2", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.1.tgz", "fileCount": 58, "integrity": "sha512-hpRD68SV2OMcZCsrbdkccTw5FXjNDLo5OuqSHyHZfwweGsDWZwDJ2+gONyNAbazZclobMirACLw0lk8WVxIqxA==", "signatures": [{"sig": "MEUCIQDyF8KR13iH9eRYeyrnhf6vPcLL/zBVGKgAh7q9D3BTJAIgZG/yuh3/C0EXctQ8WnBDp/S9wlS9GoTYhX1tOx+xKZc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2081685}}, "3.1.2": {"name": "@asamuzakjp/css-color", "version": "3.1.2", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.2", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.8", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.50.3", "tsup": "^8.4.0", "vite": "^6.2.6", "eslint": "^9.24.0", "rimraf": "^6.0.1", "vitest": "^3.1.1", "esbuild": "^0.25.2", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.1"}, "dist": {"shasum": "4efb1abb3bfbb5982df66bd6e71fea21e3a29fbe", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.2.tgz", "fileCount": 58, "integrity": "sha512-nwgc7jPn3LpZ4JWsoHtuwBsad1qSSLDDX634DdG0PBJofIuIEtSWk4KkRmuXyu178tjuHAbwiMNNzwqIyLYxZw==", "signatures": [{"sig": "MEUCIQDNy2VyDD02SW91wj/StEWFN5wwOQhwmEGGF5nUwuiH6wIgRN5hFi+eAeTY4fajSm1OtzktZvriNtosIG8RjlmlooQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2083522}}, "3.1.3": {"name": "@asamuzakjp/css-color", "version": "3.1.3", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.50.5", "tsup": "^8.4.0", "vite": "^6.3.2", "eslint": "^9.25.0", "rimraf": "^6.0.1", "vitest": "^3.1.1", "esbuild": "^0.25.2", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.1"}, "dist": {"shasum": "e408e8756a0dc561ccf20f52931034ea509ee760", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.3.tgz", "fileCount": 58, "integrity": "sha512-u25AyjuNrRFGb1O7KmWEu0ExN6iJMlUmDSlOPW/11JF8khOrIGG6oCoYpC+4mZlthNVhFUahk68lNrNI91f6Yg==", "signatures": [{"sig": "MEYCIQCjEeBdc7MBnFyHQMCdCBanCIrWBB4BM2S6R+ax3volEQIhAIos0zKrIPsvOjRv7v86Wbj8I7ztbI1YHbWZ+tSsCXeg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2088274}}, "3.1.4": {"name": "@asamuzakjp/css-color", "version": "3.1.4", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.50.5", "tsup": "^8.4.0", "vite": "^6.3.2", "eslint": "^9.25.1", "rimraf": "^6.0.1", "vitest": "^3.1.2", "esbuild": "^0.25.2", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.2"}, "dist": {"shasum": "99f4de297fc8c9707a124a43c5630a0fbd9489dc", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.4.tgz", "fileCount": 58, "integrity": "sha512-SeuBV4rnjpFNjI8HSgKUwteuFdkHwkboq31HWzznuqgySQir+jSTczoWVVL4jvOjKjuH80fMDG0Fvg1Sb+OJsA==", "signatures": [{"sig": "MEUCIQCwsOm+RfhEB0fHwXJTqEiLK5V/vhhTk0jsI8x63zm7pAIgbhwTkqTyD2cc89Z+1ip6L64qnxV7jBbf+Qt3Qcm9Gwg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2088594}}, "3.1.5": {"name": "@asamuzakjp/css-color", "version": "3.1.5", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.50.5", "tsup": "^8.4.0", "vite": "^6.3.3", "eslint": "^9.25.1", "rimraf": "^6.0.1", "vitest": "^3.1.2", "esbuild": "^0.25.3", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.2"}, "dist": {"shasum": "b6bc36ad3a10289219102028f10e6d173165350a", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.5.tgz", "fileCount": 58, "integrity": "sha512-w7AmVyTTiU41fNLsFDf+gA2Dwtbx2EJtn2pbJNAGSRAg50loXy1uLXA3hEpD8+eydcomTurw09tq5/AyceCaGg==", "signatures": [{"sig": "MEUCIDidXS6X9CPiuic8oNwe80E9Kf7RxdGTviXcCtAQeL2NAiEArPhK+XkgxLxVJmim9FuRsegDHhD+zFTCqjCOeqd5ecY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2090933}}, "3.1.6": {"name": "@asamuzakjp/css-color", "version": "3.1.6", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.52.0", "tsup": "^8.4.0", "vite": "^6.3.4", "eslint": "^9.26.0", "rimraf": "^6.0.1", "vitest": "^3.1.2", "esbuild": "^0.25.3", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.2"}, "dist": {"shasum": "1c214c5a919d6f420625eede7ec0adb60117284f", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.6.tgz", "fileCount": 58, "integrity": "sha512-zPxs+RSMav//EcKKnM3bSPUbOASVY8eYHL/QVYnapSylYsiMXYackRRSpopzRqkzfzReZBxl1YAw5ohebGLxHA==", "signatures": [{"sig": "MEUCIQCvjkZZC/qTFeq+k2jl2igFNGoKGT3iovDQrnL0kF84wgIgd4x5Yk0vIG7sFP4xHAjsqCHpqhj0uz0x1svNFZ5H+E4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2091839}}, "3.1.7": {"name": "@asamuzakjp/css-color", "version": "3.1.7", "dependencies": {"lru-cache": "^10.4.3", "@csstools/css-calc": "^2.1.3", "@csstools/css-tokenizer": "^3.0.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4"}, "devDependencies": {"knip": "^5.52.0", "tsup": "^8.4.0", "vite": "^6.3.4", "eslint": "^9.26.0", "rimraf": "^6.0.1", "vitest": "^3.1.2", "esbuild": "^0.25.3", "globals": "^16.0.0", "publint": "^0.3.12", "prettier": "^3.5.3", "typescript": "^5.8.3", "neostandard": "^0.12.1", "eslint-plugin-regexp": "^2.7.0", "@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.2"}, "dist": {"shasum": "01fb8475bc8dc999ddc4b270ab2e31f82780d17f", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.1.7.tgz", "fileCount": 58, "integrity": "sha512-Ok5fYhtwdyJQmU1PpEv6Si7Y+A4cYb8yNM9oiIJC9TzXPMuN9fvdonKJqcnz9TbFqV6bQ8z0giRq0iaOpGZV2g==", "signatures": [{"sig": "MEUCIEi0HEUk8ovHfkDsRo3Vi++0cejAhrNF20V1lga5DAz6AiEAkFYvP6UtlvU5tlpmfxw0Vw5ftcKRl7/LY1O+hYHM8+8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2093573}}, "3.2.0": {"name": "@asamuzakjp/css-color", "version": "3.2.0", "dependencies": {"@csstools/css-calc": "^2.1.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4", "@csstools/css-tokenizer": "^3.0.3", "lru-cache": "^10.4.3"}, "devDependencies": {"@tanstack/vite-config": "^0.2.0", "@vitest/coverage-istanbul": "^3.1.4", "esbuild": "^0.25.4", "eslint": "^9.27.0", "eslint-plugin-regexp": "^2.7.0", "globals": "^16.1.0", "knip": "^5.56.0", "neostandard": "^0.12.1", "prettier": "^3.5.3", "publint": "^0.3.12", "rimraf": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.1.4"}, "dist": {"integrity": "sha512-K1A6z8tS3XsmCMM86xoWdn7Fkdn9m6RSVtocUrJYIwZnFVkng/PvkEoWtOWmP+Scc6saYWHWZYbndEEXxl24jw==", "shasum": "cc42f5b85c593f79f1fa4f25d2b9b321e61d1794", "tarball": "https://registry.npmjs.org/@asamuzakjp/css-color/-/css-color-3.2.0.tgz", "fileCount": 58, "unpackedSize": 2097817, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCyxisP1OifoyRTASkl0AYLRkHFMbtQEWEEiDpJO4Y8sgIgY42gn85Zr9hrKyEM1aufq/SfGG5JQhS+Z7qiMG5vYnc="}]}}}, "modified": "2025-05-20T05:33:59.129Z", "cachedAt": 1750172040913}