{"name": "@asamuzakjp/dom-selector", "dist-tags": {"beta": "0.2.2", "next": "4.6.0-b.1", "latest": "6.5.0", "dev": "6.5.0-a.16"}, "versions": {"0.2.1": {"name": "@asamuzakjp/dom-selector", "version": "0.2.1", "dependencies": {"css-tree": "^2.3.1", "@types/node": "^20.1.0", "@types/css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "eslint-plugin-jsdoc": "^43.2.0", "eslint-plugin-regexp": "^1.14.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "fc462101b26634cffbe32c9f0b356cfad7e19327", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.2.1.tgz", "fileCount": 11, "integrity": "sha512-vbhWjARq98P51POAlz+GHYFVF1lLcjkSimhP+Tr8Etq7NjXUuCXxVe0gvMn7hFP5Jdv/AbLGQpGhMzPTi9Fv1Q==", "signatures": [{"sig": "MEYCIQCRC15EGV9xcrSLCiYbsEu/kM3H1ec7ZpOHDbS4CDdjegIhAMZlntjz9rrTeHWJj693bwvgYN0Z2VG7CTBnXkXmAge8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47581}}, "0.2.2": {"name": "@asamuzakjp/dom-selector", "version": "0.2.2", "dependencies": {"css-tree": "^2.3.1", "@types/node": "^20.1.0", "@types/css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "eslint-plugin-jsdoc": "^43.2.0", "eslint-plugin-regexp": "^1.14.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "18d1a28211fc0c58763acc9f78f3380d310e0459", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.2.2.tgz", "fileCount": 11, "integrity": "sha512-QwxxIY5fRmRgE1ajxcu6H6tbs/AAaM6T/0DkmZaH/urDxAmRKh9PQvDX4PN8lC0qm7W3lD4efmFll7zozKuJSQ==", "signatures": [{"sig": "MEUCIC0VEQ/h2784epCTHTgbfghU94k93sEp8wnIzOProThgAiEA540DH5FyCNIhsenzOnfvQ1weYmzeJ41oLoMfdLS8M2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47155}}, "0.3.0": {"name": "@asamuzakjp/dom-selector", "version": "0.3.0", "dependencies": {"css-tree": "^2.3.1", "@types/node": "^20.1.0", "@types/css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "eslint-plugin-jsdoc": "^43.2.0", "eslint-plugin-regexp": "^1.14.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "5feaa074bd8ea2b93d91db134993a84f6599b91f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.3.0.tgz", "fileCount": 11, "integrity": "sha512-/H2QwlFzG3t90K86H+LcURGOgroR5YYAfZGnrtuB6My3eQKGk5+3zhvxH0TAvODrLdJlouyQFG+IA641bXdyZQ==", "signatures": [{"sig": "MEQCIE9cS3cfboZTIiQj0JGhY1YyFpbnwfqg7v2WEVEVzAq9AiA2+aBj3xRcswVLVTTuz9QzmmcIuypIgj9UTZYM/qIr3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47907}}, "0.4.1": {"name": "@asamuzakjp/dom-selector", "version": "0.4.1", "dependencies": {"css-tree": "^2.3.1", "@types/node": "^20.1.1", "@types/css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "eslint-plugin-jsdoc": "^44.1.0", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "889b1659e721eaf8cf7725ec946cfe9d5aec29f7", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.4.1.tgz", "fileCount": 11, "integrity": "sha512-EBmiw4vbjcT1e3usIeTHr4uuIGWxF1rm+OXboivAvA6XBaKAD8LU9MxOrrA4tu/Dt64jUeQGypoXcKvXNvrlTw==", "signatures": [{"sig": "MEUCIQDxKGy+oNPKzfmB7Q/OWswEiVdfG+UsL0QVze4tEA4YTgIgEHNWR+tA+ymWVtaU5B4ck9YXsohgca0P/9OqpM4vVP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48350}}, "0.4.2": {"name": "@asamuzakjp/dom-selector", "version": "0.4.2", "dependencies": {"css-tree": "^2.3.1", "@types/css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "eslint-plugin-jsdoc": "^44.2.0", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "f3331c6dbedf598e2cd8fb45182e6095fc51fbfb", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.4.2.tgz", "fileCount": 11, "integrity": "sha512-soOUxZIgMTjgt6U7g9plfkgeI+NOs3q1occXA5kn2rjuwSSC0yR6vomVypqAJC29kV2kWTiEzIe16zSUIkfVUA==", "signatures": [{"sig": "MEUCIBUjyv1I2fX78tS8vxWkQriqhhrAoWI5XpxOnnSu992dAiEAjA11BMu+RnMJidzdgP3vZbl39uphxf7yr6uAGSmB/jQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48358}}, "0.5.0": {"name": "@asamuzakjp/dom-selector", "version": "0.5.0", "dependencies": {"css-tree": "^2.3.1", "@types/css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "eslint-plugin-jsdoc": "^44.2.3", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "94f2bede8a4fca4de50ca3f518acaf4af21c8a9d", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.5.0.tgz", "fileCount": 11, "integrity": "sha512-XtcGOxGt2jXans72gEjRDC5orj2adLJP+KpNllUAXj2HshajEYELig7SZ4wqjy2qbE/rT4YCGo90sQs5QLjo6w==", "signatures": [{"sig": "MEYCIQDde85m6RL/GX8r0dtzCakE0Pln1nuP1UOdaFMKcODsVgIhAK0nD7M9tVI+QA1DAJr/0aJsTPktvDuiIuWvINMI96pP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51864}}, "0.6.0": {"name": "@asamuzakjp/dom-selector", "version": "0.6.0", "dependencies": {"css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.3", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "b722588b26255bb9af43deeae06fb550f297396b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.6.0.tgz", "fileCount": 11, "integrity": "sha512-vafinFaG1l/V+h8AH2btqXGCTXNabkIJwnCjITBiiKQhF2/2jgGHFOksMg3UBf7lPkLcsxpU+92fY1Je0M/FIQ==", "signatures": [{"sig": "MEUCIH+AExuZLDEehj2LYxVMPEwYXfTgjGGOih0kT0+qZEDpAiEAsb8VSLXMzfqE2YRav9NNqhbR+la7Go03zNmj2jV3Tlc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51919}}, "0.6.1": {"name": "@asamuzakjp/dom-selector", "version": "0.6.1", "dependencies": {"css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.3", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "06d690c79f1547fee40d2cad7eccb5410581ce05", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.6.1.tgz", "fileCount": 11, "integrity": "sha512-1p9M2klGHYK3M8JKAKhfiq90A7v93jWSoSDDNX17eqMVPjdTIMNjYBECRKW9JcP/KcW+IHMd0Y4qlZjR+mWiMQ==", "signatures": [{"sig": "MEUCIQCXyjz8xIp8WETeX7M24+ww5GDk2rYJzjk9x5ydj3IFtwIgVU9Qj6XffoEZJkpLGGZxgfZ6l3QmxwlBB/L2syc3A20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51950}}, "0.6.2": {"name": "@asamuzakjp/dom-selector", "version": "0.6.2", "dependencies": {"css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.3", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "ee7e779c6150648101808203da0b315c6516a853", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.6.2.tgz", "fileCount": 11, "integrity": "sha512-SaHl2kf7UNXNJfqEkEVoTJgxPifVNZFeVMSshzW5y2qI7fhF5jEPR6+pF0tqJxb1nQYOUzMRpuQ3e8tSkmtX6g==", "signatures": [{"sig": "MEYCIQCKIDg5CN6QDmlfjFNvAKi5sSfClw7VXYlwVFnbrDIn4wIhAPFYlVnLUtkf0dSK3GTw0QBFvh6sHoe9jDRTVfWUqiq6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51923}}, "0.7.0": {"name": "@asamuzakjp/dom-selector", "version": "0.7.0", "dependencies": {"css-tree": "^2.3.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.3", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "6ae470aceee9eb2eb007e06aaec3700dfd0d89f5", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.7.0.tgz", "fileCount": 11, "integrity": "sha512-qLT578+7nPk5j1X7NQ2DY+lrcmsXXaXo4nkq77CfblPsFKTszQAyV4DlmAbQGGIoOzAZNTE2e1ni/2O9aVL9oQ==", "signatures": [{"sig": "MEUCIGxuvTSoieQM36y+U3bCTjigJnV7cErxpXexQTELowSvAiEAk/ai5wNP089CAGvkAuq9Ch0ifibDkiWjZFqiFQHRnb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52230}}, "0.8.0": {"name": "@asamuzakjp/dom-selector", "version": "0.8.0", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.3", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "a614d1a9fdf98a2a9e9b754a81a449e754dc7be3", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.8.0.tgz", "fileCount": 11, "integrity": "sha512-QTazaC5FoLYz/8GaOwgmxhhGusmVhbSO46J4ZVbQSJ8/la/DIzZtnocAugt+Ql7yQAg3pao0SSYWo3ZrhF1OPg==", "signatures": [{"sig": "MEQCIBcTEYbu3W7PxSfXiODQ65/Rh3vIEHTnJfSILpGR4GP9AiBr3mgNHZGeZ83aYmvUOUzK1WcRRhm9mZN+FxLFtOKgyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53841}}, "0.8.1": {"name": "@asamuzakjp/dom-selector", "version": "0.8.1", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.3", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "cdddb1e3195a796ac4691fead4f3c5380e9d3e40", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.8.1.tgz", "fileCount": 13, "integrity": "sha512-c0K7/lXTvEp1ysKsUBPoybRgg+w+zq3XhdMJFbXYx0uYbPBRnHk18504mIWYsUVDszp0K2KoxYK12LA8Hx83ww==", "signatures": [{"sig": "MEYCIQDyTL7lxTMXbV4jWDqDJ3A+B0RgIhxjUjKDY25OfNDZxQIhALcOTmydow2nCDoyPFK9PpEwRuXikndKDDo3UCAgEV59", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55420}}, "0.9.2": {"name": "@asamuzakjp/dom-selector", "version": "0.9.2", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.3", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "00defde5a75f58a05207979613092b3fc683c88f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.9.2.tgz", "fileCount": 13, "integrity": "sha512-hyKZZhGb2ZYg/W5sECTWsCqhpXkgN4pS6hwvT8A6J1OdISIKqj8qaj3h3FyOsp8ykcF58OPz98KzApIuKPrb6g==", "signatures": [{"sig": "MEUCIFk9nlt5D0Q7l5hzpYMoLStYXaejfmHjhBNrj/LYWz1HAiEA9v2KBpufYGBC57kmkugJ1d8oxTihX+v3J0r7/6UuyPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58579}}, "0.10.0": {"name": "@asamuzakjp/dom-selector", "version": "0.10.0", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "a27bee2523d2c77a2f73049a81f939c518c3f209", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.10.0.tgz", "fileCount": 13, "integrity": "sha512-dkat1VV4EK4QfL37+D2xuM1Cssm/bvFteGDshtsTjpCWQBb86/pF+nPs1hBxwJwMj2BuHwLSJT7TM4Xjz61P3g==", "signatures": [{"sig": "MEUCIE61f4VvSqMsiEJ5pu/O7geytHCKXiS2iQbSIPAeNv60AiEA/gTceft9WxK71nWPu21s646mtwNd3Cq3slZEd+UP7Iw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60610}}, "0.11.1": {"name": "@asamuzakjp/dom-selector", "version": "0.11.1", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "cc2c901ee6bd2a37f32cdef1136f2f0ac4536001", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.11.1.tgz", "fileCount": 13, "integrity": "sha512-eoW/ybXZJp9RpLzyU8YIYHATlu5rP1XQiQ9cIbsVOu2FUcTOgae4rfPvocagsBN8/NWJ9VgNZHCY9/ToirFTgg==", "signatures": [{"sig": "MEQCIBn2FSg/xZEXSacW79VOTi1ZXBgcVkuQLBtKR/iWRwFZAiBimjQORj/AVxBizqMKrMnnOnBEMrgYWoiO695Oo72Umg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62994}}, "0.12.0": {"name": "@asamuzakjp/dom-selector", "version": "0.12.0", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "e8feacec70d48159c015cf00d327e673452af399", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.12.0.tgz", "fileCount": 13, "integrity": "sha512-b1svL6lUPzafmmscvF57YU/3yL14cBQajEbH+rtg7DcIVgm915I5TbeVoQ40uGMgOjb0MkPSlYVYJEtnMzYgkw==", "signatures": [{"sig": "MEUCIBq8fUBycbbJX9kUNL26iWlxrI0+1TM9OFVKS88M+uk1AiEAg8tJzuxqckkBLQCxruZS98ZWzn6DSox6cp19Kma5fy4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67140}}, "0.12.1": {"name": "@asamuzakjp/dom-selector", "version": "0.12.1", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "14bd9ac3cb068d23ea0302dce1fefdf5eea64019", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.12.1.tgz", "fileCount": 13, "integrity": "sha512-/wOXwUFvwzA6ALl/fKrsrCBL42OJZlS7C6IVRI5kpMtDMQh92w4odtcxQMuDw3rK0Jl1kczqmuhedm1eqnycMg==", "signatures": [{"sig": "MEUCIA1NH5yTTIURDbxoFSlh0BJYuB4GLtEUA8oND4iRW7MsAiEArj6Um+3n+xwwp3nMzoY8yLvIvkgS1Mnr4b91NzKFYyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67996}}, "0.12.2": {"name": "@asamuzakjp/dom-selector", "version": "0.12.2", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "e5647ed18d5c2918e2918e72a8e0a9cf9bfc9dcd", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.12.2.tgz", "fileCount": 13, "integrity": "sha512-XKeaFAPY4ho0oEE+cyDPORi13Hzl5dEBH66w2inD+PUpzw4tAttLSIpphgl4d9lJ/vNE7KuWXahHPB0YRGBXAw==", "signatures": [{"sig": "MEQCIAirxZNgT8SkN1o4wd5r9FejHWeRC7exIV/J9ZZSsn+cAiAsLnO/0bA+lW9JTfUURdH4nTD1Fm4r1poMVQDctf6yZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68382}}, "0.12.3": {"name": "@asamuzakjp/dom-selector", "version": "0.12.3", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "78310151d2c2098d4d35a60f82aab7fa0606a5c7", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.12.3.tgz", "fileCount": 13, "integrity": "sha512-XDYWWxv3nOHiS4GRPM9ugzPOB4z4vzXhtRfudTEUf1+cpWyyMbdK95OurT/Ld1dXzMDL/NnnRYV4A0i3t5vzmA==", "signatures": [{"sig": "MEUCIBGvZf/PejFEw2QXlA9rpR7Ozbb30Cex8AILukAtu326AiEAnR0KaYzu+9CXzRjUDGnUNvTCpajgb3fi3LzYZpZ4GAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68799}}, "0.12.5": {"name": "@asamuzakjp/dom-selector", "version": "0.12.5", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "218f1c29fd7e27d600cbe8363bac8248d70533c1", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.12.5.tgz", "fileCount": 13, "integrity": "sha512-r7YFUbpANJ1/5mpemXb4bEKXoPAeCrvH6qx0ge9XAopz6wku1jAwCBHa6QhLOBiJklZUHCk02NrSbXUiFLPfdg==", "signatures": [{"sig": "MEUCIA2QeMxnRCugPW9/igjlurb02ecYCCG1kRVF9P1qkiuMAiEAulhQ9CIsLFH2FRWs0xKwXOI/QoWDJNltF4SVzDYkhbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68882}}, "0.12.6": {"name": "@asamuzakjp/dom-selector", "version": "0.12.6", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "a7e3879ea2d32293b4a3fc0b863b2e6ef361f313", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.12.6.tgz", "fileCount": 13, "integrity": "sha512-tJGPw+xVt5zcpWBU7seZWtMnFvv7D193mhDJbiGOlXm7qumu/4Td0XzuRe3X/2vKfwXm9zwA+Jv7x8LDJ8V3Dw==", "signatures": [{"sig": "MEYCIQD0rzpAr6kM/idrtTIbKLa/ZXg/0oMdZ/JmCUQMFOMVSQIhAJTC4nnsnZPmiqTmC2naB3qNtmcVlpUCHDbSTNQO7fAg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69014}}, "0.12.7": {"name": "@asamuzakjp/dom-selector", "version": "0.12.7", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "2af21f64b10b2fbbe8f3d42dc64aa697d45c5caa", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.12.7.tgz", "fileCount": 13, "integrity": "sha512-t8WXW/pJF9mayCCOUvDm9cjwFRX22Zo9fcN+moTxaQNO7FzzuXka8Asr6EG/+mvB3xEvXEfIwaE+XSJH7cja7A==", "signatures": [{"sig": "MEYCIQDP68l/7E2KIcOJdQH0T/ZBlgPACPCtByU+qS6NypL4ugIhAKjWH7/WezXHC87rtPr37oz8TLgvby869gTxvqAiw6nU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69081}}, "0.12.8": {"name": "@asamuzakjp/dom-selector", "version": "0.12.8", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.0.4", "eslint": "^8.40.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.4", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "dbdb46a25e6dac2408e023e24a6e59629d98f088", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.12.8.tgz", "fileCount": 13, "integrity": "sha512-j88+8AakO3G8GfrZ60hO7V1fYiq9vRGJJkVrArSqc2+klqdSg0ECaueCsf8brvlBqt81+O3Gb9c6rZCyHVwsEw==", "signatures": [{"sig": "MEQCIBm6CUENq4rlfkI5eARSQHnDoRL6ch9Rah5gik4QogZjAiBbgTeV8mh2sBGBnGMo9DTE6f2RKHxruNUtGS3C2LUukw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70624}}, "0.13.1": {"name": "@asamuzakjp/dom-selector", "version": "0.13.1", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.5", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "b7a8141db14843c2cf1d1d92a1e9e6ac9a12b041", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.13.1.tgz", "fileCount": 13, "integrity": "sha512-ZlQSaIlO52MG6d5iSjyv6HWTz/DrmoHqxKwKpObVKNEJ20CNfoKkbuMTLEAhcUBUKjruBcrprSNhUdj6N3rxgw==", "signatures": [{"sig": "MEYCIQDYKUIPBgwONsbsr+rpMMzo01NAp3qKDWAW1JxN0/YGeQIhAOOd6oFbYb2w1jc6pMfSsj6DQSowYBALm39n3ydx0sqV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69361}}, "0.13.2": {"name": "@asamuzakjp/dom-selector", "version": "0.13.2", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.5", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "5d2720b03f862c8013b8514e2fbeeb94cb4870b1", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.13.2.tgz", "fileCount": 13, "integrity": "sha512-ceP/wq2BcImRKmzhoVsLGy1Js30Q+7epWM/2WjEUUD9lcRLFLJ8KhGXygAjsKaYqiwswvn6J9PCHW69ifRMqig==", "signatures": [{"sig": "MEUCIHx/tDf+66WBHmyoh3iaxbPAHORe3OGNX+njx6rCu3eDAiEAgxUwB1agxxhsfLYahrC6W8EnYkMuHXFy7cpxLYOLcmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69363}}, "0.13.5": {"name": "@asamuzakjp/dom-selector", "version": "0.13.5", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "8ea1ab9ea2aca3b6007b7bae8e024e6574a5087f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.13.5.tgz", "fileCount": 13, "integrity": "sha512-OAo0hWSOfO255VqhKsutcW52gBBeG5BOFEGj71aYQ2nGD7xqcYGn1UWh8E3pT7us5Xi3KfdwqTwe50xzydyObA==", "signatures": [{"sig": "MEUCIFWFvqa45MkGSWTqipy5Dx2tDrc0XKQ6agdM4grMqWDLAiEAvfE2IbNcudRxPSOvD+90ufv9+MJPYe4+Hm4ZT1qNQsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69371}}, "0.14.0": {"name": "@asamuzakjp/dom-selector", "version": "0.14.0", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "adfa82665f2245adf7ef2eea4c383e69b497590c", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.14.0.tgz", "fileCount": 13, "integrity": "sha512-+kj08VjjMyhW/GjDanfGaR7Yv7qmpmgkh8DCo9OIBp9fa1UavY3ifb3v6iwoeFtau7ogZCiEf8VqoK6tOgjrXw==", "signatures": [{"sig": "MEUCIC7wt5xljDNvNmuhhc71hJAUFp3kr1V3+7jdH1Z/p7B5AiEAhKa9LuDrJMWNoVRGmwLSiV4imA7DgFdPUcrYJEZBXuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70272}}, "0.15.1": {"name": "@asamuzakjp/dom-selector", "version": "0.15.1", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.0.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "e4cf8bd763dd172fcd88d3c4023111eafb29aa7f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.1.tgz", "fileCount": 13, "integrity": "sha512-02P9zjlq+9EWLH7wi9qvwJPZvs16BaO8DxeMu1q0CC9+hD6UW8tyrCGIrbG09mTWaoLqBFMPV66spTxdPv5lAA==", "signatures": [{"sig": "MEUCIEhH4IAgsFvKAAjIeX737QOpOVRCMc+eUSwh8V/UQ9xhAiEAsD2CvsDM4O6PZZkyj67BtKKjhj50Z0B2tFf5uNtM3Pw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70356}}, "0.15.3": {"name": "@asamuzakjp/dom-selector", "version": "0.15.3", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "8adb4c9bffbf572b58c55f08581e992245c26c92", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.3.tgz", "fileCount": 13, "integrity": "sha512-8pSyn178icEps/rMGsoNAdQqEuLDOTP8zMlwVYJAF34w8+XckYFKbByHC0x26/TrDgopJta9yvdgsYLOxGBCZA==", "signatures": [{"sig": "MEUCIQDDUFggKZVTlbDi1ykTp8XaAf6bOdwyJECB0JXMMxZEtwIgegQS2OJAnb2ALn7E1znq10qhSYmqSagW0hxcqwQ7yzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70618}}, "0.15.4": {"name": "@asamuzakjp/dom-selector", "version": "0.15.4", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "ab9e5065873048a91679d3b7b38882d401df2495", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.4.tgz", "fileCount": 13, "integrity": "sha512-rR1aPS/aTz7lCSvg8kRix1K9VpCdAGWpsHZDwKT1KJ9MYVpaHP4csoBG1p8uZjwLoMuC/NSnXZq0DQkC0Y7nCw==", "signatures": [{"sig": "MEUCIQCvpKOY0BIK59Vv93RdRcMPu1nPN8LFP7meuK4ugpnupQIgcSnuSkKhYA7hhNcjVSoYiCI41FUkHJNQtvdyCGg8zO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72936}}, "0.15.5": {"name": "@asamuzakjp/dom-selector", "version": "0.15.5", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "1c843ec5be3f7302312be80212a9716353ec60ba", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.5.tgz", "fileCount": 13, "integrity": "sha512-M99fockQjUtljGf2jxspGqVKSC+bYejVs2aKClWm7uBbWUkTu9PZtV4Fe0aM16ligwW6tYs7/cNyPj1o21ng7A==", "signatures": [{"sig": "MEUCIAT5Mb3WB8ZKaCCb9IweXZXZYLbzOa8kSuKhBEaRMzGxAiEA2MyC8T+SNzfrqRTmDRJcUlKEEbTC86ETE/oduBIgnlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73671}}, "0.15.6": {"name": "@asamuzakjp/dom-selector", "version": "0.15.6", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "40620e41baaf97416c29dc437d22f36c3ddfd395", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.6.tgz", "fileCount": 13, "integrity": "sha512-MOjwP648gtpDDpZTb82eT8iCXKXNueap7EklP7sc9HEOmBQIvt3cSOSJxvT2litpqZKOJWV+Jquj+8zc3Z518A==", "signatures": [{"sig": "MEQCICQ0IxVi1tizzY/BnWK4//CWXu86RTkhZcKiV0ZSbPlaAiAxzTvr1TpFfAdyDHjoIPxIrXwMApO1FltZk58gwXIzKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73873}}, "0.15.7": {"name": "@asamuzakjp/dom-selector", "version": "0.15.7", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "4ad2f7c7441ec1461920f47a24218a05f95d34f3", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.7.tgz", "fileCount": 13, "integrity": "sha512-kzhxiOq+Qqw3IGTogwjSk7C6+Tun64dIT52tsaRZefqMyOX14AHRT0tEQm+wwNxuDzUoMNwaQkGdlz50s5VZZw==", "signatures": [{"sig": "MEYCIQCW/RhOWVE1EQwTC/Ppbbc2F7ZZs4HGFIXRW25Ev/hyhQIhAMBKjNu+JTJpLHnLb7gdm3j0A5N8H7m2G4QK1wvlQP37", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73649}}, "0.15.8": {"name": "@asamuzakjp/dom-selector", "version": "0.15.8", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "6323d28c670005017997788fbc790e3fae31364d", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.8.tgz", "fileCount": 13, "integrity": "sha512-F61413bUvzE0zdEb+KTqOXeGwoF+WTJWIjlXx5K7MeV20eZ1oCPLPaLIcO1hCb6HbKz22eoBdOpy+hnZPhL1HA==", "signatures": [{"sig": "MEQCIFn2imh4gluw8VqvVSiYgLlKjZvWKlN+6olHza0OxI6hAiBqUMbx4RJQg5kImQLeCHoR7xvmkxtYrVW4txiOzWHK2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73821}}, "0.15.9": {"name": "@asamuzakjp/dom-selector", "version": "0.15.9", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "a0c4fb86d0308db65fcf82464d6398b6fe92110a", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.9.tgz", "fileCount": 13, "integrity": "sha512-6S09gklHIcTrnttg0F1C3LpcakOO8QEdb/xBaUO4+EZSTllsUI8sG3I7Y4xl+pgFpLYEy31VANKO90Z79YkLHA==", "signatures": [{"sig": "MEYCIQC0n5cbZgFScankd6cH7STxlYnQNLvQL84bEVdkRVtEEAIhAPEsp1eeoVyYk/r/VsOJLiEjcZRbV30KQ40DuB5fU8Sj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74547}}, "0.15.10": {"name": "@asamuzakjp/dom-selector", "version": "0.15.10", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "c4b39f3216436de89b7b848c9cf3833daefd924c", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.10.tgz", "fileCount": 13, "integrity": "sha512-WPWQaI+2jIzAfirwB1kcqHqq8naFGIX+kKaMSwClnksFjoksNDRrwyVZItvjB5/phRGBV18SkRV2yAoWh+hM4w==", "signatures": [{"sig": "MEUCIH3L16dgTeNkoKA3GrzPsZVysRJ4x7oin77ILBeukl2LAiEAwdgvwV4/VBbHbqNj/lZFS1WAMYTRI7cHGNaSXwZ3QaQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75088}}, "0.15.12": {"name": "@asamuzakjp/dom-selector", "version": "0.15.12", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.13.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^44.2.7", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.0.0"}, "dist": {"shasum": "404ae3dd008bd502baa245fbf53005c00e39ca56", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.12.tgz", "fileCount": 13, "integrity": "sha512-AsR8eiIZkOdhmlpNTdNJxokUkhMtyNj1phKiXwnGFasIiInNIrd5pxcDrCYoevYdXZaGKhQ6fiQot0vAYrbY4g==", "signatures": [{"sig": "MEUCIQDTbMnuanDK3aKwAXjv6A3B3J014zEvty2iG74SY8IswgIgUAGWKDbYXfe6sc8L/c/WYmNpSz69LAG5mQz7HEGxaSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75423}}, "0.15.13": {"name": "@asamuzakjp/dom-selector", "version": "0.15.13", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.14.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.41.0", "typescript": "^5.0.4", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.0.0", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e4f0b30e7d84d71124e9b6a1c0fe3c049bb3d3e7", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.15.13.tgz", "fileCount": 13, "integrity": "sha512-wCMp1fvpl0hDfrme7xxW6C5oOwdE1xXh++Tvah6Afu1LcM/srvDdVi4ZiZV+2CS5h2+HDXEXQeb3kB5M/yq7ww==", "signatures": [{"sig": "MEUCIATGc//pTwn88aN8ddUlUfw3skXNAActlzDF11eZQ/mnAiEAnBqpWcD2Fvf3Qfg9QYIKMAY4XEywii0xc8Wv0eFLZtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75850}}, "0.16.0": {"name": "@asamuzakjp/dom-selector", "version": "0.16.0", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.14.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.42.0", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "99fa2a96ceb09dbb23aa8b8c5185e18bc0b75c78", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.16.0.tgz", "fileCount": 13, "integrity": "sha512-0JcXZglNLZyibSBbmLZ0q29RBPzFegjm/73P1GTCbxgQNc7uKmpRsL15jMv85KyVjODov6T0NW16hTavwZoGNA==", "signatures": [{"sig": "MEQCIFumcF7opLsAfslPyplqzaEAU0dbiHKMxTC5cgsNKQf/AiATM5x4a21IK++SC4LgaF20wiqWKRRWBdo18lDhBx1QZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84903}}, "0.16.1": {"name": "@asamuzakjp/dom-selector", "version": "0.16.1", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.14.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.42.0", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "d0d5b7e9c53a1a0655681f87ddb1c4de46af1298", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.16.1.tgz", "fileCount": 13, "integrity": "sha512-pFVYTNmahAnXPRlNf82i87TfmSZ6QMetOt0UEVRyZ/9S/BvUbHbsynMwNjhbYGFDbziRQiXDSw0I+W3kWoA4ZA==", "signatures": [{"sig": "MEUCIQCh1zycldeN59417bpRBq3JX662+7THWI1VBFg4rxrxlAIgakPGIo0cp2gfbfUa9QaRm3ZvqZqazqJiJFjPolNa9TI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85291}}, "0.16.2": {"name": "@asamuzakjp/dom-selector", "version": "0.16.2", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.14.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.42.0", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "6b71a4ec7238e0c0f3ab5b918d01049036526f45", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.16.2.tgz", "fileCount": 13, "integrity": "sha512-9Or7jExyWLjAyrwHVmRhrIYP6kUS/QCOBlS/ZyqcCRy4M3kRzpvrzktGocEdmAwCB7n7bZZwMyD9Oq7MKhEcOA==", "signatures": [{"sig": "MEQCIEb9KyE6EQDwIwkthmFt03a7ec8ggbXLvEGGPQSVIY3PAiBHZ8X2wI5SQU+qjb9rFSW5X75IT7gnqUjrZEQL4mJabQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85073}}, "0.16.3": {"name": "@asamuzakjp/dom-selector", "version": "0.16.3", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.14.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.42.0", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "7aeaaa21d1a8e3dd4e2b77c41a95af6b0a67fcb9", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.16.3.tgz", "fileCount": 13, "integrity": "sha512-cEl0TezXFdrfOVUaxjZAdobG3COa20vYIXL8oFS/jLEJbX/bDpy7oPUH7w0iu0N4xWwaIvXHy6qzDLdKZhwctg==", "signatures": [{"sig": "MEUCIAXsfGyjLwMRLkqPleqwRe3g+i5Xqb6A9hkhBMNc9qEKAiEA1YkJlvy01DlcXCOY9++36B7uCrnZBz8d9yrjG0C2VpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85079}}, "0.16.4": {"name": "@asamuzakjp/dom-selector", "version": "0.16.4", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^7.14.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.0", "eslint": "^8.42.0", "nwsapi": "^2.2.5", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "d0d33d7f75d0f1bc0ef2c17004354acb8c908bdf", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.16.4.tgz", "fileCount": 13, "integrity": "sha512-02B7c6u1pceB+PgBz/tfT2DwFiRAO0H0SO0PAPPuqDw2m5RETi8tjNzA69HjeIg24SDPQ77l2TqVhJkKPj39Qg==", "signatures": [{"sig": "MEUCIEGb0OSSrMxJFRMYvwYoJ+SHHy5zpCQRY5G8sMLtbr0eAiEAqzebxV5qHgvtdNnglg74fKiCSh06uywrVKCxqjIk/iA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85279}}, "0.17.0": {"name": "@asamuzakjp/dom-selector", "version": "0.17.0", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.1.2", "eslint": "^8.43.0", "nwsapi": "^2.2.5", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "07c8b4db7d8214526622294297104a55f04f6abd", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.17.0.tgz", "fileCount": 13, "integrity": "sha512-8V0GoahYcgSkKFEySsBlTeMAGhiA7OF6r1f6q+cz++sok6pYkN3K8hwWCcGzlEWGyr84zBMPIaywkse4LdSzRw==", "signatures": [{"sig": "MEYCIQD8fWawvMkqRDN1Q8GtStKGJ2UAz9P+BZyoiP7Fzuh2hgIhANw5561leeXeBpZUos9DRQrW6S6or4TINWjEHJVTabHc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90998}}, "0.18.0": {"name": "@asamuzakjp/dom-selector", "version": "0.18.0", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.2.0", "eslint": "^8.43.0", "nwsapi": "^2.2.5", "linkedom": "^0.14.26", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "ebf4675c4eec2692d691eb3dd970b1a3fb2485c4", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.18.0.tgz", "fileCount": 13, "integrity": "sha512-fV/+pBZ9IoppvxYFyur3YBml6Kv9fUOTGSXhWMKSe+iu98dONNFIO2t/QdgN/l72DjiF0i7y1WISXwMPyk0p8A==", "signatures": [{"sig": "MEUCIEEvwQtHtVaLW4wXoBlcfGWNGtwmrEkyiJmWyvJBmsiCAiEAw1FVTOiizD/CD4uISNBerL9cQJzz1RNrlN0fGhCuYo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89149}}, "0.18.1": {"name": "@asamuzakjp/dom-selector", "version": "0.18.1", "dependencies": {"css-tree": "^2.3.1", "domexception": "^4.0.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.2.0", "eslint": "^8.43.0", "nwsapi": "^2.2.5", "linkedom": "^0.14.26", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "07e03792cd860625adde9741026df568b4e65d80", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.18.1.tgz", "fileCount": 13, "integrity": "sha512-ZXEQ5CbDy9VtEEXFjSLZLRJAkanPYCmOBG4L7/rFr/InwqWswmgu4voLwCgOYkg/ESTC/c6AACo2T5Cym2k0SA==", "signatures": [{"sig": "MEUCIQDnwDpvqkOejvjsmUxFjKDrug1pNMGKeen0b87zqEWU1QIgG9XJfKT/20R5db2xfxIXflbtdXI1i8KDV5wEls/dAwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89928}}, "0.19.1": {"name": "@asamuzakjp/dom-selector", "version": "0.19.1", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.2.0", "eslint": "^8.43.0", "nwsapi": "^2.2.5", "linkedom": "^0.14.26", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "95aac03624c964118a1eff5d9fae223173f6a4f7", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.19.1.tgz", "fileCount": 12, "integrity": "sha512-mybqx9qItX+rexJpGr0FbCV7cnOLCEjlsTR6O6m2e709ItJ+PMwGxXG/LNkqP0fBVeXF+t4+CSdn94xw8dTGVw==", "signatures": [{"sig": "MEUCIAw2uavHme0G8PYweFHZXTrJSm8fHk8hrnM+b9AsR0IFAiEAnPNsuxkwaxHsaUqxOP/0+p8JAhEFKJer6eq0aaEOeHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88987}}, "0.19.2": {"name": "@asamuzakjp/dom-selector", "version": "0.19.2", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.2.0", "eslint": "^8.43.0", "nwsapi": "^2.2.5", "linkedom": "^0.14.26", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.3.0", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "051c9474b5bb4d71ab0a72d5f853cce7cc691141", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.19.2.tgz", "fileCount": 12, "integrity": "sha512-5ftj19Vqa1xA38Xb03z0TyRO1kr1z6URCkt7ZSSBopv4rqhL3guwVygnSG8q78mHxZ1B3d+XmPRYIMf+oLoyeA==", "signatures": [{"sig": "MEQCIELFk99/kHfe6ddZH5yf/CnudBs5pek4WbVYcpTXIM1HAiBx8UVtigDQuBODJt0BUQyOJ+VjHZWwdvUJ3hHVyrYH+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89017}}, "0.19.3": {"name": "@asamuzakjp/dom-selector", "version": "0.19.3", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.2.0", "eslint": "^8.43.0", "nwsapi": "^2.2.5", "linkedom": "^0.14.26", "benchmark": "^2.1.4", "typescript": "^5.1.3", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.3.0", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "d09beb45a5cc6e18985e220a658842a8b8728eb6", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.19.3.tgz", "fileCount": 11, "integrity": "sha512-ZmWx13heGLyyFMZyVorih9qmtizyT5BivSiEbp6s5ECQpu2Hh6CQppLzDsqKbEgYsAVFiN2O8GwR9XY5ViLouA==", "signatures": [{"sig": "MEQCIHDKHEzP2cEuAxStlpjfy6znMt902bB0vPCQe4gmVUxjAiB8hsLhnezPLyAhYVaap/QHOsLCxDK/XpX/C39nfM5TMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88005}}, "0.19.4": {"name": "@asamuzakjp/dom-selector", "version": "0.19.4", "dependencies": {"xpath": "^0.0.32", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.2.0", "eslint": "^8.44.0", "nwsapi": "^2.2.7", "linkedom": "^0.14.26", "benchmark": "^2.1.4", "css2xpath": "^0.0.3", "typescript": "^5.1.6", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.4.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^47.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "ae2ee6af45f273fd28a2aeddef5be950296bea4b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.19.4.tgz", "fileCount": 11, "integrity": "sha512-F7IJaC7FXjUm9mbq784Du8L9b60oe1L2e31pfAbfSvwHD2fd8/3a6ir8oU53AAS6qF8bZI+uYLPYOjo5K6t5YQ==", "signatures": [{"sig": "MEUCID3gCnQtvn6v92E1hcFlHIOS42GuklVDI6vVgL9vyUiZAiEAsIKNAGU97XB8kEzxAhMD1Txf2oeKRGpMjLPI0y/gCnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88296}}, "0.19.5": {"name": "@asamuzakjp/dom-selector", "version": "0.19.5", "dependencies": {"xpath": "^0.0.33", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.0", "chai": "^4.3.7", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^15.2.0", "eslint": "^8.45.0", "nwsapi": "^2.2.7", "linkedom": "^0.15.1", "benchmark": "^2.1.4", "css2xpath": "^0.0.3", "typescript": "^5.1.6", "@types/css-tree": "^2.3.1", "eslint-plugin-jsdoc": "^46.4.4", "eslint-plugin-import": "^2.27.5", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^48.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "874ab254fe749e11137ec9dd05b6ccd68953fda7", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.19.5.tgz", "fileCount": 11, "integrity": "sha512-PKQIYbFOrJU0BIps/M3P0CDE7GZoEiN5PJZvFzbmxGzqjFRVukjsCGAKGy1VXv8Alb7aVbQ/uUmWcyApDyscnw==", "signatures": [{"sig": "MEUCIQCAqLN+VLMVaxBB4aoYJInQ4wM3rxBnk55bRJf6wO7K3QIgN2hKREkMrYoQyIfM4fVco9hPJJq1+6Iiz8EkjCrtYgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87893}}, "0.19.6": {"name": "@asamuzakjp/dom-selector", "version": "0.19.6", "dependencies": {"xpath": "^0.0.33", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.8", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^16.0.0", "eslint": "^8.50.0", "nwsapi": "^2.2.7", "linkedom": "^0.15.3", "benchmark": "^2.1.4", "css2xpath": "^0.0.3", "typescript": "^5.2.2", "@types/css-tree": "^2.3.2", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^48.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "b1e31271a218a504e109cc58acbfa167bba0bf0b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.19.6.tgz", "fileCount": 11, "integrity": "sha512-ry7Bipe48mDaYC4HXcwWkDqmPMziQOPtHuSyaGJ8gpjN4tK1ydaJdd6XtwRa3sEOeoqZdTVZYgOhA9Q8xp1F6w==", "signatures": [{"sig": "MEUCIFzpFsIww4AONgDUVZaXO/gebhbAswCQMP80YLhlY6DuAiEAwIsnsCtfnuXDXmWNMk9LxriiSpbtgShyJJn1RNi3dGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88217}}, "0.20.0": {"name": "@asamuzakjp/dom-selector", "version": "0.20.0", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^16.1.0", "eslint": "^8.51.0", "benchmark": "^2.1.4", "typescript": "^5.2.2", "@types/css-tree": "^2.3.2", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^48.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "d14b0c4d277c442b2c8a2ad7344b5ae91870d029", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.20.0.tgz", "fileCount": 13, "integrity": "sha512-aTsgjXyBKZsP6UX94vja099zACikp3Lrzz7TfKLc5n3w4SpWDyZj/3l2iET3TQ35ltiN06y5S8QoooGGbSALKw==", "signatures": [{"sig": "MEYCIQCF1q3ZAtCQNj9sXQ04V2PwVOr498SCSQDG3OYWfVx4zQIhAMIhc0r2OYMVM7D2w1D0UpnHB6RnfdLaUDwyNQDJTdx0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88908}}, "0.20.1": {"name": "@asamuzakjp/dom-selector", "version": "0.20.1", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^16.1.0", "eslint": "^8.51.0", "benchmark": "^2.1.4", "typescript": "^5.2.2", "@types/css-tree": "^2.3.2", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^48.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "4af4549cdd3cefd0a0c3f8a84bb6ca98e254e75f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.20.1.tgz", "fileCount": 13, "integrity": "sha512-G7MXsM5aLQLUbFnbxDw5Y9B+0d1tv/HMxgOkE7XO07txIz4U5nksCmPF4xNGBbtz9ZEp+5RLXZpWplqX0RSQrw==", "signatures": [{"sig": "MEQCIAvMSNaAUYQh3Q+x+YRSEUJT49G7iLx/CSR0OfhjRJvrAiBw8oUuhX6nXMDjMrPQVujdNbNIyU8COyyLnUyxlZOnGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88253}}, "0.20.2": {"name": "@asamuzakjp/dom-selector", "version": "0.20.2", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^16.1.0", "eslint": "^8.51.0", "benchmark": "^2.1.4", "typescript": "^5.2.2", "@types/css-tree": "^2.3.2", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.28.1", "eslint-plugin-regexp": "^1.15.0", "eslint-plugin-unicorn": "^48.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "1df56c74d3ac9e11a8fc4f4700427b888de24b7b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.20.2.tgz", "fileCount": 13, "integrity": "sha512-hpyarwbVSKR/h1H23I12Pxu7A0//GaaDa8Jf4cILN/PeZbBn1oEDqg1Jj3ljnF0QIO5R3X+a8yN2iw7Od1oP0w==", "signatures": [{"sig": "MEYCIQDEP1C3B6kI2btECKSVJZUlnLGd8ZjhU7nOaZxIPpNu8wIhAK1VV1dOXcgadv1Mq7XoWjW6S9rylTwmYVi783JM1etl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88545}}, "0.21.0": {"name": "@asamuzakjp/dom-selector", "version": "0.21.0", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.0", "eslint": "^8.52.0", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.3", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^48.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "ff15dbd110a9e9f645b6a228a5a62dda230dfad8", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.21.0.tgz", "fileCount": 13, "integrity": "sha512-p/+iTcmGs2QGCi4lvnrnP+fJKpNyn9XNDWzaG4swGNNbwx+CBsw5329csmQv7DansZ9TIK6pHrSf1V7HIrmUKg==", "signatures": [{"sig": "MEQCIBOkyT2sW/lFzFa6bFPd6UZSa0/sZn7ijhCoIJbxH2I0AiAYsEQJYzwlX/EvhXvMBFUpVInj4FOPUdmA3Fdtacqdqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92521}}, "0.21.1": {"name": "@asamuzakjp/dom-selector", "version": "0.21.1", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.0", "eslint": "^8.52.0", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.3", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^48.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "75bf1fc5503a7c51c4e1403497498cd8ed8afdfa", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.21.1.tgz", "fileCount": 13, "integrity": "sha512-hOGUo06GuHuW0iP4XmRONnO2+PNeNFPQwoXW7c+AsN6JcC323qBzeZ6O8PtOZupWZJDjkGdgWk/fS+qES8k87g==", "signatures": [{"sig": "MEQCICm2Wtj3bRfcGLCecpa2nxEsNCcNqzXZppYv8/O7InLeAiAael8u/AjTGDZRToXOPxsOZUSRwDIm1IpjQoopTTcINQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93872}}, "0.21.2": {"name": "@asamuzakjp/dom-selector", "version": "0.21.2", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.0", "eslint": "^8.52.0", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.3", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^48.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "dcd5859d986e23b8eb38b0e3a10ec6dce3aef769", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.21.2.tgz", "fileCount": 13, "integrity": "sha512-RU87bMO9WRRlkXDFnDWOeZkrw6oB9Q+h3atypeO2gwBjbLEmIbFtVTjVRFNrO6j0V7UM7lpQojtuGZ8n7ydsxQ==", "signatures": [{"sig": "MEYCIQDsnectq6XgOt74orEFLhihXnFqU8zjoZkKnCopjV7ZjAIhAKnwn2/ZcQliw0ysKE7fxbPvMIog2sUU6L6oHNQzV3h3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93824}}, "0.22.0": {"name": "@asamuzakjp/dom-selector", "version": "0.22.0", "dependencies": {"css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.0", "eslint": "^8.52.0", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.3", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^48.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "c3945ca2b3be5e36da9946e930697acf19a47331", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.22.0.tgz", "fileCount": 13, "integrity": "sha512-EbpkFFprhU5VSEiocnCjil3Ksy31fxsFqYgeXEZi9OZTm71JA6EgHyQrrfaLYi6lsV7vcGzChIdEl0d+trfK2g==", "signatures": [{"sig": "MEUCIQDVS9tIZdv0K6t7w4lfh/IhdO076hdaKk7njyl/sbT1xAIgSA2Zw5UxMEITCI+vJzRmzsUHDAkuyONO1H4BoLZdCVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98293}}, "0.23.2": {"name": "@asamuzakjp/dom-selector", "version": "0.23.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.3", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "03ddf8148b371b8c0aca5e2163d9be9d29f9859b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.23.2.tgz", "fileCount": 13, "integrity": "sha512-0e0SR8CsGfdN+uzR/LzMfsyKnnGWiMVv3yPOFWTpGFwvFrS7RsH6DD1jFc9KASZR+RsPsx5iIleKNdLB6RpXUQ==", "signatures": [{"sig": "MEQCIEfjfLSu+PmxuIJXDRBzGvrdpq8HEGlYZpfQMKTvt8SoAiAw/b4G79oSl+hHoLJUOjwzdCEpzRJ/p7CKXB/b85VkLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106446}}, "0.24.0": {"name": "@asamuzakjp/dom-selector", "version": "0.24.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.3", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "91e4114a9e5d48cd2f14503f9f9820e130102625", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-0.24.0.tgz", "fileCount": 24, "integrity": "sha512-MhwgvOEdBK/XfHA1cufDDdg/0M25I1sO+tv6tGXy0R3b5vewQaKvePjMBlAGX0SH51rxzY8qnNTqwpIUfQPIiQ==", "signatures": [{"sig": "MEUCIG4JkhZuLLha5Sn5FtvqVmLDH7HdFW67rjs+t+77TXW7AiEArVeHDqowh/RVFM128wHaR4/ZANd70P4K5rVtWPlBMXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282065}}, "1.0.0": {"name": "@asamuzakjp/dom-selector", "version": "1.0.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.3", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "6c52d5d8bc83c22529d05a7d10a15038da991663", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.0.0.tgz", "fileCount": 24, "integrity": "sha512-EXvY59Pm0AF0TBSdoEeUW5dj5I+T8yoxuX2s38dVgCIG1wwT28R+fOZmL8Bu2z+5WGe+hzWO2IYHOZC9hH3MyA==", "signatures": [{"sig": "MEUCID8uC8mVaIx6FmT94g+XiZzqGid+rEjxwuNQnHW/or0HAiEAoQy15SzseTRBLhNlKJIZod0WFGiByO7mNntO7Khgzec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 282014}}, "1.0.1": {"name": "@asamuzakjp/dom-selector", "version": "1.0.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "18387254372094bad16320a61a30b2d80fc7af82", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.0.1.tgz", "fileCount": 24, "integrity": "sha512-hhjSH7YpOvdJvNXPmWE2hlWZw44p2h1t5bYOymuwC8k5ijVmBk+hvwYSXMoZ3hGbA2w6yUwPw3BqpinebdOGtQ==", "signatures": [{"sig": "MEQCIDTVCHCaTXMFi2VolTSGpKExqtj0MxwuQjOmFb7IURCyAiB4KSiewLg1ccXxtQ7Bm3RS6VbQtZMhIDj861GhCVH8kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 285558}}, "1.0.2": {"name": "@asamuzakjp/dom-selector", "version": "1.0.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.8.2", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "938abc9e3cd3193b8a6784751f0141102eaaf381", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.0.2.tgz", "fileCount": 24, "integrity": "sha512-NOl7ljk8Y074z161oDgvs7FlzIuKIrI02LNCmr8q8EpirJ1I20ps1SYgdeo7wKA4+4X3vptjzcqiyb7V3ZXE+A==", "signatures": [{"sig": "MEYCIQD9R5hzAhKfCJAGRmEuKmBxMx2g+fXMmxnKeiZ+egKQFgIhAIjWNSbly4v6keAVZLVvBU26bEXiNGTXUE2ydQ90JfXN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 289476}}, "1.0.3": {"name": "@asamuzakjp/dom-selector", "version": "1.0.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "674c046a236b4a2d8935a2608adcfb8a552d9819", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.0.3.tgz", "fileCount": 24, "integrity": "sha512-8ZUYWhwinoHJxJbWk/KLui/0rkm3YP0gILPaZwMA0AVcBjOPETu8jGP9hoKpXFleKIvO5It41uy5vfQ584xUfw==", "signatures": [{"sig": "MEUCICFfVHdCJPB/j7EL6HT7KXeP+RWsd0Xr3VnhgDQxaGMKAiEA3cAbS+aUdPOdejV4pa3OCzDQRipNjkjlOq091cyPwJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 286544}}, "1.0.4": {"name": "@asamuzakjp/dom-selector", "version": "1.0.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "1447c9e3fcd780798ad952ba785d2d6da39cf18c", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.0.4.tgz", "fileCount": 24, "integrity": "sha512-wqBd5dkIOvnnSclkPxlOS2BrodlpEONNHW1LnU61tNTzkK2wr+Ed7mQ+eVQWPN4pfQtbNIMijHIL3LOuj0jwMA==", "signatures": [{"sig": "MEQCIC6awtpN2uQ2sQi5Hk76FguVb39iX+rIIdX48Yd/Q5ETAiB3Vo5QpBR0yy02WcroAIASA3wQDycMSMvDRhT/DxF/ZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 286870}}, "1.0.5": {"name": "@asamuzakjp/dom-selector", "version": "1.0.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "633af952d29476e09439d628e0f85bce683c34e8", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.0.5.tgz", "fileCount": 24, "integrity": "sha512-t9lHLkMOzYJwyTPxdHH/km9XHNe6ZfcOui0xiZpYUlg/5DSU1YU5eOt78bCGNkyEEnWgvv98buPR3YRoyQCsqA==", "signatures": [{"sig": "MEYCIQDxyGgyNmMRGyeFOTGjdk+VtpSPg/zUrsjW1VqCnQ8AXAIhAKTQnHUZwgz1f1N2JQLSfV41WN3+uesOB1M99IkP4ecK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 286936}}, "1.1.0": {"name": "@asamuzakjp/dom-selector", "version": "1.1.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.53.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e3733c68a4fe5b31a276b85658d36b112d429a8f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.0.tgz", "fileCount": 24, "integrity": "sha512-lOlL6pLEZbPjJctKV84gQpM6Mtol8GzpcMIJm03wGwwDEOcSp92WJd6ESdfADy0g0t0wNfsYNdFqLQea67q1BA==", "signatures": [{"sig": "MEUCIHNLGjGcjtAPgTWKFZDDDGilSn93ciWvatVVM1SjUQE+AiEAzCkHlHjHXfVM4yVDcttktoTi/Yk4CY9GPUjwzFcoksQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 298865}}, "1.1.1": {"name": "@asamuzakjp/dom-selector", "version": "1.1.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "2190c0294e9b992c3827dfad69c856df42d7cac2", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.1.tgz", "fileCount": 24, "integrity": "sha512-91ax7KoyofWCLs79XdoKtafAhc670ArYSPkVYohlJtzbeNTvKqQfcpG8R/B8AHaCIj2YLDdDcAKuqs+Ec1dJDg==", "signatures": [{"sig": "MEUCIQDrLIMTr9BGoBvGkrOA+kDU0c24XC/w4cR/28xDf/F4qwIgNU2n3hdYVeiHK4bawpO+v8KB9jb8S04abtF00O9RLug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 300463}}, "1.1.2": {"name": "@asamuzakjp/dom-selector", "version": "1.1.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.5", "benchmark": "^2.1.4", "typescript": "^5.2.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "2b3e8707324a6f65f4b7dcc357105a0620c6f27d", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.2.tgz", "fileCount": 24, "integrity": "sha512-LTlY6aVr3BlrXmpzDhIFpFC37gGJROwa8lu3J3tcbLiRgbDRvvo3mRBz8Xv3Mkdvpe31MkjcXehPqlUylygCrw==", "signatures": [{"sig": "MEYCIQDLNydb/oHtGIZ0z9wDrgso4PuRpdch2IME0bZEPd2eGwIhAIGfSZ/MRGNePuqwJxbDlaUvyknKv+jUie2085e+thDz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 300473}}, "1.1.3": {"name": "@asamuzakjp/dom-selector", "version": "1.1.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.7", "benchmark": "^2.1.4", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "b5cc28fbdcdbbd151fd162424a8bc2cb36aa53eb", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.3.tgz", "fileCount": 24, "integrity": "sha512-4ufziEefWI6AqaASplA2GVyAbJbhjNQ4+F4trBCrjO8h61idZVzq28JQAbb1SFXinBiCMLkpdhcB0bbeuk8gOw==", "signatures": [{"sig": "MEUCIBEKOQFZYLL9inxnq7KPMPi+d80sgPjpWUa+4IcsLnYFAiEAn42ZzKLGoPw49gYK80FgcZSV58qGFaBHD3m5r1Jq00s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301543}}, "1.1.4": {"name": "@asamuzakjp/dom-selector", "version": "1.1.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.7", "benchmark": "^2.1.4", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e922f32466f37fe5ee391cb396be41e1b66f3970", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.4.tgz", "fileCount": 24, "integrity": "sha512-/DqYe2c29qNGlvtDfXrRENKiKStiA6XozH1Ly2nXOjRN1zPuHlYOslTExJBgLgqmWDrkb8GQGHYplDXkdrDU5Q==", "signatures": [{"sig": "MEYCIQDGr6cHTBn1We+UJFjkI22T6XIWi+PtqFa2PucGsFOg/wIhAIkt1yN/P8mkSXzz6SCout77R/6qWSW8JU605uFIBc2y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301887}}, "1.1.5": {"name": "@asamuzakjp/dom-selector", "version": "1.1.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^22.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.7", "benchmark": "^2.1.4", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "8b53a3d0c76feb4694ea9a98c60a06b482e1450f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.5.tgz", "fileCount": 24, "integrity": "sha512-mCyCRhID/6NMW15ihywq78mc8lX4CbB0ORWVTiYgs/08c3K1/Likq/AbD6myc1wT6nuR/NAvqKa7DHVlS8w2Xg==", "signatures": [{"sig": "MEUCICc7ZdeQnbL9t/EkI5NsxWlPIGJJ7TjhWSNAwsNvqi/BAiEA2jSLhyR/e6IYdEL4sK0+WFJB15CEi3WrY+KhrQHVX38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 304773}}, "1.1.6": {"name": "@asamuzakjp/dom-selector", "version": "1.1.6", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.8", "benchmark": "^2.1.4", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "cbe93d118eddd7c33fee7907eaad583a6198c46b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.6.tgz", "fileCount": 24, "integrity": "sha512-YHWWSmR/hfwfxVWEsRYpgdRvcsEal9xagUpzrjfPrM/H/k2Hj3sgptSy58jFA5rvbA0jJ5zIeQO1S0IbzWLjsg==", "signatures": [{"sig": "MEUCIHyzk5/l4vS+bpUAn6joEU16PaQS5MUzZ7ZpG7ran3XwAiEA40e3lkuxk4CJsolvHFcLSBKhRdK98OhbNT399IbONH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 304571}}, "1.1.8": {"name": "@asamuzakjp/dom-selector", "version": "1.1.8", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "7c6daea4a0376fd99231b5276c33c36be3e89e01", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.8.tgz", "fileCount": 25, "integrity": "sha512-XkyVc9W3PLm0h8z2BmYunxyOZfTnzKjx67k1FHiRvzUmVAVrRBVwH7wURzgoCtD9NuI3Ec37etpaXtZkGQzXiw==", "signatures": [{"sig": "MEYCIQDdE0Jau5V5AsfuXjlxyU+/6dqMnXBtUMQ20ZE/jdFtHwIhAPltFkaPJAyltxxG4765G8+IVdKi5u2CYJWVYAceKgPP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 304664}}, "1.1.9": {"name": "@asamuzakjp/dom-selector", "version": "1.1.9", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "6718ffd56aca6560d199b95f033efc0519c54422", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.9.tgz", "fileCount": 25, "integrity": "sha512-6JbFrp5/42Ku+wpa3orQx5NSFZtOyRoCJjSRtKH/WBYRi02HP0aQ+f91tkXB/sos8/K5Ug6uPntSgvRCwSwo0g==", "signatures": [{"sig": "MEQCIBd6S7ZVk/V1+h6cVsItlr1dMQdPq8tS9+lvapOW1AAkAiAzNy2bsEiHxiuQE35xpwfCN4obhKifPeZAgD9QEpKWKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 304984}}, "1.1.10": {"name": "@asamuzakjp/dom-selector", "version": "1.1.10", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "14f5aec1b7a61e467034de314d594dc6ad17315b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.10.tgz", "fileCount": 25, "integrity": "sha512-O6WrNhHzeH8R9notm+9n/QGTQqUuLJxVNk6+bOOxQhLXYw3pmVD0SJl0oaCI0k+X3Mi1K+AkTHtWXTn+f+Tn0w==", "signatures": [{"sig": "MEQCIFGz2b1GEaiioO4yPJpX6y1RQIFnazZAiHGkHhh+LRSjAiAdDMJqFbz4nicl6ioSek/e+1papS/do6CDpwxuw/5crw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 305969}}, "1.1.11": {"name": "@asamuzakjp/dom-selector", "version": "1.1.11", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "63750873d1d178c890c257ec3264582bb38ff391", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.11.tgz", "fileCount": 25, "integrity": "sha512-UURtjkYn0muBfUgsqgZdn0msxnn8VUHIKuvbQA7tBojg+FtxK8wY5zvI+j9W2G9yUn3NfpCzNLleH6u+aN9UGQ==", "signatures": [{"sig": "MEQCIE3OUMhYwj8PwGVLpU1SrsGGNverPiLGpx4Vy+JLT4I9AiAkqmDiZcz8X4hboQ200B4e+lFvasp/W91tfdRFEEfffg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 308414}}, "1.1.12": {"name": "@asamuzakjp/dom-selector", "version": "1.1.12", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.54.0", "esbuild": "^0.19.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "typescript": "^5.3.2", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "3d6f591ab357e1e88ea5f1fc01059be1dabbf16f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.12.tgz", "fileCount": 25, "integrity": "sha512-KDNLX+A7l0vXr+O7I362YqsyZOVr7vd48ssG7FzXeWNF18OsGl1lOqYNiVp0Erhz/W5L+y0mRAC0itVyiznoEg==", "signatures": [{"sig": "MEUCIQCPT5QjdCskOTOJ6c0Y5rnvfmoDiseiV+Ey3CfwrlAznAIgMdWiXLmvbuJEaV8Qd9lFadQbb8dStKOHBooKYibTGAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314009}}, "1.1.13": {"name": "@asamuzakjp/dom-selector", "version": "1.1.13", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.55.0", "esbuild": "^0.19.9", "benchmark": "^2.1.4", "commander": "^11.1.0", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^49.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "394eba37dce000b034b99000db62d0fdec0a7380", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.13.tgz", "fileCount": 25, "integrity": "sha512-Ewzseybdd6yf/bJjZd3gGdSjih4yjQQ7PEq2CClxRUE2CMF/to1tvZokdHzvvBD1a2aaJ+iJUDk87rVp8+FIdA==", "signatures": [{"sig": "MEUCIQDkmSkfkCJ2XT0UowjgtjKWXKpvi6RMImTYqYlRCK2/ZgIgQHpRtM+QHGxUjOg/RheOmowHAQnoGqDUgfnGi2Pos3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 313556}}, "1.1.14": {"name": "@asamuzakjp/dom-selector", "version": "1.1.14", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.10", "benchmark": "^2.1.4", "commander": "^11.1.0", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "npm-run-all": "^4.1.5", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "3cec2f501ed606a700aefeced3a0a32d0f68b963", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.1.14.tgz", "fileCount": 25, "integrity": "sha512-Xik5ItzjRoaY+JRNRZ9PQB7nQSV8vxsyGgI+WGGi30be3T80CY3yuFNzj6yxz2hi442IDIpW6V4y8w6Itb2plQ==", "signatures": [{"sig": "MEUCIQCLnt7a+knImm9ng/aRLsYjbOpbmHXVRRqEZrtpJDkZaAIgGoQsvaXHYI+GSoC5L2gPBci9jYJPqzHhSWlDGZMu4dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 313385}}, "1.2.0": {"name": "@asamuzakjp/dom-selector", "version": "1.2.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^4.3.10", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.10", "linkedom": "^0.16.5", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "0c4bea02ee1e769f75072b82e11121354e8161b1", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.0.tgz", "fileCount": 24, "integrity": "sha512-OZaGLmHh8n8XnjEwhL70aiLC2U3SUJaRY/hKtLtJ4JHCMRgBGs2kn+Pzgsa40nyJKzY/MOU2kDlqn1ZBiVuZLQ==", "signatures": [{"sig": "MEUCIQCzW2hBz4o3SwVEZvtiDFuxNBOQAJUGvw9m2yIzXrk7mQIgPS8OubIECuxGNgPKp+vC8hkD6xJhlDKjRL4Io2b5yqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314031}}, "1.2.1": {"name": "@asamuzakjp/dom-selector", "version": "1.2.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.10", "linkedom": "^0.16.5", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "d8ba1fb20642e563cf82923ae26f38ca714517ce", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.1.tgz", "fileCount": 24, "integrity": "sha512-qQpxOjIMjW2Xe5c7ZNl3lMPU81/+mQeW68EhsZPP/FBMJRNs0NaT/TQXVi8k1LH5fQxPRktdybTSuCMTvFHnGA==", "signatures": [{"sig": "MEQCIH78kxpIPFk4yo04RVN98thjI1b5bvaroZ9miGy3JnuWAiBfkyZxc13DCO3Wbcy8w4skrPGyDJTiCFDeS5owErwzMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 316972}}, "1.2.2": {"name": "@asamuzakjp/dom-selector", "version": "1.2.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.10", "linkedom": "^0.16.5", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^46.9.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "49b046958b0bd194dbacf026c6f6f4b98da37c33", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.2.tgz", "fileCount": 24, "integrity": "sha512-Upyc8qLfDqHAkLQMQSAyq5rN6IzT0uzk/5ccT6H/Q3D8dczOG8v4aaC4TCV6DvkdLglHs0etG9SffnQH94FnFQ==", "signatures": [{"sig": "MEUCIQCpm9hsYpLVqLWFRGc2LDPOgEtPygq/5xJp0qhYNuxI/gIgC96MjL9RAMLqBMDK0iEPtW7JNQmRpJpM9SMsiW3GXd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 317805}}, "1.2.3": {"name": "@asamuzakjp/dom-selector", "version": "1.2.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.5", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^47.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "6864d7e2cc489205001eddc33da666a395d73fd3", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.3.tgz", "fileCount": 24, "integrity": "sha512-fT0UnBIuRV2aE5Zhe62RNBxN+oLxJjT6bJM1SNta1P37hHu1Ng1LuMVHrIrdd7lXiXglln3HRDr8yBs1HD9pxw==", "signatures": [{"sig": "MEYCIQDZjwZQUnCuKI3N9a/D2DAbwE4f96PrJK8W55pipQRY2AIhAPg/zC6fmQivD+/MYY4bIHMOaZ2hRDB8TV3qpdjJoNFO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 338505}}, "1.2.4": {"name": "@asamuzakjp/dom-selector", "version": "1.2.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.5", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^47.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "a33b0495fedb31f0afdd44c30b096c13bd2eb1ec", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.4.tgz", "fileCount": 24, "integrity": "sha512-J<PERSON>ddWKYJb8NfVkFJkk9xFhpjxquDqPCyBn3N/5w4pjt+qrvUdjatUs2so3no5uqXZ1jqNmXLoB1DatiUYmB3mA==", "signatures": [{"sig": "MEUCIHbtIzpkVTVeOTqxfKNAtYlDAmqdojjwXDWZ6MgI2QJnAiEAtOZpzrghulUUnqozGxzT41Zs7yllzIVSMFhQJ1drFIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 338609}}, "1.2.5": {"name": "@asamuzakjp/dom-selector", "version": "1.2.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "96ef4ec8b57ddaa5986b2eee4c9c0963a14f33de", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.5.tgz", "fileCount": 24, "integrity": "sha512-UbAEQXTUoSGWUX234GsHKdgwEVcf+VUwiSLz8fFt1CTLxYfST4rdtH9/VhB6D/8hktOTLPHLukN/dskOfP1G7A==", "signatures": [{"sig": "MEUCIHmB+4/7HPMolb8Ax7F0iR523Ln8+WgbSTReD3PlTxtPAiEA7eJpkYgblw2Gi2YmvaBy+++PLoFiOYnHTgQ13T5gXXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 343788}}, "1.2.6": {"name": "@asamuzakjp/dom-selector", "version": "1.2.6", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^8.0.1", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "7c5e3947e7bcaeccc19985a4c47fb696c6433628", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.6.tgz", "fileCount": 24, "integrity": "sha512-Vt8IuQD497Dt77BruRE9+eonbN3jgeojfsefg2d5ZVFIoCsyjI6Cr/KO3qVxC6yyI/ISs+66kOkFQrMEB8ZVeg==", "signatures": [{"sig": "MEUCIC118vcBVxHdnxsYlskXSAgmTAQwRHhYwjIk5GV3t/D0AiEA5MPZPncbD3UBQCl55kaZhGBV8MQyDgVmfDAmo6P9gpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 342909}}, "1.2.7": {"name": "@asamuzakjp/dom-selector", "version": "1.2.7", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e6057aa99b2a39a5386008fb15894e6a81f49e9f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.7.tgz", "fileCount": 24, "integrity": "sha512-UDrd2Aa68eTfKPRLx1xa7P9y6ruxBTRju/Jg89FmZEKpa3/iExxRNh1xglvsQuZB8V4q9sTSin33dF6bACRE4Q==", "signatures": [{"sig": "MEQCIGo7CFbFCZ6Qv4eWfabeUhH2pfwthcIobiaXXE0t1zqqAiB+Rckka2T/ktywUwKC0iY58Z+2GymT6SYk0fVB79D9ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 343294}}, "1.2.8": {"name": "@asamuzakjp/dom-selector", "version": "1.2.8", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "9d7bbdb149dfb2171884ba56358bbc478dd4ceac", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-1.2.8.tgz", "fileCount": 24, "integrity": "sha512-bO5myYPDViMM8fwAka3DpWLzs91udXwGnlwWuyOlZrmfo4/MfGmuYwTb95KytkjhAUIwPV7q1m6CtiSKfl10Lg==", "signatures": [{"sig": "MEYCIQD0a+cazw+Xa/KawshPZbTehpf4djtEhZw3J7QYbSXd5AIhAKMf5s0pycwI5kwP3C9HuU9YzCOnXbB6zsi0hjNQd4lZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 343272}}, "2.0.0": {"name": "@asamuzakjp/dom-selector", "version": "2.0.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "^23.0.1", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "f61f449c9034d1e594b7b566e1135e18b676fcba", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.0.tgz", "fileCount": 24, "integrity": "sha512-DqxIXFst6m8Ueu7Vz8uxpGaFTgr3tqytPN7SJz/BIkvJ93hzs37HJ2ldcoAGTLLcf0Z62YgLfN3devslLwnv9A==", "signatures": [{"sig": "MEQCIBkCTxnO7rb001tHaerFZBWlnHgJF4Bxgnn1jNyGiTKtAiBbyE2F+PkCkDGge90DM+OwwUn2D3vc9ZbntTkIcVKJVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 345659}}, "2.0.1": {"name": "@asamuzakjp/dom-selector", "version": "2.0.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "^23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "26dd05c504faa95a200a780f192fc9ca9feaa67e", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.1.tgz", "fileCount": 24, "integrity": "sha512-QJAJffmCiymkv6YyQ7voyQb5caCth6jzZsQncYCpHXrJ7RqdYG5y43+is8mnFcYubdOkr7cn1+na9BdFMxqw7w==", "signatures": [{"sig": "MEUCIQD97b/h2BSc92OAvqq0r6sWPtzhlft8QVRzjt2wW3GX1QIgWC3bGKGcKUsxyDsKVKfDACK7sIUP0Z9MDmEPehwqpEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 346684}}, "2.0.2-a.1": {"name": "@asamuzakjp/dom-selector", "version": "2.0.2-a.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "c2f35164bfc6cb996305cfdf984eed5832c060f5", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.2-a.1.tgz", "fileCount": 24, "integrity": "sha512-L8ck+t0OWuzXAhOWOe5QSxi4ImFyWPU1lIdjUU0uJHaS73ta9+beGJEm1hivzLgA9I/8Qloe7yV5WdTs9lQ/8A==", "signatures": [{"sig": "MEUCICS7TvyvgPiMWeqng1NgzigLji03PhspSsD2Mjojw56jAiEA4e07blPNTBca9wy8EUnZBWqyVq8IK0kB/UL8XsqJlzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 345460}}, "2.0.2-a.2": {"name": "@asamuzakjp/dom-selector", "version": "2.0.2-a.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "9b33a95aa44e7da390e91518def0e8ff21395ce6", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.2-a.2.tgz", "fileCount": 24, "integrity": "sha512-CeWfsHn9ebIC81DcbjVlOIuDhR8m/fc1AwC9MnKWKZ4KuUJGjHD5hAOTdrZb3B4KTYoK1jY+AF4gmryWzb4ZdA==", "signatures": [{"sig": "MEYCIQDQav4CRlUDzBpnquicZDLV/AhfaByopB7cX5CzmplYSQIhAIQUyCiZjRikWYS8SvD9YV4tjRTUfwf5G8Zo1ecqTXRU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 344268}}, "2.0.2-a.3": {"name": "@asamuzakjp/dom-selector", "version": "2.0.2-a.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "4fd7f7e96520f8230a6abe4cdff69c4777706038", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.2-a.3.tgz", "fileCount": 24, "integrity": "sha512-9BxGiot6P6kBB5SWg8BfxZfnGs2EMUNBMarxUXhTp+V0epWRyuG2VvHH99Rbp54czb79/pWg6UHh9kAW/sDbFA==", "signatures": [{"sig": "MEUCIQD7+JxXkmHuquYNnpIRAZWJJrv89uHfZehYRAr+ErNOKgIgG64vujR8NDEEHs/3VH1wBlmXgyT8VfDXBqCq7QP4W/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350236}}, "2.0.2": {"name": "@asamuzakjp/dom-selector", "version": "2.0.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "^23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "160f601d9a465bbdf641410afdc527f37325506e", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.2.tgz", "fileCount": 24, "integrity": "sha512-x1KXOatwofR6ZAYzXRBL5wrdV0vwNxlTCK9NCuLqAzQYARqGcvFwiJA6A1ERuh+dgeA4Dxm3JBYictIes+SqUQ==", "signatures": [{"sig": "MEYCIQC1TutJSE6LUHTeTWmWL6lTF2uQz8nRtxPSbbpuj9g8fQIhALiNSkNHDKv5MhCL3LPcK9hAaCW0iRfUYZEMm45P3NvL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 339305}}, "2.0.3-a.1": {"name": "@asamuzakjp/dom-selector", "version": "2.0.3-a.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "5faae7b1813b220dd127b3d21554d5d313fb8e86", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.3-a.1.tgz", "fileCount": 24, "integrity": "sha512-A4X+AX+pNmTCO6dRztfpk7dygg0+qf6PCvoYLKqskR7eY1bsaVxc4FD8YBCWOduWu2/nKKT7RElQ9BLCAZEt/Q==", "signatures": [{"sig": "MEUCIHm+a4xex70qr4LqBo3Pk6YVD466b841fsgXfMC/ZFnvAiEA072CUnGByYItfEsh6XssD3B5KcZ34Z3RhLqXzl8HTd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350236}}, "2.0.3-a.2": {"name": "@asamuzakjp/dom-selector", "version": "2.0.3-a.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "9f28f07de1f8fb5b395aab80912a7e566d1842a3", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.3-a.2.tgz", "fileCount": 28, "integrity": "sha512-zIs30mbpQ33CCnqUKVBbT6fd8f2F3AyS5Y4UtYQYpJgYBIGy9PotroDqNJTG7AhGbtFRJgLUnFx3W9knxfGIMQ==", "signatures": [{"sig": "MEYCIQDVSfcT7bxGLzybQP6iWKujKDb9iGIfrX6kMvwC5HCC+AIhAPWbbXiWvs/GOpc/6+HItH416jqpya/eRVaRB7T/k9nj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 356632}}, "2.0.3-a.3": {"name": "@asamuzakjp/dom-selector", "version": "2.0.3-a.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "d3a4ea9584e3664bc20bea1bbfeef64ead2fb0d3", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.3-a.3.tgz", "fileCount": 28, "integrity": "sha512-ud4fJPrBjdQ8tF2bjgCUb4Fo4zfxnk5yVzh/6kGQHEyzvtumkm72XPPk7T9sKnliIjvcQvUsE0e+oK4H/tU8kg==", "signatures": [{"sig": "MEUCIG9j6AKoyUr4g8USeTMO6icukMkmUwyzKZpBNDd8bdkTAiEAivPyIQqtx/QVLt+rUCl0dJJHgI72BMXV1WsJI3IpvlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 357135}}, "2.0.3-a.4": {"name": "@asamuzakjp/dom-selector", "version": "2.0.3-a.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e593351356f5c69adeb632ea1e55cc30694bc0a1", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.3-a.4.tgz", "fileCount": 28, "integrity": "sha512-WJAHUrJH9Nr0lklAuwkQY5DiEmzosiA3KBuAdyngRScMEAPlw6ck+6mHd2v++opyjmX0D62kYEXlsiFJkJWLFQ==", "signatures": [{"sig": "MEQCIE1i83dO24JlbAK12xBMv+eiFcrld25zdpiLo7C/d5r0AiAEfZrdM98w446V4hBKCOL9RE4/Rf5FiO37t8BO3RfxRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 363330}}, "2.0.3-a.5": {"name": "@asamuzakjp/dom-selector", "version": "2.0.3-a.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "70583ce62091d4974e42dee37637ff29188979f0", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.3-a.5.tgz", "fileCount": 28, "integrity": "sha512-JuRwLDtmudH69OP6Fjq+1XvsNmhrTQiuzMl7256ZrMehehSoDmAVeI/YCf7StfCZs957enRCt46lq1zJUTVihw==", "signatures": [{"sig": "MEUCIQDxZVdJJubSJjpKMPpEOZ+hNu4VaWqbu/+Hm4NFWmz3iQIgIE9kqlzSqHvvsU4LilAKShn8m0R/CFulMVnn/iPgK8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 361636}}, "2.0.3-a.6": {"name": "@asamuzakjp/dom-selector", "version": "2.0.3-a.6", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "b1ae9cb551ccb67654dc79431d75417c0eb77f51", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.3-a.6.tgz", "fileCount": 28, "integrity": "sha512-aIwffG/jqPj+7KtN4k324BvvTu1waPisleI6xvYehSLpmUwxsVcv9lwMZ4gtDvkJZ+KjBxKgLG6nyb+wuVZAFA==", "signatures": [{"sig": "MEUCIEvNlgY9B1FDDI0B+TK6eF9vKunjzPEQnAqhZmPoT0RZAiEAqALAd1D0StIGO/HPuZhSZ+jK7dMlB8RK0Faiep+qmmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362283}}, "2.0.3-a.7": {"name": "@asamuzakjp/dom-selector", "version": "2.0.3-a.7", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "b3f686ec926121e841255cf033ad6cba342d2ded", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.3-a.7.tgz", "fileCount": 28, "integrity": "sha512-1KCBdCBA5xJoLvgJpUaz1eWf6VgyJbwSfEB25XnMw50Sj2yxJjuL/K1vYj8hls2ly3ypkM1STggztvaiPqFuuA==", "signatures": [{"sig": "MEQCIHohLZO/+7PNGCfeFbEWb1IU1MGqB0d5Hi3x+GNJ/7+CAiBevP943Gx998Wm+uFsEqS59Q+2uaxX1Vuf39ETz/6s+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362923}}, "2.0.3-a.8": {"name": "@asamuzakjp/dom-selector", "version": "2.0.3-a.8", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.0.0", "chai": "^5.0.0", "jsdom": "23.1.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.6", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^12.10.3", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.4", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.1.2", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "c9c38f8f1e2627e6d0f91b7f2f8d439a4c9f56a8", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.0.3-a.8.tgz", "fileCount": 28, "integrity": "sha512-DGFHkGpC7SRRmbSylgWwdAjvMXHZxbr6ys6QkY8N69nk02lZ/qOihKPWVwnzxVu4HwAwAxF5oZezQ5psDV9Deg==", "signatures": [{"sig": "MEYCIQD21HCOU/3/Cz4h6He2hf7hb1cob0z7ZaECixJwvVomaAIhAP2gtP6UzhoEiGPzlf6PM/4cxkpILyYZqmerE83GQgai", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362890}}, "2.1.0-b.1": {"name": "@asamuzakjp/dom-selector", "version": "2.1.0-b.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.0", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^13.2.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "97b3c20a5d9ea97bd73bb648c06b79666a688107", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.1.0-b.1.tgz", "fileCount": 28, "integrity": "sha512-o32BsuH5UgIM+ShPnYh2awZRvs8qepRt6foK0Ru764eKyRwvJA/4VE1xtoPMZk2AsJywmIDEx2x26ulWw7jAfw==", "signatures": [{"sig": "MEUCIQD+r5g5sqqw+SQ5u65QshJm1b2WIfoB4dMIM+nQbUjuggIgJ2cCFyeq/z6LqacQs/7Vumc47lnQhlFRnn9bfqPnH4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 368913}}, "2.1.0-b.2": {"name": "@asamuzakjp/dom-selector", "version": "2.1.0-b.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.0", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.11", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^13.2.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "c67b3a30de96a1ff5db78be85802ce68c9a7b5fb", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.1.0-b.2.tgz", "fileCount": 28, "integrity": "sha512-wbcJ5AslO0Rpk9vu6G/Lyu/uZ7r5+UBW01rEWErEiwZMkpmeYlAO9xz9imyggVltv3ojV1iZMd8d87wteAzsFg==", "signatures": [{"sig": "MEQCIAJOZ9pOCgu4LkQpHbkN3061Gdo3P2qABb6xaYmarykVAiBNDoaKqPbOYK/CT0qo8bmIMtUwF6lEdJlajkIzqClKNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452743}}, "2.1.0-b.3": {"name": "@asamuzakjp/dom-selector", "version": "2.1.0-b.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.0", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^13.3.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "8ca246568a8732e31065fa9029541a1adbb0a083", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.1.0-b.3.tgz", "fileCount": 28, "integrity": "sha512-S2JbdOiHduDcvcvSUF+hpWA4QkkQmNI5LHLJ6nSOES7tJdG7rXqWqQ9MsXeIjgzOoHUduWnhmXBxliEvfW1r7w==", "signatures": [{"sig": "MEUCIClsXNGmIgzVfmpzgTcAkQ7CEsvJ8TaLNQh3bB4u8MKzAiEAod/2xvp9LC2H2n6iNBbgqtGfGAA/GtlwmlZ/1vJikJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452075}}, "2.1.0-b.4": {"name": "@asamuzakjp/dom-selector", "version": "2.1.0-b.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.0", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^13.3.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "fbf6eec0eb2888d092e66c53ccde5b104724f05b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-2.1.0-b.4.tgz", "fileCount": 28, "integrity": "sha512-rrncP9mWjTrm+M/vfEgWSi+EP2wv/JWy786s8qgpX2O5FXeJb8MhrjWu1SVTxUqC7hsp/4QYff6K97XumHGwEg==", "signatures": [{"sig": "MEUCIB6Aj83iy0Qkkl3/ca2yt4xPIAdaJR/JNw9L5EvHd7NSAiEA7Cf6fLyz0TC7Ww/v2u0e6Kv40SrGAg++BSumE4fvDb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452377}}, "3.0.1": {"name": "@asamuzakjp/dom-selector", "version": "3.0.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.0", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.19.12", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^13.3.1", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "4f9fbc5455bb24316fc0130c0c9823f578978ded", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-3.0.1.tgz", "fileCount": 28, "integrity": "sha512-YVTuw/2867yoQwCUeuTJxuDrYkZSaulzd87wYFfKTv89UakdBiIecTvettbvJx1zEE8RPs4toLEGWoR9Gdfozg==", "signatures": [{"sig": "MEUCIQDGuHI3aYROZpST7KIRZdvPEkMnn6lG1c6JJw1A6VoUIAIgRyo1DBo/0P5NIz2Kf75dLWK4rjWWH1E0Rs4VQFAfT68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 369519}}, "3.0.2": {"name": "@asamuzakjp/dom-selector", "version": "3.0.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^13.3.5", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "77e4c73b130fa05a0dda9b988d79d8ec597ce2ed", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-3.0.2.tgz", "fileCount": 28, "integrity": "sha512-2vOlBkL+mXS6W64U6bTfsRePeqekkvvTdmDU3SQmSd/s85w6wJkayFQ8wOPLlyri0l89Lzd5j9gPJdRN/vGSKA==", "signatures": [{"sig": "MEUCIQCqFQbECijpxgEoZTNPBVLzG74BD0RmVIyfO0cqi7dvhgIgI/0MN6CJXUEXdoI/1hzMSBu5ACtqTyAKfx3rw9sDkiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 371307}}, "3.0.3": {"name": "@asamuzakjp/dom-selector", "version": "3.0.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "6ee47db56c5035de63c41b77270b12382ba313c1", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-3.0.3.tgz", "fileCount": 28, "integrity": "sha512-UePnadMU+u+1lhpQhA6cJQY93CnrCBKdfg8Ez8LxQw1faTswfHZuWoKD4eZdODx6wjAH297QVfu79X5sQMzNaQ==", "signatures": [{"sig": "MEUCIBKR19XqVa1Y78o+t9XdMdOnDo77zBaU7Gpm02SA/YVtAiEAnc1rtcGFNpmZfcRjtFwxK5Yr8Rkn7rmdibr5UMsxg6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375293}}, "3.0.4": {"name": "@asamuzakjp/dom-selector", "version": "3.0.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^11.1.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "71c1fb57771392bc6a3fb0cecc6e2fd2a5a32b1c", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-3.0.4.tgz", "fileCount": 28, "integrity": "sha512-GwwTJQpGEMLlmqy5YbPNRWyJiqXVexhk9oanJefiPxb264BKpB5XzTkrwwmBZObp0SXJF//iP+YG55xeq3r2PQ==", "signatures": [{"sig": "MEYCIQDGla6mEJOdr3IlXYiCE2mzUE+C/EnlNzTQAIQfdkpWJQIhALDBZeUJP8Lf1R981GJexhCMP+XV1IeZ/5N2C8W/JFdd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373289}}, "3.0.5": {"name": "@asamuzakjp/dom-selector", "version": "3.0.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "jsdom": "24.0.0", "mocha": "^10.2.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^50.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "1724d7b998ba8dd998e8881a02fcbc3b8bd1a6a1", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-3.0.5.tgz", "fileCount": 28, "integrity": "sha512-2hAwkVCUfizBJkD2n0xM08LJThanWDnx+Zaml7jZqDxg8HVBWp3Kg5GTeIck5Hg03J7ascYYou9i9fr+bxt2+g==", "signatures": [{"sig": "MEUCIQCMNd0EMCFerR5BSuALH5IcJo2k7fhkiOtr7GOHO6rGSQIgWO6rFodPPpCxiCqqMeD3LtOr9jPhJgTE4+2sI4/3Dgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373539}}, "4.0.0": {"name": "@asamuzakjp/dom-selector", "version": "4.0.0", "dependencies": {"nwsapi": "^2.2.7", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "jsdom": "24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.6", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "5d7f3b59f30e9f140db63ddf4d0c52ae9c37281f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.0.0.tgz", "fileCount": 28, "integrity": "sha512-wGiOGwCWxj61CdhCmnG8fKRrKK2SUsjuD4Fbyzc8fkasyg0o7XOztbLQgRuVfGXZxDmT6HeTiXnQzkWRxubE8A==", "signatures": [{"sig": "MEQCICq1sYDVrMOgGBGsgndhBEJBISY5PKkxVD58aUBVhsAWAiB0kpbSbGO00vcxcewf8p/5OoSMSPenfqTkvyb7BbSMGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374401}}, "4.0.1": {"name": "@asamuzakjp/dom-selector", "version": "4.0.1", "dependencies": {"nwsapi": "^2.2.7", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.0.3", "jsdom": "24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.5", "eslint-plugin-jsdoc": "^48.0.6", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "6a633520e4d7146b98e51b10633ae1bb6f8631c8", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.0.1.tgz", "fileCount": 28, "integrity": "sha512-6XjlJuFc9YzVwDGdGkGOsFdlqwMiHVoA2aOgxTGxVpm1l3+FsWW+H/eEQ6E10MiJTu9ev5+CyyV02CY7RuSxQA==", "signatures": [{"sig": "MEQCIDz3UfBQTrJbVBGyxZZmbShsIG8dgxR3x/VpfFLXAcipAiAVsKcb+I0yzLiJavTCrB8TZnAGxYDXD4SUa1vwatFwBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373687}}, "4.1.0": {"name": "@asamuzakjp/dom-selector", "version": "4.1.0", "dependencies": {"nwsapi": "^2.2.7", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.6", "eslint-plugin-jsdoc": "^48.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e1fb341030bbf23f0329ebbc4db0d901bc0bf216", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.1.0.tgz", "fileCount": 28, "integrity": "sha512-HMTOhCMIasP04riWawJyz/1q9WciEz0VNSZ8VSvYOrpTtGeOwwHi9v+wtK/1fZB+eGgzemn+a3ssc+Cksovx8Q==", "signatures": [{"sig": "MEUCICz9qv7gZLpLSYyxN8djvlXBnElFJBHvWRJNYjCuezzmAiEA3USsJeGpP+LgePZEokQQgV6FpKs/BFj7wq5aMVn+QRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 376197}}, "4.1.1": {"name": "@asamuzakjp/dom-selector", "version": "4.1.1", "dependencies": {"nwsapi": "^2.2.7", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.6", "eslint-plugin-jsdoc": "^48.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "374ab855316776e10558a6f0e434bf9cf378ff6b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.1.1.tgz", "fileCount": 28, "integrity": "sha512-DnZXhJhr0rtawA6Tdt6gUKREuVikKscKGI9XjCqVYp5UCIMfgnMDPQUn22bzJSPpwbXCevs9PqdA0zPbdzOE2A==", "signatures": [{"sig": "MEYCIQCvp6cVn8w83ux54JN5l2TYXlpHG/6/3x+Vc/N2nJ/82gIhAP8EEBI6WRmuu1pIo6mW4N3g+XGrCr7CsmHmrmX5xOPT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 376480}}, "4.1.2": {"name": "@asamuzakjp/dom-selector", "version": "4.1.2", "dependencies": {"nwsapi": "^2.2.7", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.6", "eslint-plugin-jsdoc": "^48.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "ecf288a0029c13a23856804b175363c0cd7ea2da", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.1.2.tgz", "fileCount": 28, "integrity": "sha512-wA8p7pJtR3N7VDoQrtJ7jRJ9qMLTjiZcZHDfIG9vNGrxKcIwFcAUCKEROOYtGDjU7JBOs+amtyn5KQL9AAaEig==", "signatures": [{"sig": "MEUCIHqnc6HycZuypp2mNPjIbyUWPSeRrZX4UHKVh2hlKqM+AiEA3SWNxQ1JipYXk2EmjMigovh+6QfmhQ/GiXA9f6V1U6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 377964}}, "4.1.3": {"name": "@asamuzakjp/dom-selector", "version": "4.1.3", "dependencies": {"nwsapi": "^2.2.7", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.6", "eslint-plugin-jsdoc": "^48.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "221f268661241c5d3ead12d1056fdc297c36498a", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.1.3.tgz", "fileCount": 28, "integrity": "sha512-Lh0ZNDwdn4Du6+x1cGOyrQ3KKsX4XKsCJiRGuInHyA6FISYWtbYiF4N6jbNDloruuSE03QDKs8UgQM+J/FD++Q==", "signatures": [{"sig": "MEUCIQCCSX52nvymRnXeU1u3N8rwrkq8iGx2Thy2R9oRCHmvHAIgZCnOszj7CiGhLosAN2OkA1Se/TGOYWUzr+46q0RJdLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 380200}}, "4.1.4": {"name": "@asamuzakjp/dom-selector", "version": "4.1.4", "dependencies": {"nwsapi": "2.2.2", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "^24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.6", "eslint-plugin-jsdoc": "^48.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "faacd7cebc6afb628ae4ae608829065c1f1d86c5", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.1.4.tgz", "fileCount": 28, "integrity": "sha512-GhUkb1T3UYcpc984QA+3ZJKss4i6CUSe86iH8USwxNyhAKh/4fsIoSf1JDqdomIIN/i+V8bnkzLU17LlZMZ//A==", "signatures": [{"sig": "MEUCIB01FxAT9N7exjyMEFDwRMm8limT3Lon2XtjsM7Lccy8AiEAj/C6SqQwkFGnw/5eOj4PKxBN3BMWuLxr6GP4N3DnBEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 381907}}, "4.1.5": {"name": "@asamuzakjp/dom-selector", "version": "4.1.5", "dependencies": {"nwsapi": "2.2.2", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "^24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.6", "eslint-plugin-jsdoc": "^48.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "5f747d7d2a18ee10086f36bc2be6686093ade229", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.1.5.tgz", "fileCount": 28, "integrity": "sha512-C50aXrqo2QNlG5vSHtYzbpjs0WG1hbLygepJaMUrMZ/AqG9ndyNCBySKtjFoHc7BlThdc4/znpprlPAVCNP6bA==", "signatures": [{"sig": "MEQCIE0+cffG/vvpsWgOZ+fRY2NQ69RKiXysPEsLIe6C244cAiBYBjOba7sxwFz7eOFbeN3gMEKA6awZc1hHc7CVfPXSIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 382691}}, "4.1.6": {"name": "@asamuzakjp/dom-selector", "version": "4.1.6", "dependencies": {"nwsapi": "2.2.2", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "^24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.56.0", "esbuild": "^0.20.0", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.3.8", "typescript": "^5.3.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.6", "eslint-plugin-jsdoc": "^48.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "73aae769173e7257d44477aea07f1fa54b248bfc", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.1.6.tgz", "fileCount": 28, "integrity": "sha512-AYv4ipAqQo65uxXgzBNfEPb0UQjeURXrKAR/Iat/HoruRCHQhFE3bRXyx0NQutPg0uKefwNjSN6YfJkXfOI8mQ==", "signatures": [{"sig": "MEUCIQCTgj3fEXkfV0ceKeeEAPIh3Cj86U6WCoHgQFsx1ZaLYQIgeOY/NCGiXIzIPdfQEAnp2jEFAYMapl5+AUsZCkOlFEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 382785}}, "4.1.7": {"name": "@asamuzakjp/dom-selector", "version": "4.1.7", "dependencies": {"nwsapi": "2.2.2", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "^24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.57.0", "esbuild": "^0.20.1", "linkedom": "^0.16.8", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^13.7.0", "typescript": "^5.4.2", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.2.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "848fe9e3d8d501c12cfaa213a9e1974278f12df7", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.1.7.tgz", "fileCount": 28, "integrity": "sha512-80NmmqYOuSCMHj+lErzdTJkxEamK7UH+Sb2yzNMugzgyT40bFypL+P6M6xslp5m5wsE3RjqP4dyEe+vgO7RUmw==", "signatures": [{"sig": "MEQCIDQKpBR5g+LJYyG7P4nwVaF0p92dkJGS2Qo/XZNn8TuTAiBnHJstDtOPZdeiqUwEzz6CjHhLKesAd4870XAPr53Jyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 382240}}, "4.2.0": {"name": "@asamuzakjp/dom-selector", "version": "4.2.0", "dependencies": {"nwsapi": "2.2.2", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "^24.0.0", "mocha": "^10.3.0", "sinon": "^17.0.1", "eslint": "^8.57.0", "esbuild": "^0.20.2", "linkedom": "^0.16.11", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^14.3.8", "typescript": "^5.4.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.4.0", "eslint-plugin-unicorn": "^51.0.1", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "a7985e9afd371170b78727b9c612597b203f4c5b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.2.0.tgz", "fileCount": 28, "integrity": "sha512-fZ/LOSVwXZeA7bltcoB0fAYhobirugeKQ0Lox8y6f5wkDzmhcbDVER79Jkrzo6FZNDmh57eC3fp9qTvH6ZE4MA==", "signatures": [{"sig": "MEUCICh3WZAzR85h7HC6HkXVDRJSRcMKXhWkzLxS2vcXqilKAiEA8qnEE8VdLq9mHr5STqdHCery52vC7htht5ZSiFs/r2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384077}}, "4.2.1": {"name": "@asamuzakjp/dom-selector", "version": "4.2.1", "dependencies": {"nwsapi": "2.2.2", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "^24.0.0", "mocha": "^10.4.0", "sinon": "^17.0.1", "eslint": "^8.57.0", "esbuild": "^0.20.2", "linkedom": "^0.16.11", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^14.7.1", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.5.0", "eslint-plugin-unicorn": "^52.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "92cdff2644ee8a899bff157fbc9f6bc027adf050", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.2.1.tgz", "fileCount": 28, "integrity": "sha512-Bjo6993iMQqsebQOyLqH1FyTAdUNXV7PG5WWGAS60O3QEmomuj6uwTjk9gDa+aA8QDFxtXLqZABWfPhiWxxqrw==", "signatures": [{"sig": "MEUCIC1R8E26atku7D0JohqUje16lhow+jG/hD1hXZa0CRo+AiEA47aQ7DFXjVzb8dJ9LekO2w1/fLSryPcmsK0VOhhRgt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384251}}, "4.2.2": {"name": "@asamuzakjp/dom-selector", "version": "4.2.2", "dependencies": {"nwsapi": "2.2.2", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.0", "jsdom": "^24.0.0", "mocha": "^10.4.0", "sinon": "^17.0.2", "eslint": "^8.57.0", "esbuild": "^0.21.0", "linkedom": "^0.16.11", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^14.10.1", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.5.0", "eslint-plugin-unicorn": "^52.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "a0e34166ee6b5f0f57983dc8a503220e860f113e", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.2.2.tgz", "fileCount": 28, "integrity": "sha512-oqCODhRNhIBVuZG22RVRRQdtUEnN10GB4Uu4oITs0w8tXP7Dqr+4fWZPOYQ9OpaG9TmhLYd9+4H0pomgCr1wSQ==", "signatures": [{"sig": "MEUCIGnc/djVyQFXYLDtIPBvZovYwMg7GP/y0dcBu1uxVqBWAiEAgMuoA6zYJufoLZ9yCYZgC7gCV4MeNSxneH3OdYPtKkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 385837}}, "4.3.0": {"name": "@asamuzakjp/dom-selector", "version": "4.3.0", "dependencies": {"nwsapi": "2.2.2", "bidi-js": "^1.0.3", "css-tree": "^2.3.1", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.0.0", "mocha": "^10.4.0", "sinon": "^17.0.2", "eslint": "^8.57.0", "esbuild": "^0.21.1", "linkedom": "^0.16.11", "benchmark": "^2.1.4", "commander": "^12.0.0", "happy-dom": "^14.10.1", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.5.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "eb33b86cc19b70dd5fafd86b777ba340be15e76f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.3.0.tgz", "fileCount": 28, "integrity": "sha512-jCqU1yP43fQkiR2loMEbHI65EHkbJj3LTGnUMtXVWjF6T3aIzh3Mva8qJACEwBfb585ZWS15OwJjDDih3TB76g==", "signatures": [{"sig": "MEUCIQDkT9BCpoBOacqFoQXXUfzVI4gZ0E2PoLGy1/rL68665QIgTzyY7ASEYXbb/M2uxJlQ9G1WaNzglfp3lMT1odPLD/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 389552}}, "4.4.0": {"name": "@asamuzakjp/dom-selector", "version": "4.4.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.2", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.0", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.7", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e12a350d16e00222bb3c7c894f1b057e3d7dbafb", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.0.tgz", "fileCount": 28, "integrity": "sha512-uCr69fHuI6iViuuOy7yQwPnASd7FAeHeYoSzbWmWuGtrjUAjNXGVXG1DhiN+xZDFttq98rV8U3Ywr5/HN1p9FQ==", "signatures": [{"sig": "MEQCIHULpoJrgt1DpV/TvgqZr8W+HcSz4Af7Ne+JbpILXE39AiATxhlUFBOLHwO7a8Ur5QzGfNloXUjqyUIqfJOyCjPdHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 397478}}, "4.4.1": {"name": "@asamuzakjp/dom-selector", "version": "4.4.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.2", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.0", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.7", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "88e3ecd00c04895839b39c5d51726ddc308a8c67", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.1.tgz", "fileCount": 28, "integrity": "sha512-EoyT4nBBsSTr9MuB83RbNJ2CS5zg2xBQ0MLLsL/S4teFYBkBZR25BzD5rcS81ZtqrzQRz6sJ0UWsdjd34D+HeA==", "signatures": [{"sig": "MEQCIE4GCaGtidsFmgIkx0bWggB0GG26/vZ7lC55YqmSbwCTAiBM6yI9JlmSXJ1NdzAbPNOx4WpNGJw0EZwcY5/f10XB+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 398022}}, "4.4.2": {"name": "@asamuzakjp/dom-selector", "version": "4.4.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.2", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.2", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.7", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "707e2834a577fb6eac29f8f99bc39044b5212b03", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.2.tgz", "fileCount": 28, "integrity": "sha512-FUdQEhvK/Ly4TmfgUIq3S2TK37pUYcccXeWdOa5u/IkHqcu+HRkbalj2E01UI2JhVg4Q/cck929H9qQ7YoA6ig==", "signatures": [{"sig": "MEQCIDBn1qjOgrE9DN1GP01FiQf8mAuNZBqSrYwKxy+FpEz/AiBJpHAgllcCsSLHFk4lLGnQkDkTVNjb1n2sijMqW1iI6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 413908}}, "4.4.3": {"name": "@asamuzakjp/dom-selector", "version": "4.4.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.2", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.2", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.7", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "c91dcb45f0c27bf6343a17c1324e6c7e4d99c0e4", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.3.tgz", "fileCount": 28, "integrity": "sha512-yxTvLKVwWoUp/S9HQUhJIQ+CLX2jCyFIjXdpLH4du60ZtIZ/GyVMwlOFud1EfFeY9+0xcShsvQAyraVCGamOsw==", "signatures": [{"sig": "MEUCIQDycAHmlJPC+w9DqJqliGUECtUclAWcMLgrC8Vc68XQIAIgSp/oOs5tFHVIbSWg26mfWEaOF3St8v+ml9TEHAkh3nc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417993}}, "4.4.4": {"name": "@asamuzakjp/dom-selector", "version": "4.4.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.2", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.2", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.7", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "09fb36e759988c7c56a08fedeba6403384d1ce5c", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.4.tgz", "fileCount": 28, "integrity": "sha512-kgKYGE0A0xBTx3ZKHeK8DELU10PcMXkzwmK0/bTorBvsmiuDxcmgqRUMm2c1PwGFHqyCAmKsUVjxDLKYCwRu5A==", "signatures": [{"sig": "MEYCIQCAXT/pkci5At4kl2TFctzpZSqt526QTAHRKCFhhRomHQIhAPROwv8y7JUhJm5ylpuPekt1oTFFROZYHtPJtqrw/qpl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417217}}, "4.4.5": {"name": "@asamuzakjp/dom-selector", "version": "4.4.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.3", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.7", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "fe8885b7a5a3ef1bf9de5070d98b231576aab42a", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.5.tgz", "fileCount": 28, "integrity": "sha512-3ElXqVD8RoS4+a2DESW1m49Ij17lOU+XouLIjfP0p8rE6knvVIUq878+5bmksd2KlgXJbnGlpTR2MJ7U8meZfw==", "signatures": [{"sig": "MEUCIQDD3b8XRjk5XbZ5nNoO9qMsaetINn5ITIMxcotddvZOqAIgCWgI9T1MMdurolrgePCegUMs6nb7GMEhRgXRiaqll0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 421525}}, "4.4.6": {"name": "@asamuzakjp/dom-selector", "version": "4.4.6", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.4", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "a34393b92646804a8a34683bd6175869d695e8aa", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.6.tgz", "fileCount": 28, "integrity": "sha512-VWo8DCwCZbk+M2p1RYc2C8Z+lVgl7y7bUbVH/Tnznu6EtDY1QsMomoa4MLl2MQKkU/N6L+HaSd63d8xOAZGdgA==", "signatures": [{"sig": "MEQCIFrt4Trg0m7HRU6dSHrRsQOHEENzRC8VV9gk0/VQS+8CAiAzBovag36/owhmgIHidRE4H+FXQfzG5pCD+xu/pHpzcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422420}}, "4.4.7": {"name": "@asamuzakjp/dom-selector", "version": "4.4.7", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "4b2808101ed5d082d013ab6775d9136830e08772", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.7.tgz", "fileCount": 28, "integrity": "sha512-gZMt3qGdLVpRHc53wb5dX5XjCTn597mOlqG9mhWStMJgS+QwMIFfgpEg8SYBOKAr9o9uAFKLKjLYwTi5SRRReA==", "signatures": [{"sig": "MEUCIDmxAxsa8imDG1Zx0iMiHDoc8Sf4aOEOFH3jQ5/OXrtmAiEA7GLBt0/yLOQhObcrDBrG/y4+A+k9wurOms2RuNyNoM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422396}}, "4.4.8": {"name": "@asamuzakjp/dom-selector", "version": "4.4.8", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^9.1.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.4", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "93a2cef9788c670e4745018ecfc73c671141bdb0", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.8.tgz", "fileCount": 28, "integrity": "sha512-gyg7aIczrB0MEWZYsPGXrJ8n+rEiuuwAa9o3sZNYS4pOFw6NmBFXJpe/rqEdXMk8N/WcnArmG99+pVx3dZ1ywA==", "signatures": [{"sig": "MEQCIDB+ZcHY8iku2nChyPcdLtM/x6cdAlAXUbk4EzTdKxZIAiBGa1d0AK7RQesftGTqCB2Svde8/1lMpC2v/W6kp8GWRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 422919}}, "4.4.9": {"name": "@asamuzakjp/dom-selector", "version": "4.4.9", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.0.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "bab2cc808defa6d5918aa56904581d222428c1a8", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.9.tgz", "fileCount": 28, "integrity": "sha512-rhU5+kpePAcuD8SS7Ok6wbydImR2zFBPtSNkQ7a/QvhCJ+C03W8F+IFPCThztkP28Hz3YA8nN4HyRnhC8vnB4A==", "signatures": [{"sig": "MEUCIQDWSVnP23Xh56MzS+haEDGjJd6l083ERXBbBYjQLPShOQIgSnDQN8tNF2S+RjLz8aexh8+eyYqOOysOeEvFT6N0UQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 423064}}, "4.4.10": {"name": "@asamuzakjp/dom-selector", "version": "4.4.10", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.0.0", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.7", "eslint-plugin-jsdoc": "^48.2.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "0ea92085da25645345c8e0420cb5c3e9b3d7a68b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.10.tgz", "fileCount": 28, "integrity": "sha512-9Nn8fCXywPDUoitCH8mr2fZNy5lMahfyUTTqwuN34O2mdq9cK48VUQIbx2htC0AMG5aqLJ4tGN0Iz0F1zOvR3Q==", "signatures": [{"sig": "MEUCIFZzta/C8zDGDonr0t2VxqQFhrG09ryP284wKfPQ84rbAiEArCeBndCRyedZz540EQajdct6QO2Iy3UVHiqEh1zBBWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 423662}}, "4.4.11": {"name": "@asamuzakjp/dom-selector", "version": "4.4.11", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.1", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "807cd08f87bf741bc681af91a18d47f3fc5ab8a4", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.11.tgz", "fileCount": 28, "integrity": "sha512-t7H4AcFLZfIYYs1K83NLUL8x0cmwwY/wnCKAnODyw3TY1fOl9QQfW6WXlNf2BvoGkd08iZCLvK3ySJIp5MimnQ==", "signatures": [{"sig": "MEUCIQCbck7AEDOppMg2uW+7DxuOILE3nS3aIILO9Kjy3YM+GgIgfmeLv9ftbcDJQ/TMDFPhUBLL+t8dq/ysLpVTO+G3fLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424888}}, "4.4.12": {"name": "@asamuzakjp/dom-selector", "version": "4.4.12", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.1", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "27a083837bca158b6ba5e195ba237fe12d116fa6", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.12.tgz", "fileCount": 28, "integrity": "sha512-af2VoH8webYIExK+SCXAEXE9knrn0crp8QBCCjpTGtBnOuFbrSjdQ7iYARDDRD7YDqxF2DEm0Z3fTUM/+cufow==", "signatures": [{"sig": "MEUCIQDvjnAB6DMOkk/2Nr0Rr1GYp6MVGzopqJhGBG5fp+ifJgIgPSkWq4ALwKVVdbqth8lzmVvS9PtX+RbuG2l94f3Nddg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424617}}, "4.4.13": {"name": "@asamuzakjp/dom-selector", "version": "4.4.13", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.1", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.9", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^53.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "b7449c51205857a6881974435c4daad9054c9822", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.4.13.tgz", "fileCount": 28, "integrity": "sha512-QpVgEmByxjtGy2thNBlKm01dSQ9P6gvsgc8tbtUObqhH4GtT+WB9Zwq9IhDbxwwnoLu1LeURW/ruA0zvBmjf4w==", "signatures": [{"sig": "MEYCIQCaV8oukp+zA0tUKypHUFk3u2H7jXi8k22PEEx/eGxzywIhANkw6IE510kEJCGg/hXt1MCYVccWkNlvBZI1qu8keD1g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 424591}}, "4.5.0-b.1": {"name": "@asamuzakjp/dom-selector", "version": "4.5.0-b.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.12", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e621af9a0f080aa49c06129a7bde8b196c48513d", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.5.0-b.1.tgz", "fileCount": 28, "integrity": "sha512-V2iOnXQsHjl4/AtM53h2mbxYenXBNizHiyqUAFmHZ1345l1DSQ22L0menUCRps/Mv0VL3w/J8idFAO8m9UrYcA==", "signatures": [{"sig": "MEUCIQD+AiqsVnfqjvan7/drdU3IQbEJUCHIwN/d2VFmf9B7qwIgO7gBqGKQHh3TqnJq2zl07QwjfFTG51F9TC+52f+ybJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428204}}, "4.5.0-b.2": {"name": "@asamuzakjp/dom-selector", "version": "4.5.0-b.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.12", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "f2a01e87439abfd8999c43c565e45d4b3f70267e", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.5.0-b.2.tgz", "fileCount": 28, "integrity": "sha512-KUObzs8hEhRr1t04E6ovivm0gZUHqm2hJO5bVLGhDG0908/f1hW+ALQljEaJ2d+Tv+ob0O6Q1XFjhBw0kGKwcA==", "signatures": [{"sig": "MEUCIE3Bm2zXB76+0nnSpblcer37cVk6Fe7kDRm4o5+rsB05AiEAinwJWlm5l1oaUqgb6oTWKaGm4kLp4f4LM80PsYzS7BE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428102}}, "4.5.0-b.3": {"name": "@asamuzakjp/dom-selector", "version": "4.5.0-b.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.12", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "8fd49e4d964b22d714452116f07fd354f4e1bf70", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.5.0-b.3.tgz", "fileCount": 28, "integrity": "sha512-QffjdQ+m4Uk5lbQz2AnndpTRuX69WnbDvyZ6tDWNhdtQLaKDUKWgTd4bQowetnEBHjg5CY2inO5EHnoiaKP+2w==", "signatures": [{"sig": "MEUCICqMMxGWqdirZmbgYaPtb80iKSaYmdBS02Nn7t2ryqi4AiEA61w0U2Ig4KNPkkTFJpWZO6G0TAnROqIgZ2jw3j9i3m4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427965}}, "4.5.0-b.4": {"name": "@asamuzakjp/dom-selector", "version": "4.5.0-b.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.12", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "caddfdb5fc4f1dc2727ee8e006cca06f6275d419", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.5.0-b.4.tgz", "fileCount": 28, "integrity": "sha512-dhX5jruOPyT1f+VuHgNeFHwkVAPHn9SrsIgMeWRgfroea/WYGdk8KV131cztN1NcJ4RU4RZWC1sJvvW7IKyepA==", "signatures": [{"sig": "MEQCIBAq48ob82zjkmrmTe8wuzotoqJOEm/84s2K7LRsy7d9AiB/f/MOgf0N+h21MqrotMqdS+qTOVn2hJdEb+BfBnahow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428420}}, "4.5.0-b.5": {"name": "@asamuzakjp/dom-selector", "version": "4.5.0-b.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.12", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "424c8773ae5e32733ce6ebae93bcec7e4fd9142c", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.5.0-b.5.tgz", "fileCount": 28, "integrity": "sha512-5adWCVrR1F5ZS2QpIgNctqYNy9freEAcgqep1gssbxc0o8MvH2neLkUvznhLl735bL48eMrXKFmeDUABHq42hA==", "signatures": [{"sig": "MEQCIF2fk/p6wVjDq7YgdyYPU2iGmsc0nJQQAointie5eZurAiAd2BXS7d5c5/Bn97dtWWr4Qtcc1M1OCyWuCBLZq/mnGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428119}}, "4.5.0-b.6": {"name": "@asamuzakjp/dom-selector", "version": "4.5.0-b.6", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.12", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "228839dbae746a8257ca8858e747b668b00e99ce", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.5.0-b.6.tgz", "fileCount": 28, "integrity": "sha512-H8n0FDyc5/KzktYlMAOn76zca9rV1las6EHtXCRs23mxZrsgYsqG4HzgqurG9rHnk+ayOoP7ZRDGB1whyVq1cQ==", "signatures": [{"sig": "MEQCIEKyAe481LL51RzB4GIvXYBDi/M7Pnb5fDadfbBYfkvYAiAoAKDzoGf5GiCTDacXje89zHedZbtGWJSqIm7VJ0d/eQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428634}}, "4.5.0-b.7": {"name": "@asamuzakjp/dom-selector", "version": "4.5.0-b.7", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.4.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.21.5", "linkedom": "^0.18.3", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.0", "typescript": "^5.4.5", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.2.12", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "7a3f262cba781f202390531568a86a31cc383342", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.5.0-b.7.tgz", "fileCount": 28, "integrity": "sha512-+DnqBRK6hrrgdXMNJ/Ip3HhNvr2307SqDfE+S/TZZph140+B4wqX9vOgQvZMxa2uocIoMjUjDii7IqOx88+kaA==", "signatures": [{"sig": "MEQCIFSXrjJF/5vfiMZUX3+aBqdGxCwl5gDbVamHSkOevZh+AiBM8x+135DmFiyrbLRIyfoKf5xWOAP+u+PjjVRwu9CBEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427836}}, "4.5.0": {"name": "@asamuzakjp/dom-selector", "version": "4.5.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.6.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.5.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "f2e7b62bf87b30e1ae2c379ff869c3a09e59896f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.5.0.tgz", "fileCount": 28, "integrity": "sha512-yPJWA+BIeUhlJ5ExGWq0vydbyP0xKPv7vNNzxxZAOzH4HeDqwqoRvM2a4SoqYVD/O/LnJ9ijbv3dRrhlCVwQhA==", "signatures": [{"sig": "MEUCIQCz70Fp7pgqCCrO83fc9N/wcW8ZkeV3D6WU/V5YZyx+OAIgC616aUS43v/ptKDIE7pK+jmFTBbk+ZNRq36uPfjIk+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 427831}}, "4.6.0-b.1": {"name": "@asamuzakjp/dom-selector", "version": "4.6.0-b.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.6.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.5.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "96dfd3ba9fb35266e2efae3faf1fd7c651d9d25b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.6.0-b.1.tgz", "fileCount": 28, "integrity": "sha512-CzwrwgSfaR8H/TGu8Acrnc6Ppo5afmXM2UKEjxkgG5MgVNaZlsdCqRDwy03U9w9gJGXiS8tdYcuJtqp1+H1vFA==", "signatures": [{"sig": "MEYCIQDeNNKbG4S6xXWhj4CAzC7L063h8/gDoCL+onEaPcH2pQIhAK3EVk5R6zgmHKJpguGQ9IaHHBjLoje/VH+otjIvqojT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432856}}, "4.6.0": {"name": "@asamuzakjp/dom-selector", "version": "4.6.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.6.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.5.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "2b1703d31650800f8bf3d33a9b993fbd2994f987", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.6.0.tgz", "fileCount": 28, "integrity": "sha512-j6GQc3O0NMXxfSkI22VJdXDldq3Qfc3h2rhr4XCMIwFkiXSr5Fknl0WdGk3+zQr+rvGWKVSTP8CJY4ewGQckXw==", "signatures": [{"sig": "MEQCIC1euWoLc340jUOOWG2Xh584Wi9mn1M2ahouWtR5ApDtAiAeDOn9kLN3itnREhF2m70nQXv0CrQXC52447fCtcQPTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432869}}, "4.6.1": {"name": "@asamuzakjp/dom-selector", "version": "4.6.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.6.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.5.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "72525fab03fa3eabd4b5ca4f9148013e074f1e76", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.6.1.tgz", "fileCount": 28, "integrity": "sha512-hu8teoRalM/Osn0JchxvYlPZOM3eOHYfiYZr5QfDAVUJjkiEngJHXKWo3xrzfWBhAOZ0py1G3TYP/GPSfrd9cQ==", "signatures": [{"sig": "MEUCIGWStM7JbzMD/Dq+0HkRRnSj1x3x4PCZlb7Vp7qUE6cfAiEA+I0BbcaGNEipYQv0H/K1KCX7lAe5DI21HOiv7Kq6tpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432209}}, "4.6.3": {"name": "@asamuzakjp/dom-selector", "version": "4.6.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.6.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.7.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "2a2e0b6d251fb98c217189b9093d08140edcbedb", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.6.3.tgz", "fileCount": 28, "integrity": "sha512-03oCMplrylidJ7eY7Bi1TlaI1oXyu/4XAG8q7pAm55YzkVtzeK8FdJ4tTKSJe0VtD6Jj5EQLPhtHZYv+LhdhMg==", "signatures": [{"sig": "MEYCIQCr5zpQPjF0XoYKBm5Qg/WBxE4NnAxY/WpnBH8U5hMsiAIhAPn04HfeKS/wZOtencEmRa0ZM5st/JcksPQP0I60cowz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 428700}}, "4.6.4": {"name": "@asamuzakjp/dom-selector", "version": "4.6.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.6.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "245b6de306ebcb7274396951bd66d2d379f1646a", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.6.4.tgz", "fileCount": 28, "integrity": "sha512-kob3XK2oskCNEAp//31xi++sV75XsP7kcK/2alGtF6nQd/ywosAvj+UnRwvEsAY32AAYI0BIGXIy6OFhOXmuYQ==", "signatures": [{"sig": "MEUCIQCun/KE1SxseiyqFk3SwcY2kLPmEYBzgMCPScl1MGWD4wIgKrg7c1Ax1Y7uIMSm/nDO9bAtIswMI5bmACRxXK+qqDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431666}}, "4.6.5": {"name": "@asamuzakjp/dom-selector", "version": "4.6.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.5", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.0", "mocha": "^10.6.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.3", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "ad2c2f46e35299eb1ba99795330b064c39b26514", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-4.6.5.tgz", "fileCount": 28, "integrity": "sha512-T8fKeHkkLN31ym+DOBNzlTVskuxhV6MuO2WoSk0QE/rFFt1VZ1/8EUMhTmhClEH+WFqzRzNfLxkgW++Y7VrMqQ==", "signatures": [{"sig": "MEUCIFV6Rz/cy06GdR45KLS2ozf3XPFRkOy5IZTjIUwccxloAiEAsjcU/VCP6K49tfgFRsnh+CelGPPkitRLt6/inVaurj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432832}}, "5.0.0": {"name": "@asamuzakjp/dom-selector", "version": "5.0.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.7", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "f3cade958a03ea665fcc2849659f68f6f298894e", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.0.tgz", "fileCount": 32, "integrity": "sha512-n532OQcs26J48pu1HsV6boCAWTB+3SceUs8EYo+dj/KRSpfw+1yp4wiY44uA2DV4HlcWrl3aHvgIQK6kZNDi2A==", "signatures": [{"sig": "MEUCIQDsl2AjH7LnrtiYcV1oJ+TLW1lZz9uxMnr77BKSrbi/4AIgVv0fqyoakLuz4BeDMtTkgXMJ5AmPo4m1uZq3EHs3Thw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435843}}, "5.0.1": {"name": "@asamuzakjp/dom-selector", "version": "5.0.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.7", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "fee70d059c8083ce59341ceedca6abbdc753b6d1", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.1.tgz", "fileCount": 32, "integrity": "sha512-u6tjjrq64qdAKuMD4blYYmTSXd1FSHfNDH4Z2FNc1IOCgRaMOejBbtV5v3pxMPRPXrPboRpKMaMavdoGLnSS3A==", "signatures": [{"sig": "MEUCIQCKOx1eML5iXvTWC+M6ledq0SHv1MCranpSl3ay8AMVMQIgZyjLbI6EDcM+FKxL0xul3KxO9yNkfK5/WZEI+y5Y/ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436113}}, "5.0.2": {"name": "@asamuzakjp/dom-selector", "version": "5.0.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.7", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^54.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "4ee023d0f03a2c65ebebda742ee76395daef4246", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.2.tgz", "fileCount": 32, "integrity": "sha512-4L14Z38NxSYl8jpr+2lyN/hQVX4EGgDCruBI6dhn19r3JmZ4bDLy1c8fUnYUCJ8NNFfa2niR+aZ0xOPjVJRE5g==", "signatures": [{"sig": "MEYCIQD+bXq8G3yKtQ7ujroA+VWudwU2Yt50pxdRxSFIC1JxkwIhAKvq6DKL2NPxIXB14oKV48jLWA0ffrQGS+iiMgYq+CHP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435758}}, "5.0.3": {"name": "@asamuzakjp/dom-selector", "version": "5.0.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.7", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "b2b44e617b70c4a2006701affb83278eed68f7c8", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.3.tgz", "fileCount": 32, "integrity": "sha512-dtiU0bCxo/ol2piNto3QJVYfjcPh6/dq2R+/fO2M1M7ar3MbbtvNlVSsUUSL3pAGCOqhhM4R23H1WgTQY0C5UA==", "signatures": [{"sig": "MEQCIBXQ7qw2FAuEgcEswHbofDU++ewDVF1fQ1ay2GkiwnaEAiAY6nxX7fmZAotYGTbpB4P0XrwdMaJxHsmKFgSUL7h3Kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435993}}, "5.0.4": {"name": "@asamuzakjp/dom-selector", "version": "5.0.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.7", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "bb4dad636dde09a94310a97ff11f3552cd334fc7", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.4.tgz", "fileCount": 32, "integrity": "sha512-86K2VbIsWWh2TWYq7zkgOsWUNrDFgys3dyJsW41AK9oY39d8gUHWzo6i/2qmxENxkfodSu/veeWgBmKxbLZ1NA==", "signatures": [{"sig": "MEUCIEf1ZkKAPsPx1mzVSk0TEw0c5BhaJQDQPskLmU7jXiEZAiEA2YQDvpQDPXXTq5pDVJgIavhUyWW5SJ2Zx7sfyUjplbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436846}}, "5.0.5": {"name": "@asamuzakjp/dom-selector", "version": "5.0.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.7", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e43acdac774249009d6beaec7ec2ebb3d0c4639e", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.5.tgz", "fileCount": 32, "integrity": "sha512-OlBy7qwFTzxVKGtggGCVlM7FOLBT2RChzghpq+7xrDEY3JWzPlB/NBrHUu4dMRCq+SGkXIZI3ZZKq1zfzmSyaQ==", "signatures": [{"sig": "MEQCIDcUKGnyrt7lDrLSvUPAFabuu+XJW5h6mRr9UNARmqe+AiBFmERRSAhDnyIoS0U/gqAmMdoTRb/ynb3LhG5aFSz4kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437394}}, "5.0.7": {"name": "@asamuzakjp/dom-selector", "version": "5.0.7", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.7", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "824772ec45decdc96dcc816973219d3d7659a732", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.7.tgz", "fileCount": 32, "integrity": "sha512-8vriVXwqkXve8umzNeh/objnlcBbVTuj4REEPlaxVy+gDvMtNtFjVaNcl8rksjsDG8a4c817i6+ALXOVqVtJAg==", "signatures": [{"sig": "MEYCIQDyLKgoAPYKOA72FRQWqUb/fJvazzOFUzIhyPV3sVf0LAIhAPwl4TE3JwiDTZ+4XeNw8Tr/GLTQWemCdLFV2RbICXf2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 437406}}, "5.0.8": {"name": "@asamuzakjp/dom-selector", "version": "5.0.8", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.7", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "dda84ce1bb1fa91803e42c6b7ec29c21290fda28", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.8.tgz", "fileCount": 32, "integrity": "sha512-T0HuZA/chV7jUEHyFMcunw7qhj18m3MBBN00HHSFGR/hbikT9z+Ghwc29bK/Fo4e4kvQ/PbVI384Fv8+Ca1qhQ==", "signatures": [{"sig": "MEQCIA8dfcJJXnaUghjsO7qYWidmWFSHbg/N4mu5NZc6/a+aAiBNCH003rPxMz9FyHpGcQ1se90Wc4F2LHmFOX2t5UCzeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 439376}}, "5.0.9": {"name": "@asamuzakjp/dom-selector", "version": "5.0.9", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.8.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "7e5a8c28aac60fa19dabe37a839beda3fa15c149", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.0.9.tgz", "fileCount": 32, "integrity": "sha512-4oJbu0S3TMBCqqyHpYY8QLM2KwVrii6CXpZd3wzPdvzuDtlFmeB5UgPfYI8OlkHMGV//204AjXDJNFJGLCkEVQ==", "signatures": [{"sig": "MEQCIHszBTdprZC4vIjJpaJAbtBZUovL4fBcLjww6JEgJTeWAiA/JOujQxFInH265jSDY5K9t02i+Wqf3WdAqi5rTSb6LQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 440216}}, "5.1.0": {"name": "@asamuzakjp/dom-selector", "version": "5.1.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.0", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^48.11.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "2676877786562a417d1151b56adaca4a3c497c42", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.1.0.tgz", "fileCount": 28, "integrity": "sha512-ltde0J+BpDl8UkdtrT7hUGUZr35LXT5p4jqBQAtbsF4gRrDz7uPV2fX085YfG6aOKgeNP0pvpBTRimnG0DS8nw==", "signatures": [{"sig": "MEYCIQC93g/L8ngkhJdtws13jFLiJye/x9a7elddXaeH30tVAQIhAOzQz1jzEu8DnqwxgO/1VEQqPyBUnayEUgZ2F7kgRtV4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 438350}}, "5.2.0": {"name": "@asamuzakjp/dom-selector", "version": "5.2.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.10", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "d7fd34fc681a3653f5da6aaabe955cfa6780c8f5", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.2.0.tgz", "fileCount": 28, "integrity": "sha512-u6o/AoWvQC0TAVfulIOuS7FqVHKe2rGAfR4kLg0XZi2UJ+RsezRQEJeBXiSYY6EBfGImWubm09QQ4xU15gndyA==", "signatures": [{"sig": "MEYCIQCSKcqhMuOY0uQ62vwjdeRySY+eD4+zQNMQWQcskGbUOgIhAOesotzbSThAIvU+giPcwjxO9ijCcVMxcnyrF3AYCNLP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 439106}}, "5.2.1": {"name": "@asamuzakjp/dom-selector", "version": "5.2.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.10", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^24.1.1", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.0", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^14.12.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.0.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "81366860551b47ea7fd6d7b63faf6c0ea7812b17", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.2.1.tgz", "fileCount": 28, "integrity": "sha512-fQhrq4d0CcNeSihdAGvwFBdJVtbD5RfJFJFG21Sdmj0w2C4/HWkw/bwaqrWmfylOhqS0M8NP2Kpl+FbAiRUA8A==", "signatures": [{"sig": "MEQCIF0TRiVoerDGN7kWPZLDIl7SPAtZPwv/z4Dnc2ur4zzYAiACJSbtPkTtF7QeOxpRe3zqq1mYuTSi/rsXE42ORG/WbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442472}}, "5.2.2": {"name": "@asamuzakjp/dom-selector", "version": "5.2.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "40c84caea727997f451673ddef15a173e0a45634", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.2.2.tgz", "fileCount": 28, "integrity": "sha512-aG0rN3N07FONBEINzdc3qItZq0pP/LVeBvg9wJgnrQLsehLKKsGWPZJ8Yp7MOoyiqbIhPBRrobZAkGxFchmGSA==", "signatures": [{"sig": "MEQCIGHnK0Vcj4YSDwCYNvAwOP0Jnle7m7a2G6q9V8sngBq5AiBVnE6H0OBD+DOjBxSH/3EwJmX6ec6C84nap8hlFqqP4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 444752}}, "5.3.0": {"name": "@asamuzakjp/dom-selector", "version": "5.3.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "dfe38537d4f1592082d197ca6ee644c66470404a", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.3.0.tgz", "fileCount": 28, "integrity": "sha512-P4AFg8NEcwmWd6EDtP52YwvZBd3yan+hiSaI+cOsRU6I8XggKZgJB0hjiKXkOfJsfCVnHtRT61dDiLB5Z8cIeQ==", "signatures": [{"sig": "MEUCIQCB3TBfQfRJeMuOfXpN083DCOaDj3fZdcKtz8Vez01U3wIgCrq7CG5WJkkSy/Fk3QlCQjWgpN7DmuYnwngp2WoqSlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 440241}}, "5.3.1": {"name": "@asamuzakjp/dom-selector", "version": "5.3.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "27f476c0cc5dfcc0a6bc376f2179438d95526010", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.3.1.tgz", "fileCount": 28, "integrity": "sha512-XXm2zpZNQMfLvIoNHMh8UFXNXMPAiopYAGLXxPqxO+yIp8HVmePNQMZ+48tw5Jme2DzDf7ZdMSI8tJKjgPNaEQ==", "signatures": [{"sig": "MEUCIFYydsWo1UfPyiiOTv42Vug4UCJ7PpfAhILR3G1/GizEAiEAoUtP/FUT6K8HNMurwb2d4czSDsO1UXsJW4s04oct3GI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 439748}}, "5.3.2": {"name": "@asamuzakjp/dom-selector", "version": "5.3.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.0.0", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "1cfd0906b1ba19a380d156899d47ea123277b322", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.3.2.tgz", "fileCount": 28, "integrity": "sha512-7V7o7ywWG934WdGycDylKx974THNEGMGd5nOn0ihtlYzr+qcDH77psCwB/HCgKv4cMmmwn0VHK0L8bAN5CbA9w==", "signatures": [{"sig": "MEYCIQD6GUrzfqkBwpkUcl+lk+70JvAEFg4d5MhnSbig/tNlGQIhAPUa5ThfelcIYcfoREwWvh4h87Hn4rQ/rId/L+lzrj+P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 439212}}, "5.3.3": {"name": "@asamuzakjp/dom-selector", "version": "5.3.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.3.1", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "eac37e195b38a714cc18a0acb3297d91eaef5339", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.3.3.tgz", "fileCount": 28, "integrity": "sha512-5hpGYLjae8iSj0gV0s/OMQ1DsdJCXnLfHgFX60qR+4PUozD6E21QJgapQLWCDWcgHh2NaeaVqjTasq4ztWwScw==", "signatures": [{"sig": "MEUCIQCajmXeeTzxiF/S7OQc6+GUGjFmmjP/h73dWFQoKIauewIgBnrR4ddvFDRQVH7NcplqMlfr2N7Sl3R50u39wb6OmUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 440266}}, "5.3.4": {"name": "@asamuzakjp/dom-selector", "version": "5.3.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.0", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "078c4364dd94f661e8a3d324e36f3704511bc1c8", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.3.4.tgz", "fileCount": 28, "integrity": "sha512-I+TRG6le44HWpncPU73+u0X+gQj+ZwCozctVP8xDc36jd6sZvn+OOWg38Mf93XgWGfDxqmkuwpPcs2TUCc4oog==", "signatures": [{"sig": "MEQCIBsgc5e4f98uVN6MraB/m5kHVHimm5calxmmuhAlB9iCAiB6pmvub4l9E1sToVrrzkwjbatvVM8hg3iirZyrpeErsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442954}}, "5.4.0": {"name": "@asamuzakjp/dom-selector", "version": "5.4.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "3635f0d5930fb7dcab0422f9592314a3329a7192", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-5.4.0.tgz", "fileCount": 28, "integrity": "sha512-80z1coZ+yDvJiY+DY+YKiKOurb49XqQjhbYauQUNNJkCZgorST5CBzaI1Ol3l4T6pLBeebvp+vuCY2xndikmJw==", "signatures": [{"sig": "MEUCIFpuqxjlMSWYHbHJBRG00+rd7YNSz+tPVZG4AYpH5jt0AiEAx1VmibyN4G8PdMJPUPqsmpbP4z2OGoXIfgaJ6RvjkfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 431276}}, "6.0.0": {"name": "@asamuzakjp/dom-selector", "version": "6.0.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.30.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "dead37f19073557ffb07accfe78cb2360cb5edbe", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.0.0.tgz", "fileCount": 28, "integrity": "sha512-qgT7DokPdSQm3giGjpO82coGo8WJcmXlgjwOL/K+mAjwINdhA3tNPefn9an5ZleA2ix/THRpdRTAJAyIFIxxvQ==", "signatures": [{"sig": "MEQCIHo8JVCUAQ5/mC2ehSx/I9X0UIhsOaOn2LGm1clOpht9AiBZxFMI9jVT4QupejqAQ/SPL/JLrQ4sAkD8m1R8cncxBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442291}}, "6.0.1": {"name": "@asamuzakjp/dom-selector", "version": "6.0.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.30.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "5d18a3991a663852fd1a28e2d85adb08cd061f66", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.0.1.tgz", "fileCount": 28, "integrity": "sha512-ax7UMfitDVA6HdL5LU0+NmmunSg6tJ9L+Gkh/l/s5KCTP+Vp/03EktRDeo1unI6CdEkRgD6BET0OKGxS6RIHWw==", "signatures": [{"sig": "MEUCIQDA3GTkA4Ygs+uIjx/U9XMzb7RIWCkUOMKOfdJ97EgTdAIgFPq66Qa5/Fw1hCnbuOZ7m1MghMqpYeXpDiQYiG6mIS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442218}}, "6.0.2": {"name": "@asamuzakjp/dom-selector", "version": "6.0.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.30.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "240f9593df9ab107ea121d5c040c0245350e1173", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.0.2.tgz", "fileCount": 28, "integrity": "sha512-B2nwg0q1bQJA3xVcp9OnqQwmaqzwlufWUu8d5TQJKVeHwHXh9dqrFQnaRd+4w3oHZWcplMk6PbTQLn2wMhn0AA==", "signatures": [{"sig": "MEUCIBvPo/BbZdnNeBCT4FloDM6I4esRAFU2yLZ0Qsq3rF5qAiEAw8Rj26vxDsemgZu78huQFqQvP60E9RtdHl4EDvAWn8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442462}}, "6.0.3": {"name": "@asamuzakjp/dom-selector", "version": "6.0.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.30.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e2c4b42875a2b6025389622e59a46acb0dce539b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.0.3.tgz", "fileCount": 28, "integrity": "sha512-0HRAB5GaCw+FbeYX7ZRTH7+QyPRXKc3+Z/pK2anGEqQ3JI7Y2UooE4rIXWiItQvrWB99KWw7vFo9owHD7mTiJQ==", "signatures": [{"sig": "MEYCIQDuqAMvY4399cMlLxajP7uUiGhXxXHZuMHxC+b3Z5zKdQIhAKqbT3PwX/+uoPYK8ofzFz1MpwQuWgrHbIQ3H2alkNI8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443028}}, "6.0.4": {"name": "@asamuzakjp/dom-selector", "version": "6.0.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.0", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.3", "typescript": "^5.5.4", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.30.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "e550a567879d964509dc9d833b0c064d1e4238e2", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.0.4.tgz", "fileCount": 28, "integrity": "sha512-oROs2SIkR8Emue1E3q1dxrD64Oh2IIkXoaV1QfLCpmcaRj1wJWupCZxMa/97FirQfN7R9VkyNRinFeo5N2sHSA==", "signatures": [{"sig": "MEUCIEHL0amvMMYvBzOQEZhzZmx/zAYRbveXulYrhXLdKihaAiEA9kMtl/T2FeoYkwNST27IuhOyLK/r7JAV1NoU3adjXfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443281}}, "6.0.5": {"name": "@asamuzakjp/dom-selector", "version": "6.0.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^2.3.1", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^18.0.1", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.3", "typescript": "^5.6.2", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.2", "eslint-plugin-import": "^2.30.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "eb1acaaee9dc40036cdf7f26932e9d58d5408a68", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.0.5.tgz", "fileCount": 28, "integrity": "sha512-K4wA2IBoKWncYJL/iN24z5pXGGI0Nj70YLKiCNShBe5JDeBGTCkvf7vFHeBhaS5F3J5qNF+Vht6FM1tMNeyAcQ==", "signatures": [{"sig": "MEUCIGdHdXIS775jb3CiOufuVZBDBH+6dYWmcFW8tC6i/KycAiEAil9DN1Of7hiZ2/vflysI41ByfWUdwV7rwUQVVjh4orA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 446237}}, "6.1.0": {"name": "@asamuzakjp/dom-selector", "version": "6.1.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.0", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^19.0.2", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.4", "typescript": "^5.6.2", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.3", "eslint-plugin-import": "^2.30.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "b9ce92305d4dcb600ac2e15766b1c8ec86d9989c", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.1.0.tgz", "fileCount": 28, "integrity": "sha512-I7jYqKaEqIYYk2J9bdCKrMfAAZVNCgNOUJk7Z1NmC55g7u27/eSIWM7NypSVKvGaEOSW1LbLQVMDWNkS3J6/dA==", "signatures": [{"sig": "MEUCIHPzFDDBON4IIqbLVtEt221tWTBTy2iCFQMno3V8fW3OAiEA5eu3yxKsMQfcqaz+D2yvTqCOtH9AUYh8aS8R2Rg1qTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 442026}}, "6.1.1": {"name": "@asamuzakjp/dom-selector", "version": "6.1.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.0", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.1", "jsdom": "^25.0.0", "mocha": "^10.7.3", "sinon": "^19.0.2", "eslint": "^8.57.0", "esbuild": "^0.23.1", "linkedom": "^0.18.4", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.4", "typescript": "^5.6.2", "wpt-runner": "^5.0.0", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.2.3", "eslint-plugin-import": "^2.30.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^55.0.0", "eslint-config-standard": "^17.1.0"}, "dist": {"shasum": "2bd33f8a87f27a425e1b9aa41890cd272a72de58", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.1.1.tgz", "fileCount": 28, "integrity": "sha512-BQzh0tfOCJKtHN42jsXSMj57mzcbSYwQTcBgOODpLCKOrBLJkBMkV7ggx5w8viUlhewDw7NYmb3/C+ZGTbEexQ==", "signatures": [{"sig": "MEUCIQCINWCFMBvHjP0Lajgu2Sn+XLjvyiaLJSuwLgGEYkfN/gIgR6L2Xy258e3ULsELcMQI+yz2PYiXcUgWvGp5vN2uZdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 443434}}, "6.2.0": {"name": "@asamuzakjp/dom-selector", "version": "6.2.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.0", "@asamuzakjp/nwsapi": "^2.2.16", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.7.3", "sinon": "^19.0.2", "eslint": "^9.13.0", "esbuild": "^0.24.0", "globals": "^15.11.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.7.4", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.7", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.3.1"}, "dist": {"shasum": "5559a641333ee2c88750d451c11a9064d9befcc2", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.2.0.tgz", "fileCount": 28, "integrity": "sha512-ufJqFrcepP9T3S1Jmhc9pSS/eIAniUm31ROlbrrkKdj7zO/CP2Y07NZiiA0lwQ32fdTsbKc+8oufNP/D09IbzQ==", "signatures": [{"sig": "MEQCIBxHJ2D+3zeeWrgTIz6yjFiLhQWkBSe025RNfYeoEiFOAiBbMJDDJMmAlBlICFqWtMLW8n6jFFlK2xg3F6wIn3SyWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 448577}}, "6.2.1": {"name": "@asamuzakjp/dom-selector", "version": "6.2.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.1", "@asamuzakjp/nwsapi": "^2.2.23", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.14.0", "esbuild": "^0.24.0", "globals": "^15.11.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.8.0", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.7", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.4.0"}, "dist": {"shasum": "e9aa65ac93f4c78cf4e79d6439c41a9eba4609b7", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.2.1.tgz", "fileCount": 28, "integrity": "sha512-/mmLVNPsnOKsde9nKoLARLQRA/q2uKx2GkHx5V68Qn/EAagabmInFwz7frG7fFxCZy6yURbH0Itcg8jnv6Niwg==", "signatures": [{"sig": "MEYCIQDQLAQkADwkYXvTSQzlM+k4hCU+CU+5+IeiT3VAMg0dVgIhALrn6IeboK3T7MF0MRzQmjrg1OAofWekW7P0Alp7dm4U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 448490}}, "6.2.2": {"name": "@asamuzakjp/dom-selector", "version": "6.2.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.1", "@asamuzakjp/nwsapi": "^2.2.24", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.14.0", "esbuild": "^0.24.0", "globals": "^15.11.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.8.0", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.7", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.4.0"}, "dist": {"shasum": "e3d4dcb313151caa23ee2f1b538ee0f0491eacff", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.2.2.tgz", "fileCount": 28, "integrity": "sha512-WXXohm1QbXKYVLTNPbHJgLSW09Vh5/nEKaIdezayiVqpLn0hKQpmtCdPP1IBQ1dzV5QibL2J2s8t+dMTnNQ1vg==", "signatures": [{"sig": "MEQCIEtMyIuTT6Sdfm6XDdD6NXwagRbfItGXDpGGo9vUUco+AiBdeJ9YPsV77w10usR8M9hHqbUHR9feXYRsl3G6EY+Jfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 448490}}, "6.3.0": {"name": "@asamuzakjp/dom-selector", "version": "6.3.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.1", "@asamuzakjp/nwsapi": "^2.2.24", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.14.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.11.0", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.7", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.4.0"}, "dist": {"shasum": "0cdecd6bbd8f39295f1487875b1157d6e562b201", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.3.0.tgz", "fileCount": 28, "integrity": "sha512-FJciGxRblBvmJ36NMK8CkPir8xFUJ64oW/kzUGqByfHuPH/fpZwcIqXDoDLzqU8lG1sXp1dIVg74BJ1MjcpKfw==", "signatures": [{"sig": "MEUCIQCGbzKt6m3NAeypgBWSgLVKB+10OA+/EzEqlA9kIbI+jgIgNTE/E1N56W9eUD4LmVMFm62w8VoseL+JinwyBE6Uc+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 434691}}, "6.3.1": {"name": "@asamuzakjp/dom-selector", "version": "6.3.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.1", "@asamuzakjp/nwsapi": "^2.2.24", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.14.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.11.0", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.7", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.4.0"}, "dist": {"shasum": "c996b6ce0b48a3c1201392fd7e4f3de9c4f08a4d", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.3.1.tgz", "fileCount": 28, "integrity": "sha512-pBz2+l3LEoxsiuKUQZ+GaNVa4aKcm/Kkaf6jIkt3k7/DS77O2oJfXt+haKVbQsvmVTkEsOjl3Qx8HAZLpSG83g==", "signatures": [{"sig": "MEYCIQCukL7CRq+oLc8Kj14mwbkezui+2Ku0VygoiXgnnzBV7wIhAInGa/aLTiCmI/O6h7Wc0Pv8me+wTUtP2Jlkr/8BjlSW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 434626}}, "6.3.2": {"name": "@asamuzakjp/dom-selector", "version": "6.3.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.1", "@asamuzakjp/nwsapi": "^2.2.24", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.14.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.11.0", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.7", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.4.0"}, "dist": {"shasum": "c61fc0503fad4830165a13f98cce3c7fe87f5ebc", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.3.2.tgz", "fileCount": 28, "integrity": "sha512-hsJUVtaefDLGFIXVt5bJIOK2MX7hzFPkYM9Anfmv+m0+9g39rMg5d3ZDy4flE+VK/oGUlCRZSKgBr46E7O4X9w==", "signatures": [{"sig": "MEUCIQDqZY7NCt/TSyeKAhPzawwTxiBB/yW3YRlen4UrRrM37QIgYjqx6RMeSZgo2xU6x+fwihLhBXreupreXwC8jvD5nBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 434933}}, "6.3.3": {"name": "@asamuzakjp/dom-selector", "version": "6.3.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.1", "@asamuzakjp/nwsapi": "^2.2.24", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.14.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.11.0", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.7", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.4.0"}, "dist": {"shasum": "e265acdd62608d4e79c867120dd6ccb4e8859284", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.3.3.tgz", "fileCount": 28, "integrity": "sha512-twheP3rWl1Dg9lKvrpOL6MKc5e6Novuv6N9tphZGAx5gWCbnT07cpLKnsD9qHfji41IZYDvzf5zw0xJavrlh1w==", "signatures": [{"sig": "MEYCIQDM3Hl21Xnyywt0fqmL2C7ATbrQhwrFo+PE4NCp812NbgIhAK0Zp5uveyKyRtt6cVnE+ryT/Px4BbMjSI6duAhnIahf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 434868}}, "6.3.4": {"name": "@asamuzakjp/dom-selector", "version": "6.3.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.1", "@asamuzakjp/nwsapi": "^2.2.24", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.14.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.11.0", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.7", "@types/css-tree": "^2.3.8", "eslint-plugin-jsdoc": "^50.4.3", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.4.0"}, "dist": {"shasum": "e4a11d82b18ddf17274cdfb50aae317a0e3d7976", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.3.4.tgz", "fileCount": 28, "integrity": "sha512-rUrpbfPW+sRIFK/1C6lfoRxSY2io3x6YkyKcFCfD3lqZGeinrzGVoDmbY7tzUaeiO2DbY/bhNpojsSTDgZYk8A==", "signatures": [{"sig": "MEYCIQCLWnX+kE2SWO9GawH2bMkgRbl7+qNckNOrF+fdQOQEBQIhAMvrGRICgecKqS02hI9nms7O73VBSVjC5spu5WbIR7GL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436224}}, "6.3.5": {"name": "@asamuzakjp/dom-selector", "version": "6.3.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.0.1", "@asamuzakjp/nwsapi": "^2.2.24", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.2", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^10.8.2", "sinon": "^19.0.2", "eslint": "^9.14.0", "esbuild": "^0.24.0", "globals": "^15.12.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.11.6", "typescript": "^5.6.3", "wpt-runner": "^5.0.0", "neostandard": "^0.11.8", "@types/css-tree": "^2.3.9", "eslint-plugin-jsdoc": "^50.5.0", "eslint-plugin-regexp": "^2.6.0", "eslint-plugin-unicorn": "^56.0.0", "eslint-plugin-import-x": "^4.4.2"}, "dist": {"shasum": "d0d235bef12f406df038b3e4517c1414f99d7007", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.3.5.tgz", "fileCount": 28, "integrity": "sha512-XleYWx/hHA/pxgHZYhx1K3sJ44JJ6i65rWv04G5qz+mvWY5PQppw8r1HEaHqWHsIhOHlxDBDgBrhU9FF1M2EXQ==", "signatures": [{"sig": "MEUCIQD3ei/xX6BiIdsXabl8bidBisd+5AIBVZ6nogtd7/LiigIgQ35HkxqGRLlWvEtCBmDO2x8xyo08YhIaD42bW7WLMgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 436313}}, "6.3.7": {"name": "@asamuzakjp/dom-selector", "version": "6.3.7", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.2.27", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.1.2", "jsdom": "^25.0.1", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.17.0", "esbuild": "^0.24.0", "globals": "^15.13.0", "linkedom": "^0.18.5", "benchmark": "^2.1.4", "commander": "^12.1.0", "happy-dom": "^15.11.7", "typescript": "^5.7.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.0", "@types/css-tree": "^2.3.9", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^56.0.1", "eslint-plugin-import-x": "^4.5.0"}, "dist": {"shasum": "0d932ceccd8d2ae5c332dfe09f7c13c2ea21fcd5", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.3.7.tgz", "fileCount": 27, "integrity": "sha512-05UNcwqVOTRKhgeUrSz/rQ06RNRJWhJeKEQPxd8qwa+5Or3Xv4e9gutjkfqTqtwduJPf/Ve6E7zxHsGPo2VsPA==", "signatures": [{"sig": "MEQCIDMaF9atfyqEbliLBDigMdNMUJSJ5Igb6ZhhRgynNQa8AiBpAkm13d3siKZUaJrPT99GTjo2vZy4RtS1qfX7fpNw6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 439007}}, "6.4.0": {"name": "@asamuzakjp/dom-selector", "version": "6.4.0", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.1.2", "tsup": "^8.3.5", "jsdom": "^26.0.0", "mocha": "^11.0.1", "sinon": "^19.0.2", "eslint": "^9.18.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "linkedom": "^0.18.6", "benchmark": "^2.1.4", "commander": "^13.0.0", "happy-dom": "^16.5.3", "typescript": "^5.7.3", "wpt-runner": "^6.0.0", "neostandard": "^0.12.0", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^56.0.1", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "ca481bd02fed9797a45b6f9e911386e06c58c105", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.4.0.tgz", "fileCount": 18, "integrity": "sha512-o1amEqqi8W9+fNrEE8QXmhpTHRSXbESmZSEWXwCFp/dHxl/LBXm5cirSwXkak7MbE9MLmEcURdQncnY4RWnexA==", "signatures": [{"sig": "MEUCIQD5WPKoikBEiEZicm8AJAhdx+fRR0fho4r9pcD2GqZ+HwIgdT9fwzB3djh/9+9xCxcK8HIlBhztzFtIKFoOKFmaSlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 435832}}, "6.4.2": {"name": "@asamuzakjp/dom-selector", "version": "6.4.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.2", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.1.2", "tsup": "^8.3.6", "mocha": "^11.1.0", "sinon": "^19.0.2", "eslint": "^9.19.0", "esbuild": "^0.24.2", "globals": "^15.14.0", "linkedom": "^0.18.7", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^16.8.1", "typescript": "^5.7.3", "wpt-runner": "^6.0.0", "neostandard": "^0.12.0", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^56.0.1", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "87718c7f1d29d8ec5ca39f73dc699de4e960512f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.4.2.tgz", "fileCount": 18, "integrity": "sha512-tjZlWI+GeSHu1RNm6bAP5Rux0rhSzBnNBJzMxldA8pQdyLsiSb9Dh+MQjvb8g95aqisjH1fQSv+o7Vb944xlXw==", "signatures": [{"sig": "MEUCIBxrlbWxgFdtTJzc7jDF+Bie43vNN4PoqEr4fN6rU+17AiEA0NhJLX7peoP9Zt0BEahipJWbVBMV1gDU49D/EynKH9I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 435904}}, "6.4.3": {"name": "@asamuzakjp/dom-selector", "version": "6.4.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.4", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^19.0.2", "eslint": "^9.22.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.6", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^57.0.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "d36eb34c573e5ecf9f84a7e9aa2b7be91b1f4d53", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.4.3.tgz", "fileCount": 18, "integrity": "sha512-6dZvtyR56op4ziYEQx81Gz9PVnfL9o20pI/LsOFC4+sEmfni3BiVEaptt5iYUlXgg/g9NYtbKkp34yi1UhaBgg==", "signatures": [{"sig": "MEUCIAmg8NRexiNeO4EJRMMynPcAkH1u/RPPq6nOVDvfofbiAiEAteKC9UF1DHEoGC/VbiFEYmYSuxoSR+9K1xN2GR9QzPY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 435954}}, "6.4.4": {"name": "@asamuzakjp/dom-selector", "version": "6.4.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.4", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^19.0.2", "eslint": "^9.22.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.6", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^57.0.0", "eslint-plugin-import-x": "^4.6.1"}, "dist": {"shasum": "9ce53f5feaa4ed192144696db2c853d5c16f3efd", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.4.4.tgz", "fileCount": 18, "integrity": "sha512-uF8JTUAqq3WryYICSj16lpT3DG345LuluFovyis+scRg8kWBGqHYYYYKsMAE9S91hLO+/cBZuA4umvX7bhZjqA==", "signatures": [{"sig": "MEUCIBFOoXiGWvwufxOfaoo9beF7ST/aXwihrfzi0lKEk2M8AiEApumIQH5LE3g29oMftvTPQpTL53TPss6yYKxuR4K8wHI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 436011}}, "6.4.5": {"name": "@asamuzakjp/dom-selector", "version": "6.4.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.4", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^19.0.2", "eslint": "^9.22.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.6", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^57.0.0", "eslint-plugin-import-x": "^4.7.0"}, "dist": {"shasum": "028b74ff037d21a99d23213e89fc7ab45d78d387", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.4.5.tgz", "fileCount": 18, "integrity": "sha512-WcB3vC6TnckQCMxaKjG0Cv/IX1ZnHu9rV7+Lp+2VxGTL6RP+uQMZQvOHwImbqArS+gVSQVSMVDnfVP3tzBlTvg==", "signatures": [{"sig": "MEYCIQCBMtfbmbZzp180hWs1XsHIqj6dJpigyuOvQyLUhwiq3AIhAP8bj4H6eeNM2xU1Z/Ge13iL0qqweMz4ZaZBBs6sYcoc", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 431504}}, "6.4.6": {"name": "@asamuzakjp/dom-selector", "version": "6.4.6", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.8", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^19.0.4", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.8", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^57.0.0", "eslint-plugin-import-x": "^4.9.1"}, "dist": {"shasum": "ef12f37667531258d6029bc2b5f5cb873f4aaea2", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.4.6.tgz", "fileCount": 18, "integrity": "sha512-qDvvEQLYhgMXFNJu7TDO0NRyk2KNcUDeENxuwZRgRq8uaWQvzgl7SAidF3xw/tT4OGn2uGHNIWx0XpJ59f2ZnA==", "signatures": [{"sig": "MEQCIDf03/iB/xPJvb/KtOBm+zQiVMvY8kAg0uE0Hlbdd9YQAiBZlTxEdj4vHFd/s7wGZgkEjfk19i+WRvjbYu93uAAE9A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 435744}}, "6.4.7": {"name": "@asamuzakjp/dom-selector", "version": "6.4.7", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.8", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^19.0.4", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.8", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^57.0.0", "eslint-plugin-import-x": "^4.9.1"}, "dist": {"shasum": "4ef3af5621b25a4de276ed2d2401b9f0ea0711bf", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.4.7.tgz", "fileCount": 18, "integrity": "sha512-FjkJ/AALPjKWsf6CaUtkcQ0Wrm3L5rI3sxP7bMXCaxiyBLJDZN7eWJ6loAma7GR9/zXP/XcDLnh92vBOI5I+TA==", "signatures": [{"sig": "MEQCIBxAqzbcm/3plT7TR6/YOwL4j20vZj6bLIAQU2S9ogUAAiBkHsSoN3WfI2rI37FokbiwQlMXHxzr0i9V1rq1gFyt8g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 435839}}, "6.5.0-a.1": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.1", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.8", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^19.0.4", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.8", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^57.0.0", "eslint-plugin-import-x": "^4.9.1"}, "dist": {"shasum": "7741064af9ba9e5b7c93934213e101414946a8ad", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.1.tgz", "fileCount": 18, "integrity": "sha512-cnSiech/j1XVBrMTBlpVMirV0nLjb8zU5RcOhoivuKf6VYY1URT2LZftd3OgHJpXLICcUzCI8vof+Ihh5M8q4g==", "signatures": [{"sig": "MEUCIQD5gTxPygNlMmyumcJBPr2zQb9CxEVVXQ9gKocQQHHo6AIgfcVxHu7kY5pmXXHhk5ED6lRHwxmpafyTILUCW+AyBdg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 447587}}, "6.5.0-a.2": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.2", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.8", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^19.0.4", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.8", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^57.0.0", "eslint-plugin-import-x": "^4.9.1"}, "dist": {"shasum": "ef00772537ef9b98818d7c6f017613622ba89c27", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.2.tgz", "fileCount": 18, "integrity": "sha512-dLbNUmMn7u6k3o2JYfnCb63/3zO+x0oxCtYfYKmfkpO2PerEQl2HeyIvg68zafSPp6l+EMOqfohRcLaP4qpuUQ==", "signatures": [{"sig": "MEQCIAhTHBjW/QweRfsFNk9C3tBcHSehr4ztBLNPRrFYj08oAiBTDE/IXYX0sMhSO6pskgtSLMAOw7dGrCW6vOgrg8LBaQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 542415}}, "6.5.0-a.3": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.3", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.8", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "eslint-plugin-import-x": "^4.9.2"}, "dist": {"shasum": "4930170efde8559d9d64ff4cb6f3e36896d9b3f3", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.3.tgz", "fileCount": 18, "integrity": "sha512-+t/CLcGZ/Mm/6wzPLji00OdH+RHr7sWuc0bLehj3fOJ9ZuTS3BqN5VCCfqHelwNiUkRu+KcIuPMBlgceD6NrKQ==", "signatures": [{"sig": "MEUCIQDiYU0x/yo59mk15JryphVPoQIPOzq7qjF52zbYf5xm5wIgNTCydwDCwil5nhiffrpHDyJYQVxkO71qN14RidCjdYw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 444038}}, "6.5.0-a.4": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.4", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.8", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "eslint-plugin-import-x": "^4.9.2"}, "dist": {"shasum": "32be5566e77f5aaf0f36b9c8e24d9ad13222602f", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.4.tgz", "fileCount": 18, "integrity": "sha512-IvhtlvxWJpTk9vcJ7ptVp6uuMm1ukwDLOAiuOwPeqFlNzF6RbdWKkTZO5g0o5m+E58qlaTzKFgtzVg+d70BE2A==", "signatures": [{"sig": "MEUCIF4Y1zOCXv0Die4m3KMQ05sitIwIuBqJIjy8epGoUF8DAiEAm0Si672Y2NsWn6WLwSGlI03gNa2pMiEzk0Tq64ddTbw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 538352}}, "6.5.0-a.5": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.5", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.8", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "eslint-plugin-import-x": "^4.9.3"}, "dist": {"shasum": "5901d119d0c603cb08c11c1fe701931a9e5b1146", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.5.tgz", "fileCount": 18, "integrity": "sha512-ljuJ3DWcPb2FCM8reshHzxhkouxfiVyyM5SS1FAT5NzWNvUxjN0/7d53v3c28ty9ug379l1FUIdZwVcQpp4OVA==", "signatures": [{"sig": "MEUCIQC1Xc/DvRGplG7dt4BjSvjbO2SlEfZ9E6MxRhXIziInTQIgNhnEvqHYk7zLEV2IYNAQxmiSmb60LnDEsAjeTmEychY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 545522}}, "6.5.0-a.6": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.6", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.8", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "eslint-plugin-import-x": "^4.9.3"}, "dist": {"shasum": "1164c74f570025ad2dbd4993eecb2530169a3132", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.6.tgz", "fileCount": 18, "integrity": "sha512-UL/tMUekI6irSz1r2CRPoVDIPpCSmu3ppViOKqkGXTyY4vzcx0GVtaaJHNmmIC4DQ7yc09uEH9UGDXAD4hVtHA==", "signatures": [{"sig": "MEUCICUTN41wszo2CdIWSZs+1YdJIaoWQcpc0+TCEAhdABBwAiEA1Dh+KebxEekdm0589m/UWn8uI2Ldyvnbm4wYdAmu7ho=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 545773}}, "6.5.0-a.7": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.7", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "eslint-plugin-import-x": "^4.9.3"}, "dist": {"shasum": "776b3617236f6770611a19ce7b666d0b8efd4a09", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.7.tgz", "fileCount": 18, "integrity": "sha512-/CkOouES/yVbzC1+nN6tvjkPxI9r8HAAb4XV8jANYpQZ3mZllXVvUfSwVkCPhnSZ6KksDOhOtSFtZEjtMfb4rg==", "signatures": [{"sig": "MEYCIQChjqMb9atNx1UuV2tI7PRbfNHe+jO97kXVu23htx8IKAIhALrX7Qoq3HpXf363pYHBeBN4ERkGg+2ISM6J9nsTOzs/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 546289}}, "6.5.0-a.8": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.8", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "eslint-plugin-import-x": "^4.9.3"}, "dist": {"shasum": "4d77324c88bd49760ed8364d0f073c8c87e9312b", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.8.tgz", "fileCount": 18, "integrity": "sha512-caV4FTSbDsUotTlMFtdUy3M6ukiA64IoE1n6mixRBY9Wiypm63Iki8tMIByvy4dmSHha+oWod7H+DexvgAbDTw==", "signatures": [{"sig": "MEUCIFHqBZWVlBF64Uu3mGLDNWLHdaiixMMhzIBAno02gDz1AiEApln7N8hor29b/JdDDawLaoP+HrIovJO0fjamxb6I+P0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 545874}}, "6.5.0-a.9": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.9", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "eslint-plugin-import-x": "^4.9.3"}, "dist": {"shasum": "6891380c73ecfc18a120109369fb35fa2e4abd29", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.9.tgz", "fileCount": 18, "integrity": "sha512-LFKe+O4V8Jfm7GdDs22MicwUxr9hIciK2R7pTGo/IobW9Q3vGS/TRhXlMUaplLxQXk6fzo6tXs/kPRMKpQy9wQ==", "signatures": [{"sig": "MEQCICU1/P9CFw8TyzMLdRehJuF63vghxvt8FMHmb0uNEXLSAiADeWLmaWMlYBIYUMpHYFWWahYRm3Sttu3Zu2FPPZ4LRw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 545950}}, "6.5.0-a.10": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.10", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.1", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "eslint-plugin-import-x": "^4.9.3"}, "dist": {"shasum": "f8a1fc8ca4ced1df5c456c567d0ab823002c74d8", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.10.tgz", "fileCount": 18, "integrity": "sha512-wvUGhLRK4YCW3eEyfD93FK3/3TSk+x2OqWihcnZ2mZXNG5K1EKgT8E0LpfWfVQ6ynl71w9Ib71MDEISoc277qQ==", "signatures": [{"sig": "MEYCIQD6EAWMnvMEPawDL6Qm1BV3euMFv0IUgFv91aXK4qnAYQIhAMryJL1PiYVv++rzVZyI90odpNW/1/NNsQdkZBjee3aB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 543359}}, "6.5.0-a.11": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.11", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.23.0", "esbuild": "^0.25.2", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0"}, "dist": {"shasum": "fc72facd6689d2887632154dc21398b79306377e", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.11.tgz", "fileCount": 18, "integrity": "sha512-kAVvYnrW5JoMIqEoYj9prloLrWDpFPqhQMu0Gdbhc2NMIQTQWjaMTiBRol+tewWjAmABOpYJ3cdcn8CvKqPZAw==", "signatures": [{"sig": "MEUCIQDAMuBSpBmo1NVUwLCKTKql16uCb2PVEHKjI68+PpDTFwIgdOm5JTizoKBr5E71x9rY/vtgUCLslfbnPiNSvPW+DmM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 557461}}, "6.5.0-a.12": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.12", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.24.0", "esbuild": "^0.25.2", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.2", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0"}, "dist": {"shasum": "09d9f6f77f6cf1f8156dde658b359ff9bb54b5e4", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.12.tgz", "fileCount": 18, "integrity": "sha512-GUxJQ49w14v7Fx1RQ5qW9Ol0qJgqTVC13bHtxyn3obKp1DicqsGCCJhPVQZ0dBcK/LvCERXbq1+4eV6c0OnNPQ==", "signatures": [{"sig": "MEQCICRnyTVjlheQJHQ7ixIL9v/5914gTaTU/uYaN9SS9Rv8AiAUPuw9Pfi1B05VQrxWu/5O5bQyXAtlGLxwUHOO8iBefg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 557787}}, "6.5.0-a.13": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.13", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.24.0", "esbuild": "^0.25.2", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.3", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0"}, "dist": {"shasum": "6a4e09d06e0110dc73c8393d35bd25ecf87cf50c", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.13.tgz", "fileCount": 18, "integrity": "sha512-xVE+FGwgDaU83Nq/knoTZPVX1KDAG9y4bpEclbHnOm++lv5GuA2Nuuq0gelTq7jBkWiLVTqug+St7jymuZ3kGw==", "signatures": [{"sig": "MEUCIQDAa+ibrqn0zp7rIDf6yMQt5wsrUWP6VO9mLiKRzYh22AIgXyv6xh69EPrWvIHiGpUVGdpoWoMGCaoHvfzF/8bSRnM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 557963}}, "6.5.0-a.14": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.14", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.24.0", "esbuild": "^0.25.2", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.3", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0"}, "dist": {"shasum": "cd46dc16fba95f3b228e66f69c59dfcfb1d6f5cc", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.14.tgz", "fileCount": 18, "integrity": "sha512-BqtsuK/WK6NDBNRI2DTnXkSS9wYmAky4u0rEQ7tiHhQ4wFAitptn1ZsiL+8OHoIfLNyoD7HoHUpfAwMsSDNFIw==", "signatures": [{"sig": "MEUCIHBkfWodUN4EsbY/Ald//JwjB+LsTy/3yGa/pSxrb/aGAiEA7zcgjE+jkIBetoL7HYwB2HXd4l88GGJSW/Kh2bC7fu0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 557867}}, "6.5.0-a.15": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.15", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.24.0", "esbuild": "^0.25.2", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.3", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0"}, "dist": {"shasum": "839d1b5436d2a74a91ef10562d567f290b605866", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.15.tgz", "fileCount": 18, "integrity": "sha512-5vZkeSv57KXB7iUc07rZxuqzjOXLXNwjQOVBsd76iqjdtr08dY5em7uXu9gReks7kNDkH2CJueZym/QxdwAFLg==", "signatures": [{"sig": "MEUCIQCT/F5WwVHW+OgvNVNVsrOHOn3DU4yafQqrRAlusHgi0AIgTTEbVJAA/84uPTKGWKbUL8XAu/7tNtxvBLsETjDOjqA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 560079}}, "6.5.0-a.16": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0-a.16", "dependencies": {"bidi-js": "^1.0.3", "css-tree": "^3.1.0", "@asamuzakjp/nwsapi": "^2.3.9", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"c8": "^10.1.3", "chai": "^5.2.0", "tsup": "^8.4.0", "jsdom": "^26.0.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "eslint": "^9.24.0", "esbuild": "^0.25.2", "globals": "^16.0.0", "linkedom": "^0.18.9", "benchmark": "^2.1.4", "commander": "^13.1.0", "happy-dom": "^17.4.4", "typescript": "^5.8.3", "wpt-runner": "^6.0.0", "neostandard": "^0.12.1", "@types/css-tree": "^2.3.10", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0"}, "dist": {"shasum": "053c3dd33c9591406ff2a439a2d1c07ebc40d309", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0-a.16.tgz", "fileCount": 18, "integrity": "sha512-jX74cPeoVt9I7PlfGTGrToSvJAsE5XMEVF99yBb1zzog5mcJK9Bq6H7vrQqyMibwHow17gCF/VuTisQAAQ678A==", "signatures": [{"sig": "MEUCIELomL+3OqxTTl0vQ2tXQbN40vjTm17UvfOpaWiWqP5bAiEAlEUbK2+g6T7+0J8V/bcLCJd0/fiS0fICbYwDGSOHM6U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 557867}}, "6.5.0": {"name": "@asamuzakjp/dom-selector", "version": "6.5.0", "dependencies": {"@asamuzakjp/nwsapi": "^2.3.9", "bidi-js": "^1.0.3", "css-tree": "^3.1.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"@types/css-tree": "^2.3.10", "benchmark": "^2.1.4", "c8": "^10.1.3", "chai": "^5.2.0", "commander": "^13.1.0", "esbuild": "^0.25.2", "eslint": "^9.24.0", "eslint-plugin-jsdoc": "^50.6.9", "eslint-plugin-regexp": "^2.7.0", "eslint-plugin-unicorn": "^58.0.0", "globals": "^16.0.0", "happy-dom": "^17.4.4", "jsdom": "^26.1.0", "linkedom": "^0.18.9", "mocha": "^11.1.0", "neostandard": "^0.12.1", "sinon": "^20.0.0", "tsup": "^8.4.0", "typescript": "^5.8.3", "wpt-runner": "^6.0.0"}, "dist": {"integrity": "sha512-xcWlHiFRopoPb2/WQEhlA9dPyH8dWVv9BTsHqvB0kdU+MjVgGWWARP7Oepl98FZ4Wl0eUVE+QtqGqzQbPd5Pzw==", "shasum": "95988c32faabcf6cba2dbf0476d5b204f70e9681", "tarball": "https://registry.npmjs.org/@asamuzakjp/dom-selector/-/dom-selector-6.5.0.tgz", "fileCount": 18, "unpackedSize": 560376, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICmWgOYsqDMQrdvRN+uLbD73C/P2EPpIfN830J+cFHR6AiAErsk2SQ0FQEudIUbMFYyde4XENaHg3XoJXlcXDE3uYg=="}]}}}, "modified": "2025-04-15T07:35:50.957Z", "cachedAt": 1750172039442}