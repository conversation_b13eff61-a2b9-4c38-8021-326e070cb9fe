{"name": "@testing-library/dom", "dist-tags": {"next": "6.16.0", "beta": "7.0.0-beta.4", "release-8.x": "8.20.1", "alpha": "10.0.0-alpha.3", "latest": "10.4.0"}, "versions": {"0.0.0": {"name": "@testing-library/dom", "version": "0.0.0", "dependencies": {"pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2"}, "dist": {"shasum": "5b6f048abd5c1f32eca2c35515292594e7dd882a", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-0.0.0.tgz", "fileCount": 66, "integrity": "sha512-gUvr9FEc3SGHuGLMqcLqJqufj9yC/H6kpx9r29wFkkmzVnEojLan6a3QOETtk8JxhLXbipGpQnT85+5k8OrJCw==", "signatures": [{"sig": "MEQCIEsgKj95xipYwJ8ei19AZK1KWVDzscDL1UYjuNpuLijLAiACc0x2xtXQeCz0uP6nR4GPJP5sK9uLNep/afxnt2pnSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1032943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8A+JCRA9TVsSAnZWagAAPuMP/R/EETRYP2OBXxgGwbCd\nFI6VqPHhiSgM+8/C8kOL09B4JPq3aT/wsUsW05od37Aa+2JcQ2Kivmq/OALa\nIwN6fAnl+wDUkpHYSTo6ABJ53mhVEKaGxCPoQ7CmR+qSZGIwFwixRsJup5Fv\nHA64dF49yL0en7KZwdfUK7rPt3Xxj8UaBO6xTQBWwMmxBaM9Syq39v/FGZ+e\nwrS9PItswgVMteDydT85awB1oKCCZGT//o0A52BhyFUv6gdb4mTEGB1iTAml\ngAMicOUlGzzr6H0OsqhHNznLmGGaIKlXZlPDrI+rxbSfio/1S7GZqwiLiNxH\nGO2pElzJnJkXGLG1Ui/SrhsrqbZ6iswGl6142s87/2npSOYVJbxwu44nVUTZ\ntYQdViOy0k7QsgUIXC/eo4u4lu8E2Ele+GFr3QrhcAjzCPn1A/b6hCkSwLTy\n5z0QKldQuOgV9B+jbqblqcGLgwU2j2trk4yTKSsBAW87ntn5OMG4GGFA3vv5\n2DcUuoGsU6RQIC82gqhsRMchFYCLDvvgLrLiuodl6YFnguk8Yz16pXtI8kkj\nlRpP31bfWvsPtF9Gm2vnJK7+xAOd8iREwBiCKWaFsovmEh0CUvEF8acjBVSv\n7Fe3MRY0TPp8TukMPf7gRcfrsUtdjB26mrDhWqX79EN8MPa4iIRk0xkXE8vM\njwRU\r\n=0Q82\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "4.2.0": {"name": "@testing-library/dom", "version": "4.2.0", "dependencies": {"pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2"}, "dist": {"shasum": "8e2d61e8c25ab816c67e449651b665b1c6366204", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-4.2.0.tgz", "fileCount": 66, "integrity": "sha512-Ny/ZdOZdAX+1amcffpgn/WtbJg1jNPjvQJ91ny0d0wMdC86dhVNgz0pdelyJ877kHx3o7DlITVl80qpBt7RZQA==", "signatures": [{"sig": "MEUCIEQlwjrcoaqCOv+qG7WtHDgd9D59nTudCBQ8hSE2V6NjAiEAsPCFvPhhi9STL92LxyQfhUyIiFteFj62NHFX9q9C1RY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1032409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8BIKCRA9TVsSAnZWagAAbkIP/Ap1g8I7FtJ3ooe5UvOa\nZoXGIDMZhthwgoCATTNnPm4pu4GQBRWJk1i6exfQOAJGf/vSkK4Rd0LSo1Hx\n3LB5NprcE00CTgOaCgwDMOM45OW1FVOySNByOoXtV23E/R+jSCwAJuw9O79n\nu02/a2mZEuNBfh5xol3gsjfOLsv1d5q3HEPJ+3ccw4Nq0YEVo6stUCIn/WNN\nuAJcqnyBF9MTXBmazgxbwMNL7XuAIZcdkKFx9qFeZYXlXnHeee0gk/OgjZLH\n4yPqs5Jp9wgOHrqBPfjdLKovhNgAuXSM94BQqB1NmsHfCcH5HVkUbTB/x2uq\n+s98oUfyzceQi2xOpG5uPSyU2qzje0XMedNzZvyPd1fxqZJ1wUvPChqSYfn3\n22A28ovkV3Nn91gu75gtg3beHSUIaoYbxUE02fdRTdPR/UG7Zf0bHwU/dq7q\nXhmaaeJSGVNjqH+EbgKI85s3DqDkJYaRUDSxl2M0A/UymWsFEhKrnHhxI/XM\nnwX9dc/LYieg0sR1ItBgJycg8oLRElP0LkDPXOtX4odHniTFg2ieMHwxALVt\nhv8QNJo7/KwlhaZ6x0G0y5Vz9MHzjjjKTUG7c3Ghr8y9AIRlzJ7F3nOkdSjD\n7xT5PDw58KYvngGwBYlABCYgNthyST+koXXLcN9LyB9DVxrFYzlj3pbhewJC\n7zZp\r\n=+WlY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.0.0": {"name": "@testing-library/dom", "version": "5.0.0", "dependencies": {"pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2"}, "dist": {"shasum": "7be01af93ebe6b80cac7fc359d9c14b230ead97f", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.0.0.tgz", "fileCount": 66, "integrity": "sha512-Mu0UwXBxxt6TgRKnXnCjBcbEWrB/hy9jovOe+2vXCdvBhn2PRomQNbflaaaHMVpQP+ZOZBxIDUhTVAN3OGX7lQ==", "signatures": [{"sig": "MEUCIHx7C7TLelDA0xpK4zBGwLO+rl08ViFJm2Xu7QyhNIGUAiEApyH/YU0ZmH8nateCR4BQZDaXRGbxYXMxNFaGhTQNcNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1032409, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8BQICRA9TVsSAnZWagAAulAP/iBGL5eRBCNqxzo4q6NY\nbpFBK7yuNp2nFRI4XLplqsdlake0tBALKaaqV6hUvHUHjX0+neiN1Xtz+qi0\n40vQs4slajWbbYaayhDRL/gNEhA+8CwOcDlDAHCcbhRu3tdDPPB1END+ILjJ\neQnHbsLV4ACvYd7OU/GfRJ3JUXAtCBkETYmBz/5cZaIPnI2cPlxUFkV17Ids\ndIByY+v0ciz+sBfLG7pYAzpT8fMOr6dkwUoJxbfwMLs+lHKupEnlWK7X3c0J\nHVangUcJO/f7fs1KlJ7dG9aLostcadSTUoKAskrvNTsI3KVOEKW4LeGQZUS2\nze5R5C4PJLcEGmweoYsUM/JTdx64MdJqGlWXymNaVRD3E1fFdG70QSfmdbda\nikYbpolNMdTy1SSLP/fbGRvb6dq8znzWtdzCjezixggNwvBXYU/X+VXXHSyU\nFaS5gxVUVVDGPAI/QDNzQxA47ADGdpcVGs4xaytpfBwt1KQsqqloa4SB2pjP\nvA/ZRlGxLECIIt6e8vZqn1iYiKD2yiSqZGUihEorjvOLDF/8u9vY9nHg84jK\nUwSRF573DaMvbdAwku/TzNO+Ns4liB/C+gkg91sbnxw4HOyqzff1jujP9nOc\nyaQWvoJoRmjMLnd6PhQWcpHa0UzY4gBzKS8jsegCMuV7L7l/Niea7AAdXIjK\nih+w\r\n=KkSm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.0.1": {"name": "@testing-library/dom", "version": "5.0.1", "dependencies": {"pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2"}, "dist": {"shasum": "1673a56f27fd9748576495958a27b0e5a408db5d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.0.1.tgz", "fileCount": 66, "integrity": "sha512-HmyN4b/PmSaSB1ku0tWjgnTtyrwNBXEpp44wgfNaDhyj6IJTCWp1GAf4AANoLGItgMsYjepwWOdMyuJ/8iyStQ==", "signatures": [{"sig": "MEYCIQDBn55AWsnGXgnICjV1mOSnu6RzjY4J+zoZt7iaiuV+5QIhAPndoUCojSRTO1lrRRYieuz0jUB57dknKwr9sC3KDDW1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1032413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8Bl6CRA9TVsSAnZWagAATBMP/RDio8TEuqq9hJ7XsIGO\nOSWftIPF5NyvoLeU2ryaba5WrKUweVQVF+XGh4sFbxOOhm4Dnan+Ttep5yUp\nB+9cBHzSLGHLLWixA0bURq83+FkOpP4yB97KaC+vTxcfAYmKYnc0T47PPInN\nTJ1P6CppAM2OJDJhUAfLkYXCxAVWSin29XGCs2PHV7foPULekBNQXtWeekaW\nSXSRIHH7movOpMKNrzzEDSM2SZ/GHaJg48deCql1DJAkYhbV5LuFrMaG5YD7\n7wTmUmhgKp3XQG91kMMNqTPnU62FgolgtEKG3JCkdIZUd2//ZYgrHzV7bst1\nxpnGbBZeLYLnPruRD4AIyEOjMsaXMWq9sa26uH9dzgLmwNHJDI4o5ZXdzFou\nAykxJzxlveGFGfDwHaNlcSng/Saw2JIrfaQV+1oTQwTA6jqS1wzjM6LUeUU2\nTXI02qY99AdftS/n2ScNRIpAjv2bKgAtXWarNlkqe5NOhV4nPYpwT//5QtUA\nNf6pni3YVChPtAPI8NXvYsLXTI3IqqjnXxWFGH4EtveVJbp7/cS/QR2p5CfW\n/y2e5TXtg9k013lNdezPQ/lknDkjUgJpugxRML1QpLPeVf4xAqqEsV2pa9bL\nsAJ/Ls+Z4EB1gSfXuvbHv1cPqY3iWfSfz6dcaanTq8NUOBTZkZsfe45cHvml\ng4EV\r\n=ANEV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.1.0": {"name": "@testing-library/dom", "version": "5.1.0", "dependencies": {"pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2"}, "dist": {"shasum": "d44484d361f5df03ac0966195f4bbbd4e452f541", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.1.0.tgz", "fileCount": 66, "integrity": "sha512-av/xJQnYvqp2IB6a3/zQOtcHskbtPGgpm3QaSYPNKtRDhogb/5Ed1AolVSfGxTBc+xs7laAncOmmZCCfco+lyw==", "signatures": [{"sig": "MEYCIQCnAB9m4RDhTbFikEd4Iv9snOXbBozSMPA2c2bG7E1KaQIhAJMJ6TXueRcLp2IPSSOuhW+z8jJO370h1l9tBp9A5fje", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1032794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8oK0CRA9TVsSAnZWagAA200P/jbP2zzbVa5jlrXM37Gu\n4EM3+wljpHJV1Llle1N1Xfkx/gZbNagRJhATeLnedTZyyFTdfuf/w8BsSkhm\nYjY6WfsEZMFweRM9vTFNWle3mJlXy1A10iV9bctLHGnIzxqPaqdPte3I+M0E\nM3XMVDPjU05wiKXNZ0dVl+EKcS1VMfgNdKe54skxKGsHG6cUqoll2jf/Jtr8\ndSf6ynjC5l2oUlBG24muOLd7rCJNXTRVA5xu+mgF9wi4si6KlVYF4ykovII3\nGYiKe29275rdYIiM+/x+valrciaXQWFHooZKWkMn+G/rpnv+IAwv6imweBL/\nMWqcEte+8ClkKMGRv3rZ/MXEQYcEyyTYcZUrVA6lypEnU4beZ8hkeM5d3LmI\nCIh50/un3uLAt2x0P5QeMOKxZ5iLPnvYe6ikfp/ZBuLD7pnOhOZfbRl8/PyA\nnJB8TexgZ6PbcojiBfTxb/ZQh9H1AwnrJhunILeQ1sFGdqksZc/peJZoAlzL\n9jYH/LrQUN312rz58jKOrQ0Y1TwpWXYE9Kj4rZYyeaq5BsWaamwgYbnXRNL0\nv6qOsMi/Bn214OHZqbZGZaIMaMgk4pzk1zb4bS83Ih2FUXcOugrkE5grvHYp\nybWAo4ucO75DbjCm5Wcl5d47gudGxcWIn1fqBs/b949DuiCtzlN6jbof07X4\nfpGM\r\n=1E9U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.1.1": {"name": "@testing-library/dom", "version": "5.1.1", "dependencies": {"pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2"}, "dist": {"shasum": "091a30b1ca058080bf432cd1aeb2b7c646022f97", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.1.1.tgz", "fileCount": 66, "integrity": "sha512-twpAkqomsI0xeOLehijOAmPxeKvs6+WZC/6/nXD0+HNQupP3OZeZho/PBlNhrGL+8nQWiPjdvmxeyU0tq+hctA==", "signatures": [{"sig": "MEUCIGaqLzx9+bOlLV/nUy+68kUEs3fbZDntRi5VZolX3g8+AiEAlLF7TjPKrVq9UHyRYlihzICS8EJnD4R43wj5kL5fR8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1032946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8wtVCRA9TVsSAnZWagAAbOAP/3PhINpVtRAV1lVN3fsO\n0/d+3IF+3pfwa8geSCkaZRM3WB+aFSb+X5VxxsF5x6q3wwCz/D8ZdkmfZsDy\nmzH6qQ9hucjIqpp5qhYOU3YzofVWI7PZXw5MwBMsRyB+V1Khm/lV6dzVMdAD\nTcZDCcRzZbNFuXAiQXuQKPvkaysBGpEc3Iy9V+NfcgfklBLddkITpbVit2ZU\n2LOQccUH39CyBIavAcOTFGL5zUFnUokVFznK2U3sCbSSb+HNqeURcdZGmZlV\ntRRKbc4RtprUBo9aH368HEXHBE4A7UY3isD1ZdQfTHXS3DziGCZetZp78oHQ\n4/WC2av9lmFktJg85sHBRlJcc+dgKG4SV8lUnpFGTRa78FyprNrVvV0eyHwn\nBFNaipzcpprrkV61ugmjpXgRK2JhaVfsEIBKfexPinJo1qVxp9PL2jqyBFV5\n0o6Xzhyd2ZTEAi1ozo4QA5KGengt887GD6UN+TsemuGzXV98OnVSSoh4+UHK\n079i6fVXvXoEpWsXfDLuolzvVepRd70kWX3OmRVc8pKxE0+AvN7tIvUftnG4\nH4EHWgNXUGlbxiHV730sw4JkBTazyEy/0v3vfnlwa7V1ai+KSg4kQFNyfK3e\ni+C+sTyMom94XiUlfkUu4SP1RxYDOnDek4Ho+ZAGep6tro7YYc8eQMwFxMe/\nANNB\r\n=Fjkj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.2.0": {"name": "@testing-library/dom", "version": "5.2.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2"}, "dist": {"shasum": "450bd09f93ddd8865eec98fc5e81fceb2bccdf9c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.2.0.tgz", "fileCount": 66, "integrity": "sha512-nFaZes/bzDfMqwZpQXdiPyj3WXU16FYf5k5NCFu/qJM4JdRJLHEtSRYtrETmk7nCf+qLVoHCqRduGi/4KE83Gw==", "signatures": [{"sig": "MEYCIQCT9n0IEXXJQ6I9CT9KzhW5io1/yg48s2nmmRUqiVCGTAIhAPbWDYGAX5xpMjcGAc2qPFwEnaqX5mYETSsoMG5hXAAz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1651971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/BEkCRA9TVsSAnZWagAAqPMP/2NAo7gvG7fiICfndkyj\nASIvDxUjD+zHvrVKRCuluAV9F5OeIVB+W0I0rCohVxYJnJgRMen/lEG1P0aN\nA+sR7DhW4iBkyLKmpTbw8UQNQfKUpyLj9urTNrpcFH16M2KrbDukRClLY433\nG7mjqEeWPH3EJ0ezLE+WQ/7HmVYMdr4VcQ+IdMQ8QCa525yi1FIPgSxmcGRO\ngQeaLzzoRy39L62OZA3eWk5zJiTOAFVvlcvBw6HfEJ6XiWkoVWzlFFJ7Sw1p\neA0K2dBmQVQUsrMYHutRTsbsxp56BXQG21FWclTNunymiWrmD/QuB0kI7xa8\nEMu8V63ohiGYbDOQ1FmsOP2MTevWT3pcy38Q7hT6vyKJ94bt9xGOUTJZY8WZ\nvV5nzOOYnc1Fc5V25CqkJJDSGWVFaKLaRPBk2xw/kBgkwzQlUbvQ943kyBmA\nqu87yAEdjgo3qaf3xKq2FwN2f581WEi6ga6IUQR2RlgMP14f3sgdup+Lt2ch\nvwsih3M0QGZr0KJtecTfzYT1iDOtJCS31MoDM+X9oHXy4xseeXR/8CzlFL8L\nnVtPCe344ly6UVkBMnSRPUWbgnjKq6h37npkMXDhrEiDTiu6BqBjvuiLw1rQ\ntieVJLDuUPkyNhvgFSu60eH54nQODfwDYXJFybuPdSg+XIqHJ7L4GSxc5YYK\nnQOb\r\n=zRFU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.2.1": {"name": "@testing-library/dom", "version": "5.2.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "3f2af5229af106c0ccd5078bcb820958310604bc", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.2.1.tgz", "fileCount": 67, "integrity": "sha512-K30UE+UKwyI/iTaQsSJXVYBPnPsTLlcNT1QEhHKKqlsehu7SzWTSkCE3xW0qvMPIkPb3/rVisDx7Viez/qmAKA==", "signatures": [{"sig": "MEUCIGRepfojpE9vcG9UH7SB5+iMP4AvUssgNdHaDfcDNjHeAiEA6UrZ+7+7SuTTZYK+oGBA4MKalizwvwAqTyP5u6gsFBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1656342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCrfGCRA9TVsSAnZWagAAIK0P+QGFwwIKJyLX6ug1K4ma\njDuQPb/s/iNGE+nNPukKRDKxixQWqV995HcdJBVQ03oP+yDTCWU/bcj9+iyb\nszY7CIgnNkLMndo9AW+B9p2uVGGnptJ7gyrRSBwLYpHFogx42uIz+cG26vQu\nQdFra14oZg0NOzqpiCu4hItpH0EhsARrdVHA+L6UBlyleKhh5nISwFRVuJTt\nPxczhHqVArUpwZ1wAn/QuxuKry/mnRq0QArjTeQFzG+Yv/t5b+cpOFufa9ja\nUyA9RP0jFt4Qem88di+MJG4IpSFtm8HVhUy234o7d4479gxpahVlNHs9fWKe\nVfrXR1EDgc7hbZOukqepHfDd5itRuO7Fo7oyATW4cYE5O9uUq0n8hJyXdjPD\nK5QXeM7uHy7SktN7CcFVFkkN9OD7UWzaewz0Aj5UZYkX5YenkQ5qGYZGpD1I\nXnFcwZShiDWSget8MrFN3Or0lJ4M4oVE/mIG1Dd5x0iz/Ez86R18QaR8BzAd\n1bVm+Zu0SgVK0KRs5F4guPdYHt+cvreotnQpWyTDXjsWYNfKXRAkci/+HoIC\nEhCkn6NQbGVbTZWtTljp+8FA1IbTe3RSaByeWzmcdWrrc3UYMl2Qn6heKPfS\nACp0lNya6FcgCJEiQHb+3eiooriZ6n2Ag2so7osRI+IIE3jbxFQtfqNB9aYb\nVikM\r\n=Lssm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.3.0": {"name": "@testing-library/dom", "version": "5.3.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "14ff830902bc331b60302a3d87120cda7e704b2e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.3.0.tgz", "fileCount": 71, "integrity": "sha512-+lUZK3e5Ez7qFLMZXutvMN7yEq7X3hWQecRUHVt1cEC0VjlHOBbZcylbBcYn3WAlVybYVqKnM2IJd+GPyC+wdg==", "signatures": [{"sig": "MEYCIQCfmdoEfIy5wem7o8J7TsBwVGUa4SlguK2ACe6Fe7oFMAIhAKqQFJsryjlB29PAePi928bxhkgwww9lHPuEJv2XmcsJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1672464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEOLBCRA9TVsSAnZWagAAJ/oP/2dlMKlsZQJowLfS9zfD\nLMAMXhU6HJIFrq1yZgTLlPIYuHWmu5PnQYbdHXZF79xf3t/LNqL95noPW43b\nvZD2SoeudkU4LkJP9CsgaTlg7pgCDdFuIK2LU05zB1/vgHL8heF2omsGfUOv\nW2dezVcPrusYJa9KMv/E7O5mmsrJgNrGPcZkAIiH/yc4JWsmnhkw1Mc0NIA4\nVMgLCRZfVbqBDh26HYeSuNdQ/LiUezzpUbjraoqv7C6efkLTxneXnB+c5d4o\n9Ibftj9AD4sJ78xw2Nwmjbq+fUDW1mifZqThPJMhRvDqiwDZOJ3j7ynwIjQY\nTk8hYzdHU0fOExcG1goycS+44ySCFK67EY4uoFBR1+jmhhEwUrbHB75twzJk\nsbFE6At/S616xr6k10kgXxCsIMtKYqr6Hhs/2Odh66FuxqYBNU0+7fmnlqNN\nvQviSHQVc+1FWizqIhOMXCPMhP2wPBTtwnh/FYKdAWv6kQY08dIepEuNs6gq\ndAeOQV55QggyPiygxeIgHiTxYZj+nmUbpewi1vHzMzR/CohjexnW8bN+dQ58\n1WFmrEQvcEH08MSy0LaFCXvA5r8JkbvdU2DdzqpOSA+byJsnPmEryB4pbjlQ\n8xi7Ny5+kMJ+eEkm87vW37wdsYjpWZVghdgGIw2cdOsj7hK1pv2ilNk1I7C6\ndE5i\r\n=OqX5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.3.1": {"name": "@testing-library/dom", "version": "5.3.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "568aacf0e7d2459dc6568fc5b971e005d6998697", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.3.1.tgz", "fileCount": 71, "integrity": "sha512-iCjHQOLXNrA/0eR5MS1JUhium8xP5pIkiTJk87iFoQbltSfAnSIQcpasu2+paPbvV/oKNUi8dG645dB3UGyGAA==", "signatures": [{"sig": "MEUCIFIM+uJPwN30Mj89ssJXYjIjfXCa482ZVeGCkNrekOFPAiEA+5FyaC7arD/88Sls3/VURqEm+uMTJpN2OsS74xEIYaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1673376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEOpCCRA9TVsSAnZWagAA8TcP/Rh+2jSgHruVxWdD6H7z\nXbsN2nQSOqSHB28RZ5o+goztcHnDKH4Iuku0CK0w9D54tTST0Sl6bm+Makn2\nopp69+BOmn+tHtR3PfMmGK2fHs3I9mIKsY2ydlGm5EPqbEU4A34BphGFz5Hp\n4A1vND6m8APyD99MRhosjdXbtvSzOCPHv66hdcChJZER/4RL3WUlgZxPzLXl\n+juYrFqjGWMMjH8uVak49NiSzIR0uYKQyr3SrybrfxZo94gjMXRZzYzLjE36\nOw2sJJBpib5gwaminwtJBOCUlrGEpGgM3zVLlVaxXF7LlPFStTL3vUicjhwN\ng9OsV66hkYwH7/yRzO/VtvJlMaG+liyBLKpgzAY1LTexgAaCLHsOF5kvpKHI\naG6A2AK8Fv9vqePYsMUj4c9vW7UcXMUsJYM7dNgg4DWt7tX1ftd9fnm6nuvN\nx1R3xIKUxEP4ad8mMZVYFVJO8eLdfvtLWzKpUVw/48MqiDdopsHBoZVXY3Bc\ndd6M1Yk8mF/SKSazOTHPh/4vdB31z1v1Cfc4fC3/y0G9q+Yi/cXhAH6lcx7V\nxRa0n4+IvbTNaBSZ+dS7Dh0WJuGijzRhO7gERe70lYsKs6hgfSIVcYuqTIPT\npV9jmsi48FduJJfhJZj+/ZWEYKhh25WMsG+2wgjTWgOltqepLBeRMa29wAcK\njpLS\r\n=Esp3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.4.0": {"name": "@testing-library/dom", "version": "5.4.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "49f41c99473286a4102721242bc47571fb7efef0", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.4.0.tgz", "fileCount": 71, "integrity": "sha512-0OQsquNYfbxgqqoGf9RZ9lglXEYgKlhSe+W9UFQGDAvT554Y9PG6hGe0RHYggAXe/GoNPccSsl65nn+qq0cFKw==", "signatures": [{"sig": "MEUCIQDBCZfM5p6V8CLEhlvI6z+5qCc5TiDRYC6MEy/BRU/4qQIge8feZIWW5Uqf9cr/PgKLi95/ssIO7y/X7uU7MD0KgEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1684555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEO1nCRA9TVsSAnZWagAAH+EP/ij0bkFb5aJufdXOknGC\nLoRLH4YvOicQz19u5W+/JAixvZaED9Pt6bPCRx34xsdftDznC3Tff9GlIVZI\nvpa0at7wZ3xui2T4nJFWPITK8ArrUEaTISM4uOyffN3CoN2hE2PzSlBSm1FG\nyctIu+PXDS5/FlVcjPOTUXsNDn/tiPmYf74l8hvRvvAP6PFOnNDgwh3MMcjd\nmUNo79getLF5zfvZ8i42EzGHssauXLduBSMDxQDomI4PfHPe/TgDWFwI0wC5\n0Jpq3os7dbExRqOHVeMM3qQ5k8hN3PN3jykUC8R5XowbUddV7KQg7SPWfSdD\n6GvXZV/M71SfsmvxWlmX8JmW4dStwjmUQ07bKp8+4P77FDnqm8an854Q8bIy\nOe/8F8ygMJTQbLTnCqZuCLUhglQLRqls1hNx2s5+Rfjj2wiMB97EGOWiXxzB\nmB5hyA3nOC80tBteoGF26mG7scw4+Hu9VjlbQU0Kp6grLgMdvMBfbb481lHY\nWtuW+kwrdxlQAeE4OoYa+0VbT81GdzD2myigwtV16wvq0nadKAznndLYniZ0\npF6AMc7OtxRPExCBF7fJPURIJsHk+6Q+wPiOt9TnJBE9sGknSQqHOYD7R9Ls\noiI2Bj0783E0e/DGXsedYPCHnXbjXaA/Q5aV1NOvp8rqRBCHoEts2eabzhmh\nwg49\r\n=bqQa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.5.0": {"name": "@testing-library/dom", "version": "5.5.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "7c5024116ca8173187b52c538648884ac6b7b2f9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.5.0.tgz", "fileCount": 71, "integrity": "sha512-QuY/XBp9fquYXP1jklKlG0nUmFVLJXLWNYANmoFs25RDystdujLXxXSVhacVqL5oIF8ESThBzHFX1FUuV/J0kw==", "signatures": [{"sig": "MEUCID5+o4jgXxnivjN9T1UyvL1Z439pp4YRphaFyDRqwwJRAiEAsoL/AHMIX37h1kgLRODlLU8+kt+U/j65grYvHowY/P0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1685667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGkMACRA9TVsSAnZWagAAZOQP/iyIMednCm9Tkn6+vO37\n0dHPTl94h/XBSA8CGHt6tGdIFKkltW31jaG3CRF9S4wmYWtXHVm3TqhjKUai\nkDWb9n7p2i/i28V6lXEkNtz2W+HacVt7HqnB4KNZVNJBUYTnekBsVeYeIqQx\nsDSBW6w9DDbwB7gNlZ6ybyoXFAdYU7aIICryUcSHHRFFMaM3Fx1qWPiP3eMZ\nu2BBw65O0LpkiQNAK4qwqsRJjDQDzbad9OawrGWEc7dDNTqFf/jjPbpFQ4aP\nS2upbeaTV/eKrUiy4wEGhbpC/SueMtCGULpvZKGLiarUF4dGWu4HFQRvqGej\nEv1QmkkvAUZDXtgWGWe+E00fqqdGVrMaLokNQ5SKlxMDbpw0Up35Tz0D7dV1\n93CJHDSpLA0RFA6jtDpdszFOELb34e5BX7Ko+PNCumlXluFy4WrFtRD1hv5L\nQNs6exL39toCKOWX6S24iCrIuPKxjH8Wn4h1IcUwIeeJ5lvEclPJFa6X1Hkf\nTFKH7kUpFZu+ipxhd5fDSG3Bp4rORh8LsFEu48JyL1aoHKtdL0NN4i8KkHVX\nu/gS0CJPm1gg5TpvIdNH7+RUoBbVFps4YkjUHYOFm5Tw8k8E6vvbd1fnXSKd\nLVc705DUBYeC5bH7R9pHHIEj5WLuDd3haEZAWaM1Y2++DTSSyRBAtewauFAA\n5Km0\r\n=kFlT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.5.1": {"name": "@testing-library/dom", "version": "5.5.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "ac6a5244b8b7af3e7000834abf1f79d26cdcdd1d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.5.1.tgz", "fileCount": 72, "integrity": "sha512-FE<PERSON>xR51dCBixz8WoIlTHJucLYlVSZ4oMaWcnbID8SKRy+07JNACwUDHzpeeQv0853Hme91niHnvNebwGWZu21w==", "signatures": [{"sig": "MEQCIGfA+qRpFbZS6r2EFMjGHX7yCxCkvc+12BofKi+763PiAiAVaYQnguLV29mu4lnBOU+N20x9z6wf1/YjKzFa0Z4Mvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1686386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI2QsCRA9TVsSAnZWagAA1e8P/1leMtwZmeDGm5uqtFrr\ninGc1lH2T7x5ZuNNqS9bE0g7G1o78m77Dx77OqtnyKXNpSxD/5wVmTg38R5L\nKzU+7SPARkwxVbo/8cUw/VDjcIouMFA1G9RBCs4s1z/bOHuK9iWn+CdB4RWd\nPISrUktJtVpyoLy9iWYsthbvv23/VU23ltGEdLfJ49yURdpO4Gz7YX0laGby\n7eQxL4YNmo2NmLSEW3jtkIe9ZfCXJDoA5/U8vMToldw1DRqe6DTi3jzqUhcH\nir2KsakAqiKB/XK8u4YmUwI6Zp9mvgVzA9fTDAcbXMgf7B9uVHg4RqJxxhsj\n4fEPRa4ELBbpprxxISjrOfc0a9HItYicbVSFzVWmQ4gbtTFcMgXca+P241aM\nuQewmimuo062y0aLkHPlG4oNjWJw4UDQJqtQKBFbILJgzt9OJMTI0x5khazV\nI6YRDBav2Y/c2wZyNWpOYdj2ZUsNTf1J7BMFOWM7qaTo9BrKVN5EQpga/JM/\nbaLgs8RvBZmTPQeDvgDN0r+dWDzgr/CFpixcyTBd5v/2qT0AGwO9Dwjqosu8\n2OOPPGx0zsTbvl9jQi161RR/dg+VFRKlROBDwd9wCLbb9luGAooJX1MxRC00\nZ0RgbZ2Eahrou+DqIu8CefDHqi/7Gd/xhIJ+YEBCXRGqEfobJJwGTeeGJ9DJ\nMOhk\r\n=1URc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.5.2": {"name": "@testing-library/dom", "version": "5.5.2", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "8adc02bbfd70bd2b647aca63badc86e8fe3b24e7", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.5.2.tgz", "fileCount": 72, "integrity": "sha512-7Yrpqr67sN/+96tCEZx/0rZ/mD4/ShyF5Ve8eNWhfSJQf5uXyXqbMzDov+rrAqvk89cT/r8bal4XhHwAySH15A==", "signatures": [{"sig": "MEUCIFugmgO2QRMlJdsOtfdVPyAA/E/5ueBExtopzhgcJ9nmAiEA2+hHILpxPxJ5STeg/IxmNUnlxRE+Qh97DQvXjrgbR1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687157, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJhBICRA9TVsSAnZWagAA30cP/jq2h3jLq5gLKNPRvseF\nzrpEZZ/AtwRV2xcpwgpE6/rjMk0sylC16dmXNtNL/3Z08CjQPl+Md+rH4c6z\n56DcMVxhDQGLNVC7TtleOKxp1UcuECFiYGIPdhM8bqXuaq3vV1GsXyl3NDzh\nlUgj89fpDKUT9LJlqXT5a9Gh67PvidVNdfQgzT4An1OZKR0/yE8q/nXQLds2\nu6IlXyWaGU3udUx46BJ2lYO4H6uOIkk6AaNSCmh6pYUr2y+KSoAB3trvIBS9\n7vV47WUEFc5FofLtEdz6Hku8x4EqTIn8NBOMFBTg/AgtZnZiKeEtzWBvb9wE\n+67vF/fRxrLzhAUdMut2A41/zqu8O7lk9Pk9ZGgqx0M1/vddzktwQQ4WUmx/\nmhATVMfVDCLUaGxbC/dLDwbykgObHzl+w1H2jh4cj67GvEqWBKEQNEIS3cDM\nmz93P/PZGw8sucgRNMDDYDSWOx51bNFBmiFyScGr+ElLRb6rdYVRHfKikkNg\nl4bDoL7OLZ5jV80wftgpOvIqIqtVrVz1yrfvtDInB4mAToyoProsmTh4XvjQ\njpO4ErUQgS5Apun/4TmYXDWK1DmqKg+r131QBtjeQarZy6+8/J1uTHbDQYcq\nSGCinsn8qY6BVpfRATN2PXSujheB6/B9GLUzEv8JcuV+nBUss+MqjXuO5zJG\nHptp\r\n=x0hh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.5.3": {"name": "@testing-library/dom", "version": "5.5.3", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "e601362fb2c5b194836c2d6851a35075b086fb68", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.5.3.tgz", "fileCount": 72, "integrity": "sha512-qHgb8vU3KjOO4GXkRdi70dpINNGRN6w3eyXDUZzScBthLFmVBoO5iFQvtlK2DiViTefKDeFwIQqJ7nxHmr/8lA==", "signatures": [{"sig": "MEUCIQDI6SUSQK++H51tUwByKcvFnhBcbnX8zLltKUIbsdr3aAIgIdNegjYFDC2owcvcsz6RhnEJZ01utggJoh3qQ5qkPGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1688152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJ7tlCRA9TVsSAnZWagAAKp4P/20z+YWsr5J/uMoRtKZP\nL2tPQAccs2ift/1R96zBp3zlpLZjWLGQFtBgAmuLM7Asp1o5bCMCBn5+GWUB\nT5Ea7hY+h6Vc+awZM97C/Mj2MZfcaviRZfgtD9iJ6FnikpwBhz3EdlEx4Idj\nrY31ek0HrPG1CLcT37XccUzXWMdmxwaSr2/ewVzPSczgBlq4RFXTgn6WaXmf\nnSeOaj5fF5kLNEYnBGPgT7ro76QmviGM53FRZ9I/1oYNhPSemfZj8k+zxXex\ngGJomMTMaglJOowvGp1NeIUI1lsBCcKADfq4/K06RM9H32nVVMQHpzhzHA/J\nxaYb3V92Md658wxJVWcHv060P3CYj1TQU2g1R5U8m+MUpSEhJM0YoYhx1uCc\neHYs4b5OWxoNhAXfWKNezZ8AEvgETsDV4xJKWljUMIKHyZk0N/QF1nzg15YY\n6By7aYmxx6KLvnHgsDoNjN836ST4MX3Q76CPIvsj5+kdv8l2kV0LUZGi3oYE\nje4TAjPRS9U3dB9l6kpKZ8X/RVWMsbbSt+q7xk7vJRr1gHm+WDtgG2JYpDHh\nULrKn7q3WciZLZyxBUsvz9d/YhSRco3sPEXBZZntHZ/RoSUkYaR/Fhhp1IAi\nS9Q3kt9wUEND3ESFb+4nVFsl9/RQ4ietD7F/bJ7gskUbIe8pQF4TdPnoWrYy\nOr1u\r\n=abab\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.5.4": {"name": "@testing-library/dom", "version": "5.5.4", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.7.7", "jest-dom": "^3.4.0", "kcd-scripts": "^1.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "36883d18bbd3b1eba67699be0f5e76124593cd97", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.5.4.tgz", "fileCount": 72, "integrity": "sha512-aqHMqo+4DpFWnpycttXnv3kms3QRx8xj17YTLNvs4v8BVlhGfFhJcKDmVDwFu2tTppcb91LQ1pwzfk+oEw0M1Q==", "signatures": [{"sig": "MEQCID7vAQsU22fcTcRZEvhmOy1MgXYe41oKZsFdvJpqE3viAiA4XnZ4FoTW6SY1Pj1AZ+MpKFVhOxPVB2HIWRSvKUp6Qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1689119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKJmHCRA9TVsSAnZWagAAHNwP/jZlzJdPmRSePKRbN+AA\nqjHJGJKcD6jRWXWtcdApL/9AVSIkvVcw9Ur+Bgr+vGVtN2ikUnlv2V7XJVua\nzyDHaZ1F1iL5UFZG8yoDniYO+NbuGXtfOYOTTjgy5RlOJwYvfILiEgn1MJd3\nT3wnORwzmQDIUGzkBGdHe51nsQz5GIA6gTpD6hIGENAF4bxYzn8GG2c2fYLr\nTNKCVvLhCyd6u4dPnfeba3BjDrvwE399u8Md+YLS4/Og9j5bFDdi1gH3gYg9\nBu6jxYQ+x4EdUelfhRKAtlYZ200wWC6Kz/4Z79YVQ2dmW9jaebC8a/pTvuQA\n0SuUT+DArhHpWJSL+AstCGcj3FcY8rPMK6wQHEgX6qD+d39Glf0AUmeQcBmy\nVC6qHZOV9n5/vdRhQi/JfeIGLoLmqISIxLGrXrsqmETUn2gyrP0/VlwxwLHJ\nUxkUzADwLTE6XFJ8jsEoAnXj6vAYq+FnScVG652N4NSo8iGzB7GqsYUeRj0N\nUTvGUrhQsO0DcQ3entJlbwZvsYF75CKjpEE1sjKbZDLaaPWVyLfSfW+3dt2P\nmm6A8rOIPbqENw5H8vzoYYGq89eGY+BiiA34CThYhW8L7OJORRvDWvS7uIoI\n1MXShAuRsIe4iFt89uhs3SQEj2aZn/1ep8T0DSyns+Aym07/8vwYWkMK50su\n7QI8\r\n=Pk4A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.5.5": {"name": "@testing-library/dom", "version": "5.5.5", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.8.0", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "24b9cff64f2ba2dcedb0273b341b56639f4307cb", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.5.5.tgz", "fileCount": 50, "integrity": "sha512-LiiGjLnlba3p7NueFPEszE2v6pHRidinKJ/QLd9RnWek3FjGui7W+VUqTo4MLN3od8Sc45rRRJb2XciL6b9dwg==", "signatures": [{"sig": "MEUCIQCLE8azWztPnx8dcekJuhDLlyZL/v4p1iFMRSXUumKehQIgZqFxrIq3R6PLSf15sJJJ3Jrn3hVbEcm1VGkgAtXWZn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1615362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKK59CRA9TVsSAnZWagAAT/4P/0BiU2OREw8E9Tl9Egnr\nBW8kpCqxX0gBAVEcMM6UnE5FHadXUAhmAV3I9Ep4i4fNlCoaCPcgp+N9Dn5g\nOn2Cn9AsbNqO3Pegah/QHH+HYz8RA/cN5Na1znFrnv3Kv6oJnL9LL+DQ6l4C\nyxKgTRNC9icBSjuTup46M8ouKjpQg4+DJP0APOqlcs0QGlUmR+JYlVqDNjia\n9PtRXpvB9NNF2stvC2PWYdHeJ+LBW18BpT4oHYccb5F91BsjHVpw9DDi2Ywx\nf8J6rixTv6IT8LIWQ54HNY6gA6yx9jGLWocPgCBB0UPLCjPs29Vn+YCfC+sI\nNqb6xmBGoEcmXjG+xCRk8rKHF9BToW5o/RRL/EVgHacorhz53/IoebdklnIZ\nkYh1kby/ySqKVsh20xiNZ6NodZBT7qvsFqf7CviuNfoYhBWrJZK055/zn38C\n8TYs5GF5kqF6bznS6ZjJSHTcvhGcTSKbtvJExixZN0h49YkX2LsPXyLRxSBK\njj2Eni2neeBeKYFkvL/r5/w/E1bzYQNpCdvaCvhg4X48ZMOJr7VYTw9LdhV8\nWbDHi4g8Yd1EtRwc5N+l59CEn0O0+kh5S/NrQ3Y8lUSOkF0Uop7gwGJJAYW6\nCFgOd1+ejllsxLm3oHO8oaTeEW/gx9bVpMZPmYY9zL7/DBkRT+9xnBEkJ4dY\njVXs\r\n=FkZr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.5.6": {"name": "@testing-library/dom", "version": "5.5.6", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.8.0", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "9b8f3fcc72646e49ba54361fca1526dc3486490f", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.5.6.tgz", "fileCount": 50, "integrity": "sha512-gcvncl8vyuC9ZSjDZGdcq86W5SrTtia8ID4ErEuqZQ0StG1bQlfXhl1726qf2C0ALFI9OZfRazbeeIEgJlzyGA==", "signatures": [{"sig": "MEUCICnX9BVVfs7Ma49nYxGHFkZXtZ3ZMQBMwhCXwEOowtnCAiEAhQw5gh2nFVbeLmTqJNj1zixjxjWnv7fBoTz9SWQxNdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1616094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKMLzCRA9TVsSAnZWagAA6ToP/iJ0VyFeaRUZE5QsPawV\nh1XeaqtbUYKAR9xctkooeLYjVS2exJydpYyf/oI3GBTxEgz96o61iA3l0RWv\nFD6Y98oTjTIDX0osuTI9o9WwcAkeAh8WB/pkl/YgMbz+B66z2Euud7i+LmL+\nhUaUJSSHx206DD4ik9E1fObQxIJ9lX7zE9v0Lqji3IatVgU0AM9Y3z6lNpKx\ny6+WvCw/twfNHARmDebKuYlnU56hcn+FL2Op2T4YJE8QtaITwdv3nx5DE4B6\nMBvwKs7NCluXKY26tUsCD76CnuAIt24PFtfyyjyh0uZLv1YkVdFMmWi8BLE5\nvu0uLXemKmQhaQorYAAsrv6MwR3QQqKas0xHOhUepbnzNXXkESZy80o3q1rj\n3/lbuTDvLiK5T0sVlhKfwVWWo8fOpCXMdWbK7ZuQz/vT4CgK7uZ5jJ1SvLu/\nPTBBzIKRkxm6BmaVHiMIRLzXJ/VOcOtySaOCNWaBJxxPoxgvhC/AxJwsfIrE\nfwiu7j6mJlyAkpQtTNkxSA53afuLTwYpCcd1+tUicMktxYCCJPeUQDiFXBy3\nUWWTRPvs+A3KcZA4tIOgz6cICRpv3Yo1FO55nMpDuX/6CDSSjcIvKHAoQdzz\n1kQ5LQo/D5v/O3ye4h2hFLF7rmcT8xnldP+b/h13kpSS2b3sYheA4ocQ0i2q\ndd1i\r\n=5CJd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.6.0": {"name": "@testing-library/dom", "version": "5.6.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.4.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.8.0", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "18a7c162a6a79964e731ad7b810022a28218047c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.6.0.tgz", "fileCount": 50, "integrity": "sha512-nAsRvQLr/b6TGNjuHMEbWXCNPLrQYnzqa/KKQZL7wBOtfptUxsa4Ah9aqkHW0ZmCSFmUDj4nFUxWPVTeMu0iCw==", "signatures": [{"sig": "MEUCIQCB3zkXFkamQ0QxWuffntl8nVnDJMS9u59PTB2TfLGVrAIgDB1RlqMTLFYHtwkyQsfqGQA42dUgPhazP/0jHpElztY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1617886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdKMo0CRA9TVsSAnZWagAAHpYQAJVPfDJ8CwDjWA/SrMeP\n8dT0wkFfmdr13XinJuCPpRlkQTqFNFh6DRN927m17+G2uLzVxCN1Cm+O8QQR\nyoghBGIgRNkbJhyS/8fs+ypqEFDEQQMYSnAlYMqa1mYM6D4ImoiamkT4lz4+\nutRITyMCwzuUy9VlyxI1O5KtbdOuxFqh8GwE4cosho0ug2vu7ag6DRzJNDxL\ndZxNkzgM5ii5m9qvhAC1XhtTRmZc5guvpRHswbgX944kGHsKyAvmo2VC4vau\n8t23fp/h1UmEX6l1gd+6d/fq+fazvz9rh4w+afW8PuIs8KrNYfgTjZhMQuxj\nIwKmJ+Qnvk7w0xuQ+812rCx17ocstQz2ZB/E+VARI+D/yPzCZRgfzKzHwu6v\nmM09iNRrLAHc44zz3FH3CDnV+QA+pxpvkRCw8H8/ZTjogiuSd+9a6WHcUI7I\n185R/4UJzQO16pKqhzECZfiSCG7kDM7nkT6RfFc0SdJ2qbEcIm5+wX87V1SU\nvtzPLLMmz/JZSydYZGosTPypu9ZfLQQd3+OiAN/8POlfTp7vEKeKL/3ynuDJ\n3HJU9r79JiTqZaoTHo3/UE/StfWHcw9zFM0UKs6OBAJ54YJnnc5giNklSx2w\naVScBunO6EtNJRC0/IvH4d9A+BNk6OEmPkcnggpZwAAClhQLvxTi+IH0W9AG\nLHtn\r\n=E89D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "5.6.1": {"name": "@testing-library/dom", "version": "5.6.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.2.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.9.0", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "705a1cb4a039b877c1e69e916824038e837ab637", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-5.6.1.tgz", "fileCount": 50, "integrity": "sha512-Y1T2bjtvQMewffn1CJ28kpgnuvPYKsBcZMagEH0ppfEMZPDc8AkkEnTk4smrGZKw0cblNB3lhM2FMnpfLExlHg==", "signatures": [{"sig": "MEYCIQCC5dUyNrqmRNqIjnjA1Z9bgGu+b9oFc+Der0XkQ460nwIhAPXRSvg2UHKyuvxVqFwFXvDU15OfSGCvnTQKUEAygM+h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1620129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdQhuKCRA9TVsSAnZWagAABNsQAIFYQjoFPWnjr7WHD2Ve\n9R1yn/Csku7ogF5CmOOcVxBJCT522/tZBF/8j5gkSY7o+TAw6MLq2G7unfmD\nOQp2eKlKpBN2azCR2alcK++K/qc6+T6n7y9VCAowlQlyoN2C9L9XrPXXEpSk\nNXxkaC2gvrSaobomPN0H2pJO1/jU6L+j8OtThyH/tlxA8d0h+OOPV8U+zcO+\nlW9W1zDqz4JhICcJfe2/zQeGbT+Mt9jMh2uKV7Fd6IdDfyzgiST3YVG4f+ZC\ntkUkOCujHZmI5HWhvXttjIlWO1Q2A9TWx6pCqpSKf7S9TXVyVGq5Bax+cxQt\nNP+Pwby2a1ChDHsu1919TG9w/Bx+wh8k2FhvFK+SIfQWYR1B5PcM/PL1VQI5\n6fskJ+NRJE8RWrWK8nBrXLFUSLBg1JtOMqNQ4Kfzqgf6sh9ykubQjy35Hbqf\nSRteJwD0tucFuKqhFy9XCRxfP0Q/JLJFQ59YK1ki2tqLWcEEkPhYatL8uY3E\nrohbND++QfNrvj/6dLgIx0w0o3jamvzNacHiUvHXEaH0nklGPvx7/G554q3D\nrR0bLxoxRbVhu0rJ3IIKo/SrTvmJtlAQplmWvCKmWGlE4042AogK7zdUoojS\n9c+IxgudoY9b7G1R3CLFELf0ZI7K2RunBWocI1v5nhnFqbDI++f8+UPjcMrW\nHsEa\r\n=nDTa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.0.0": {"name": "@testing-library/dom", "version": "6.0.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "dtslint": "^0.9.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "34e28e69e49bd6347fc64a5dde4c4f9aabbd17d3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.0.0.tgz", "fileCount": 50, "integrity": "sha512-B5XTz3uMsbqbdR9CZlnwpZjTE3fCWuqRkz/zvDc2Ej/vuHmTM0Ur2v0XPwr7usWfGIBsahEK5HL1E91+4IFiBg==", "signatures": [{"sig": "MEUCIQDypifmT+rUB8xw7h7qfzqgdUV1GkefvOgCgY7m2CR6gAIgZcwT769NA42KJaJxfk1uReFs8mMY9yUHjev5mznZY1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1619795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTc0ECRA9TVsSAnZWagAAYfUP/2Yldvak/sKkA0sMiJjL\n/5qSjY4R66gSVrjrNN4ln8BIsQTV2pHiUlZHyPwuNl7KpwdUU2TXVWEl/XdJ\nFpPq6QvSlnvezyphxlIMVJmgZ8Pz92Knd7s8wFR/5CEw2mBqZl8maTno20ew\nZpHMFciE67u+ZPllmdIEFzTsoZXYb43TlQ0aaI9WKYv5CugF7ElFKvRvDsjl\nZ83o5HEl4xWjfuTG+2rSrLKpxA89nDlSNnYNc6MEx2qt1gshyEZgHnWt+7gj\nYUB1HNLV0foJ5WAMt5xtLrzVSdjkb78h5RJCR3r02ivuzLmhJ+1wOevPVcnR\nEnTib2zbkIrkbDlKX5OmBKRzXrMrOJjU3y2+V+cecLpGRXB5tKNzIwWsl1XM\niYZKZ4TiTdF9iT1ta1josQydy2VpjAikWLljoZLHKQRnFUsGyroUWb69Ding\nmlKmVg3VQW+Rnb/FCNpdmWl8P0okHRhJIl3h3UcKmqQcJD4MGZm0en92eAn1\nYjjjSKncxJRbV71vxIXNgq/LAKSyBsRCNifuhCztryYfXJGk+q9xkCQFMbNx\neY0b9gYelgcbpDRAHEPnhdWgexbA+XxbtAplaGPy8lFlVSE0tqf7hYM3SZ+H\nMm8pJ1qeaNR/+YV5fIgcD9B8L1jtpqwaIkltQ28Xba1mbnt4ow2l2ByMJPVG\nteOk\r\n=pjfz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.0.1-alpha.0": {"name": "@testing-library/dom", "version": "6.0.1-alpha.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "40fff2928f85ab83d597312d92c68841765beba0", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.0.1-alpha.0.tgz", "fileCount": 34, "integrity": "sha512-grslFXz/RmiVJJF2zUb9lGvN1g181dE+Tuk0gzzt8yuuJdZelgADFDA02lixEna+8ZteAdqWYg9pVmPb8dOrbg==", "signatures": [{"sig": "MEYCIQDs1Xp/wsljXdcQTJpbAsIfIpJ5RiGzkrvqCqtYeT4VtAIhAJF2ioLGKOoLI3Yffs1NNhzVon3DnrNvWmkwWNSnJsV3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1610814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVasRCRA9TVsSAnZWagAARb8P/3gOfAQMuNBc4CNfStKP\nchClOcFqD+qVu379pXHlmvC6QtTOtWqWPEl4rln++3OXZdjopTgOdBWv9Z4P\nVrYxdIjDDLoMWm3K8tLdxZZVhB4V9bkPLeYtTPD1u7XPy9GCYFIrLEB03BP0\nKMOGtchnuWZz4s5EdaJ5NmDLkUTb55JrroBlnDIYEAahbCmcucovpt+HxPpK\njk8t7zruVZaD1lDayHicYwlWIfBJh6r6xBGc6Qq4KN0gWvOKyncvItjcQ4HK\nBgi0CkuJGN/dC1cxVuRkIycTJZGe0uVLO2s2GpDY3gzQCbvisHX/7/eZyrZL\nBLCuowII75HdCIAsfx12QHBeutgjJHuLRC/0MRVfHBJ0xxNei+91NksYaria\n4rwpz3oi7xmyOCkZKd1KZkC5MYBcXPVN6rXLHupbhH9zpgpe4CBlRCUMI5yL\np6YTyBz/xhSrQy60e6wr//Il0IE3TDxlhZXHbpj9tQeVL7r+ufdJGHMZqutp\na2tmjIlLDbzxH2Kki45GReg91WBYUeSpsUQJrykgZrzUddQ2DHdkVHcDNme8\nOClmcYsWndGHht/7ycXYBGRqJjLHayHBtPOBoQMK21zC1UjMh7yFrEXuqiGm\n6TlX4RDffk0Gh4qkbsry3tu7fz0JhlhK++OlboprRk9Eba6H59FZsV+z7sLd\nbHfF\r\n=1RQs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.0.1-alpha.1": {"name": "@testing-library/dom", "version": "6.0.1-alpha.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "1a095748f9c07aa93cbb794a9d7bdcc38c72450c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.0.1-alpha.1.tgz", "fileCount": 34, "integrity": "sha512-4KhfyA0tmpHDjT6vZ0wIy5DdCNJdx/sbWyyIJSbshyzoBhogMYqHZkFyEaLAeE8H4Qa6jwvvFQFNSPKcteZTyg==", "signatures": [{"sig": "MEUCIQDgRNkDWQA4OInByzO9cnYXk9bWRPwl5dAJPRCNJgzy5gIgcIXvMg8DsSC+cjZJCXRX7izoKdA1kPfMoeSlgGJoLOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1610859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVaubCRA9TVsSAnZWagAA7T8P/AjMZllfqmekbT30GaXV\nN9lSpPT3L7XSg4u0sB8Sfn7K5y608+5Wp7NmSDfWu7ZhNCHoVOGUW3972Q3x\nVVdsKDGVTw8XP+kMjMFbukenSlq4PnRo6frBiVrpjNZxXRF7T3nmGUBBH3DT\nUDx8ix+6ODlVL7l2FT8J39b/zcR6WxhCLOtpkva7/s+jW/ZRWWSlbO4g5Klk\neiwj2vneoMK7AmVVv3pljq7O40bmuO/aa0tR37GBP7kgmAmpDNpLduBBfYTl\nnPwn6s3SKj5deGq7A<PERSON>iuz7IIehgkh7lSd7mHzSaCSm0lHM07BbQtv8kiBn\ncXo2rlNO+oEd6P626DIzDrem/xCvRDStq/PEfj82ruxHejJNt8LyglfMF+9j\nitAQQt73wOZ92hL+AT4n21AnlyyOggd//d+8wdtl2GKRuZ24V9mI2J1RvbyI\neVmL9sQd5dPjky5rhSsxtEFwLN/XugNSdGFEVevMtzCL+7rVch/P1KQVkGCY\nprrcY0CIHaj4OudQLzwgMsj10wxeJrPvvVyBR4OAIpvGLSh1ZRam1IB9CaN/\nmwAe1ZTfG0aQgAztDqVZsIyET6cYHCjtiXZMVZ/XKxoXrNU57OhlBPzRdYlh\nki4jHaVgm7gyo0LX/nv5GHGWlCL0gsVjh3vHN3XWjL7b3eRPNGJfdVHON2xN\nnoV7\r\n=K120\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.0.1": {"name": "@testing-library/dom", "version": "6.0.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "939c7524bcf91d750bbb3bac3366d17e166224a0", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.0.1.tgz", "fileCount": 34, "integrity": "sha512-A07uE2btlK/F8HsdocRE0tdLB5waHFzc+BDk8/6qf37YmgNqwrMszElHaj2V61vLi8zQUKKqs/eKKLbjMAkTEw==", "signatures": [{"sig": "MEUCIQCsNUvCTipRaC8BCq69/7gyfmmVerg6EhLM0ud0K47NrQIgOzrQzo5aWtqhPTVheyZXsP1My5wMZyhvk4VcI14qA/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1608205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVa4WCRA9TVsSAnZWagAAwjAQAIYRxQQbTn79Z/5PsMW3\nmr4jC3qTXCXamqsD1yBbmIcggQFsYoW5kBskJBG9ZX9M7tKE7AFYEt7uuPGb\njCarzxgAs+KVBXaYvwDeMGBxq0DDCx22iv40rPZ4h1+NGDYZK1Mbucew/weT\n3lmIwjneAm79nOwP+qhzWZLQdhTPiofmwVb4rJHIzejPPYiWVSgIzlKfMQRj\nnjbwbsZxG8msd+oJ53wOXvWr1EKw+Ee2uWhKCF0/7yHDSfPrPBIDq7kwQMiC\nsQyTjjUjOy9OH2mJLE8xb47CrrcCvomDzGvsjo+a9rzCsLZhij3MjUfQXrXh\nNC02KtQghFsURuIoLwC95VphYfjX4ZjmcGPM5AbNJ0F3Yb05oTK0iq120SL0\nZb+tTzVZIRCVFNAzzP1wuW6eJfivEUHoxdoQKYN2CRg3sL0URGiQ/7slZrXy\nl/dYGINPG0wlXNnfQD8UY6qeEDQS36ZbBaPaPqTnxXRQbjkRcWp6eM86U64D\nnM5yXj4geiAT4l9Qc372mN2bwiiHMWsSDn9ZoHWS+ozkjKgBlC3dSx+wjGCV\nCRXpcmi9ck/caYufvxeIJA7nfJkPpov+nQJ/NRiXui1CJtXQwUVOXblN/Fv4\npsrnSjsS1coCfEQ4wvyCAl79URCr8eK8e86DOhQizsF/LI7qJFkWuIaCRI5V\ndtBu\r\n=nqgp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.1.0": {"name": "@testing-library/dom", "version": "6.1.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "8d5a954158e81ecd7c994907f4ec240296ed823b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.1.0.tgz", "fileCount": 34, "integrity": "sha512-qivqFvnbVIH3DyArFofEU/jlOhkGIioIemOy9A9M/NQTpPyDDQmtVkAfoB18RKN581f0s/RJMRBbq9WfMIhFTw==", "signatures": [{"sig": "MEUCIC+02I/M80FKyAKDYMMj5EgcErUHo1CQeHRSXO1/TiLyAiEA65NITbI6PLwX+mfbNXFCE964l3HoStaII/FDzSnAa5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1609601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVy/4CRA9TVsSAnZWagAAMvQP+wd/WY0byF1xS8jjMn60\npFXMGhvaQYIjTJEk/FQYs9gFvJrxOPJIHeNympqi47XyqSZm6Jn56RU8h6N0\nKwBJLvEN3ByIt3uJljVKvupM04ksJ4B4pP5q70XOHdSdb5xQhg3l2qxkAtWr\neF07FAl7V2nWy1e7HxGftLA74J7AAEvkQhsjmuZIuskvr8xGqKfMxXaLZMrx\naWWQS4klSjkV8dhQquwrGqDMq5F2bkbXaD1jZ8scx2Bkmkq/uhwTSHTFXOQj\n18nGP90AFYCVnDPoFCHkPPek/hJOK0rpZ6GVkzOPRS+liffxRjcpnS++Dy2m\n3jH/pqjCWu5iE5ydUXq0L5RnR/zoh5A5FDHh2beQU2y0ypAdVSfpHY42vhL0\nO2e3qkJTD4MmSxJevaWyd6C1eQoojWcFQbC8QNAQC06ntOtWH5OpvMFJ8Wey\noY0gThsYtTMpS1n/uwDT3zURbHdY3geSkZhyTYmPoepq2PVDsZCFfyUMZZAg\nCY2cTXkmj1/wYZM6aysI3qJLaKY6ac8Xs3h/4ZvenlfD5UwFvtomkqnEKy4g\niV0xJFTfI2vqcJbt5a4IaU+YRN2ZkLWG9V21lt0lySwgqaAG/mY9a0Lmr4AQ\nLNdKhK2xfSbFei+oEEx5E/ksQveNqVU0XgcYwjJQ6idT3Bge+GNp5GTSn7aN\ntWZ+\r\n=qx6V\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.2.0": {"name": "@testing-library/dom", "version": "6.2.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "6eeee738af12a2f02e74dc8cbadeaed1fc4223e5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.2.0.tgz", "fileCount": 34, "integrity": "sha512-YaaoAIDTNV8AfC19XLa6KNeBB5KuSxWYPrgYN1vBu1i+czQlfWJSCS0A3yd2V3BUH9di9C1BD+7OoyVBpZCh2Q==", "signatures": [{"sig": "MEQCIBkQpA/uuZj00jQan/ctO+X/LQEyniDoRLfN4lXe6txLAiA9gWMrnQ8/kzBDrAllj8nQPcUsoqh+pbigYTNNfDOmJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1611972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcSkjCRA9TVsSAnZWagAAhnIQAJfOvoPc53NRBs1+C4mr\nUqQQsS7oOBCvf1wn0CCmwCVTHWrbFDG+m0sFNOgi8aDm9ZWYM4G6UKNgRLM5\nLI8826gzUeI6JTHrvujCXiQTmU6YqJhrY30Fk/XsQwczpfHWbk72hZBb5eaS\nvoCFXRcfzkIvFlq+kvT9nV9Qfw1x14xFMisDmpBnYEZqzoyYakz6cDHZqKo7\ny26/Qi2ZuvjVLenj63whwpYX49FZWgigKsKRfxQGG7mUj+cLC3G5LPHiU9Ml\no4cIdTCef851FVS4HSBRx0wV0cVFe9KAv3hQp2Sr4YNEZ9CNSu2RqYjSh+NB\n80Z7SArQ3wR8OKwBR6MIKY0Zcfls/dlCqcuGwp03NqijV1fy0AnvInGq+EgA\nzyOvnqTz3kSQLVKt8z1185bTIQvyqjcPyTIyCFM2vztl/jK/otrC5L/Z5NIe\nwNLBni31Dp6WnmQlZi7gFumla4fid55J2wE/I4Jiu188Im2zPkiPwV4pDrn3\nt13k1azZ+wB/F1KJoCEGdR0qt5JbmJr6tVKvddqdWrSJWIm5/9mm5BbAtz1H\nkhwCsXtvJHKz6UN0d/822g4nL+CLI3fSCgmAFo0L3Stb4mBsElJVwADZp63z\nFlc62ySk+c3eVg6j2EiqQKjyHZr2z1sCjCZ3VTSbfurj127S3vSVYUy/wY3y\nwKOn\r\n=liev\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.3.0": {"name": "@testing-library/dom", "version": "6.3.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "27376ddb61094cb43177af4c5def17aaedddd99b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.3.0.tgz", "fileCount": 34, "integrity": "sha512-CNtBr4JycDQznfpSK5oID5uhJhkmA2TH13gbyFNyaX+BFaOUzC6PQGLcVDvzxOm5EN1gvu4B8Oe8e8a3CfUIGw==", "signatures": [{"sig": "MEYCIQDT6RsgIULPghADZLxziipN1ZNtqfRkYnTgxY/77a1uHQIhANXn8eoF5df1j24kUvnlNG8lrdTHuZyV7uCNfkLgCp5j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1622556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdhQ95CRA9TVsSAnZWagAABwMP/1hx1i7LBDSqDinECrY9\nu7eLTqBVJwjMpk5CBfKYW4EhJ/oPSo8sH4aVQo6Q8zqzWqM8+7oYsZlphuGK\n/mUuiHW0zXoPCnATHvnQGrtxaD3ycwPchNN6zjRJ8MDy9bRTdgT4yLxd3Uxs\n1NHDAu0J/e231dhLt2hE11Sfmq60x/zrWCkCB/O5oPpyRapMlKjb/YGc5wdC\n9GQ9WsqmSPDreLNwoDFgDeAH8CvDVaTKEbNsVlp8P41DxfBryzTU+R5klY77\n3NhpknP6U5Oy+xR/q+uFfUgwJIM/lU9ZBUZjLvroHE86OhHrOVzN0+wB1dYr\n49hTO8ObaZ4qyUQEQZjZwbb4ZZ4C0tO+CiTnzwzbx3/htznsAogFh6ltH09v\nVNx6DgQg9xtoWL+wH5rgJLnsxaPcImY40pjd/XFLQH7BMJgkYMEejJKsMqy+\nPekPZwWR0u4SPmP8aBnOSCIKnkqir9HuOLEQQCaZKNn9M1z9ObjAjQZcMTgF\nYrjWyCwJ7+M/DKSZ4DEaKPTESLrtQVOIVwkcObzkmk3nn3FXcKNCgMG1+2LM\n5lvKdLer80HsChnhkaWWxxzCa88F9B+SGjdXuRotVSIuvkE5Lc1CnhBv2SS4\nOgMhw3e5XeDoINQB+hMf9z7CPREcgenPKAbWmllY+4tZdwYdCFZvcIAfx5Xi\nCjj9\r\n=HmG/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.4.0": {"name": "@testing-library/dom", "version": "6.4.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "aaf7fceba1272516fc7c5ac0716a24f63ea6cdaa", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.4.0.tgz", "fileCount": 34, "integrity": "sha512-uQFwl+mIH9THk9Q9qVZKBgoL/6ahVEQu9bDeOmY5yB8uc62L2Z9eYs0g7zNTdMsg4I0bOdPPMs/sNETYP5+PEw==", "signatures": [{"sig": "MEYCIQCBUwBlkZ1iEm0GCkg/QDXHCaaL9R7u3SB7PLyKPv+GWwIhALt99/DV+jP5EAa+LwkgwjwjDxVa5IV5M+4SjodkZvhE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1643363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiRQPCRA9TVsSAnZWagAAdRYP/RGgKus79Lmk0PGSGUJ9\n7Reh+D2LHLbL/FWuVlWLxNkVX90koJYnz3pq1yrK3RNK/dgLqU3CYlFeVzo+\nG5HgXXPfi6ovAH8vcpr3NriExqiGDAIBMjdEbnC9DzOsVbxghZbch+GyKl0e\nKH9mVS3yfmBHrpGnd41SWwS9/A8xqUgF0nRjPWC4FocOh3cjoYyy1lz5Rlsl\ndFIPNo+ux442Apcu72eCnYyV9E5O34rKsHGGIWC88FTm1r5EvUT1gKYZEN8X\nz0omOTZPn1uDTvxUzMGqX8VqqMm7bkchSTKbMlkflfraEY5WD6T2VeEm6CWk\nx9RIY5OIUsJh0/2U+GXxybCwcfHJbE3eFeUaxjQh76zNdVDMmKFv2Eki5K8X\nOx84F+thltOOFyKubR414ilwnIAFwlhoy1mxYh0Sxa59NfCHf1LEI2FGmabs\n2gPkzXH6+l6eQNGoVDR8EHJ2qWaOPDqK6wWhh6QWQvk8WSTYYXQQOz5meL8O\n0SVActQDCl+OxqB6BO0+JTr98iStCaZMc8e9NhDxPdbNTABxwJagotHj/70Y\n6sWoS4Rdx8IG5IKrxcPz6EA2O7BXAOqVX2Ft7sL896lpA3Lh8HW7ve3caeii\nlSK8orbD/gUIqVXl7aFFRv9Th5p3D2joSnXN4FmccFIQIc2YxaR9y/iDImRU\nc085\r\n=DkKB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.4.1": {"name": "@testing-library/dom", "version": "6.4.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "4efd38d896b9b2255025acf9567e2360e1f4814f", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.4.1.tgz", "fileCount": 34, "integrity": "sha512-bjPHLO5NzlTvA57Tfz8txHEUmnOed3NuvObB2ttoKfO6A/utr7TZt9bDHHcYymcZIG2IsQZLix/m4ZKkedDDwQ==", "signatures": [{"sig": "MEQCIE1X7GCmc1fVpntuUpjQQXCScAZhS0Q57CKYIt3jxgJYAiAcQ/m59tkNjOqI1uJeVn47CARgPbqh452YyXDWiK9NhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1643486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjUS6CRA9TVsSAnZWagAAa84P/iOpNaAi0LyV1tuqcVhv\nMP1im5lorzRuFSyYexUPmf64L5/3yozGCsiFpnmfjy0H3nLi9DVzmUjIlvUO\nOzik4Wb2oRvDzRJf4ncnYoti601awQdu0QdDyIJdDnMhT23O6W5fmiAVb2gf\nOf3y3SRlQsOSpj6he32kfTZY97qfaFJJUFG814rRIr9pOApfEapnw9ayC2LP\njwchB12xsk+u69m9XnQnyo8Fcb5EqzNTVuawV84hcMNdet7Ob25J4fhj+lYb\nhI27qUQ3VonR/jC1Wp54L4FlxPemq+n2iewHwwgT/lSA4DAIVIT4BLbILCjs\nvz+7RZUOtTAK9kqlw9mdese2IghUgteKSoxeWLwAZ8Ri2TvfdS8cvJoXf9ih\naYZh4maGsXS8czevhdJQ3LA7MnfQ3b9wkOVR/9o0jF9Zmh40DFEMkzrhIky+\nMwVhUv1jMZ33dsCWQoo8GPMWPCGqEVCKwiy0C5ti75RUbSLt1uG1eA7jd5mW\nxAEIV5vnUXfGpelRMPTSH+XHxGXwe8jB7FNoqcC60Hv0/Kb/POJfRNXuBFck\nLe33Dobt4tH7TEAvURc0iDaMDHRzF+LgxWgRZCCC50quCAnwrpKBGWhWVWw3\nztXqa85dWkSUN2rEg/cumdkIN5aCdjnBZKGJNS40REpZawJsln7Vz8bjjqOv\nl0TJ\r\n=JWFn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.5.0": {"name": "@testing-library/dom", "version": "6.5.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "9419fec57a544917bd5e398d40826425be50ee0a", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.5.0.tgz", "fileCount": 34, "integrity": "sha512-3lQx248dhJzvV2a76F1VaqehX+iquSVVW27caDaLoQZdUHEZjB370n7FO2WoYwOQQ7NB10AvfPhrARYnNgvf1g==", "signatures": [{"sig": "MEUCIB8/ziM8hdxeUErATpNvDMWVaOgFPfLCBWuDXsKh1p88AiEA9CYu+fpnDT4JEbTlFOjSnS29/7PJCra0LgdxZW5kvg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1644820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlOIWCRA9TVsSAnZWagAAfEgP/2TND8PRI1vhWLdJyebI\n2TOBYzXWSFSz4rJsPXO80jSUjQIzGDcRyXmrALJhnrtQTJQV1vHcpeFakxJq\n5mdNRLBf3TTHEpzOyNAkWsad22GAZnqxBXmQE5oxacXm+reXofXM8EVBcpe0\n2OBwkfycYKlGvhZvga0V2G7sJ1iSyGnAmKRU53Et1/aOLLnKKsZapQ3wfE8c\nH/2QzCfiha6uc1ihf80SjeTMEj2GEfJFuLKQOfcc9G/xVmpy1753o1TFtxYE\n5NUxlpiv9EQfaXz01bODz8XuRxnXeqXNsYhpX4ISCoi5nhGVpIX436qYwbmp\npLlMqjhJtlxqfX8pOb2FpdlOVAXyjOdgNGgRqPZFVU7E5OZyOXmIMjTS07/l\ni2AQ5l6fjl6QXg/EJGp5oUNgtitPf4hy776TlPRNKN6gfw1dUlgJfosEtXjF\nUFdskjLpE11IGJYjUrAoktl/C/r2e9hL/2bd8Zol8RTiLPe5vC6mFYQiwneN\nB92UMzxxXe01UG0laLJR3cqcGzjywx7yPvjY+6jKScLmTsJJmWtymvUkM0jQ\n/JzmRhq6yn+MiJ9ncOibWuktshqoZzzw2QDHZCLDSGrPX0B+f2qNbMjFAIP3\n7XckRN8BujgEhawdDceniJiyFse39JGY69QXcC749zbEE8NOqQEz4MuI3xNq\nInUG\r\n=fV4C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.5.1": {"name": "@testing-library/dom", "version": "6.5.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "823cbdce85795b1c523b8ccf418ce8aa3f5817d2", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.5.1.tgz", "fileCount": 34, "integrity": "sha512-Dd1HkQqSmg/vc+kFrTxj+yF0QK+VweAlTMaAo5dADI0IQJZRkuFhjZ8nIKFfvqAmeE4BX/c/+ejhdg6csOCj8A==", "signatures": [{"sig": "MEUCIFZF7ADNtsyC+WtCu198RfoZUpJONL5p7XPdCt53FIKNAiEAxK8c2HOEJNql/LqQDfCDSqrYZe83XQWQUbswvJumNBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1644742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm0ERCRA9TVsSAnZWagAAR6sP+weOocW7/vS/vZIl+KXg\nSURTM5Qm/ORrngSZaZUXrFZK1eIxkMYP9JLdRbUV5+lQabk5JxAFNvvAPtJP\ncOuxMgamt1IaR/EZ/Ek0oBWj/lLWR4jYDDMpiqZq59E2zKS9VqbZDKhIPi++\nuTh7pCWv69INo/8jWVLCFozA+SOcbJLFfCKc1ZgUHzxyzx7CX0PW9h4lVDGx\n70xOqFqmGII+0K6ZmK0OPByMKt+uAjesXa9gkagh73KDHuGDrMoDc4pmjouL\nMn2VoxEOB85Fm5E2hXS6amR5eI5099wPafyQAgZpXvnUN/KySg8eko0WFKek\nShRtsoowI8Niu0ntvzG+KCC/o+aPchMn249q35KQJ8d/wyyBp1K9Mr1NWZPx\nmWoyaLrWTDDs0KX3NPMCsN0JpTiDJepQ7nRpB8gAkmAktwMxGBsTTz9lpFrx\noek+TTPFrknFYVhN9odBct6obxVP4QChQNHFjxuzieIpoIWCRwxJnHOfFOMV\n8JObu5fG5+/axuqNcrqK5nlafGJCJA9N26GATTFsneJ67N1CmRYPmhIbL74t\nlCwp/VAp2s8pS2knmmHuPqXjRHz/RIQJaV/JNfSKoMOECFSp5YpI3o6RcnOQ\nQ4zcQOpyb1diZKqof6ormvhKA29WtcRj4BKG/RB7CZSZ5DawkVgpNLIzM94P\nL4bO\r\n=U/ta\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.6.0": {"name": "@testing-library/dom", "version": "6.6.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.8.0", "@babel/runtime": "^7.5.5", "wait-for-expect": "^1.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.5.2", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.0.0", "jest-watch-select-projects": "^0.1.1"}, "dist": {"shasum": "74139051e6b6743b244b2590bbba7387f51805c7", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.6.0.tgz", "fileCount": 34, "integrity": "sha512-x9Uyww+Ub5d6cSsBixECrPlyNy5wSKXxPQToKdaLYa8KvDBDKXlqBPGo7QoFFTrMocQmOEnY/tHxZLN77+tzAA==", "signatures": [{"sig": "MEYCIQDSWN0OleeYu7iSen9MYQKilq1GAzfNmt2GiDsSqiF7kwIhAIvgfh0joNZDJRI5AJfhlxLfXJuM6/T8/0/A/UeoeUaG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1647836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdm5pUCRA9TVsSAnZWagAATLMQAITeb7ftL8aEcCNpTyuo\n+vwhQfMa3yO1L7PYZliTCZInzJi52ZThDn9B2qJjPN8PPOhfVGo5j8mmNPru\nMlvo0pJFDHsYCwjFCa9oz7UdG74R4mQHRMCKCRlKM5DCU60szlHhb9PUsDg5\nGuZ6BZvrfkn6GxrjJJRg+PG0+HUcROB8CkQRHtgg6WHN0qe1ZziOPwfhxFn5\nuOespHFpXIo3q9u8bnkVA8LVBkLmJfm1Ob8buhJJzNz40VguJgS2HgMw9QCO\nZvhGaSKicsmq0Y0yOaSJRkE/+EHYSeImSR03YHkC+104Zhixg8i4nTZahRaI\nHVStYzPEt0o5nbRMA++RkEsPSecZQpnvsqcJ3GStwcoxLf8uSe3glPjMQJj+\nSXepWNKk9rbXupqtBWHyKU9fyLWHy2s2lTvVEiPbp5k7L7VCbgKoO1KXo3oX\nTs9oQoTitTFAAOFa9e1hbLBpEdn+3jlAOvBd2LsLqw/ra8XPd6gxQHtT/P36\npAt6FZXg2Fba/dJrQlpux3Ff1T7aM2DPI7GFQGUMAFZIoSW4dTuCbneRvlOp\nfFRflzpI5sBUY7AwSLEKOhzAYRvrxIcXSA43BLUpzo2NLc8Xh59UyzYisZCz\nqK2q6/KbHwXhh4TsiZKZh+hXwMF9wZoLAiUskxy87XFPepQHJ5hQeeDFhIaJ\nbSGQ\r\n=jQZN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.7.0": {"name": "@testing-library/dom", "version": "6.7.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "74f0738734166cc2899b8322d3d93e60c92b4105", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.7.0.tgz", "fileCount": 34, "integrity": "sha512-Ypqz7DLrsXWiVDbuItwNztzMtZTRLtq4l1OEvw5o4ho05+0a19Zm9Rm65XuXMe9UB8DuY6+pIrqJVxCvxBAxlw==", "signatures": [{"sig": "MEUCIQCoFn+xSoffGy6N2KjfNXWLWD4x8q0bzPF3elSwC7xANwIgZqGSKO7Unaw91hpSY8KQN/2Qj0xbXjWCDlVAZXws0eY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1657231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnpKGCRA9TVsSAnZWagAABSsQAItIuQfrD+FOOHKZQQud\nZKItekUN8niwSvjvXw/k4Co1LC6fyc0xInap/bXCQdH8xVUj7bvXz7NGGNIx\nlmQRa56xnRz1Rt2KSI3g1Gt/QEZUroR7F2P2J3j7M5+W4f+aFKCEfqa1VZxe\nk73IAnZS254or8UZzsoSZpyjkrGkjeQnh0+1L1pBCx2TK50zj6atGcKs36Zr\ncyWj0Z+/Idm6S3jLsgN7arR5u3uRHQMJudxPJUAgmPu+NJk6WJYhKl0nE724\nXB3xNSGvWOegqoG1r52JEPgZ+bLOjLPwDQy8C3RRX4JLIfMGftQyig7NB8bl\nS4eQnW29x8DJ/rWiLXrXX03srFu/nv9HDoa8xbrMB0whp/JehNAfXJJIl99b\nAGELAzhr3Yn/QwlwYDjtwNYhIllWkTt+bp3v9/uwwKa0GeiN5UqhgcvKBad0\nDH0G5LwqfG8CHZYA5KNrKbxcE6Iw5MEZXEEJs1j9gk0P/8/bsOvEJsSKvbyq\nPfUHksAL21w1scQdO6XwTb9wB9TfVjExQxF505z5qrbugg2qEZpH/x9VDGq5\nm1Qgq6CHw9ijcX3BIm5irqi1kTB+DP1z5G2wAcHHoe2NjkonTxobOuiQG2BD\nyVN43sja9IpTTTour/uVJDxs/FFySwUMuvqSs80C1wmCUL8yMoP9oPWdUr+L\nIEdB\r\n=yZd5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.7.1": {"name": "@testing-library/dom", "version": "6.7.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "90985ab2524f712be695872714997a8b66958223", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.7.1.tgz", "fileCount": 34, "integrity": "sha512-whU3jdiv18l+v+6P8I8aAt1phaZHx6LwJtCsxoWTEp8OD8rEZs8V4SOnCpK70SXUcpZVE/NWPY/d/lNAffpmtA==", "signatures": [{"sig": "MEYCIQD81Z6bn/wAgvKNftKuY/y7imCHT6U7fLz5/mp+7dfztAIhANwuaTcho1ZvMmW6bWuGjU2UwEOA17dAhvzMTGv2VDaK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1657639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdn1J3CRA9TVsSAnZWagAAjyMQAJgvllNYpT7Qt4asBgNM\nur8F1O1U9dWT8p89aVR79UwncO6sVAEVmN0kEHd3bo6RkZZ18meLg1G32TNd\n5zKUy/hf1Pq+ZhfMBZkxJsry/rumf54csYdKkMce3ztLz0zWpR3bW19I4teI\nR1IBfFbwsaIufwBPemd+AA1hMXDT6lQg5FZFQ5WfD1rIgvDA9vN16l5G6yEg\ne3VT9rertm2vXkyZwFiHe2Liy8sT9pteKodst0Br7+/W6SlQiY1zRSIpJSMv\ni24Okinn0gDcVlfFoyw4Fme0wYt2C24zDtO3dBM6yNPgYlGfR7Xxu7lMInje\n9M8XD7Ns6EEPKUY4CdShotnqaBA2NOm8jfvWezceiLRH4Y2hGjoyGLqC8aPw\n9Hub+C0Au/GMvaCovDP0Vyz457ps8DgdUZXidAB/qbRMREdzKQdD24t2WrVK\ntGqbybx0q4ZzesBZilGFL1GMXkIGO0x53QuGy7LBcOIr5a5GX3bC6wb6gCX2\nHilkRjXln6MT67JLxtgk9Fk7tQVdePsnbsYV92x1YGvKU0vTZmAyE4ZfMv83\nYwxGxxRS9dv5RzOrWAYPw0BVG3XJFs3n6bMUvPFnXSAXyE+hp6hxPkcxFNjo\nishdPhHAE8aB98XcPOuEY9rPgU0KOy9+eeRD1f8MktiNHdI4FFf9yi0Jh8Js\n56he\r\n=MfC2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.8.0": {"name": "@testing-library/dom", "version": "6.8.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "25beb7ff489b9e06fd505b37ffa70c2032e9f05c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.8.0.tgz", "fileCount": 34, "integrity": "sha512-ZQKq2M4I5DOvOOJpGdN25YYCjMQ7FA9Wk2WiSi1Or5vmRWrU6udxuTEyV5MukxsZRJkzrfSjDsrfNAyMGAhcdA==", "signatures": [{"sig": "MEYCIQCXm+WTB4ItGkja2gQdBfduPbRs6ZdkcPa5i9nLfW9BIgIhANmS2ZbtJepFMhr3Ps4fiZoeOZwZqgEQC7M5Vq4h10ed", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1657520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdoMNWCRA9TVsSAnZWagAAtw0P/3su1fUu3seC/Hsrv1NT\nKrpQCT1p1QBd18ual96ZNEUft2xlBjby4OJjBt8TJ6Jh9VsIYeEDrtqhfOke\nyUokwkwIWJHx39K9kPsOdBRYi2vHR7tPMelgx4b4uKeTIPDtPIVSLsB7fLu6\n0/RLwxss0HrHsoQmIUO9uR34ioEHU1EyAyCNOToUuVY4wyUQZcZg46GOLgWK\npTp+ItQfq1oQzLkfCCcvw8A3kh0C4RlwZeOiO549eWb9Oi7HHOfA57A2oJgS\nDXy/GrriDX3pml5ihVy3bpZI0bZmqPzCzfworjXXAkDgPm5xlgp0eoxfI6xJ\nOA2kjV8D6i6Nst5XPhbqaK4Y0qoEf2ZhBdnkEowSpx78A0R+N+2hkCZDuzrO\nG0p/X9+zbV55ew1ljO//mnwoqij2flthQna3+UfVTOGlS8k17n+0mnXtfG5C\nBJ0U5L65rCpik0dD+rXy/6MthcLo/vYCiiN7x5UNppnXueI3DK0PqJHT7YpD\ntr9QjU29pimhJ/4MjDEU7MV7LPCNyfx3kxIzwyhKteHSbDbsFRxz6yBCEQ/2\n3SxFPqW75FVhnEsGPbnx7XIUX7eZiMqnORuXrWSFiwCeXltACIS/axseNAO/\nK5SHdgcCEXWslt2uPsrlPMErV/EX98RnJ5xEzXRuknR+oAAygO5UYrWMNNlk\nX1oB\r\n=VBrz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.8.1": {"name": "@testing-library/dom", "version": "6.8.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "47b4e0cc0742302fc9d122ac43010e6fc60bee65", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.8.1.tgz", "fileCount": 34, "integrity": "sha512-b+Q4wryafqsSTFBV14cc5xqhN/OVB9oMeUQvZwy1kVx+kdqs4zQAcyvCsFkdcqx7NxibWpUN/fBIUaqyAEhitw==", "signatures": [{"sig": "MEQCIC+6VpWjzMQmon4dvWrgJBrtBmu03KS+XflXu3QSm7oNAiAomWCf3yekeGb/zPhSrbXUb5TM5uDcpeUmcPEUZ5uMNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1658066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdo1b+CRA9TVsSAnZWagAAcjUQAIJlsnIH77UBVMTs4254\nO48vD0uRVIW03/EM3fwZ453EoXT49CU1lDaEmnt1CGbV3QuTdq+xwnIeeqQd\nc6NA0VG91kfC/XAkkO/r39PXUVEZt2SzG4gPvccQ63VqFkUFkgDiRst8l0ok\naYr75D9O5XVPjxXiMCFzNgGp+a5QBnBOurduIK9w66VWGs9iRMBWmWyAQbSw\nVLRBzsw39Iy/6zSLzsvDZJnaQRPQQEhVaLf5kB4CfeLXTTI0l/Q9xE643w4Y\nF+ZfSbNIPnXnr5WyAZYbgmhI7/DuwsN7GgrN4EkF6mIb+30EHIYN87DOEps+\n38PwSa8HOVinlkb5YIutFEqcMM3ICoXNJJYjq3o0fmeUCqkii7HC1O/KzNvf\nlIv61P1DirJJ7mWaGDKe234KzMLRHMHct1l5YxL7f/VB4tTTbnKGgFvNHWZ2\nQvTe5GnhjpC5p5s55df0iUhZF8nwkxh+NOuEVULEyhDddKpWC5dxi5qpXA7W\nxfTeAWoEvJn64QfdXn5xEaELgOntnWGnDFsS8L7hGyjhe6MXjiZpYF7GjCTa\nDaddtlOVRUrlwxfQG4ilHM4RBU+RkQQOSXhqsE9gjvcc05riddCBU8AMUtoC\n93H2fstjEBirAtQ69e6r+eqp+HV+kUaNEsFFqXAc4xjZAPtooRzqF7CH9ePm\nzMKd\r\n=gSLK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.9.0": {"name": "@testing-library/dom", "version": "6.9.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "4a275fe4bc0a5ff5d109007c89a34dab0bd815e5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.9.0.tgz", "fileCount": 34, "integrity": "sha512-PZvYFf/iGsvOr4TscIdtYWTX3rZrH/5t0NRBMEZIyLtK728d+Z34D7+oTgZzjh+ZM24+L5Ly4XGrHBm1W/i45Q==", "signatures": [{"sig": "MEUCIF/h5VlbUlnHSlJ074peWyj03SpEYjHx2xydzyH1ijeVAiEAsigNelUIt+Lyi0O8RfgKShM6a0Ba/c9PURgpVhT/QfM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1661288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdt1wWCRA9TVsSAnZWagAAv38P/0AAF/B9TZzS03WwwUcv\nLhQzKwemw90NnizTZBHBsvfo0NHp0ksLZBJof3gzL0ugqToWKNYav7B/n4rh\nNHJZBxLZ3kInIHBg3l3zzp1tCpwxc9DhfxNn1qlYKRBUkc1UYWplCPjViDu5\nQLH7fUz2F/cYCi54m8XpnGN70KguYq4PZKhC9e+WMjeVdxtXHYjdyBp4uaTc\n5RsjCYlZdo9DguLCQtSVJh4kogTW4ndDOC6pOoQTTSBjJlvt8U8puiPq0iri\nsu6zgSVlRooA2iinGgzDBlOW9IeU5aCdcZiLo5piRuVxof70N2RpSoXLarlO\nnaMMin+GO4RgAIGI0XQ7OzCmFX9yCiara+xe+IAr6lNibkkv6mpApR5EEm7T\nnOGLnkaUhuu12VBeZrvSu/IRaQ9qbxaa9nCK6Rt16Vr8qAiRAel3dj1OiZhO\n3kTB7Cn8JVZuUgAWCyGA/h2gYa5Cc+/2+M6z7w9E6lpfHpIrkdd2IcMPczDx\ns0PZ5uTIuEy2gmdbGSd3WQHL1PCRyrQlHt/twrZwPhyd/Sf/EehMWJwJfktt\n8XY1eBce4Bise5gDZRbekVS7Y9NUFfERKr5eWk8EUVmvCdrTyGH+dpRWoNXD\nHVJvZMKwJ2YCyinUO589IytdgQJKMUhoM8v49Nr2SfTIP+cjF1/3QphNZoNg\nZH+O\r\n=4hxc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.10.0": {"name": "@testing-library/dom", "version": "6.10.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "c7e7d613faca159846379949db65abf8ab214999", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.10.0.tgz", "fileCount": 34, "integrity": "sha512-8Tq4aRDeukB+6WE0rVXO1TlS38uu05CpbHgEa9SLR3JWuBajOVEPk9HgOfOFOqWqoo5nEIthXBgLobbofEhuUg==", "signatures": [{"sig": "MEUCIFzxqniAPPVvEZt3AGutdSYwNdr7OEnHNr1am2+G+2NTAiEA+jeeq4pX9KWTheqOp3X6sT/cDa8nCMa21dR/LfoNwcU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1662408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduUNICRA9TVsSAnZWagAAG6MP/iZbogQg4OSMLb+qxJcy\n/cWpAhMMVpbeBWajLQnsyc4JZ79xDZTmKtSEzzqBmTgxQQ4Q/5JpPtj6hSH6\nN3RfXkr1YcsFK79jlc9GG01q9Ij1BO0uPoG9QgRF7GaubWkwhShpNIRQoH0F\nZ5g9fqGJac10ZxQwmQKmIN0ndGzTOGj2ub/K/DeuX6BzVFdtX2gbo6oHBSsB\nfrXSbZhee7Gs16ts5w0PzHQOqftSC9+/xSB7qJ22Mf9zvuIX8DwCVkNFjvhm\nCRLiwBQCyzo5XupnOMWyBIdrQluQHIVoG7j1C2S8DXeHfEBlIQ6EDqs/SDpX\nB0gHslqzk4PVlugryFdFH4TPCfxkk+9YR3l9FzL8yMyU51LwwQKA2kuD3h1z\nZn2IGv/JYiGM1UuYK9nBX0FuuSVz+LiJQ6Ty+KytjRBLJf8ts1hous/Kvt8n\nCfpolKy/tA3uNuZKw+ffT6X4IlIkkl4I+o/lzZU+9fXwL0LgtXTjeAiK25EI\nM/fY8PVXGaOYuRo7v/xBgqd/rx+UkZp2aYiTptzyrhgB4mI5Tr8vUC8+cjhB\nXbZtgnaflxuw0egbSHPiR4ghq0vQbD8NELRTdVLbQJIaz6ATyc04JDzrmrot\nvctreXQoBSt/tdkk1R2N5ym3HcjPjXVW3fAfjg1XVQJnBTcOQdgBCHdoSPwY\nEv9G\r\n=zi8G\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.10.1": {"name": "@testing-library/dom", "version": "6.10.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "da5bf5065d3f9e484aef4cc495f4e1a5bea6df2e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.10.1.tgz", "fileCount": 34, "integrity": "sha512-5BPKxaO+zSJDUbVZBRNf9KrmDkm/EcjjaHSg3F9+031VZyPACKXlwLBjVzZxheunT9m72DoIq7WvyE457/Xweg==", "signatures": [{"sig": "MEQCIEn8EXgcWbWmrT7qOT5GechVfxWuBDfaFD1WKmhdIW9yAiAaAlGhA2nezhjPTMH8LK+oOlFfOTl+JwbinJrOpPLdkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1663017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwkX+CRA9TVsSAnZWagAAXIwP/RBgvk7sMYi3sTD4vjHX\n7deaBHUBU3SPyYRws0MzAZbkLM3wycdvC4lYo2p38B/UeK7y9lv0SsKmm1vj\nSVTVyJLoYT+Igw2I1B3vQOC8llwkh1NJZKf1twN83AkOwV2tTj11YkCa8Nok\nJG8q2dnonB0C/CtOdGoUygbKuSTmfqRWDQR31PDfP80WcEGo0P28jlzjsEWe\nLmb/759L1j80+8+W0SEB6ct2Gi4m90lwW/qV3iK+hVp5lZL9HyS3e1HviSLq\nbzSD1q2Sa/O5AYDEyvlblKNubcPPaLPoINw5kewF8jfNwdfevTN6ACNIJ552\nkqwomCtyzmzM6VbLlpCN1GPjbZHaghBdq0Z1PYvm6JNtKX7CUoqQ3U+Dp9yh\nA/qdWB1+n7okE4mr16mgm4yYMhjQOr6KOvx+CHBq44wl7HqtxX6Vi5as7Z4U\nWleg/x3ZrXsfKNTgnTEQUDhA2Lt3EMcbRJT7ebbLkkCqFVWTXK4nfjVFtWAb\nB3/p6mBB7LhB9cwSgGjPePWVeX2rZzuDywsdpBPK+eQ4jh5k0kVP3aK14b5T\nme0bzOZGfJB9BH3N9TNk+TLRdRSouWFFn4VrSLHWZrSbdmyC4rPQrjJyeSOQ\n/VzfXk0g1uL5eDxw6jBtZvFxRmXVz2AacLwa0Bwl7dEE0cP6KizhpWFNkacI\nerZo\r\n=Q2br\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.11.0": {"name": "@testing-library/dom", "version": "6.11.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "962a38f1a721fdb7c9e35e7579e33ff13a00eda4", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.11.0.tgz", "fileCount": 35, "integrity": "sha512-Pkx9LMIGshyNbfmecjt18rrAp/ayMqGH674jYER0SXj0iG9xZc+zWRjk2Pg9JgPBDvwI//xGrI/oOQkAi4YEew==", "signatures": [{"sig": "MEUCIQCx+XW/nUo1yNSbWe/K+pNme7SwUx1LangHJxc9VtPB3wIgEPOGwPYtrL5rPTe13TnPyfiX8VGxIn+ZAV/cCVfxvI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1667240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd8656CRA9TVsSAnZWagAAWgQQAJOvJ08lx92QDVSWUy05\nEOeevdHI8vBwm8fiftvkdx6KOTa6ZnbiNNF1DZdAL73eVfMpLBzkW0f/di7v\nYl7A+xNmroWEIeiizgOSavJFe0yg/8kNzl4hqHfPJdWSEcFn9ZF4Zm9cMqw0\n5zc72wI7Ex5Ku12cMwLXtYtNmV5FYaBkLjyFC26Q9BwZw6OX/hj/gkgVOpHe\n8XvLXODHHxB4n+Sbdzw10cyzWKxf/AijehI/QWlZeFAVwsDLaZU08+DLaTq0\nMWgyZB/EPpARfibLrHUcXndgv+5LB2rXQPoqTnpu9J8zAtRX+M8vJjPLbWAy\nvqmTlnnTfJqpUN7gWH+HgBF69LTJAr+0pJcS/t4+V/pQjfGpp/lrMP594o6e\nq974Z+kWsNOpT6sOV+OUd9YYs9BepRrJpuL0Ncb37aESaloRr3X+eIajcQeC\nIXV7gN7WmMJByRCjPRzsSi+lPYEaVUd1h+P9o8WaZHu57NtVEgAZcMymVdBZ\nkJyFqEGxp9ZbufjyZsBa6p6yXtpg3JSJeaauomz6xpBm0JG+HCxTWe8BcrwR\nsSpHASOOkocRXj8BwZBrISFklbSNcheWEcA4pxSiaprOTE6q96s7CDZA8B/W\ny2zb1vGnRufaibr7ieR7BISCRhQeiOBb7UBxVLLZNaBriGDiOz7N7Jqx6Shj\ny+pQ\r\n=YYQA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.11.1": {"name": "@testing-library/dom", "version": "6.11.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "b8a6e51b5e300b9dc7b99b5aba43e10b855bdafa", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.11.1.tgz", "fileCount": 35, "integrity": "sha512-v8MF+OjbYxDcH/2WtNv6XxFPDprPDpmiFIBoxju0m8+NiL5yrXNvOsHDooeapkupbve8qbv2Y+GmXYg/RZQlCQ==", "signatures": [{"sig": "MEQCIFtdXwv2KLqES2ZtnQrnA3XN16zGUAcvZPN0sT1RIq3cAiBJISG5aVqJyCwgFRIoeD8rZ3q2BNzKMHbxOVfjIBS1Cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1666574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMK61CRA9TVsSAnZWagAAJbYP/3D8rn4NEvDTxJ+qdjj3\niubuyH1X1u3zfZsBYq2/eftBjqtJlWI4uC8Q0VCnwf2U56lK1bJPU8SnCzyC\noh4fMKfdhIlFksQJKNpEBQ6wWhtzHhwn1Q3MYPKf6PPWrHM9dsQjxzX0yjyk\nTYqFb8w7tzqI3pvn5A5GYoDw79bD/IfQ5fedX8HP378Viwy96z229x/oJFbP\nY+ud/i1g7VamGatE0sB0hP/rWwQoehDREu8H5ZmySiATmnTiEqDiHOJXIdCP\nm8cGT2YDVJqdQRsXG1eBi9hdX7m50I8zfyxkONUpqqUyYx83Gi2+TCu6xuw3\noz6K+c6nx9YQv2xR8BkGI1w1TvOvh4/VeUaQHBDKZeRcIrCD3STs2o0zCAwE\nE9BWNDGv09/oYjzLPXR1b3l1UtI7LTp+qL01ATepb9HoxuUGG2kSR/Wz+p8o\nHPDDzRW2bHHcYZXGsydiAicZ9u9ElVKOutGA2vGn6Ja4KBQRCGYvHQ/DzMdH\nFk+dWs/LQoxLnFSkZBwlMy+rQto5Fj82yUR0k70hgSM6YnPXUyrtd9Ue3RWj\n+FLJ2F8HPJcKvfAS9SHyXxXCBwqwzXds/8iOLRHBbGzAhbEOnvYXhP4hwk8X\n7gbYjAHlBhgGlglou3wPXdmM8/TGOqacJPwvVx3aO43ixkpPWa6ZtRtJEbLk\nfHuw\r\n=DJCh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.12.0": {"name": "@testing-library/dom", "version": "6.12.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "0e40efd58d85d92ad9c39ef7667952195e835bbf", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.12.0.tgz", "fileCount": 35, "integrity": "sha512-OMZgdsqwRICjfX8gzMcIdB3jFYiPKX25SaOHeq7zh3l9+MxpVj2fpk/LicL79L8t6LG7kY69YL4Y0kbdj5Yx1Q==", "signatures": [{"sig": "MEUCIGtzA0rBIrMLwxpQ5D4MPWjiI7k3QNJhmFyk7PayiI6RAiEA7WKBJ2BJvKgWO2FCcRX20mM5JLYIEgckSsU8ucnuSr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1676270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMQs+CRA9TVsSAnZWagAANS8QAJxxUYyedb0XIxq5//dV\nUgwv3hEv7hWstuhIXDWXY1DitVJfMm0PtauDoHbiumzH3ianc2p4J6A1nPDW\nu+QTCzyx0I7Qr9L/My0jgiqElHYeSrQVIdMOoTerfl12g54tz1Na0ilZO9Oh\nfIIyipoUXKeijfaMOVnwI2TcaOPhtW9I9pvtU7I9IYHfsrBjaShOu8ICi5NH\nUm7ZU73rkGoF4yxqLyIG1TCWfEePLD+nPRE55DONjxNWTE0+rYf7Yqrzi5lt\nwP1wE3rcVVsxlEUAKVj5wlgRXFwoAe46no6cOkafdPbjR5AUaxTzgMKuY+TS\n2LfJJcj1eQ5b9t4dY8aDfXRM4xC+qEAj858zxpyJKmFJ3HusANZjt7eZd0MJ\nlfR1YAwLFY/sl8IG7B2ikqW/R6z0Xh2vPweOEYrOdCBc6L5fLQq/edQPmwTi\nNVavm6zL3DEpCyrql6gWaN1wuJ6BY2qwnwiuIInkum3FIKIxF/0FVshCisM5\nFy6BGg7XlttKG/pe/yfTW4qB6W05f275FYEEdR/i5bKY7E1pbDTOoehVhja9\npnGqwpuz55YnuNQIqDuNBYlQzsBmo4gWjQw+QyrULtRGtFKs6aELAdftgLAq\nEkoYdphV16ZuPD9ewBzMbUP0WYFMzUvYcvk2myFWOENpagdvlgD6fFkWtZrS\ng9Sn\r\n=Fc7Y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.12.1": {"name": "@testing-library/dom", "version": "6.12.1", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "3f1b6422d92c0f67209877364d771ee0a98a3c11", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.12.1.tgz", "fileCount": 35, "integrity": "sha512-tjf6uLcIxOjSlYdajgF7lrhAa7TWWj1pljQx8ZhEEPIpdJKk6ielU6YkF/Jn3CY6Gkda723IcXMuPRGCSY/2Yg==", "signatures": [{"sig": "MEYCIQDlphVmndjJ56BvR0myZBQuc0AzrQI7LTEPA72hwR6ZlAIhAImLi2MMdPLYSRuAiO9kbxZKbizRmI/Q98wSk9FX4ug4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1676382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNb/NCRA9TVsSAnZWagAAPlUQAI4qKX6QgG/3u3iprMSQ\ntatsFqKvP2GNmKek4j9P4gZpahf1hM6b5vVHAcN0feFIthV6wRSbBSu2sJEU\nH7VrlCpAmxpGmJhOddVYo3iwzJIxDEviwdPb2lAWfWZCMoITFg/6kbhTBA80\nnGh51q7P70MMPmaRf/guHs/pkdgvYAjWn1xIa3wb7ZnMTefvJ4FP9Dfbc6ac\np7pGyDLgghZHrM+MWjpLpnr8NYrzL+fn8VAnbX8S7lGlr6Qj4AStLM37X5cg\n0REwGpMGXm/yLsz0SMDMJsSJiCYJkTZFy5PS3EpB96KKv9/+XiL14z4dMM6J\nF2rJgpuwCmGT8jzC0BwdrygZSgnp0dR0J9/CAahOe4awv/2Zt2ypL6cYJhPV\nfJ4gZI9DC8bZyuwsAy2g2Rt+OufSTNhIxpA109eawDi8osBD/c3GIq62DzZU\noqERGcKe8nZjbbEV37LU7xrwcCmi0avLBeF+KhbfKZDDMnS1tLh5Xx4OFKbE\nJ/cpJAxS6El252VquOuwipXLzh/ENX/uOMAVKjvgFiHwJ5opTnuJSNlKpTAo\n+QEQWm9rQl9UOvNLSTtuPsm7UEdZvpkY7vXUjaic7wUDw/AxeZCXuhFm69LV\nu18lmgQrHKiPh9poS4JTWvyKphwIGeOaMPoPgYTIFmtbr4MddmK+h69k52uA\ntCfs\r\n=4+1v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.12.2": {"name": "@testing-library/dom", "version": "6.12.2", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.1.1", "kcd-scripts": "^1.7.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "5d549acf43f2e0c23b2abfd4e36d65594c3b2741", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.12.2.tgz", "fileCount": 35, "integrity": "sha512-KCnvHra5fV+wDxg3wJObGvZFxq7v1DJt829GNFLuRDjKxVNc/B5AdsylNF5PMHFbWMXDsHwM26d2NZcZO9KjbQ==", "signatures": [{"sig": "MEUCIGA0tXFLjBCUgXsCDwJ5Gb347uzkoNGCIeLY5pZKpYObAiEA/c9wsiA9zQob+Mn/WVtFkO3j5FQmeyVaIHO/U1gzSRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1681874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNj8nCRA9TVsSAnZWagAAtEQP/RdXG0J7VZX/CRCWWZ9G\nfzcI12FXJ/mfiSRH41Kq10gItkaud3yNIM3PpP3E1vFoSH8PB43nwzgZytWO\nP7IWdebvKyemgQa+Or7t4fON1b/nj9HmWqr3wkeW9yDLwgUuz1nDJbCyiOln\nwR0XnURGJ30+mxM35gJremCCeBq6xNz9S52/iTsLknTB16f/2n5oDZgl0r/H\nCTH+rld+RnhKVpNMUUwAwF+sfPIf2684VCZeEuG2MxcqhQ/m2yvXG264Ip4+\nr7OZShefGhJCrZLFl112FsyYrB3H4hDGa5hrViA8tCNz5J0GV9ZDAoe6TEbz\n+SLQEKbNTlIdSL9/zutJ+dTy5rMI5XPYmttti7qKTaO7e4qkcMjs8jzlwF0U\nQ0VxL8eof1RTo38sIx1YGhkHWRjje0q7vq+y/+7SF9g2UG8JMg3XwsfGoYYu\nP+IGSrROoz/162bWb2sgs4YKQKwakhSNnUGqLZEERZ5rV9FYNKtTQ8qTIO/+\nDKEqeOaYLcIWZZqmjewh2eaFCUVC02M+hDU+agdOutnT9StChgCUj4nSdsVQ\n7uGHOPouhiarOmM4+ev/Wp57YRL6woQtIp5KUWR0BDBNNbfJ1X1TEGPfshNN\nmix2AzegnRoMeIcEhsjZJvLxoznXn4CD0IlPbvIFhmgEnnVSJBGqYdG3Y4n7\nHCz/\r\n=XC3K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.13.0": {"name": "@testing-library/dom", "version": "6.13.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.2.1", "kcd-scripts": "^5.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "8509d3cceaebd900c193e9598b911e4690608fe3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.13.0.tgz", "fileCount": 35, "integrity": "sha512-kF1mnDK3eOCtOGapkbQK0fXXL4/mFydTSbyqNTtUHEf9tO9Dt33fwZyVNqeVdGiXRP6r9Ti1Fc56cX7fGr5ZJg==", "signatures": [{"sig": "MEUCIDh/LMu+GcyhOV88FR+ht+BgeEhB4vq+dXI0V+DiE61cAiEA/i8CehdGcYofbZkUdSmID5zC2Q19u9xDfhrIx6LjuhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1682207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXxhNCRA9TVsSAnZWagAANkAQAKRd0NknigxZcWOkLP+w\ny9gkVQAei3KN21vQOvBj4U/OXoOG57tHayqSk9jtUJlFQX7lFjF9zv4tnLKc\nS5F+ZLQx65BZSFABNpzRZLyxrEn+mHg/lvY/UH/N8LJDefy4M9YYYTC4jNNn\nt/XWgxijjNZ6QkkbJdBV/dlnCK2VGvi0B7szuYiNMLkr+BlE2y5mV8KPTwb/\niOLStNkeSAOawtqLkKzysZ5ZtAURH9EnmP+EXp6Hed8LxJ+ODbS10GmyRVMF\nuDDMuKE9x1U70J4IMx9xVK2dpr0/JV/ZCF5PZKt7sf4GHdeYx2cbB7Ordmuy\ngQ/sw0iobsa4vqYZWTLAfwSnzUCjFjmwGEFId+hrZQ/Sd9/6eGUEOSj0nkOj\ngM9v01SPaoke+qKJNNVvf/DNz6cAAAgnVpYYrKjL3aZDKdeLWEL5ksgsAMse\nudlVZHV/D4a5kH6cUN1yfBXzPW8p7ocl+e1CKWlQiyXJMNMUJb9g3fDcFgdh\n7tUuIIL9zUblul0sLC4pqkK2KLi2agKjfjwtAXMbsJfe4uKndkk8zEi4Gqm8\nokwLeFaCEny396pq6/nL4y0/aDDvLziZzAg0x92fCGKZs8z+7IIpaq/Xh3tL\nnpmQhSkrBKnAI3zV4iFEi7QEj1aGZPoHV0EYd0qZb9QE2z9FY+fMMOJL3mTu\nAjUq\r\n=8P2D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.14.0": {"name": "@testing-library/dom", "version": "6.14.0", "dependencies": {"aria-query": "3.0.0", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.2.1", "kcd-scripts": "^5.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "0004076fdf7e20a8afdd6b66903a119ff305d1d5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.14.0.tgz", "fileCount": 35, "integrity": "sha512-yy265Ukih04xv7opGUFESRuoOJKyBurN3UoNzHfm3cn0krGXrHZbxDHSPbK0VgpfSsRV5k1ANbIjhCcluqjSiw==", "signatures": [{"sig": "MEUCIA9ZKZvyRQVRsp8UpVPYgZEOtc93YE+IlbhKZRtQ7Zw0AiEA1tuzFo8DL4hlTI1bEZPl/kXhgzoPVD7ZgDcRwVD3xHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1761420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX9MHCRA9TVsSAnZWagAAzmQP/0DeT3z5zYkikuJHIFci\nZzM4ZNrD6can/TpIY8SEWph39gMSurEghfaM4fu8/s6rFzOPl6AAKEhs45Jp\nZfY7e2+KxtfV+Cr+0/Ol4vQKvgtqb64MtAD/yhcMUprEpuhIktxWnz7FrYK7\n8wcqNL83bjFslDoWFaUQKM3+BFtyoQSUVZWQs2aTgK7MfnphMLHIqhcmDC4d\nS0ALE+s3G8FkRysK61n7QG0h6Qfj8kF1FV0hQTPahUCHIl97zj0oRb1/LLlq\n/obI8PlQkTJXVByS7jnoxUmoiBoaJcTTjbQlLTGfwdO6Roc0uPK7H6Q5uYvx\nnTgW07cuZrBjW0WBrkgMn5WEn+1FHZ74ZLGV1la1dimIc0BGv4+grqEgeJsR\nXt3zRIwt+BgWVPQSKeNQGV8f9Qnl3VMm4GH3Z1vq3wKeBXDPczUAUp9L7jqc\nQF+Pr4vO2NvIuEGHexLNPbqmSkp09i3vj5oFK1T1HCc8mJhm+m7UPo4zdpdb\nTUjuAWai+ol2/fypP9Z8IAdzKOBL0EGUWBRWZ91ecItC1d71xL+i6e0fDPlz\nsHTtqBFnq2a1JqFRI1SZrrabYnRp/AyYJzJ6IZdSTboKj4dBuvRgSRSdxP1C\nvDbDuKc1ca/PdAEElsFVco2C2CWTLB+ILP1CyBnBNK9Z6gVYdnIldKMGNY0u\nPw7Y\r\n=MNcq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.14.1": {"name": "@testing-library/dom", "version": "6.14.1", "dependencies": {"aria-query": "^4.0.1", "pretty-format": "^24.9.0", "@babel/runtime": "^7.6.2", "wait-for-expect": "^3.0.0", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.0.0", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^15.2.1", "kcd-scripts": "^5.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^4.1.0", "jest-watch-select-projects": "^1.0.0"}, "dist": {"shasum": "8ea52918bbbd52dbeefa6a5ce961ce6c236c588c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.14.1.tgz", "fileCount": 35, "integrity": "sha512-ZQK+9YQmmgR114C06HRkWeY8DdTKgWhVqRV5kkespc6r/0FHFgBHqmV0URHIITmMLkPy9akoO3M7fi3M3ppLHg==", "signatures": [{"sig": "MEUCIGpNmQNivYjowvYIbWH1VDjxFpXIBduyqzsh1P10uehhAiEA6/VaPBYUdUilvXE5O6woHqn8BJmnLtFsB6WC9+1g0vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2254205, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX92OCRA9TVsSAnZWagAAUbUQAI5q1nKese9JFQtWpBoR\nVLYpUnc73xCsOZQTv45hbCKoaXtXG2kmDTB2aoc+zQGhujUsh8aAeGYxVIBy\nUp5YOFr0iOlkp+Ot5RtkdNoaK/vXBH6kdhqGjnR3HUz1lhHn2LAlfLWuM/qX\nEOmxe75KTbbZFjxdjMwBN1zjPGJXEKpj5OH7OnyuK0nw2YqA4KfC6rbwpqVT\ntGUeuIeQchuUJedM7O5n/2Ryyjdx9fN07IIbxFqRdWygNscvQHVXfxlodaoy\nHgCGUFCaULWFSTzwQukWtXn2f2KoHVZK1iYTB24vArQoEPHzaKwZ+DSjTz3o\nUdzqvW0W/qFCA02AsmrpJwNFpujCTmJ15Dx+kqI3u6MdC06O5PmbL8474oaS\n9xYRZ/Ji0O5ioO9PjIJ53qsSUTZVruunK04bfu2QhIXQGQo7QXTyVL9tkSbd\nN/B+qRcdLAwL8VDrLu4uDRq9aHa5FSzRxi9/tjzpCXdSVcIOakmaHGmDFASW\nn1KLfT5yVFFeKfM09kxlH0YhTtXSzJGSciti2xYhjqYZP/44g3AoqJ+gRROy\nwd6H+QviSpWIjnpTqW1Vf1ifrDMU7VxaPevmzCDDj1lhKCxhCnTr9ytmGE7N\n9e32aS14ljIJ9wYx4VF65un4rBbOh/9pQ/66dp69ZAmeeSbrZN1pC9juAFnY\nXb9Y\r\n=tm3B\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.15.0": {"name": "@testing-library/dom", "version": "6.15.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "wait-for-expect": "^3.0.2", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "042abea7b4685b70d9a919100da9024507dc20bb", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.15.0.tgz", "fileCount": 35, "integrity": "sha512-8N24c4XwOigPicwc8n4ECgEoJW2/mMzRJBxu4Uo0zhLERZTbNzqpL5fyCigu7JGUXX+ITuiK4z9/lnHbYRHLwQ==", "signatures": [{"sig": "MEUCIHQFMYbgZbeEeaHTtn8wWi/OBuBH94Xi4Py4W2ROU/XVAiEA17TncvuE85ciVZq+3lpxaw550gLqWByGyof3lrOn5WI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2242922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX97wCRA9TVsSAnZWagAAfvQP/1BNxLMcSDCu0Do1ETO0\nhFKwNKfbW5EBwwSMbeo2Xt4N19+Jzmci+hd++r8mdJLytRabHiuh5SM7qn8b\nsWCueLccuQthIB2CpOKPLPbS8vpmFZLqKiLPRs6VO68dovvYal+ybKtJTWdX\ngB8CmgY9o9lOQkXfoWT14vmEyzGmNgTn5axdRQ9xzDguYXdL/4aTh4uhhUq6\nhoosAPFTpDbI3r7saK9SUTPn9kK6a+arBxEFf8I00BFtpaQXdZx3ie661+U5\nqcNZs1ApC2MnY2fzBhiVsHmMQTOEDx7Sq35p6azrQP0LhRm/5pxLSIDztNtD\nymnsew0Csu80bvjICnOTBrHoxejQ+eSNGtEjxaHRVHXZhQbmP0EaCHJn7VMy\ngr4+VjSrCw65eO0k7n/5RKlMarYEhzfJys88BHxvak0z96c6JQVw9QXlrjPa\nyNeQIgpal5nTTNDIjhBYtEX5ZTvldr6wHfUFM7wdA6PM0GqmzvXdvOtVtOUY\nN/PM2cWE+uugSGdZxxG2kXOtCWuY3a/9VwjTL9l9J37M+GjRdz2E3kSaDTeH\nXzdTTHq3yXssE3fvRuHnXxqRgwBznwtfLQIAmo7AqGWjSwRXl32vCuuSh/1s\nD7UaKi0AvkEyNptK7KBpjiVD2yRm3R6RDKsrZfiNWBElWcfwG0dS6Dwx2KOi\n+MIl\r\n=hZph\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.16.0": {"name": "@testing-library/dom", "version": "6.16.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "wait-for-expect": "^3.0.2", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "04ada27ed74ad4c0f0d984a1245bb29b1fd90ba9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-6.16.0.tgz", "fileCount": 35, "integrity": "sha512-lBD88ssxqEfz0wFL6MeUyyWZfV/2cjEZZV3YRpb2IoJRej/4f1jB0TzqIOznTpfR1r34CNesrubxwIlAQ8zgPA==", "signatures": [{"sig": "MEQCIDUE31dLXbCY8lq8QKlY4eK/zy/8zQqM/SO09Lc8nEDAAiBTloOHCaThsoiVWi0f2/q0plBDu+HhqVxDeEdWWoDpQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX+dcCRA9TVsSAnZWagAAnB8QAI7nukoregf9l5U2847R\nwa/7EtGByxaVqWvd+QCJZW50yl3Gvb/dy5dhPrqDW7eGQWrHJccpqopXJzNo\n2n8O9pNyku8iTHow6Ubx+hkZ9z4qfVtxOT0Y07CoX9MaZ+mSHX0pJXTYgPaq\n2wH7JD8CrO7mrPNv/Ia0BWEpxoisLCdgev85XW8H/vp9CT6iYXwGliiF8LjN\nnJULwJ1DUzPP08/JDH0Z0hkDoInx55kbselKXgMLk8sptGZbSlwxGFz9Lwux\nCFMBn5gNVN5GfnlHKMrX1fTIxRNA9jcOh8cooAficMEEvE66ZxbckLGU18rl\nGDjmUFgb+waV0gU5nmWSmwYxqHlymhO2PFzV+/Gwg3n+iw8hM26XKj3ElgvW\ng+BaLpORLF+pTniIw8hx66MHqgGKm2xHcD7Gp9sd7YghDVTDB4hoHIRd/ehq\n7HyJen7klNgW62vnx01wdxaVr8I7URFKu6xjfh55JrG/uEjP8K28ZMYow2sp\nNl9mdfO7Zqui1OhpElbzm4USqiu0RDqlEeUN1ETXOVNqrQHymM5LoNHmM0O7\n66q/mD/KMbkAvHVkIavIPif6YY/f+x0ykBTADn1dEmCSp3CArq3bLVc8B9/z\nn6f4DzkRQxsYKTwIRDABPQGmJ/BlyVY8g6SI6SMvcoP5c9HSQeYtODFcJdy3\nYwQ2\r\n=38yo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "7.0.0-beta.1": {"name": "@testing-library/dom", "version": "7.0.0-beta.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "wait-for-expect": "^3.0.2", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "cf9223c2f5931ab8f839d83d0a4a231018fd4dd9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.0-beta.1.tgz", "fileCount": 35, "integrity": "sha512-8XNSD3wLGJiWBW0BRuvDjN1YvPwSiCvAhv0tQacpkt9HDeUZG8+IMZLAWsbSnWIdya5k8DCMQ6/0yiekxIaQiw==", "signatures": [{"sig": "MEQCIGQxEjEdjgKyFYKdd0zrhYHGEtK1k9krwv551LjDD7A5AiAVIz2dhWY4ufCT4wCkEjjr0QP00dKIov70LeMthAyk0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX+v4CRA9TVsSAnZWagAAUNwP/jQ389Y9LH3YzpbjLSDl\nmEQ0s2qA/jddlu+W3sUi9+3namLiIBob7I5nAq5o/ctaYW3GYeEEtgGdVq3x\nM3ktE5YJW6mxMDtWk7qsM+VViOP5pEOJ3Ic/XtXQdX4wEF72Is76BEqzCN7s\nb6yL51Zk95hK9h/llwEcjJLpn2+590jU7vDsMGYD+kdq1txrLltDukfWXyvq\nZgD8faISuMOVUJbcP02MyqlLZ8tzfjbAZeHFJaoz93RAi16RGRIsrztzFiuS\nQoRY63edrSDCuwDAKBc1eQdGr10heZDeg0r+7rvgGHg+WaI9ejI0sF30xnOV\njj64mS8rMw9kBjn6zcbbA2k6EhJcLAV+wFcCB/8WuEpSRFb28Pq5WBNZT3ha\nBeudXCCw7ZfNMScH6um/LyUfotCANuJx9vtaexHJWV1gWKmhbKghDg6yDBif\nb1OAJZbr/wIe0DEOFU+JVAuC0Hm5Dhmt6mnZuevaiAgZqTpsut+N1N1KILIc\nJVZJJ8WYvTJ/m/kc4SFBy8zh/PgXPUkIR4uDaYOHg3gdUyrwyK7NkLHuuhBU\n2zxlA7QZ7jelKqLl3ioxp6DjetMqmx+43qRZfPUU+NCGu9uOA8s3XaZP1gzV\nwV5uFteuOMqbuvwz/e2VNBbMc3j9ddPJLN+h64pTzFkKYYCuh7goo3DzawJC\nayOE\r\n=A4z8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "7.0.0-beta.2": {"name": "@testing-library/dom", "version": "7.0.0-beta.2", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1", "@sheerun/mutationobserver-shim": "^0.3.2"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "02e75ca853229461378e4d43f8a67e6b40a2100a", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.0-beta.2.tgz", "fileCount": 35, "integrity": "sha512-0iSD8xVpiAfOlsFQ0jPcB/e1IsZ0T49zsh3LEwvn2fHW4m9U7E++erhrhND33Y7w13mZ9/TPRpa95lBEA63+Dg==", "signatures": [{"sig": "MEQCIFoqWhgQaDQjkMGXacQfJh5f22WcpQ7Mrf21ZIjapBiKAiAnrC2qbZAPgzOdOwwHzK3QC6uyqirWUunbtx3KkkpKWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2347660, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX+0oCRA9TVsSAnZWagAA8rwP/iTiGMzShWV7D7scJjOk\nHG0jm4Smmadcwh6z+GVOUWpvwJKDLGk0gCgCgEaMndLdRD0D16+poM0Q8emf\nxo3ga+ZgF3QKEiHxmy7ejF1AnipfrIj7/gb1+SeArKpOLyVMcMXw53RqGFKY\n/JC38+jf2kO3Hz8MxcTJWyeTPGRFQS3jFxKn6sgEjrph2WfJ/ASiFm8rQMue\nkckYOx0ZQ6ooUq977wfCywketbEweV/Adxw8Q2ObUDrXKouVWLwey7zEA6Ey\nHuJ4KsmK7+5PbZ1ucGIq/COsNkHZq5slZrAfF5WkMguHWNsqiNQaH3agQomc\ndk3fmKiNa143sln2lq3kiopnIZ04sn1gRj/LQzuh4dgKOVcKOnrbSlN+sDJe\nvGLtYpHEPumSV7edMBIY8Bq1Th199RrEIX1BKNhid0sZkwMgtcbT4GBf6ifb\n/f2QghIeFmuiq922pVhPW/JePwWrUVlaAuBDVXeSNA1Y1G5l+Db3Hc623Va2\ndmCSBl1p4uiH8ljP+Fz7HWrbZDHauwADnClF94KUUhPA1Tgd2VyQe8y3sHzF\n07wlerVfRlUQ2TSNv92RnEv2SC6vOUuuZZvQDzyBUHd/se/YjGQJaERXsi9f\na8upXScrR4hdlQ/qybfk09eJDSU/N/QPpOAJJ1MmX6F6IApRIVkBD94UUgR6\naSTf\r\n=wz9v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "7.0.0-beta.3": {"name": "@testing-library/dom", "version": "7.0.0-beta.3", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "ff6a970e362451ba811de6218726ba6286bb49e9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.0-beta.3.tgz", "fileCount": 35, "integrity": "sha512-RDSQ+j10SGgLKt5K6TkRSH6/mGnaDWvaEkMxN6c20c0o6ZhqDntyB/XUHk4WDvAQMFGoul2SeKfg9dLvC7dZZA==", "signatures": [{"sig": "MEUCIQDXajwEgtZfg5QUu+AtDS6cGFjXgHYcqFu43QVdHjMxTgIgebDyU/Pn5OaLbY3fHjqsc/iCiqDBu7lfPaAy2NQl86c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeX/P9CRA9TVsSAnZWagAAbWMP/1fQVfOWkZqXFvouU50X\ngiryYXkKvM/p0CPAhuGD6voLGGfPN4Z45Y+WWhJkNrZXWSWcXVWQqjOYYHiW\nO6VR2NQS5dpDxPhAtLeUCa3HmTF+nRPMWwNrLiH4aso9wGllQhqQJgZVY0AO\ncDhVrwpaKwVUTJbtyeDRNqftnDSok9id909GIVoR6Ry8T5DMGVDX+mH1UVz3\naNRusuDRr+ZKd3zLwJOGfDpIhj/eqbMAeP2BY1ywwxUu29iDgnhuoj+YCTVJ\n6XSAGHWejDcEM+27fLslsAF+8JIFhDGUV6wik5390sRKjIbiLKqBWezRzYY4\nn5Hn9wncq1AKLB1y3nreC+46+ubNe+qIqERvc39j0e3A9yFPMf3aAvNuPGtv\nEgYSUpoBQijnaqbFWnTULfPuSIKMj3ar/3J1BU//uDud2/Fx79g3pukZ4WIR\n9WMgNXNH7xmMkr9JTLGeX/mEB7CJ11a2V+tJYW4BYF4Tv0+tCxxCqWuVuiyF\n+XUgqHLHJlKdw4enrKe8IlX7mNPLjNbhZctfm7CinsiGwTGomf/NgvnNWGtC\nyg5v0WQpIfv8wHYd3W22P60hnrxWZB+/7eZvRubSnFaJWk3X22mdS6exoafN\nENj2CRfRyzvyC9sCusfTUi+pKEk19G8kj3J0fJtb7Rqv3lArxbfaAk0P0acL\njvhm\r\n=ZFjF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "7.0.0-beta.4": {"name": "@testing-library/dom", "version": "7.0.0-beta.4", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "611dc2110552e41d0ae8720a09261e229d77e991", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.0-beta.4.tgz", "fileCount": 35, "integrity": "sha512-Lv+wttj/aVnSvykV9Qw5BK60Na5IAjz75Sy5TYxdFLCakaCj4VXAtveg2pACqI6Dh0aqKF0oThq9KJKEhccUXw==", "signatures": [{"sig": "MEUCIElZGOek9fPWVZuS990b6vBcqrn0z5zoGiAIwD/oubfJAiEAt6DSH6Nu34C8FKUcIJXjzT2FmJ6XfOBXmmg/KfI5vrQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2250125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYBBlCRA9TVsSAnZWagAAJLgP/3lLdx9kaI0A+OHmp7eE\n77gT8AUEfj9pjZlX9d4z7haBUGx1xqJY9PP/ECuuicB5PXUnk0DcgRsZb6Vq\n1e8Bu0Wq5H8weCbOL3sYrcY+ED8WKZ6JrvZFhXBNU7mD62aoBZwFOmoR1A7s\n8nQYMcUf/Yio1xAWdnJ9rYOftgjiUNsE65D9tyeo/rIfH7DTBMZvoICcyKqq\nNi8NT+docAHj5dHTQD5Xedx159rUrt8sQaevihM7yXQcCabX469CBcXtg3Jv\nquj95ylPJ6vkP6CK2dyqkkg7rLXnBuxMUgo7IG3Y0Bn8XldmGZYavllTbVdS\n0i2bc96Y6jKsRW0YzMbR1BU4SceI3Jk2+spTkEd5QVPvpgQGnQK7b/lBP1l1\nkCZowda7dhTxpaKjglVocbwYhdNQo1seMjoqtxSotfnihOoAOaK2VIU2dHEk\nNhsR6UR8Q/hhyoD3QfP3JXHI+7GUXMNG0Pyb8HWGZWAg6reLK5ioxEFlDCDn\nGfoYaxvyKBLL08nq0LL60N5s4hMYFCIuIxoAdBsX197+rz4XglAgxowLORlt\n/61/FgUhdu6NPm4RuXn82aLIVEYJicjHRGCRGnf1QKBvcVkTD6sI+eN8gv5z\nnhOUkYGmw32Zd9wHnjN3+GSEDu1r2zCWS9RW1oGOmBK2beK7cwakw5/aoz2A\notuA\r\n=+Yqu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.18"}}, "7.0.0": {"name": "@testing-library/dom", "version": "7.0.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "1828ac29cb7317092675489832af36a71ecf1e34", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.0.tgz", "fileCount": 35, "integrity": "sha512-rxnQKXeP9rfVENpw76bv8bbwywAsS6tTQZVyXDAe3FvQlm1dHZhlpVoToWRygKupwymuDrJysK4EpmH/Ehwl4w==", "signatures": [{"sig": "MEQCIF7vz9n5WlesCQcKxqRRLkNDN6uvyYJGqdlxbnvOu/hJAiAI4eIIAF3eogax5pFR0a2AenltEamVy1g21a1pu7gw3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2255457, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaq/LCRA9TVsSAnZWagAArfoP/jYyvkBwgGq6CISstnr/\nDdUpbN//1LcBQWkEJt5PUaoVCqGL+6GzLOq9Y9U3eGR8WE3L3Xm9T3Jxl7Ni\nqbscyUWr1UMcBDVXUZrySoYuHRN0GAIgmkApoPd3iyUGFLe5rXUKNNw+WVoa\nuKjs0Kj3ig9JAJFrpkCSeqZwRdmC3Mpn4e07IjKVaVtj8YxT7BQrjwcwpH8U\nvdTGtl2umhuzhSmdaAC8Sfohs1Y78/E9eyXFAoBZJ82wBNI8MIVkCaHFK3hk\nSbhp8a2T40pfTH9MkQU0+rdHS2aeC8qYdQr3z2tD4Pz5oI5jWYntWrnaVkvn\ns9G0lzr35ltrh8No62MpKpiRAZyJXaljYKJx4TAODzPC6u9XVl6zzmRYqWou\nZqHvAf4UiPYKYp6TwmIKpRlOabADmRCm94c9guOFzT1f1fcMuN7VjEJUX83N\nXpGv3v1BdwXBEXr/upvS36lzybELakJR9KwGSyupckQpKXC8FbHZVzMD0tdO\n3grWHHqNu9rd5vNXijvZJ94ChlE5M6M2odUcDHhN/EO+n5qIaqBRM183wL8P\nnhlAJiFSFXigszU0iZb4CEWZdutIXUCjhduwm5zW9VEurNSUB2fnUlrC7wuH\ne+Aomcmq7Gdb0vB3fIHXhWmbEtVyyNueYGHYmyGHDAebAmMnSIfkxKlfwdA/\n6lzc\r\n=KSdR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.18"}}, "7.0.1": {"name": "@testing-library/dom", "version": "7.0.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "33999d6c73341d74baafe2a957d0d2ddf18935a2", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.1.tgz", "fileCount": 35, "integrity": "sha512-YxJnmr9TjjODHtc8K57la0Rk0ISAC/pw207PkZqiTsnZAbldW+SRLtlkovZ/39oJ+28XeGLqR04CII/JJTn+cw==", "signatures": [{"sig": "MEUCIQCImAUMRraU/KVwc8QAHEKRBiG3uKu9jbO/Ls9E6OA0dgIgGMd+QTeopFgIpcP6LvsdevWDzA60NHLoeI8qmc3CqqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2255576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJearqCCRA9TVsSAnZWagAAX70P/2vHdAvDCPOQlxOU4OME\nC/A3b/h8sZ90SDWpokS0c8HpOmuNMDbA8KL1+rh6PkUOlvGnTpDLP0ynjdoN\nhBuf7Klp1APHl/0S3Pl4F9jQDTbct71Tdd/DOCTNSbiBXtIGmKSpQa+3/ogu\noFIjwNjl3cxzJ1Iq7RiZIVkjWTDjYooYQAxWEjuXXueJsvjkwI+eBqXyszBS\nlT76spJB1HCCTHzMQ0WGJQuQsakVomaXgOQFVk3bg+JYYMeX+1jA+9CWNF3l\ne2QtCFOYUkQtdbzl7OA3ETnqGLX13Susn2/wMb8DUXHSc/js47TCYAnRykhz\nXTsfd683SaLQ8EjlSb0dX05F6+5Ncu5WcYANl9fS4RuMbIJKWq/ZChw7X59I\nGz3/ouFlYrW0kWBS1tRoVaZuhgF96t8He3OMl/WkzcKVWDelZMn8yulDXggF\ny6mWuZ0AN1ie4o1eAhgRkwGDDbx3vfWxPhbYkssHZREuFfBqcK7rslLfjJ+F\nm1k7usVzTwhM1RSSB8YfXNLHUUoEGRA1RCaUW2V1bhFLxXWe3qRhFYya1Bc1\nu4ni7PoyKWM6vugeVL+hly2Kt6BzAUKxSwPV4CutVpStbDJLokMxdYVFnA7n\naUrpXOYTx79spMmFCnIiCFbJ+PVTKaQcmdb/aIrbKgSmvWJlGIEYKp+a1JDn\ng04C\r\n=EOW8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.18"}}, "7.0.2": {"name": "@testing-library/dom", "version": "7.0.2", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "9edc922dfe2644a3f0ff70f256d8c78a525b88c3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.2.tgz", "fileCount": 35, "integrity": "sha512-5pqMlPaBC/5bvvFtuNTTcB4/Vg8NjZfs11LQQc08JhpMfx82W85nVw3re4D92A/C5O8zDkxPxyYPWNp8PtnQ/w==", "signatures": [{"sig": "MEYCIQD4MVhn6aHQ+yPHav3hU5jv7MLJRQfml4vXrSuZNv/7HQIhAIWYhH1XF/flCcTW5OeJ2EQ882LXY5Uc+KiNhB779bVX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2255204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJearvICRA9TVsSAnZWagAAhEIP/3iG5WRNEQVRJK7vMKsJ\nXz40uNvLb8Gdy9QeaSNBiqnZKPg2RkmCERGXtK2+/W7gZmbUGzaIKIAMrIDV\nJzQfht8ThsuaBrBQHm7pmx97sgJ7zGnEaU7A/w+/TuxbJ9OPDlFeNg3byenI\ntFEFrqPIqhn/eA7tgDjnkXCZCwQUrI8sqg++XTWQjCjEqm+2cAHSHMIxJuqV\nSSUNUeIVOIWL3++qs+sZEzBhbb6QOOH5+zspdr7Seb+Ey2e5iTDf+NmfTrKl\nIRnTX/Hwfb4Czmhi43j0R2U5D1np+OCt1K6q+B5/4fIEsDQ2uNj9sUpuWUzy\n8iQrnSkKCdY8WYAG5/XaZNYrWlGDJ8tUKvFb0K4fHeuy2+GPWCZ1qATiUN4t\ngjoSM+TI4Wh0bRlYft/1MXv8/A7eDhmHIt/w6iEMnBbSvjSpuNdSS+iYweuP\nHmwv8M2zDDP3oFxJq+6YqFQ97vojpzYsqBi1b1Z1N8Z2wMtfIdBZZVn2BKQR\nyvNn20lTugU2j854PtB3MiOQw++ls/zeDQYu9XjDBYY29YXn9STB13485TFS\nTYDDlIvauHYkAEpmNJrDice0jp9DN0kCw134o+Tqjzm49Q3GbppKee/Ctitr\noj64j4sp14fcRnZOOw9WJVovcu/L0uoY9MeQdPJLc51hPfsUXKVWVfNL7zdu\n9Uat\r\n=zozR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10.18"}}, "7.0.3": {"name": "@testing-library/dom", "version": "7.0.3", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "284815f9995340171a4804819410db7288fd26b3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.3.tgz", "fileCount": 35, "integrity": "sha512-zBGQX1Ik+dK3nCdLa9jY5d0DHA4Bx79zaT0Zlv6avOcgop9gf6ff/43Z4rJDDIO2nFsWdoLdoJQY5VQ4TanLsw==", "signatures": [{"sig": "MEUCIHc/d0wP5kdKCP1O4DmMl/EmRt+s3LWI53xuqp3zkLAXAiEAq540IwLPVTwrMyM8BhAaPbeXofi/H6i3YhejTUbYVtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2255543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJea4ykCRA9TVsSAnZWagAAfOIP/RVXlqP5plkzQUc4+dqK\nmITZjRcLTFIprUXF7bipelguXwUi1+TR1nhYpF1XSR4kRpyzBurVbLG+J4JD\nQQqtHCtjupRoxuvdEdIubXti8ekDWRrg9X6IWJO8OAqfiG8AwR4WMK3XgNfs\ncxt+BDvYz4bRLzJBivF+vvsZap6XPjqC2aHkVW/gFD+prE8My28kaDiK/W0Y\nYDsE1Vdk1Sxt0OpV5PPRJ00712q5p5tU8WgfGJK8rKI/OPniACngOYGQDHYt\no4jr8kAugDFwa+qQgkI/eg6OeEpE+8j8MNfbwjvzro+s8eRtFjBTNGB4I9Wr\nuTKsI7gP52BSFVYOf1dxIvUkQmt9vFJoJXP6EBFuJxzJpv2vjwxkJ6MSUM9m\nCrxhRRqh+KZuhDe94KuzuReiQw3UDPlGYrALyTyFgrsOC9Q2dMcmqsV0XyZ2\nk/rAQK4ab3lyyLosQ6/ecwxX1jrF5rnIj7wUfKe4I3XS5RGFHbGhLajAyjoN\nOmjtfUqcFMSUnfDnzeJvuC4mFt/MspN3gs8BY3MCuaaVPKI5vk+PyUk8dyzG\n6Op4s0Z3htmfEAog62hy6z5CAACO+w++L4epfH19GR5t0tPr0EhjY5Nqhwbd\nKN7+yJIFNg2zWKsmeZVou6tO6dRPh8O39AG3u3hU96UlMOiCLtStNtcm/wX7\nI3hw\r\n=lbzY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.0.4": {"name": "@testing-library/dom", "version": "7.0.4", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "89909046b4a2818d423dd2c786faee4ddbe32838", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.0.4.tgz", "fileCount": 35, "integrity": "sha512-+vrLcGDvopLPsBB7JgJhf8ZoOhBSeCsI44PKJL9YoKrP2AvCkqrTg+z77wEEZJ4tSNdxV0kymil7hSvsQQ7jMQ==", "signatures": [{"sig": "MEYCIQCA6e0Ibz/6ADk1CSLa1L01qSeclmo2EnSD4OYvwdrUbwIhAKcRgbEdCTmdIFm/Z8NDopNrXsUZ7gnU206lcAYT+faT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2255844, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeblB6CRA9TVsSAnZWagAA2kwP/iylaeJ8okUGjqky7tXr\ncjvxolvLF6ZFXiFpJhhG2Zxj5OfMqp6ZjjNglqZRkHTajnPzjo6iPE9r+ks+\nEyFJAeC9W4YzsCYB4MTR9cMQiHWn1/oP2mssO1rMrb/3Naoe/ULtx7ryzgc5\nqA9Fx40MThzReA2HKtxl0m5DhzRqEKijMezZ16PdTZv7nMECIelv0rTnEVpE\n+Eu9OSVNT53TmjetFEj6kFmhbAy1k+SK6WznbKjlNr9x7amj8UABcvcqMTn8\nXGjIHloAXj7TnVm07VDsy5HjHa2nM4TeLpv+kycxLcr3swLmoJyvYxXV0uR3\nzd8lHKypjPcklGF/Wl8QJ/nixAR1DdV6E2TS6yOrdXKPL0Kiz/E5ojloo5tm\nIY3x5dWstdO0X2NQ7VDbXtkbIzKKgY8tp7Xo+3uvyxipuGHcmYkYN0f61Ydm\nIkH7EmQVGQh+nDCtgiDkKcU9rfrxmXEFDcLG7+7jb+KPW0O+C8pz6NH10rmW\nu32hIEsJnDIn4QcJDM/hQfUcodv6Y50q5qxLI7xIC0dzzCU0N/MWbSqxmu4D\n47QX20G8f+kcx7yUTSkW5NZ2Jtr9TyLH5QZnJeEAIFu4BR6y6f21NtseFbVU\nPj8//BT6IduRx9Jtfcs9+ELPXixViCKdvKtFzwR2HIilekhDJ0zeOZvIrht2\nHr3l\r\n=np7O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.1.0": {"name": "@testing-library/dom", "version": "7.1.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.8.4", "dom-accessibility-api": "^0.3.0", "@types/testing-library__dom": "^6.12.1"}, "devDependencies": {"jsdom": "^16.2.0", "kcd-scripts": "^5.4.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "edf364ac2f2294107c28a2a4499f7409f326d895", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.1.0.tgz", "fileCount": 35, "integrity": "sha512-/R++TMdxmhPdcGxamIQLovh2fwheYeO5UKOVi9IgeN+tfJw0/4SPokywevlNQXW9x2QDcaR1LqFO9FI+yHNmhw==", "signatures": [{"sig": "MEQCIGgV0+CLdaoksSGjwGS6H68y1MIrVMKgidgKm6dPSzVXAiBjCO/A04w9QWQtc8gzpnS8t9f6mfU/9fQxBDvyr+R8FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2256742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedU7LCRA9TVsSAnZWagAAvxAP/3eCLn32Puis07F27XEc\nV29Mb1W/AE3yFLuMTjNlx0pIW5RLqgI9wRIlCHBoBJkAAtfiDiyoSE/cxKTC\noWrNRZHTQqskZ15Z9T6+Zh2KG0B1CdOIad2vmssobxDb55JccxgUWFTcBMFp\nMrj0BsTLs/x/pSGlNRUlOJ4vu+vPnyihc6sucRPNZ+as6Fi2Il2IJ94mNNWF\n7Evr5lz+zq2OtMC5AP8C4SIHVx0oGvEYFyFKqLWLnk8c7qASEzusTu604HFG\nhqT/xzD/DK3tHZJnrFpZI4+yydtCw46Z9VL3XWC0pvuFBNTdoKPzpC12jblK\nCcty6w/v7WQuIamqD//lF0s1+vVe2Q6rkYUibLLEwyoqRw8mOwXRNH6Wa/KE\nj4ngm3KpX7Csnss0pYaU+cgwdK/5/Od1N/+bW9e/dhkHdgpWlw1k9PhviNzo\nQmn0NYG0vyH7mOerBe2o8DEK4kyUh+fdmv/F004Es6m6AdIcklQth1PCzfl5\nxN/+lqv/MsG10Z6egLqokQ18eeZK+yY9RF3GOmbrSeqBJzlva9tmVeYe3XIt\ndu+e5idZHPi2Mt2N/Z64nDmT/1xqMwTMMuaGTlCSpmqPAeyY4NfKEFayGJIa\nC+rU6DNqak5qiSXbCt+p/hZCjsXii1jLhYhRWN6hwUSClLxdgVjKwmCczgRL\nX7f6\r\n=1aYW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.1.1": {"name": "@testing-library/dom", "version": "7.1.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.4.2", "@types/testing-library__dom": "^7.0.0"}, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "bf890c2468bd717ffd8b336a819686907fbe4daa", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.1.1.tgz", "fileCount": 35, "integrity": "sha512-CxspAIntyK2joLUJChOZgnwx7xBxdBC8ugwP+Z49Dd9O3sGVs0wHkOUOsfBVXHgBjmdZk8E3SyHZVrMRd9O1sA==", "signatures": [{"sig": "MEYCIQCHtUvrc0KjJAdkxRXijq602TbxSNBLlZp3hL92bWo3yAIhAOML3rVvo9rUjaZevBLVmoQPYy3IL31bGxxBTvTkrOOp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2282961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeMsrCRA9TVsSAnZWagAApE4P/23yJpxJ9EkrO9m2NH2s\n31DFrb6K2IrP2WMOi2s2T+dirqzt4ZJtJiN99scofxByWFN5EdhJYRvrAlHA\n4g5muNxQS4ARk/GF/f3v9RpzHcBwmQdhSSA83wZ4kDXyvlluOIRcOHysG8rn\nCiG5QLsBb1GceRHsHSePaVQaZsUxwhlxmi9BEO6HiaHikwy6n4+Iibt6YEXw\nmKBEH6R3k2tAFczMO1RMo7Pu4XDX5Y6W6YJVKHjl2f07VPS8dNoVnHIPX0zj\nRAtRKpIjN48m3BSKZxRyi5/l6HqlAI3OLS0psvyptStgyeGU8fIK2rA7wWJP\nInNftDFR7LpIfwEbbDTVnYuf4NDr+s+nfzSwIJ4pu1KCQe1/i4qM/pDKpj7+\nVM+tZcW0JmDu+0WpAsGh0zklIEXxmMRYEcbYSvcnNmnD3haEatxetmlLIph7\n0cGDemaGkYz6LyCFP3z2WZUkypTRex3zGZfmL0i433KP0S7C4c+U7a47ajmv\nC1DPP5teJT1bzcfYhIORkaFJeI9UnoLOx3ncAz4y3Si5ErqMv5p3Y8heMmOJ\nRKnBbYQYNrv5dWPDO0EZjTXGCg0bVrelR6m/zagr2EwZlsGcIrlvonDFwQJn\nu3tL46h1HuzSH5DK4jiiHg83QTNY7HkM50Q9i0o5mRpt3XUDsu8BVsUodgAS\nV4w9\r\n=okmp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.1.2": {"name": "@testing-library/dom", "version": "7.1.2", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.4.2", "@types/testing-library__dom": "^7.0.0"}, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "0942e3751beeea9820e14dd4bf685f1f1767353a", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.1.2.tgz", "fileCount": 35, "integrity": "sha512-U0wLMbND1NUMUB65E9VmfuehT1GUSIHnT2zK7rjpDIdFNDbMtjDzbdaZXBYDp5lWzHJwUdPQozMd1GHp3O9gow==", "signatures": [{"sig": "MEQCIA5iEmIDU53NafnUfrM3KvdWYr6Xzr4xoS5OVFah8un9AiAKnoTiSX8s5pQeUy/jjSr6FcK1+sWFbqxDbiYGUXk6tA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2283296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefVxZCRA9TVsSAnZWagAAqYsQAIQlYSsDMfLSXxZ7o943\nIyZuBzfRJ7eaT9pKiz4UHW/1fkX27GOgTjpR76SJ4LnhjtH38VpwGkZumJHq\np40TsZhD5xhkaQoIZUB4AEUoKxLFqnXy9yvg3sbe/GX831KC9XxPvdv6+bJr\n96xwYGhaziRfIJY+5W4U8TE1VtEIEQz+PKNTMfGZ6Q17TOgiGUEYCgDggLXi\nkIawQDxcbZYWD6NOooXPMBD9FdH5bYiVVE5L+hnsFOdJQToaVtlOsMwD9PcQ\nL+5q6dAEvrdWTNLFbClWaOxUKOsJVlYhlKBhIsK8i5vCnPBISUGdw00n5GAo\nlvInjyT5vchvck78nTZBQKbVcTRcZq0DkgmBgyQCu5ebJ/lyd5qrPdhVZR6u\nzS6WOpB8N15jGcX/U1942TzJxx3YM4NryvxVNfpJtvYBnt2i0StXeFFAy6cD\nZn/uZWMUgA7qaVWElsbTyrAJ6Ggsu85XnUdwOUfLBtpI9A2UdvgYxseBPuS0\niZwgeTikX4GpuybgLjbJ+Vv1rlCeF8dTigzYCd/JrWCsZj4qhSmcLWK9yeVV\nWLkSIPRkgzvFtmjBXr8GHVCufF4/X1Kw5HXJo5pUwvM+lfFfd52bldyqOfCZ\nH4Ysc1pnWRuzB3+wiow9Hu9OPU4QNRC2TczX2xfnxT6to/CmcJJ4bvqDs8xW\nWEQv\r\n=1IT6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.1.3": {"name": "@testing-library/dom", "version": "7.1.3", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.4.2", "@types/testing-library__dom": "^7.0.0"}, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "422073afb5ae9bdb910651ac4d51c9ed4382a2f9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.1.3.tgz", "fileCount": 36, "integrity": "sha512-wFMQdKrTwTTlTe6n0KN+KjOtgOV0viIyfr+f+7Csq+1pD0o+ho7FrjCjkNpx6NEsx8jHDtZ4WU3cGOuLc9LvQg==", "signatures": [{"sig": "MEYCIQCvMf9+NJpyJ0aVPyMrWdZ2B8lIdZldKXMe5DOqvRF1NAIhANX4DUz6ci2Ed/aQ3qOysGRPUvIQfae0Vr+9qdNaV+jO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2293538, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefkuxCRA9TVsSAnZWagAAIFwP/174y5DF1qA0jjt4xYdi\nJ8bxnLQjwgwX/DxEYMC3e1UH/DuZ3y0+Iuhj3S3d0vHHYSV66QdBFCxhjsyv\nk1C+qzuc02eM7F2oIsUDcBMYwr3TzwEvAO3sYS4g8aE6KzsdIs38v0ZUwa8H\nOBoKdGXT+M2Nul/nOX2f4EeRfGQmrkxmQzsOX8PgQLjiF8X/cta3Oc42H1M2\nm2SS3nP/sII7me3o2yvX9i33fLD4SMG4+FesNhl0lGfArTILyNTm1kDjQZ3C\nauNpHI2/mtkVyq/maoDaSxyskA/jbe/L8BjjioaXYEBBGirMd3WGDkN9G2Z5\nj2L+MM2NlgbVXdIQKPe4gycjcWuwKEc6wPiKWR9usZaT8zAAVGUFDeGfOu5a\nQoaki1FFz76alUAiWA4NYjNjHYB0WoGwdcdpqvXUT58q5JmKhIRgUAfpRJqL\nIwUqekv6K0I0pPFyzDOKLT0pyAx3HgiT1GlXezsu75SqeUq7XtJfFNiey9zD\nMjonVt6riaHVCvVB3Qcbs7wYkVWZgVgT0pmipUlKDQTBS87rVIyhOUYNpLHI\naBJcikSrpmdqFZ8Lr2M7NJKx0nEC9KnM2UQSuzTDNqxbhYYzwBXgZYgPvgL4\nf0p04YzDxQKRlVWzBEP531krrM8xrTZ7fi7tlkFg/gpFq69rUMDC7f8Ge+yP\njtib\r\n=HP3v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.1.4": {"name": "@testing-library/dom", "version": "7.1.4", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.4.2", "@types/testing-library__dom": "^7.0.0"}, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "affb29bd303b01eb0937debbbc5671dac640086b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.1.4.tgz", "fileCount": 36, "integrity": "sha512-eifMaV8NW4xk/moC4+ga8Jpl6MNLNjQgBgimDBqmcZPVyDPcWYsapAMcp1HIGpVsTNAXBVB8ILza3uyu86v4ig==", "signatures": [{"sig": "MEQCIENSyk4Zv4QRztHd9We1gs3oQQJBdZMtV9b2D9nSSiUGAiAyAcSBlz6xHJr6J4lsFh3eO17mStsB0iCFpzeIHWNhMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2295086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeghOdCRA9TVsSAnZWagAA4iYQAI8ek8xUDzLQmUJ1U8A3\n4iO0Bma9K2MHRGgHl6hVzier0BkEHhIRgUSnXFDcAivEQwNZNvexxpVWz4Z0\nDhZ6uj1eVmwDG3z1WwI8T0kIfNYUXwZVzPag4CNsFQjtvUOnWEGZXnaXTkq8\nxNkdMiH5W96Lfg9y34TE/+AU2Fi793rgmtE9wPjyHAuUfx8N+mgmtkQwqxe6\ntzzlkdQdiG+CqvX8EsDX85AyzGzBz+89DNW+nLipEDl2ITrZ0slrrlFW9vXa\np7cafLruos3vm8jDdfzvw4P9JzOJCM28uCnSWmXq+wfvt20h5qjn2kSFB1EU\nU1FDZMpWwHLx4s5SRp+DR74GiMU+jslPYJ9Pp4gAncJ0kDTV5IE/UvEcTU8O\nR+GGhjBBNRfX3ghvJyxq/KfBwFQmSSLd2fu5lgen2dyTVSJa6tiF+O8C0pUW\nZXd67U16NdFJbru/hnRSCdDVaZOuNIYsD6RPYtqyD2RJxGQyM3d8gyBjT49P\nWlPmGBwm7fhRa4FC4vGIz8rVDz4BW/6kwDaaJSCFFQxGQuY1wkPRMh9W4f94\np0WO8+IMGudvpdrBxdoDXcQBWm5r5M5pgoMtbb5nqQK7yIdbLD5iEaLNHdS+\nJDitJjarG0RbMQjN6gzV1hnzo1sWZcHW9Hbl1H2aRS/2+Pjsm8o0G8Q403W6\nnEIS\r\n=dGTg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.2.0": {"name": "@testing-library/dom", "version": "7.2.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.4.2", "@types/testing-library__dom": "^7.0.0"}, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "948894c2ef52017d299c35da02e085498363cd1e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.2.0.tgz", "fileCount": 36, "integrity": "sha512-K1Sao38VxsTrjTkFkzeW8m/oCtgCI5lANCE7u9ZaF+TTL3uKuiZ+vazeurxjvRHAsE6PvXjOIl6JFuZfgcWJSQ==", "signatures": [{"sig": "MEUCIQD5/jCpXZ701X/pIZ2DBEgESY/mGctvW1K46XCXdYaKsAIgKiWEou7IAnskmLrAu5c/iD/NXqHGdYuES6oJfXEIfww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2296003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegxrYCRA9TVsSAnZWagAA+lMP/21vBjJ5URwLubuMr3OH\nna8v/TtHmhwdecwPhLkdDva0CFGGnqOd5QCL5j+yqXopyAuKKGKmJ0a5345b\nFqJv3n4+TLhDNrCUyNXPGchm2AJMRG70FewPBReo8Z7P64UH2gUjzLehxnTT\nfhgJrrWo1rIsAQTtNZSgJHVddq/Uf1n3+xzBSBU18CF+F9WtXT6r12zgztrx\nNoJhtyQdbn7UAmKce4lf1yjQUfSB+ZANxlx10b7R44fU5AwYMczo+JJLhoNa\nYSXzC5TOQ5T5E9aOKGU11fiPVC09K6Mgoq2gcdlRt/BnTE1lYKcA3DGb0MFK\n0gdstsW9h14UrCTkXtLlY7pyS1qDKA6zUCmYNYankJ4v5w3U08Pz+d++Vd0r\nXpb2riAe7/oQMzs8UZ+4XR95PEqbf0gwozrBosbe8SFCCR9Xpp6Ap6j/YR1/\nB61Jq0TFe/DKsnGTtz1k1tzpZE2ZIIrX/21C/BEfnMX0RwJUklcYObi1cjms\njZD4q13NWlrr6ZjYZBCLL075VMLS8FwarzXDJ/ZrZ238nqGr4AwFJWKVXS8C\nyeSDBQn8egO7Um/m1hzk3BqIb/KAaO7o6cgXYT4ZIx0YbqvVC8+DLpY5V8sv\ny1nLddm2rvaUaKXjkHaltE6TrdJXMOXnj8In/JB4JDFt2DC+Ac8xmZ9svGUv\nq0GD\r\n=Fd3N\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.2.1": {"name": "@testing-library/dom", "version": "7.2.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.4.2", "@types/testing-library__dom": "^7.0.0"}, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "bb3b31d669bbe0c4939dadd95d69caa3c1d0b372", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.2.1.tgz", "fileCount": 36, "integrity": "sha512-xIGoHlQ2ZiEL1dJIFKNmLDypzYF+4OJTTASRctl/aoIDaS5y/pRVHRigoqvPUV11mdJoR71IIgi/6UviMgyz4g==", "signatures": [{"sig": "MEYCIQDseY5BrcNshgcoAS5mdLhyvVprmFQy3t6KSK+58aMh/wIhANFyUhccqWhOrpMoYT8QUxy9Tev8sOq2cA6QReyHbcMg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2296824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei5tDCRA9TVsSAnZWagAAHC4QAJQMiCdwD3Nsw/4mBIV0\nOA3VMzz7eY6qwwK4x3xvx7U2cZV8RhK3oAj3mplMAHTzPtcXYs4hhiWQ+yZ9\nYLPbHXqBNBZbikf+FlRuBhuMQ4VAYnjYYfv7BzJ4BtYyB3N7lUJiSbHF8B93\nbUi5ca2q6qbr7rg6dF0/3D35LLmUFLOI+KZy2HRd9qYDDSKg5BPN0L2RFExL\nAVYxPmjjfhiQkmgkqv3ePcUAdzuvq8gqHHfCuB0pDfuClQzsZDMSXt4deLZa\n0vkVQrzqsAnY8swEoyB3RhAMW0ptlpO860aIYngdUZ13r1Fu0s8awdWJIrzE\nG5ZWzmXIImxGntzBgqd0MBmoizePPhRGkHV6s9AfNBAsCNpxiozbuEiC+riB\nSE5ga7r51UWpqx2n7SomP3cfvEg7tudTWTkuV3XQvIE+80DMgS2aOLOkSrdl\nmxu/iG5BLwXR61zRcTLGo0mgf8LjnVZajpg4c8mTC8dLF4bMkAwaQsYkRcjb\nDMlB414tZLOUVD6u2mfZy3/CgQgdCFa9oLoIamv7tzfu05g759F2sP3Y/UU0\nLxmriUGciB4n58YJq43VBBgbMvIF/7tEwh/nJjsGZ31m6hHQZkh0mVjxoRN5\nCrPGyG9RFDl0PWaZ7f4ve6TvkPqHAu0st0bTSyV7X+BRlACYaLCTMKS5MfhZ\nXmQB\r\n=9Nsh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.2.2": {"name": "@testing-library/dom", "version": "7.2.2", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.1.0", "@babel/runtime": "^7.9.2", "dom-accessibility-api": "^0.4.2", "@types/testing-library__dom": "^7.0.0"}, "devDependencies": {"jsdom": "^16.2.1", "kcd-scripts": "^5.6.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.1.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "30ab09cca132fe49b2ca61ccd9ed785c5f0a6fc5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.2.2.tgz", "fileCount": 36, "integrity": "sha512-g+gT//COYh2FgRrlgcgdkifkjqSk7wQIS7F8jbrf6yoEsh85PJUJ/QtO0bJ9QU7pQPYQgKcgqNJsOs0dlyFYag==", "signatures": [{"sig": "MEQCIATNm+cYk5vPzhcScl+dPupZRokdyK+LiHy/sbxC7+CnAiAO0TAjwyaGbE+Lno6snmcGpHq3/gjO/99cFtSc6UUPCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2436229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeo1uSCRA9TVsSAnZWagAALa4P/R28QbsKFVnH/2fL9OIs\ngRsk/lBUnOPZjJlAym4V7/hSLWpKbI7okydNHU2JE7peUm8vXeSPjUeJjeFp\n3gjACOqu+sh3z2g6u40PY8o6kXycrm1AjDo9WBIPyEbGH/nA/G1e1kM9ObMg\n6IbVW4DJUr/jnci+qGckA7Y2eGL5nT2CKjt0QvNmbqXFmj7w/82EnFOyTX8S\nGGpOs/wvRueZCl9Ng8RlskWa/quQ9cMeijvFK7hlzxFjybT8nigSz6oiJzPC\nfew+1DHw87OaaXp1Lrw9Z+Zx0LDKV+/YxIs/gBGOwaTjwS319SMIyNMQCHCV\nysyvOS4pMBkfFsGf9Rl8GLe+RDHtCOEUMEKhKDDipE3L3pge5rZXM4UXD1rD\nW/R6TgZqoMJFJDa7jDJLsCwHs9HwK0xJFcZ7lRdJ0SgcUrtzudguQwbAQNpK\nVTlEION6dEnulfnP8OqkswVX8d+OqlPLHElJH+tIiWHeu/IwY7r8uhL0Hw+T\n9gajaKX5+CifDSP5gJhR0Qt+Z3BdqOuhUd2BEosRRgQ2o7cLF3SSWRStkchc\nb0uP/wOMwwkLecOD4ajYQem3MLDeBR+GIzVooUbbE9qwSx4bPrztBG489512\nmIbo4fgzg/FZir438M81+hZtBjBjiQpUwdc5wC1vjTPpzaMBOJ2taF2x9f+q\nJRpT\r\n=XbWP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.3.0": {"name": "@testing-library/dom", "version": "7.3.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^26.0.1", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3", "@types/testing-library__dom": "^7.0.2"}, "devDependencies": {"jsdom": "^16.2.2", "kcd-scripts": "^5.11.1", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "f26edc23023414d25ed5b187d2db240f627f65de", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.3.0.tgz", "fileCount": 36, "integrity": "sha512-F/gFzOGsX1ulldWMREiDkoBg4/kfKXhXi04iW9irwwwF/BLW9dSMLyK0zqnBjCTx76A8B584EuYBhNgWNOnUfA==", "signatures": [{"sig": "MEUCIQCJv2WSeprtPfJFQ1p9iHE1ObMNYtw4OkQVKC9H2eq9CwIgMZY45KEIFxaBU3mHV+Yu5rmuwu0t7HIpu/HpJx4AMyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2442476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesaOdCRA9TVsSAnZWagAAx8YP/Rb7fy7Q14H1WnEGb9+E\nd8JjrEzxTBq5r+vZDrhf/10H3eOFgifFjSiAc5kaB2xvJkYZW4YwhGE99K2+\n7EkvPYpDKW0V2utbzo9E3kzkss8OUP5vcedbw8CcBXFp+KZvEQIpyQaoME60\nLXRNgqBufCoyG3ciGovQrDFVEd5iDjM1T4aQWU0YIY6c7+fUkI1bBIyiQGzC\nCY6HWqa+KNpn4RJ6RItD+P9WzYtk6KHEdfYehFVnQf9ziKmQXz2FYpFrBgXZ\nnPzrPV/R60ydH1AJKlIMYiSRFHGQe3ArzigG5Ytq7g2aaO6HPXZ6CtUI62HD\nYMRrRFx6zTbjjU+tXNpWCABpBcrzCUZVwXhvkGHt7WVktNXSKTNeQK24+SLB\nCSJo1cO1s9R/KJhn5aNmZdK9sdoq3hkYNEqrU90xrz7DvVx/6krJnrI/EGZI\nDK8GDsNzippreZduwG72Ocj3N7AJRJCObI8CQYB9IDU4UIu8QsN5V414IMb/\ne+y60c2KDBcltuEHCppAiJ/UaU6lGDLL6MHGQACx8KXgw8FpNPgYMhOQ7Gxz\nV2hsSRRWgB0n8Muly17lYeyeVJWc81yr9iHerDs+AdMrV4JggYL1Q6le6UUd\na0CJCTwK7PCKnjuHGpo0izHNefMZWVI+n/dQkYWAnwmZCaF59a6DRVuQywDS\nAbaY\r\n=DqMo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.4.0": {"name": "@testing-library/dom", "version": "7.4.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^26.0.1", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3", "@types/testing-library__dom": "^7.0.2"}, "devDependencies": {"jsdom": "^16.2.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "0c2ac09f209c1141324eb5117ff1e9e04978bbce", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.4.0.tgz", "fileCount": 36, "integrity": "sha512-47Zy7msjFXpQAHuqNNEeBm1wHn5vJGQKzMN9RA5O3KkPYGXMgBtCg16z7ihhFg5AarJIZxGX17CjTzNzDsfKUw==", "signatures": [{"sig": "MEUCIQC+hOGvxHDGrzGKtBuZ5sFDms96/ONc6jC8A4mdvY2CZAIgXh/6QE75/BvII0Fijvo6Qr/jPHKHr+Ovrt7C8jx/axE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2452130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesyU9CRA9TVsSAnZWagAAwcQQAIZf3cFc1gIFlO87TsQB\nUNAa1Sy4+Zdo24bpC7lw8CMUxusk+vo53arv6sLQVH2jUJ0rRbYLHyQ37/uu\ndFqeYmk3VT0/HOpq2xInNHW8gViog2sdOOrpF3F3VlkGG2Q0lQtnu5mGlRdD\nnjqaSMBDjAfeVwZcNanM3ZbXzFGn1uqfatBBSKWnCwwXag3TpXjLV/UoZXpe\n/eo14JGO3HaBIMjZNfZufkNou6ak6vpU99XfOmHAjvt2bVW+dxjyEQ/XB+pP\n7KKsuV9mPwSBg/Ejjb8mj0CpH5Eu+qTjfca89j5tRAstvSN3UzE58m9Kjg8/\n8tD27WpHDy03//4wuySgp1lMyeMbDo7ubTEFrILLdVRGzlvqOXQ2+kxf1rah\nrPKvqbldEbcqEA+GtpfE7mDx8c5CQdyZtRrO0kLGEbl126h2vQZNrgtueaYk\nl6/OoIO8Qy3wBolKY0rZOVDupgBTibmPg0shLQFPWTAXWmppCttNWNNRzHqb\n+DklBPeCHSVbhUb+qlwPfy55a9KAau4ihFEPZ0pjnVGF5e2DAeV6CKT3VdYb\nlaFM+YWCb9+CVROXygjM8HU3aTxXpSNQAp6JHyd2UcKAhO3VQsod2QfijEg7\ngKPHoVpwsMqRaAb29PBahz14NkkAdXIx2HfuJybjrT1VMWBOCy8R7HekbfQi\n/xv4\r\n=zN07\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.0": {"name": "@testing-library/dom", "version": "7.5.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^26.0.1", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "5ce5de905daa782b48c131790377d844e46af67b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.0.tgz", "fileCount": 55, "integrity": "sha512-v5YzEHPkZKVgopz76mGdY2pNj7yoOk0x59YDUVzqSRPrYxNAeT4cvpKsOzNXa4bwBV4CCjVNyNj8ukBCEzRERA==", "signatures": [{"sig": "MEQCICBqvIiYHDIjydQxAonuKUkrtaHhVg49njSM19XbsrY4AiAPsaap3qRrN89ppMO8rYvNYgkL0tSzvt+rnZGVLCCq7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2474874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetEroCRA9TVsSAnZWagAAurMQAIBqICXAAsAbEzJMKZL6\n8CV+lScFxWKwzb06xViZ/GiWGj2Y4642Fqy6Kr2OsiWxM2szvSrmJ/LBcnIW\nb82mZJ7K9TrPVJ+x3h6diCZDtyr4bcH3Vlo8sZBq6tj9L0sz+BGWrqMXS8C7\nQG14V6PH1qa0d1yrDRPWq6e5x/G8mreCHVXO61A+vLV6EAvU4qFo+3jnewHB\n7HcaoZ9+2ipM0uhYfsNJvkH/laQvVKajwBEapOPgoaOnrVQdd5rPmMAp4Dok\nUEQDx9PnDcKfIZl7HqVGoibu7wUdY+0mFmRNaBrwB/bOyH0dkxrL/ntNJv/t\n9B5zI0TT3S/CyXePYN3f6vuEgUxp1FBlvtVRihjV6A1+t0HPtXgSj4yr2GHt\nxQ9xS3A+1QD4by8qfPAYHlC48tT27oOShPssWYRJzCp7Fvg10HQrsSQSHXz4\nDOsfDj7lT1eykUC+mn69uWD+cc3BRx6uJsurx4J4xTvMnyNs0sC1OyYETY9v\nL147fNPKu88TbwbQc5DpRhRWNCgcTQt0oSwSGGBcd2XZAIDr2b7sjrcFxsuu\nUh6fl71NwaxLw6+8fZ56xuQvb7NtrkKumCp6GWS56KyFWxbAjKNHPngy6Efm\nMZNcJoJ0UeRPV2/YRY25Vt/s7ShovYM4GwFXBtNRLv5tr6bV1hWru1qTHbpI\n3wtF\r\n=YTkU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.1": {"name": "@testing-library/dom", "version": "7.5.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^26.0.1", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c3537e2b970576072ea4083e603463843c8a8d2b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.1.tgz", "fileCount": 55, "integrity": "sha512-c9iQWyFtdsqaVScIlOdgdG3HXWkYvGzin02nJ4LUnzNXKyxqeqq9KPIAAjnmfrSApbRc8mK1l6esiCeEredfRw==", "signatures": [{"sig": "MEUCIQDpaffRVbni7Ysdke6mpCr7vkRI3Ris2NMtSoZtYYN77QIgMqS/u7lZlirfIyKo0e7YSuazaqdriGYt+L1sy5iN5yU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2474848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetFt0CRA9TVsSAnZWagAA8OgP+wWQcmLv9fLmmBM6Hebm\nMptSNfSFW3qPMLdTAj/rO4LRYwf5QYI3wltpq95TbAdgMCNMWrjUDI3sUQsV\nCHlfo3lPLZWx3va5lU8HmcgIC5qIb4BjE6m9qCLseioFaQtS15z9xczkEVw+\nkgdbhvmblfS1s311neiqQsZ/Fkq4neMQd2Yq81taCiUCHVkI3xqJAQ4E6haO\n3L3HLzEYs4rQ0/CzyYlfBSj1Tkj6sY01MZGrJvKhX5YlD2ZAvEdrj8Na6pIp\nhoAIxRsFdrHCxsq2gMgJLdoc+ER9dxRNWvy2ZKwW5qPKeByU7i/7LcLBWIxx\n/N02gFOgQrdzpp0o009snziLwFWHOxKrwuzQsoTcbMppUEojI3/ZV0/MP6O+\nPkrdfpbN8Hk/h+eHU4o52ETzgNBDj56d7YMel6SKhlVQLNnTjMYBZjaLVXpO\nZyPhEoP8Qpuptgz7xAsvtEUcZSIJ6VHjInWyFeFEkij8O/uiPvt7Yyd8DVvL\nUMnvCaco52kiYNKoajo8jgu+MEX1QA/d2vX+V5rEW7PKqzIGJsZPBcirEZxD\nypgNnZjXUfU5F9dpeQi7PMLVTbx9Xw7BW6EWJhdI+vilhgZY+CfKX4uxqWv3\n6t3vriu3ISUZlIj2jNsKHcrp0hdMgnLO0jEeYnmM2ZiInqaK1if+T5+jmeRD\nhtk2\r\n=AoDh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.2": {"name": "@testing-library/dom", "version": "7.5.2", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^26.0.1", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c899d5f5d20fe9fd1e46d8f9c37677cb3e37768d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.2.tgz", "fileCount": 55, "integrity": "sha512-40/BsQLG14Ba0vUPZRPVxl02hg0LQGF4vZxlOw+MzN8wvjhPeAYIO76zymqC5aJpVPB64tGfPnDyjnnAvIfzIg==", "signatures": [{"sig": "MEUCIQD0mMQ3lPTC3j33b/cA0mWtMEQ/SJhG4LPSv+iQ02nzBQIgF0aHDAwE/5nCi02cJXNgbQZZPhmuh/K6znfXR04+hJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2475654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeug4MCRA9TVsSAnZWagAAQNIP/1so6uw6JPBOOPImAe+q\nd/RNbMowQglWCg3OJH0nKbupVm3wx3gldC86fhLxBzvKb+Z5ohFCc72riyrO\nGS+ThgCYWbEVZz5VUoPUgyl/rGNhIGlQaZs8wHJvxQjsKkx2iCJb52VJfxht\nN1qE/8vZalCrUkBxoyKkz68AEYQZLztH0PWi9uZkzOiDAkgUUg3OXC1L5Ixa\n3ubdac8ioXX+rB5CfYDOURwxHs3UVHYXjMlTjtVIBMAz7iwaJ6Wk/uMMUrrK\nzH8kwoXDdMIYO5l4PZoJ++lh8ssVs3aZNzGElFsxtrxfo3Qyb1qny7jK5GiG\nycojHu2ynj1pWBj6FXKsYRl6oqLbyX0C9+arRVqOoTLy/7xXBF1e7WTUPlAH\nf/u7cZ6st6ThjwzeBq5PExOylNpUiidl/Mf2S5TfxpbK6/BU6PeNKlCczcPM\nVgGKdV5LGrW1xLmlOlZ4KJGiHeDCIx2g35YvVRI7MH1mOQLL80IoS56IOQ30\ny82S3TC8JG0Tod5xdb6OH6h5Dit1/8b+IbCz9yOvQi0NDujz04kkEDjYSVQR\nqOiJoXGhZlRTEibx77HQFHQ4pKkxoKcJCLfXjL97YiiMM1aRc5W2JF70UXJh\ntV8kWbo9BMK3IrbskOdcSGP3b6sMnU7Epl8MMXvUGmbGxKlQLhZ9cjcn0vlU\nDfYt\r\n=CECq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.3": {"name": "@testing-library/dom", "version": "7.5.3", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^26.0.1", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "1f4311ec2c0c823bca8f854b97123d1a2ccb6183", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.3.tgz", "fileCount": 55, "integrity": "sha512-fTZVcD86FUhbLgbdcw5LcmuD2JwMWaRHuvuU1UkBiY959/QLMyU4sXWZcBQXlCGGJjY732riBN34ZI6y6YTCbg==", "signatures": [{"sig": "MEUCIQD0vTvgNbPkpXQd5oUrwg9QRZd/Vc6xhB6gBeW6I+zrHAIgfRq5JPYmf/KieF4d9hLSJNs+ytq6M5St9Hkf6RChDvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevHDjCRA9TVsSAnZWagAA3fMP/19g1Sq//9q1VqAUrP/g\ngdTwW/0X7GwYcl2LzHdDDrZQa5oZ8qajcRbGN9ZBXW11fZcbYnJcKa+EWyn7\nz1Wc76Mx4MWETYaFdlewnYF7KDIwQpuLDkdQHQTjPHtqv6wArxd5d7RjFdrB\nfv3vPS1NnBrzeVpjQ2QCY3dL/GKfDOqLbd/1r38ojGYtpm2aM2aQb5kIqYaU\nVXgMGUEQ8HboF0lXGarT8/W+KQhch58k7oPLBcWhZ8jKLVybMAKgB+StIqbl\ngB30aAmx1T4ATwPh2m8FuSB1Ilyb3WRu55k6861s9rDuZb2QFMCILEagyQBx\nggVniNnwZJLcL2GTvP24TeV59CvS7Xnoxy8GBeBuL20O+NBGumANrch9FNfE\nX06i8k0VM8q3vF4k0MrgHYRB0dOZDsCRS6HvITNfadxt34G27bE9ApK519Fq\nr03gS0R6PryVYEJSIwO8DHQvJUVo4InA956aulh7BcdX754rdg6EGNZw3Ktz\nX0KOisZUQ8/jBAF1qUEdmoPL9g0dRFuDB+gstc8W9BL8EhPMFSFpPV+3AylE\n0GS7sY9jlst19XAHLI+GKKnjrZ9pcLmQplzRlj9KsP1bKGi8vBfJsB1djr/3\nY6ZXS1x6mjnk2e/zfHnrCy3AGlabKHCq7IzrYcSn531VcipPPX+bdE1CVVxW\nHyeB\r\n=jaBJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.4": {"name": "@testing-library/dom", "version": "7.5.4", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "7404d498c0a7400f5bd0684702ef81ccaae31607", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.4.tgz", "fileCount": 55, "integrity": "sha512-wubluhNntK0yzDyj0z9x99mUfoI7cITJpajF9KWMOzAiecYGHKEfCcj4PwPNtPeF/b1v7SjvDfjY8On2PyOM9g==", "signatures": [{"sig": "MEUCIEJFgCMgLIOoW1iLgmjof/LsCJ0+P4vVzmL0wKAfhAxwAiEAtiyRqjqYqAxskrHgGjkIG+eDWVbk8fVneGzyMHeupg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevHXfCRA9TVsSAnZWagAAeeEP/3tJCjFCHIfttaj5FhrU\nhwzyjge45jr8JoqIaF3GPrRLkstFIyAG3aSiIn7EFKzcxC1I9skqkl3UKZec\neJjU7g/pIwAYbkUWTJXM6Zp318nWMQchnNzDWKEttl5z5iHDbsM6xQyNbL0I\nCxOXkUftw8DK7hIyw4nzESAhTHCjRIOQbQQOfWAkbJD5qBPAqdm+mUSH2eFf\n+nkI5JNtls3EcxoR14c0wGGxLTeGXTm261bdAg7EtMjYASIwLvt7B11+Diy3\n3SX/Jlcmch3Gr9Z94id6t73hgzCYjpryL1uLCnjCumlH1pdBgpsDiMeQSsYc\n6lJ5baQ7VqBLdjuDaliF1hs9k+dmwarEi8Um0DNGOfPTdvsGZ8Dvn9IP5EU4\nmpnz390qOr3llRMpGLR0Zkk+RhBosiwElKLgNARpLrlapjIMf7okbEG6TEEr\nQJGxdbJbF1HBFAuoz28mhjt4VxoRhu+k5RihdECp9P+xEaB47/lRSmIvIF9Y\n/nxJUe+2WgJQwPDjwZBL+SoT3qY2cw4vYiAynQuJUkai7GFLWCaKX5yh5o/u\nF12FrjL2K+bCWMc57BQ0asIP+sSjEvn9jWUh40THmFaXSthD6Xes6KI/YrTD\npmj0XxAAJxgBgTjfqJdpOFXSY+WIYcv4nKytW2GDxFlDSXTDRWbc/G9K7xze\n1WqT\r\n=8J06\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.5": {"name": "@testing-library/dom", "version": "7.5.5", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "af79467c5c35ec859e75277cc273ee4245f45b49", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.5.tgz", "fileCount": 55, "integrity": "sha512-KFVa+RHO6zCLZO9qz+q+6rbbH2g4J5YBVFP5srYm6r5aPJru3148EkedZ2XSnyNjJW/HEm8HQ2didKvxQxMOcg==", "signatures": [{"sig": "MEQCIEaATXk5B7ya04l29G3CYEl2GvlcLaZojAmOzP08o/8/AiAlBdTzlSMIt2S2RpsXBiOiTDH9UkYBBaATQ4OQJMmhPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevWsZCRA9TVsSAnZWagAAHBMQAJppq5fT/Wkd48HgzDTU\nH5XMY8drKZ7Jpp1LOPIfH6CgfpVGiAOd0RYhhd3fcm38EMb+iLIONajaadGX\nZsrD43UpG9u3lK/vxaVM1n45MhsEOHoLJVqhF9ti65v6HkkI+iW7xGTyMQWl\nMiUkKexSYdDYSUp+JYKjMHvBJCFCUEbH1IjnYbTXY4mJ7EL9IK2sLxo8NUUx\n8WDUXoVdan+EFz5ExrmQEQI18pGjnoSOuBW2YCSCW5njjfSuKthtJoRewoMW\naJdY3uu+Ks4u/YkZicfjt2l1v/kTP6fHhgU9ZrPo4YS3BrMNTtqaDR59lpd2\nRADrn9HmonlVvWKzJonBisBaMsF0IHmjsD7YiOKOgXzaPBA/Vb0VxXnQC1rW\nySK3GvGWGLfz7SDFO9/KWxqZ01r4JdfzZ6p8bB4tOghtG67590OmV6pSBB0q\nNsXr1B7x8cngoBaSMwiQO/HuHaUpkdUlmgSKtzoLgLiSFINTjNskDxqBjZ2E\nlJgljv+3WRX8ZxSGeozKjJDXg8e05sdvHOmxvF6BJWohpJjBvJapP2Um4GqF\nAvUAH0bqdaQQ1fhIQzTCsR87WCdTWwAG9vPmA03SLoWqGiLRoTzSf1x6GeU+\ngjdSKfCORcNUXdiqQEwTHie0Y4cT9oP5XixohR0CIPJPctO8U+LxKH/69OuU\nseIp\r\n=5E1f\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.6": {"name": "@testing-library/dom", "version": "7.5.6", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.3"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "cdfe1636dd1c91209a765ef8d10d0ee2f0a09f29", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.6.tgz", "fileCount": 55, "integrity": "sha512-32E5//qua/eTuBqMjq3mbHYdAnR9BaIsvoZcXI5EdCJqNRE6C8t6MX/n0cao5zNsAIRlKPp/7z0LPFK0obqY3w==", "signatures": [{"sig": "MEYCIQDQO0z4Vkkvzargg+itKCQMGE5MImCOliYUUiDUxGwkyQIhAOydBkP34Nn6shQT9sHIzbv5oW1wnlrTnWjK0xVpBzBj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2478668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevnyFCRA9TVsSAnZWagAAzbYP/1154Rb7kp4K3uFL5R+V\nxcIkKN8nz/8jvIvGOWqft3WtSb3yj0CnDBgj2it/05Mw9JFXMwuhrpEyyXgL\nsSf5YwiDE3+qA0dPfYn1fAksxGMA1vgfBb7Nvltej2s78TQ8jgVrOUnpqxXl\nPm60l/bxLKP9z5zfuBXyILQ6Bnln8L1v3/1eGK7EQRmxiVZs1w0yNAWzlI9H\nxJQytBv6pBGjWMsmH3WYKcdFaC3mJaygez5kCjCXRdNi7i2jODrA7M9qQT78\nXAZL/ha6j7O/HGaMlRx2dHzz69/pRV19GniwZ22h3B7YmmHWmuvkmbXUYd97\nOqYDn9D023dET0ItIW+2nVRQ5vHT1SnOannFCr0Uh182Zq+DRRRks5xjW/A/\nlZobYzaCKYck29ChhpLPH78ZSLBZsI08DZTeelOI2WFBviewJumQUXhtlSQu\nI/scqWG3q5g1GGylAZo1Gmu0tK8RGGGa70zoa/jFt28wPC2ePRCwIGlftOzT\nFP0hGjMT8kYObSLJThzuhvmySEhzbhpfthhmj8nd9mFwhVrICDRm0w+7N1nI\nEh/a60PLwhSTjKPzX21Jl7P7lleSj1+HPZUmHFLsUyAHijuml2JR33Y6T82g\nl4u8pU+oBEc2FDA5oFZ8zNDbdEJHDB2uqGnOQKwUZaK0263XAqy/r3cW/GcW\nTYgV\r\n=Vkgx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.7": {"name": "@testing-library/dom", "version": "7.5.7", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c4bf683a65083d4a78644588cfa4ad684c113fc7", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.7.tgz", "fileCount": 55, "integrity": "sha512-835MiwAxQE7xjSrhpeJbv41UQRmsPJQ0tGfzWiJMdZj2LBbdG5cT8Z44Viv11/XucCmJHr/v8q7VpZnuSimscg==", "signatures": [{"sig": "MEUCIQCuQDiz7kBi0dBYEVq9jyBNPOP102xI5W9Apu5QZpW+8gIgCXDNldBjaMRK2JSJF6VpnvmZf0TSggBvPY9VeDcG6jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2475884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewoeoCRA9TVsSAnZWagAANZoP/0KfvwatwQpyIDcj8JYm\nSWyQAxn+66m0HIbGC2XeXS5kesEZLFv7pdJSOTIBKjeGz21HN8VPJ4V0QvBk\n8nyvMsXUGTA3Q7BRpo7mOPa/WURrgbJVtCh+feqS8X291AsP7nrvV2c9KKhQ\nzIbhidbL31xB77Z7T32NJLZUc5aEDjNNh1xt4FU2HJDy1UV98hwOxHKtayNf\nsjvDDqfLVVd0pmyTJbemEp+9ifJkXhXEJCX3T+RF4nURNDZbQCiTpo7WnKlm\nf61+DUzNy9FqKu4grR+uByANkfwce+UHYCZGNDBYXF23siIgQkKqbyVK8ofd\nEDqRIK5VZFxFS6aTADpTm/evqbtjLb4sFZyOWolIGuKNZwYzkfpAw1Qh4Mpe\nBVakkmfEHrKTd4sf3RVwe/k4A9X7MRiGFGrP16hspFS7I/0yPrZ48ghJblAY\nXzqjRrEKvWdHXs2boRIBYvS1nxBDwNZ3jwseyWkHYgQk//byZfBTFriAKstG\nPn+dIaymowLr7/f++vmHUPj2cBcTUZnaWOKUXMjiwDlt2Xz9asKv1zpqrFpn\nSHSPBerK0xO5lBc51xLX0yeUFpJsM5YqLYQilRYKRtV79PaAyd67NJ2SUr74\nv9wtq8u6MfZ2iV3UeLzukjHJ1O/3BIiH3gKRYSxzLTYG06XQtOfR0chcguLo\nlQE0\r\n=L9Dk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.8": {"name": "@testing-library/dom", "version": "7.5.8", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "35eb20f1cfd90629a09728954fd916ea142e5de5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.8.tgz", "fileCount": 55, "integrity": "sha512-aEK4GDeIk3sHuuF8NNvZrmZg5xfF7llvdlVfjely/fPg/GE4yLa0cVZEBWpS6oVUBk2tEXjwTDPFnMOe/M0GTQ==", "signatures": [{"sig": "MEUCIFpD1NOpqDKDP2TabgpJN7cj/QcugVvJZT3ZkK2MkzmQAiEA3EYBDFY8vOhHkJT7XkJT/Jp2BulmI+JHgj39aaYnhKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2476998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJey9MxCRA9TVsSAnZWagAAe50P/1kxLgzTePgRehTW4qYp\nrBZ53gIOqAN1NCjJ94B4qmLKFcH2w/73rZRAoW7W/2owW9lhX+9xODev+kC1\naSnDqC++4ZTrLm3KY1l7a0n+IZ2fWiV32vtRWWdjV5CAGIFzKoNot9oejFjA\nCYfwv3hzMuc/FVqImODiaBodMHEy5ldNddfgQUDSTDWPPKsRqgRJNiGZStzR\nrmPV+mGRDIYfjA0iRwb82LHW6dJGJ2O1I08tzZTgopLOlQ2k2fwau2Ju4Inn\nYhafn/58txCg95RNWs5m5gOrQ+jbx2t8qy4YxQ1xd5sDl6pz4OxeUz4cj1aL\nuRBfGTrdSaXzEUDC5y1Vj9fYiefF4o6vho9xhwRKLbKLH01uigr64hhsWeG+\nwZU1UHh4lG5ODo8GIg05GFeNhfmqnOd3pKpBWBJO7cIufCceoc0pyoOPsLLc\n3U/s/gUS4R56w34Bw8ILFwLAeJgd0oZZLEc8a88jTfpVXVgXz5eGke/xs39X\nYLXe30FRr+3hlWy2FSNLiASU6AkXQA8KbSzmvCcT082IUWNVroNcSDPztFb6\n0GkxkXa/+qhUk1ryE/6o0+8DpWzX/B7wv/BOGGBPxe6ihOI36PrIBH/puVP+\ntPIV1jmRclzujFaA5akq23AzCnlafqe187UIE+Flvu0ZeojqmgdKxOvTvvPP\ngU14\r\n=EaR/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.5.9": {"name": "@testing-library/dom", "version": "7.5.9", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "25bab5872ee2c446e21bceb209c8d0003cac5d40", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.5.9.tgz", "fileCount": 55, "integrity": "sha512-S9Oy0fyusO7FNBJHNq1GwXYsSYMawk6yo8uGd6vB+mL+VdpFPEqpQD1KEFNGtHQoiSCo8+hwk7gnZ9x7af0p/g==", "signatures": [{"sig": "MEUCIQCeP36K6APBlnmk4DfrPcgEpOWyQvIZK8dXG/5AeZ3P0wIgRphswWGySx07Zv6HjB6gFmiDUHhO6K6dGIp8AEj0Bgs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2477104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeznjVCRA9TVsSAnZWagAAkl8QAJWMSEmTS4uEeqaslRmw\nRo+UXXb74VbhfynDRUk5G84PES8AsjauFzMKT9luUUb9YprTNozTSHwBZOLf\nx6MlnBBmx6XFP/KFUZNiNkzWwZPKr2U+/6ZfkyZi/dh/2j5DCcS50+zzY2vG\nT8eIjHQXK177mCMni8Ey4K4QUlcjYslruRKappvyTLvQADMYClTmRi1FO/2h\nygscDhSX7baJOZXBcwD5KUvKU8keGxOzsXxVGuL5tFy1cVT9Yd1VvMEw1HsW\nQZhpP9HLrQoBNn5+57eYvrrjdUJQ8ogv6hiuND9rmpWTpxJ0Ls6wCkhRkEub\nV0OEvTrOWhMj9TYBiKLjDdl4SJWzqurXauvn7yArLzeg/4u5Yydr6GclkMnZ\nU5gsK71HXp4eMv1UfwIe6xEmLaP4Zjfh77iVxFm2PnxZL39FcN71gZFgFF1Q\nghw96QTAlWKOlB0e+ckOnVzs6q/yR+0CJrDx8UY+uqqFaZNhcz5eQEqdRyhr\nthG9KAUoMob2HO9HyjUzT2Ubg9dZQ1IJspuh4h5ocGf6v9/vOJHlbzrKcv9R\nIP8Z6LX+RRxFpaT5R591JvcbWGf4VvhEb3L5iWAcb6ju1/f4pvfvV9Iermd8\nx0TBnQmXsFJmB6rLBai+V3MR1LerPNhK+mGGKjtXO4ly/G619Jcloovu+BeM\n6Woj\r\n=E+19\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.6.0": {"name": "@testing-library/dom", "version": "7.6.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "bce422f3b69d01ced6e8f883c3b018e8f36b283e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.6.0.tgz", "fileCount": 55, "integrity": "sha512-CvriTIYAzmo/wo6kr6vPXZXHR4XDTrrp6jgrAnXeb4VTVu6UE0ldsS2y1blSB8z8NnGkkrsDcRG7aHFQ0QQd/Q==", "signatures": [{"sig": "MEQCIBIWA1rT0/BG4xV36hXNEezhDQOQaOTtTJmQ4aHwY6seAiAVs32cZlRZx/7aoNZ0qYGMEC86fCJ6HGc3JdfiXpVEfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2480673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0E0YCRA9TVsSAnZWagAANhcP/0oCX42Lql5d/+epXSaZ\nSEJaY5K7U7bA6XNvtbHB1RO19tIBA4lc85uyfJOMS1kxX/kdwhyZzvd898Jn\nN0E1oWn9+Zw0OiMYq5a8he1TGuoggCQf6o2nLAslTej0nPEqnnYX8ht+xOhw\nl6u7dFVf6sFdvtAmHdlKVNPahPLfN/2CnyRMRYXp/E4IrPWIMadKEmigjMHv\ncn90lw54SYcgqIrCLEqZw0yMqdu+gNuaARIvciCWXllJfcIfX3anPBz7xa91\nZ7+hoRhhqKIOMTedwhID//qi/tLatkYrewfhrzbGF2Vf0imMKG6P+oj2vneK\n/ayZHFbzjcH0B4kPu3381xRNSYePJdrZZMg3BEqLIuM2N5MQGMwzPbwDkGfZ\nPKNuxk+y/rqyzBRZh93IBZEkt8wFbIyfOH5SPQJRoUadXkAAxKLX7FJffF2y\nwcAlJRVqIyWNuYmlIR1PIjjajKfH+q5bVqmmshhL0A1yOeWvlAMCs/jGN/6u\ndO9RXkUwdR8VC7sB11TQGl11lr6V6Pf+ogeUZdbA0TFVs+u0suAeaxlotvZJ\n7MnJ3+DK2BNxcX3UNGcfCxMRFhGacsVIEcV3mG/FYysY089Fm/Q10GCrW4tW\npSZOlSJDdvDnKjbQpDJfhn9Lljhp8ajaCUQcwI+qzJF5yR1zD+yRXtjURUJc\ntpYF\r\n=2ra6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.7.0": {"name": "@testing-library/dom", "version": "7.7.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "3567f4669f64b10c546c2fc42adec5a0cc84b956", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.7.0.tgz", "fileCount": 57, "integrity": "sha512-Mlq+5hLGyitcoKrvKCnUNm8euM0+QEy2q1AaZzJTxEAaSaujBIut7+exodTSnhKoe+XLUZmlA45O9M4T+kHu9g==", "signatures": [{"sig": "MEQCIBBj32sOHF4fSsM7hQiB8v6KQ8ZKsHeeCqP6KZS5yhAWAiBw+M0UQIr/VoL2uHhkoP+4aR8cuXFbYM+veLYdZJRFEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2538521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0VngCRA9TVsSAnZWagAAxAYQAJOIhLFiME8ymihLFT2E\nmCYqeJ7k2eKYjUmcAPRuR4TBxAn7skbfRanZGWKOC9yqzfN99Lr8OhTcdRUx\n99J5XKY9TvUdOOWrDngUHKi5QF69PrfyPV8TJwVtl4wGst3wagXKSphGSVfr\n1VSe7kyGjBQypt7hOAyHyR4Kh688jnPYUHov45ZxpX5S9SbHz/2F3+zmJf5/\nysVnfDpxiq4pUZSLO0Qg+Sfcvyejbuy+veZdikJMYz/MaCJAZ2IM1rl4/WrH\nqlP1GDq0xkYcXp66FTESkS/p+DX+Pq/fmOMfrWinkZdNpDfYEfbsdzFHWXmr\nmfcA6pdTXVEDg8U8OjQUNUwTQ10tyC8l6PscHTshauujxHxtBup+ZaBt+buo\nEd2b5rf6QgUH4PBeFvE+uefqnh3JJ3FvIJKJ8fYbaM4TB+Yv+rSOE1CRQURo\naOCePHha+TcUwrY/TfBxyRmIMEG+mkLa56E7PrM3SEuCtJ+VPCi+LAtLjPdd\nRKw9LraLqcgpbi4Efz/M7Jsgt3vJ6N33XymsW1akn1w9omfcSRfbVLsjkI4V\n50shSHi6+FbSoG3lzrlAZV0sY4OufBUMFj8uVwwUtKe2LLB/swKaRcsnvo7C\nmWJYMUxpV52PI2b9EGW5sv/aw5f5Ickte264lQdc2SPX2EaiZf0XVw7+Z7MM\nzMzT\r\n=wdzs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.7.1": {"name": "@testing-library/dom", "version": "7.7.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b43db5c0c96440fcb51229de34e8111d0ce03c98", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.7.1.tgz", "fileCount": 57, "integrity": "sha512-lJTopFmsapwonQ0jrw5xnRWRDdyFQpJrzQzyRem3sXOa/qvXBJHy7QCKa1wZS2fvkBdYWfxCNrQh4t6EZU8s5Q==", "signatures": [{"sig": "MEYCIQDlDL7xbEYWhyKTvK+3Hy/fO9f/srd56hUVVmHLrYQKkAIhAPnJ+fvcCx6ITUUM3uYi0nclSfQjcvIHWI2URfrG0O4O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2541314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0yOdCRA9TVsSAnZWagAAF3QP/1F6lN/E9RuIt8JdQ3GY\nfatxmA3WoqTSTUNNW4kZEF1S2GK1ijwB1r3c4+5h9SddRA+cvgjDpWOSNa9+\nGsHJPlDeRkExCOowMiIgfcEK5fdytgtBdVAA5XqoR0PQxRSYnxHTY/KD4J74\ncPGlCDZ2pkwLDAGu/50sCrhlZ6JQkocFV8lIHv2xqG46SkqLclFzik1rMIbb\nYVrHYJxSDe+srOVTtloJffRfiksF6JnHf28r+ni6gjTylCmntbNPGI9sZfuE\nRynF23QD/mAvIvKdQgGvwDy4co63pJXFoJgZhceG/ZJoVW2DVPmmm0acJtXi\nFaB4a9eGvA3FoQy6Tg5hfrtDaAlhZPMXTAM2cMxY07muvwLIkfLQSjj1ukZm\n2oenTIQJr7JhJOj0qLSymU0cTnSnjTI5CM4yOV+ltzamef8M3TCwVi8EDtl8\n4bjLHABuTpQ1wL0LD4xJijrK1cGnHFo/un4gwDkrwtOZioZ+4txhR6Dka1/0\n7yz0ck3Wz++AlKzX6//XIxa6xuFD+afQBPrsq13ktmNjwCqwg04hFrr5H6JC\n+uRUspBw77Rq6aOR6MOrv9IsWbKRsbPJZwjSOnFOAyzREKs+RTAzHQVVlXVY\n6UOzkYUqYI56mpyiWnUG2EXCCVbwbJYBkgEwNtY9Ufw1SxyqUs9cGnsCGxgB\n7ZBP\r\n=Q/Td\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.7.2": {"name": "@testing-library/dom", "version": "7.7.2", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "979b5b02e595c95439d708f956b0853c86b28208", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.7.2.tgz", "fileCount": 57, "integrity": "sha512-yLPRWaMM3/VbMx45Zk3H8UHuM1JkMsvx2ahDB+i+YBcfPpJhAJCgOmtAptEFhIT+gnPnr2pBYjvsuXd5BALW8w==", "signatures": [{"sig": "MEUCIQDd03fSoW80jO3GsPp+WD3jCrwMeO4WbWE60BhQp8VNeAIgTv/kuubcDgsluOlFrxRDmd2NNll/wFOj5g8LqlJTC1k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2542967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1FYiCRA9TVsSAnZWagAA2xMQAJUVGNZumf+/DeukPRrx\nnETk0gTD8WQ3xrCTAZjCacoZXChJ3ZvZpVVh/3z9F9OaW5Y3iL/yDSZd99TM\niLzJPLI3FSx3s8Vr9QaQrurT3hFsk/OoRQ1IUuOBhMa8uX5aPCLFcisgekWM\nVQN3AC0/XEUMRMVjdvfkho03QEtmgAq7xWEbxcJn+5U+yiwk/6DMKpK096kv\nNtaXCA1BFK4Zdl6g1mI8wLCMQt/4FDEdbJLnGXI1bBLA5NgSuGlEL6QGOpxw\nyx1ULKNshuSVbPWqFfUAIi8SqixHOCsaKOCOis0i2X4Vfb7HwrzdwvZHzc7w\nVy9oqOzBjNjFVlbpXkfTPrf3qYeCe+Njru5NhRsyjEFHdoXhmyeYuU1s8+4x\nHXyfBr2GyhV6E88MUeOKmCL6elfcVAMR36bXSksL3mcXsPN/I0VB6RurCQ0x\ntQdoI2OmfbES3uYTsXvUM8JYF27QE5bpNwR9sAfftSdIIhDtss9VZWYtLOmo\n9TF3GUSgntQ1JcBwKIHBiRWQVKuB3hMoO47I8rJdzlpzlgdobkHvByQAmUmM\nMK+47WWzEqOA2u1LRyyXsp22CL1sLhb5TUZ5D8Z6DXa6LCBE6OWUwwE4aShD\n/DITjQo7ny2x8cX5+67M0aFDLRy39QpLu7QtLYKHY+cDoHwB5z9UzN8K4Syv\n3Fyu\r\n=Xiz2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.7.3": {"name": "@testing-library/dom", "version": "7.7.3", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.9.6", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.4.2", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.5.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "04fc0881f67ded05a017a2341c78d567c56aad0e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.7.3.tgz", "fileCount": 57, "integrity": "sha512-TiYhZ2NOg3zyVp7lT1vg1oKdjgVD79l7klu5ysszj6M5ut7DnCTYSFc8eNMfOymnN4c+tzF7aEePMIVomaJOgQ==", "signatures": [{"sig": "MEYCIQD9vin0vzcIV0lT+wh+EwBtfIHRpsb2xSySNPYSc9KrzgIhAJyKnFZKyNUmvWhEgKLsP+YwBoCEO5K9lgwU+mKJHVwi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2542998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1GCjCRA9TVsSAnZWagAAyUoP/i2Pw9YuLDwHiV2SnOMF\nWuJAqsUZsIwuzlNLuIh7zs6COCCI0oY5fjt3iA7sZBJc/vupBa9oMxHvwluk\nHT6eSQr2zF/ZugD2XTA4FuYmfNU2oeHoYDOsyNkYArk+JjPtBr3Tn+m+SSTS\nbR2g2UhiCG7QEfiWmHX5b+ndyWTFBxEwsImjrvSlzGdd5iGkpfl/KQ9WFBKU\nUpGydhD5UDDMxloFCVNHp7GElOXlUWiOIC2BMuK8ThvCtVLYRmi2Jf9eE2Ut\nof6AlzT4sUD3Lq0KJuW9ISKgogrS2D7NTwgVdkEjije8SoB9BMkA+Hn4YNdO\ny8ngEjaLBWhYYvjSyxHgp5zkmeeX/k7+S9PRAF9cKAuUUVVYEOuL5IHDgnj6\ng0ZDg3URNcw+sl+NhOAM/cn/u1gpjIfqgmem40vi0msYHj3ZKUHQY4Ew+5JF\n6jaRYtuerfMUs/cuXVxlQMioi+YX4BYfdYAQu6rIJIVxLV8QPuD68JwlJhPa\nJn0tgihtCW79aI2t8eMPjcyRKnuzn0QDcPgpJJlWOUulcoySYc8kJ24p82++\nixKh/SOYaMR25pSkmZ4NR7l9/K3VAgYLTo0ZFA3H1fQUczDsih+TBaiDLTue\nsQBgoQNVXc01Qis/uYLR/2Loh84tsN3lSwfRxA5TYdN+m8rFrMn4OkTUqOmD\nxbxK\r\n=KtRY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.8.0": {"name": "@testing-library/dom", "version": "7.8.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "f8b0df6bbf8346e7c5e7b314c6c109d0b3261531", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.8.0.tgz", "fileCount": 57, "integrity": "sha512-Dfk8AqRF0h6CuWxTH0nX/kbxWfCkmQtJ+7CuHej/vhd71jX+dZz5JMpxc32WFwrkwKnRoFtPgMauS8A/j8GrUg==", "signatures": [{"sig": "MEUCIDQZYP30qx6Mxq+F8WIRC+ydXO8ATRiXnuQYtzPwbJZQAiEAs7M8BlvLRfJ6K3YMUAwtpDJfl5Kr3LBtFVS/qEOt5sM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2544193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1YGpCRA9TVsSAnZWagAAeSsP/0VkuUvBbQ5ewJsqObzQ\nTdaXzFTYSB+QhmXvEjhMtTWRaqDsP9S4CsT95cZ8UyImcwDIRWCbZ4YgKiHS\nbF6zmsli2OqhZwmAjeTswSBG3Myer11TZAmJBa1S8kf9lgpC0Etb7UQGxsC1\nSCBT2qcbBhkboIj2nC/tSLybuGXAjgIGi2YA29PuZfFryBxMpm7pqqUSf1Ip\n4lWXEbFT/0+K39vdjaTC8sWmEHAzyCma7X1ypp3pOYDNh5zPxCvYHbyZ7QEE\nL1Ou9W7UaBWy7BgAsnkyas0CmSTrFkyvEtjRq5KzkAo1tKwDeyYmrYKD8s2/\nBXdjldX7ncR3Ja89CnLZjeraESpgTsS3oJxXyFHgHauqXcUfBmxTH+QTgQ8o\nJfIJodim3EMMil04BSnEdKiLmF3vPman4DViMtxTsPdbmh4XgPqkWQFDph3r\n/uX+RpJiyfBlNKJNrWlkQEi20W0+dDQCfkIDjX7Rvuwrqt9Xc7dPJCOshrGj\nY+SLjbzMBwUjjDXKQBdkTfXmPIZmr4ivIRHQPYQMDvfYddfLYAUaT8LxbGqV\nMQcrjANbhtrI5AByl/JptPqRD2+kBFEke1MGIFeXjvmmAU/FClEUDhBIvTvt\nArbrHmrUk5u0YGKgDkrOl4X4Fu/nY5J+UT4b0T64P4GL10BjzZFhrlDR8Wrr\n+yW9\r\n=sfEc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.9.0": {"name": "@testing-library/dom", "version": "7.9.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "3805cda47ab691645775c59c93c9044a2cad4c91", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.9.0.tgz", "fileCount": 57, "integrity": "sha512-WYnJx9I94cYKib/Ber2BU3v1dUB+4n5wnJpvWJLTiwgERRTSElsivEtfX5S0LSljS122One6Bewhx2kgoZKXzA==", "signatures": [{"sig": "MEUCIFHCTLn4wy0/sQ/r+pZ2GzsZVhzSG1lr8sVfB0POxQx2AiEAue2zmk9jhONBE8zdnvTzCtzLyrvd2ew4IF4LMXkhK3U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2544428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1pq/CRA9TVsSAnZWagAAMxcP/2okzz1c3BB0HKkLsW2Z\nyEdYUcbTeIs+JTS09EjkfY7eUvPdNoHt+Colzp2HcPXVagfJH2jg9bvhoksQ\nGVTpZpFN/n1aabRI5md2//c6wII4d/gAr6swbwPyHMwweyVde/h7m33Fc9Di\n4xQ4zrUeHLWFqyrNzzKE6aDjhNsMRPYx8r1s9gwdhBrMWGfuJUkv+0camTzR\n85chC+JkyaetKGg4++wUQc7HuP59/Zyaw1TUsi1bJHIp/i/1FEW/yv7qyTby\nrs++9CrPqVdfa/bsNwTB4/Ss7Sxd0hl5GYWpMAtUcLmdVJizlN+fqSST+bCh\nRiu69lJFNCdL7HrmkkVAex/6/FdwllJWE3ACeIBPuGRgncpM/Y7KsU+OFhdf\nJ46jitXpScEt+IpcqsI7Rrwt/Dh00j5BLJQ/9d6OgvJSBBuycS1s1y0cN9Gq\nl6Z+rdwLa86CMxwNejVB87GsTjwbwdsbq4JAntPsodGMxwX4el1ESX+b+iVC\ncuK1rrHbgaMBhoOKLhfUjn7p71jWHaHDjx0wV2hdScxpJvTOHQ73l4eKRZRQ\nhCz/kktqTxHrk+D+I1I36jIdx/nbFK8pyHvBRkQc6coLj5U6uQjpkJgyTOKQ\nejpKQ6wGRQtk2RtCf2nb0ZET/PHOQgKwPnttdPEompBc7qzeQylUBYhXrdBX\nhS8A\r\n=Fqis\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.10.0": {"name": "@testing-library/dom", "version": "7.10.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.4"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "ceb77b80ddc0f1b5b71f49684eabd18d07f2d3cb", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.10.0.tgz", "fileCount": 57, "integrity": "sha512-nCCutwtbChYPnWFvmxCIRINeA0mlT0B3dAeCb2RxX65nZZVbf3jHfd6+v//TYnWo5O6nL5j6x6mJE5JUU3zNSw==", "signatures": [{"sig": "MEYCIQDXHkqD8iLK4HbXRbxLnVdMHjrG8VartYz8WN+DsIdprQIhALBgFBZHDL9Oe8ZHqUKn9aw0QzN9Sj61I2ht1/xyl7fQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2552302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2ZCjCRA9TVsSAnZWagAAZxoP/0tliGtME4Si2SAnro87\n0w+gMjxuA1RroZlYAQ7IDJe6rT2ZMj+768bGS/q6O0tqWg6gFEFUn9rQuSiX\nLeHMAXOTkLkI+l9IVYzGsE53O+AzcLWC+r1iLkqoUn4R1IDSO4S7v7O45VTG\nsnv1+bfqWbfURI29lq0nrR3nEGKdAw1wbJgMevd/HBzyp/alQv/i9rARYGj9\nMT2HMGgHTOqalBdV4GVTuy7ALE1rDIy5DrpAqFLhgMH9/0MNwXyaW/KVxzqG\nqk2ORINaKhPNbq2fHIG8ohA8l6se2MCrc2wXuqk+zfRJb+5KxLLD5pQuWoxW\ncliEi1Hx4egOb77tuy7WjqzJJsNyFaOwvOn11L3kPWgvnumvL4xF3IKwWNhU\n7L5Ri4mQCmQbcB97ZUIWZOLOq8o1qFSNh44kr4SFJ5zrftZHY5CB65AvMdyf\n5RormyTYqJJAfOfh5X4KlkuDMlmUdr4GLt/Lr5kyvwg9yLWkXNoExuY8ltN0\n83VF9NkXiYRxmZJx7xhOKhbcdCpZ5cXBl3AuV/dY9r9hYCdsROx3RZH59Tye\nNNIsaV9z5LOZgtE3zpyLO72Zp6OJLfZo0NUvB29vGtV+XKr0bq1Ugf3lWGc6\nQgIpC7IzgExhfEUKM/SFgJTORs8JLd6nXu40SP2Oh69ZWcGVNvYcw3h4o/Dw\n2kFb\r\n=iBuQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.10.1": {"name": "@testing-library/dom", "version": "7.10.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "dc2cac517b5956020d28a027c58b24b393a44320", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.10.1.tgz", "fileCount": 57, "integrity": "sha512-shB6yx0eqoKya8V6zqV152MioYe6R4iIorT9LdGhGMZwvqny0GYMBqzKbAcxbTMlBmG0M0xaqO8AnzVEMuUamA==", "signatures": [{"sig": "MEUCICufjEqWfouzNvbGK4XCRAk1AYu35+0Z7LcvlIl/cl9LAiEAlpsN+pNJOI+htns5vCZ28qE6Xj3R2+1fFqiBjFdRgts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2553060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2oqGCRA9TVsSAnZWagAA7o8P/3YrfLu6y2uoRNod4gLr\nyELOe5ckqQSj6ixkqZpsTRvORG6+SYBRf+Lxdxau6jkaSPhfhXVLmsEVaMFI\n8e20yoSMde+hCSYUMjGzosmIaUPW+HR8SHZEmtG6fCOpOsDzox5RmJ23E0as\ngAtm4rOyVCwVxXoinPd438fWNy+cGMiRWvQGPghcgys2aJY3eUnvEO8C/v3Q\n4NiUYOhYwXavoR1QeR3mWlyzeY6BGRoj2YK17qLgJCsPfQebQXBEm29656/6\nTMnCArqDm6DP1gfhAOwS6wVxv4Ja09WDvT3MH/5oW9dzJ8bq3Al6GD/M5rc+\npdEk5QPTDQFrBJpy2Kq83mbdvEWFW+qFWN2vsBHN3qt5i4L/MR5C230LDjg4\nEWaA0u7ivIVelxLb7RlWs79iVRBX/5CMH46OdXYL2nvKU8yEWlLc2Yef+5Ro\n+B1tc7+0oBkSkfv/za4UJ5iSJDefzphH4VFwY3lD5BONfWe9PIDWWHTp9G3B\njMjuMwcfCUGml/uaLWhh+zALYY0iAW9tUTlbcX5fNlaFELwRLBG+/3GpsUDc\nKAus3ePnXAoHVPFMSEl8otnuoidzV7pFiF0Zl9icxu6xo0GmiwVhIyfWHI6r\nLHSrHDLpVS3YRpjRKeF+eh1ecMhuYT5etfvHv889Xd6jpV1TLg5RUSVju8bM\nQGGu\r\n=PqI+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.11.0": {"name": "@testing-library/dom", "version": "7.11.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "db8678bc55aef7cd6091d1510e8d0949d77d79fd", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.11.0.tgz", "fileCount": 57, "integrity": "sha512-2j+zfA6dwnzozReA2XrbZs3e9DD9UdetONrWmumIY2QdGH0h08hRW+SaLE8kIytM6KPO5yzypHZhK5AZNFEtiw==", "signatures": [{"sig": "MEYCIQC5wM5MsmSusQ7WvqnLjGnPj1eA7qNxLp0sqoPyJm/h7wIhAP6hUu9sHhtxEhMn1AHUAbaDWsv/+agXnXwso8e12UCh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2556424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3kLECRA9TVsSAnZWagAAFIAP/jfAhCfGX9kmPTcW7Vuu\nEiX3ARP6fkQRujEDU5ihqL7wyIl8jgt5E03DACp62t+WoKhAVXkAQtxxE+wS\nzWn4T58ZcQqMGRoKLr05AlzMKR48wbBkodOJMY/vQ52pxNwCUtEV4vAZfUmr\njwaM98A4tUlG0oRv1nkRi51tc/4cy+MPITZRPZdwNwS7bNFfH3rJmfoYYlt3\n5zFfiAQcuB7kibYfvXwmU4ECVg+1Y44ML6X+AoSbEP92tuuzJwl5O/2A<PERSON><PERSON>\nziruErPNEdj7ohuDfdAjLFAmRAShn90L75VF8aoRNP1kPoM2Wr/srQYu+hI+\nFXpvYsZSd1gKkzGFfuz/+podVU9aI/Z0oo5kDPUe6Dwt9OpAv3A1AQ2dkRNT\nSWdwpwNByELzlEYEyuVIrpkeLMPgVAIa9AMtaoM6lWzYA6bY+QdpDdgvKhKG\njMzxLhSj6k/OzWNJkXbCdlCOr2rjrR48mHxYBqnW/xwuciM7V0Uor42oUn21\nMaqaGUv5sIb2zt/cI1mZeFiIbDm09tzbFZbym5GPV60KCLPmtNP9dQ8YzUvG\ndiqN5a0fMq3dkYpEPj/BYyqtV48KgQAbyXehs2/ROuwj5LWsHLTLWedrb8bP\nfLWGT8UC2I9rZZnGDdm4gNlO6vGmCV3Yh2O05yYqNLaNOz7CuUZbMST+kOtx\nt6vb\r\n=bahG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.12.0": {"name": "@testing-library/dom", "version": "7.12.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "1c2f7c36ec77059f2809b0104b5fca2cdc2fd808", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.12.0.tgz", "fileCount": 57, "integrity": "sha512-DaP36Pa7G8cFros2MFQWNQsZSwl1DjG/OYQzFSM6gt1N1bzxsqb1GaxPo6LyTKA622xDC2KqPpqF+PsOlYd00Q==", "signatures": [{"sig": "MEUCIQCkaKhrcT3o5uSC67RVycwHfG5KoENsxub9Q7uYGeWmdQIgWx9kme1t40jGfhORICeR/MwfRM/RxAKLWq9QEUM15f0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2558384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4k4eCRA9TVsSAnZWagAAIL8QAJKrJhe2INtt6qrrdLoa\nAUOZxAwv1h4WuBEWxogidVSkv199oDqwpaiybg6HU6cxoOpEPTuaUsUR3aER\nKltLLoe+C9+ZpSWf3TsUEPaeLSUNLgUuAzB3z+arX/6tDdHjIeLCuWTHCOFZ\nMWHcMf6GK9r5AhVjcfEiuy9fQattjrYf0egUSmzvGKxmYQGnChJ+jq8gEHH6\nEA4XExsjvuxeRtbcRH4Q3hm0feMiHgDh+PyDnnMyOPGkec0ZON1pZGbUC/8k\nF5qAp5jw0z4xvaRKj4WDCAS8Ofr39JblZqY2J5xDd5uCXiVwYW0oO9WU6yH3\nF1yV/SjZDyp6L5hv+EROJmGII/n5SmK6RUCaf/UjY0UslgQdicxRjtNJUISD\nt+k6/WX0nAW4BCQRiaD3tTlRMHXqwjzChnipFFlHVoHgNVnhBxAMwiMsT04m\nxRhZ8tMg8qOcAtJMLvyg4LjPFmiVocB9HI8ww3MlXpk2kX3PROXTwe8/5ZMo\nOolYGbE9StVZD5+uGxMqZvnLY11CPxdbNKykdgH0bB8hFAf+baK9nuq9rlpk\ny73r3FUHtFPTt30fLH9piYb29/dJrs1/XaDOdtjU7Lac24VmVuSIdbMbE5P5\n8nFGAvLIsMCdbdxeq93vtT1f8aw6VpSUhVQ3Tx96lloEOZUBeKBU6v/Hr24G\n+sr2\r\n=s2+E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.13.0": {"name": "@testing-library/dom", "version": "7.13.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "d5cae2cbf13074ac637e3447e9f59a8cfac5f56d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.13.0.tgz", "fileCount": 57, "integrity": "sha512-EMgi1DaRaEH99gP+hurOdmMIiayZepARLJFntiFkVybCKmfVEfHaNKZumk4xvEsnr9AQqqLG5tpUzDfa9qXdPw==", "signatures": [{"sig": "MEUCIQCXpxNxyGLmwstHuuEtFRAFba4O44k2zKrC8ZGqaIKpsQIgVZrFJgSljZthJzn7p3MDGQ+BmmzDc1EScOLY6Faz+RM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2560460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe4/sVCRA9TVsSAnZWagAAi8oQAIyP2tNu5s1EHFCbx2Wh\nxHkRr9n3Zz4HNosz5cFti26mhaL9i5/vdI8XCOiiJ9rNs6p664gvy4fSRdga\nNt0EBOlub4ykYmR1alSFpzw0wMPO9/jWNAp7HoiBvlDu5BDr6jUpu+M3UEEM\nDQX79hxDKWpUzNe+G5CmQQj+tuQ+rxCUtPfg2Iew40BOUIyEweClmLM2Ylmn\nauyJqWCJr9Ao/dIN6SZvlRQzc5SxwoCjxFjLFFouHTR85IDz1VmC5qYriwJc\nS5gXvm3vK2Bmj4Zco8o3pR+9JQhlT6Wng9J5irbErS34r+oePVaF3h7KVQND\nOXqiVB9xF1B+fWZEbv5bw1ljgoTdk19mc2w2K0HzfoqsDudAPt+8e1peDrW4\nO0eG5ziaoDWzjFV6CHyjKpX4yeK0TBP2u4d4lF691xVYVLOFN5hUD3iMSRBj\n8Udb5+or0El+5gjLa6epQlSBbdsYZ0DPf37lk8oPnahJWKSD878Gwu97czjX\nlBYEC4xxhUPhM8CebI1CT/boq4juEZBYo0GxDGSyXuaKTsa59+0Bx5VaNdIS\nXmgMAOizv5+CqMBrVCDpa4b3vs8YPPbLROnPKGkEbSUkCMHdg3j9uOyoaRQv\nekQ1yUW0Oo61VogArmN1ftPQ6QjtSa8YNXYalOIrvkMTbJZMVmo6b20DKtjv\nrw+o\r\n=F4DP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.14.0": {"name": "@testing-library/dom", "version": "7.14.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b7d4619987b55340249a0302499bfde5f6afbf6b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.14.0.tgz", "fileCount": 57, "integrity": "sha512-qZuzHu6ChzbVE5r7TOwTwSTSAzQj+XknLSA5Vj6DV04GXMtvXocNp1SC3RaiyOOxGyTrL33Cmatj+AbfvZ9t1w==", "signatures": [{"sig": "MEUCIQCKo+8+yRoGCi9k2KlaGLUwhfSZkrfTvDcXL9wYvTGQUwIgeKLNVkTCzvWQFZRI+upNLVe5VmyDhLJ04wUzfJBfVk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2565955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5NvmCRA9TVsSAnZWagAAiLMP/jGQpCmJEADpbNEiieIr\n0TaCe9iSGFIJWz9omeuPg4Aecp70jxLXwTSoNlcI2VdGM9sIZhCb0Nc+/Qhr\nZhp5w1oVWF0QSGdq/WoHZNeLDON+P4P9z/1d6isAocEbysWW65FIiHWg6jH5\nFYt/716BnS3GrdL2q6yTSW+5ZXbqbPhNeNMR3xSi/hTcduQw03SNQXOQV7il\nATr6S8F3QATJAJla58Z4eCrjlI62TYpvAHP8sn5O8ppQtPNeuOyz1L1Sq4iv\n3RukUCqBZnNUupcAoXVtDj+ML09c8KVd9zUZeEzkRuD8vKs9GmyGdZEtN/c/\nNo2lyOCthMfIUfeadHLOFvrpe4rP0dSeaJygDa1iDbJEYiVeQp9c7cKbOjXP\n/IYqSprlcsB/BZxpn+GakrAvn3xbzu7hpw5yJuYMxwZon3kPxWDV/P92S4lg\nZ+H7Tm+ujxXIZu1kDBQmXjS27Y7RPU994MG0WPl8EQAsq5tbbNRp6oXx3vyO\nHt0o4jwD/fajmlTR6SmuypoZB3wCzFev9j9NXZ6yDGd39aW/yuxI6gQU1EGn\ntZ5w2P3J09MpZNw3GekGvvRYXJfRutUbeCOWZyybLpaPo0JatfxSPKG1ls+1\nbP8kmbcJoEgaDJeQwCLEbCfuyZszYDOCxmweJsHngEFbIS/G0Vjbr1AUV/h0\nraUS\r\n=ULhp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.14.1": {"name": "@testing-library/dom", "version": "7.14.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "cbb06fe52fd819cf6bb2859b7e20b2f6ac4def73", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.14.1.tgz", "fileCount": 57, "integrity": "sha512-inDCC4QInw58JkxIINXqo9AcinV2xNonbSrnDC05FBsnTktJ5T0DUvtFVj241QDNgA2prxQAPttyHcYWxMTS7g==", "signatures": [{"sig": "MEUCIQCe1SPLc7goW7fTgguZ5TIueHtbRL8RQh+isYdixYw67AIgByP0cEDC0qea5c3caSm6T0IrC47bIaFiWbjpXjPynIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2566693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5R04CRA9TVsSAnZWagAA9d0P/1UL2Guxr+lM4ZwMYzby\n28gXl00gssnfUsThUl+Zv9iYWI0eiLen1j3B4iHEvb8a7DGuauIhT5Xfeo5a\nKNE883q7BlUXp2ffE5BDCk+X7ue2qin5zxNz1EfjGQVl1pfIAEcYN2BcuRGk\nk0WbmP4cADcH7pkXS8jSiGaYMj4ssJFgd1Stpv2K9/Z3hLPmtUiEE7X2FugE\nZuLBhx6nlvLbnjXMERZv93F5Z5YGkVWcSqXK7WU2yvdPD1sQxY7G0tx0NVTZ\ncb3khNyrhccQjRaxBGAsFWj9WsRJ4J97W2zx1wcxbZW7YF1GEZdH1Izu0cAl\nwCEDKAyq5ZMcSq/khAKpH5Y62w8GuSuuRHhgga+SK2s0X5CuAotZuL7go4MX\n5SCsAw/Z9OolpBE58NqZHKQyUrcxq9Qpj/r8uS9Nvg2bW/KwXYUMI5OyFZJK\nDztdkd3/ZkTazDfG8rk82JnyOj/9zOSTaY0P9nnJ7PzdZSU08GwojZJWq8uj\ndzVOo75/b5So5T35jej4DHsyJxMm33OvoQ5tJUESKpP1wFqPL/nzoiCtmqp5\nKLadn71nyhtd3/gORlpl6QZcZdABezcH5fb313goUsAIJm9qqrxSyfGA/8uY\n/+0HhAhfZUIzZBnB9+g2EpVHLUgorMfqeWtY/htHgww3Nd4LobBuJvD8C+tp\nJLJS\r\n=E9cn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.14.2": {"name": "@testing-library/dom", "version": "7.14.2", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "e539b2342c5a67e2dfd285bba02308abeb237195", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.14.2.tgz", "fileCount": 57, "integrity": "sha512-MZjve+Qn0bPFAJ8w0SIkuplCMd52Bh2JyNAvS0xh/RSZVAsux1HGL24yhTf6fT+HtHMqG1F0vZFU8ncZmmAK1Q==", "signatures": [{"sig": "MEQCIF6PHd16jl+Oo8Q9K39AK9chtcn8AUTS2I6XBRrFCTgWAiAdWbj8XN7dp5bphI5/i0PIJmCevOyaC3NQhyAsNkGe2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2567949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5YgCCRA9TVsSAnZWagAAPFUP/Agby6MeQRZpGx+2kcwT\n38lAp5lDfdgTV8aMO1c9EyljMJ+o8RsLDT8v0Wlzur9Cs7uUDN8UdyGRwEwm\niIymoHUnSuUqwkIY21aKLqPsoFsY91mdYqVEKmZS3fQ1qsJ1i9/AmV+VQ49t\nQLhvHDaMqR8vzfQRcnZWQMjYO7HNzxJeNsVluuoWeViqaGJBCdaHV70Rvlj6\nwAPsA7AQWuOaqKxgjlVYfcKMcOeKYX2EGgZ9UEtN/gLamzIr0yBiS2kYBIko\nai63+pJVgx8bGUn+ZW21YNcz38mi7HVN4ruOy414ss1kwrUFJtUrdMRN8YSV\nWF0YKj0ajDJlb0w0na5wFIVf6WLyXilKkBWegy2yVnMCangYldixJKm7Dfhl\nLj1Z19mOJX8cuoDKz3ibh/eNzBa3eoQGY7Bkv9ULHBUWTxbHKoj9h2Yuz7Wx\npjBSUZzZb9t2ttwBUad9EAcf8B50W3TkPFhyOhEUZH5kUd/BmeB06JmzD6aA\nLQwJc9L8sX3ijFQaCvfBmbmOgjekMaVbd+yMZJSuM/6qHPYvDhDtxspBaF35\nF2VmeHb/e1oMu3Xm3TVqdfsubbpOWrTa9An+HtQBtTdBVMayBFTlPbWcEORF\nooJXlkN/RTRqpPTa23kN3MSoDNPTSLAWn/2bn4IasLHaqmaNe7AvTJRM4qvt\nSZB6\r\n=tzHB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.15.0-alpha.1": {"name": "@testing-library/dom", "version": "7.15.0-alpha.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "redent": "^3.0.0", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "383fa2e11c1c5f12b026984e71f079b8d9207470", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.15.0-alpha.1.tgz", "fileCount": 70, "integrity": "sha512-GdE0LqTj9ZVxknyO/XpJ2KM/shTw9C2xZB5kkZ+ypXoZJ7Nf/fOrH7H0rZdVYzILznDP5Hh5WeeykTF1uqLNEQ==", "signatures": [{"sig": "MEYCIQDJwuYqNBr7sHC5KZEUJ+UVRAvxWrz/nUOXD3gf/LlBjAIhAMNerbeL5QWgXEjfhUtbD83RsoM71j6CK4n0iWQY0g0n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2992155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5Yl6CRA9TVsSAnZWagAAEFgP/iWQwrUvrJ2MUFpSMbQ4\nRw+6umL2EBgyGSYZm8i6+oxGd4uRBSyJm9otDGrEDJD4etzDUPithqoOUI/4\nDrS9JB9GwVob1KAbYXHxawj63An/AL1SdJbfPq3tJBf6VDmxFqIGrBqbe54H\nqzPztW4mRV0ONasQ9pi5voUKBc/qSkc2DQ7IklsIS+SNRkbuq/oNjCML6RfA\nqj1YX0ODYcwXdUVslUDVUncFx4GE8P0doZ5Qk7Hl7JTVPlqGu0HRfIps5T4Q\nIPecx0HXDIZcCRTtJmAlPXeKFfDuF3M2JaxMJ3QVr3/868BFCSPdnHiUYFBt\nEcVAgTwPl9IAQ6v/Gpq4tRWgXQhoUhqZnd1fYy5zzKE2Uju9+wWeNPM2t6GS\nJByokA4kXVxYPCcXalEns5NOAMjTq9z/kh05yOfa4bM9+qBXLKxY1/5i65M8\nc6Hpx8RnH5Tts+eFUUmuAisZnoMxCIWhkIRqvegJM6OoUksjjh7L9ChJU19M\nmhRQiroTUZlbcJpB+1V8jTCfXWMFRk3CapY4OiLEebjyhmKQe2AgE4Ju/AlU\nscjrGEsL2GJcT55vZjL0pekWPt9aj3Sa3J+iksQHIy0lNnF1aMZqDCJXuoLs\nT3QNqZ+FiHpsZya+5CISVUZZRkTAkUT6U6qDXKWkLQnqNcwzOxvJNqhD6ijV\ny/RH\r\n=1DZ5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.14.3": {"name": "@testing-library/dom", "version": "7.14.3", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "aee022bf96ea8796709f7dc4822eabbf901b3719", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.14.3.tgz", "fileCount": 57, "integrity": "sha512-kuU6xykGy4eBzvml+pxYUyiwYm7/gsnry865WTDpyOQSzTEzFjJD4aHWj3r5lM9fNwscqyGGvuwQXStU9ebyVg==", "signatures": [{"sig": "MEUCIFush0hB0KM9hoPsH8C1AlYBapU4Gdb4xcopw4K5ivWAAiEAyBPme98+Km2ZfXOn4kHFv4VLH11bWUUE8W3hKbbzeE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2567949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5n8UCRA9TVsSAnZWagAAcrQP/32zOS4/Q1RnVpa7t0Ow\nVST6sGI5fRkNeQEFrLN8QKLxi8M3FV/BHKHjtr7o9kDr15cDCswY8IKedXM4\nPIQvx7OVsIZzExnWGTCK6G9frsvPjqt7UBGMQD+PvgAirwS4/+mSi1mYD+rw\nXn+F4bUDY/DAmpSJYi14nvXklJ7T0xn/PfplQz+f/WOUwTDSdavqsW/kpcux\nCYrEhvR7Mkldyf6mUn8I8URn8EEMjTWwk5BmZ9YjT1VirlhJicFp8yKot6jy\nF5mhmVi30wjNYaD/xhlS/y0FMn/5nxC19cr4i49naJf62RVDoa/JqT1aEImz\nINmgD28dHJ7W47wuhHl1BDYpzVeJ329yknqipI5eMC5pzj4IwPkgg4sROkxr\nlxYtQ0B8n2rt63dIlHv2ctaC/c5X86DZaQKdbB5bIvjK5t31kqh09zitv1jK\nlMzaFUNEZaq4UhItqSkHtEiH8g9bdgtw1Piut10pJaVcEIk/Qt0dUCGVCC5R\ns3mOt4k6DiPZYv5FMIbPsQ5GIzHzX1GWecU8TIHgPhPB7nSJABDyG5h6QAVm\nwdbWSiFgokv8AzHZZNm7Mfkqqe0ixXqRmF79XSirC4onRSiqkksnXz50bPJx\nyr459HmD3rPrPneOtBsbEbgBlIIJilRmwkk3+kp6MZ9yyBDIQIDGbgsnao+w\nZR4h\r\n=D3s2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.15.0": {"name": "@testing-library/dom", "version": "7.15.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "f47d2c391406846177ad5153f51bd5c5ef0c972e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.15.0.tgz", "fileCount": 57, "integrity": "sha512-H+cQksHNYjxTS62S+exT5ZcBZeJXE3NDHUKs6MTopp4cMgd8DHX78IUohyUGqJRD1AthtgnKujrPTxYdWZ/w9w==", "signatures": [{"sig": "MEUCIQC4nukYHd6JbGI3tNb3juzpljZxGVaUhyXe7LotCZLdjwIgSzkO2hb9i5jJVOZDJwZJu7jxJellM3+ci47IjtLk+mU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2570332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5wRXCRA9TVsSAnZWagAAA4wP/3oQj39KKDc5pBqaybET\niJqecfW01+t6RIu+KMbdnpqf6bjlCIq0eSjJwT/30Fj7nS7BCIZg4HoHVeL7\n8VCCIiLqOs49iwXCPpt/KQEpwAXPviGyYO1VO820/So+GWJk5rudTqQB6OS7\n9zOZo35RUORkjB3CjYJ4hqp3UqqpFVvA14zkFjaHYG47oVKHLOXmuY8ttHSv\n1uAygnYMmgLK4biVrf7+azeSQpB1KAjLYcWCDlbeaBqSCC5O3cYqO2mDTN4L\nzTFv1OWqBYUu9andQ5C9egEJxumrMi0BioD2sonuhvoyzNhC0mTmIC1KAeST\nAe8CyCdmL+RVfOwxJUpa6TAjGWk2/lo6KtIacqm4dkJ9gPyghcdq/7m6y6Wj\nTeWNUH1CZKB8srToMerg1i2aKykAnU4sEl86oPIzU016+BAS4GFzdXhyKJUp\nfZLKsnl3Lq2mCj6pbOiXJksNaciHbD1Yf70BM4yGUycNooTLUn8npM/yomzE\nQmmKgb1tI9smz2w23txWHw6Lxl0p2hvhNmZCC8RiJM0cFBYxbvjVXMVZZLeZ\nuvbibDr8nPtJhMUweuavcOxsaKn4dL8IhlTPPacb7thbJBbuQ6HdsbJNmEYR\nle/X2TH1lwJUBET+Ny0XfPKCNZmtuXtzhqy8owMy3sza+RhCNY3R7vSG29N4\n0Qwj\r\n=b8Fl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.15.1": {"name": "@testing-library/dom", "version": "7.15.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "ed0744aa9e410b86015c6610ae35c916eb451316", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.15.1.tgz", "fileCount": 57, "integrity": "sha512-lte2B7Vwyc5U9sA6fEbuh7rI34Qw7SniSe3CLboUIP1eNQZWFLXNWrChDTAl0NEkaaFq5XpDWby0QvzbMHQd3Q==", "signatures": [{"sig": "MEUCIQC+Lxz0eTzOzZVeTpUwBtUkjZ1COlmt/teqVZHNpLTtXgIgQ3xc19PS3vYXmPuxkkW8ojvNvmY8eVW7CCexBhviLnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2570806, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe58WSCRA9TVsSAnZWagAASlEQAJ0FBOkNyLQ7p7ckaCYf\n3NF/uX6Fef+9S5ngDiwWPmrFbQe9A+ejDbBwTuUQE4Wc479Ek+ezszZsW+IB\nRxY98zY2Gkove4OAYel3x8D7fKwaBwGYIDyWI/hDVe5NrPl7oAEHlrda+QDI\nnmUlH7AgfWy/OWbQN3vOatvu4Rg42at0KEI1qySGfYXFVPrKp1k4luSPjm8e\nNnXY1Hrl0LbYc9PrwynughMdedxsVbUAWIUtm5crV+Qxgq0RjHe4FupYDgJK\nPcm6/TEf//Qyfx46ybC7Uc4HTALvtxdCQZVZ+bptE9lV9ZE/ZdUhAT7kVgWd\ni0HAqtgJSWrdlqK5B85MjlychDd3h1H2avjTwpXPAD6NQx/0qBFw7TzvnqHO\nqc6anmzy9M4cRgs/YTGKFvvkFC99Yz+w53Kt3zNOtgyXp4bP8x7VaVRTPw0h\nWVAdL+OEhPczGb+NrY7FgfYKBeP4vonZ2lxXGgiP8OyoJI/0taU00aIT5LF4\namR3msJdj9z1678xhRNCe6Mhwcwif79vvW3/QySGqlWr61ETtMOgk6xkCw23\nNGZGFlZiOIs7EPWytz70Oh8VPbJtiWWqVflDEf3YbnPtVZVJstEvtu3R4h3a\nsCaxSncdpqHOVuuWLkVXWey1DO65KdIpVOf8pSYoF/EbMp/6dOaTSPjUjLRi\ngUb0\r\n=TvXd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.16.0": {"name": "@testing-library/dom", "version": "7.16.0", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "15c23c5e653e53ecf20a0642706caf89218027b0", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.16.0.tgz", "fileCount": 57, "integrity": "sha512-vV0dBOuolgiujf7tD+GGGWTDJrtvj+lEtRUHPuEkG74TWKTm+zA/MZBlgAN6VtU/na4Vnimt5NWekXO1hduZkw==", "signatures": [{"sig": "MEUCIA3D45LS7ut4cKu64z9BnrVl1mU4MbEw22Zyx3EjPWNAAiEA5TROk+GBaaBaVIoZg5RjzcKwrOyNTVGc5jiUCjrrhP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2572529, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe59uaCRA9TVsSAnZWagAAh38P/37jdtjnav6ope/MkNtQ\n52s98hJGRHhZWXP8VjdPhAa4NRNp382aZXY3HyCqQJVFInVV63zEomYWKIwt\nkIW2rmaT97vTY5k+2nFJSu8GBW0pjG02RnTZbc6AS2MWOZwcN3QIU1vOKHZ7\nd/7UH1ZnfuCMavRYs9tV85lws/dbdWGImf5eyVt3N7DDDOCALnPYaqK2qdzd\ngnPFxZJ2fsPv8eDpzsQ65RY7LW9+VEczmHwRAVRxHfEOj9TEU+2CYTsVMwcS\nrZzZa2iZBY2qwZG1cOZ3GoMhO+ZRf/sKC5/hMXm1hdFmZRNDHP0rx4JnG+yJ\nB+HRLFXPB+mou6+75mSqLEW4etX3KrNnKW6hQSHLxSRRQrbbcwora9nDURKr\n/evywfNpbCaAM3hEJP+WxixAX8aABSxuErBUdA2js/5M+eF2DXzKru+bKMSz\n154BTuMgttzTbpJmpcrLd848eey0Uma4rdnz18iEOED3IMnIShr4YvJrrAsM\naioZlJDwr1G9lFM6kLWk/iNU3fVePwD+BsF89v77g1bzb7gQYUBTXiUG03OY\nJZZTGdSzNjMnbR2WIOPiaUc9jlhJlCT4IhUMhb6rPcfPbJfEzlSFBNwBeZ8k\nCwPxFpODyPcIwYfkzXEUJuPWKaHiS2QWzQnEbHt5kuDYSGJZYmuqq1pZBhMH\nt/L5\r\n=Gz44\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.16.1": {"name": "@testing-library/dom", "version": "7.16.1", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "a6881d53612f2e8f7bcc0e0bd8825c6788cf57f2", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.16.1.tgz", "fileCount": 57, "integrity": "sha512-u0Ck7tjWDyCcGn+f77JbUHa7PqgFu62ohRxegj1/H5P3REKsM+2roCvcnWJjMSHvK354NAS/Pgi92E9z6cB7Sw==", "signatures": [{"sig": "MEQCIEd+fBjJr6W8s8zgrLg+WDmVkysaH5a3QGLy6lbG0DJJAiAjmsDpblPZewrq25fxZcX05YruyuDHwJA1UxleHOgjYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2572682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6FEBCRA9TVsSAnZWagAAK1sQAIlmCdse+65yJWA7Zl8d\ndqw5pG5C5oi6TyQzFZXIDPB+izCFo/tOKB4Dj70gPXZREFXvk7wVxQarrNgW\n4sE5VosquZiL/hVbqMQ1lovX0B2N51NPRJuS1DiroWMKHjMN9ZCR3SaT8JaO\ncEbgSaJscs1/V1t4M73bLF8XUXxgyxp9VH0dw98SZFC4qkEMtDgPUQ3mrVR0\nBUFf4otU88pfwCmFxVB+kXKTxCEP0NXBKMCwXHHq7hnXZZb7p9yV6WNbZGf8\nNHMKK1NS8epJnUW3vMTINakspGE3XHBeJp4yaAqGV+u2k5jfiREHWwddioZi\nwWjJPZDGwVffE1wvbhK+wlivAPca/xLd+KJ/ajT5oHqbmSYHgwun/4kU2oJo\nxuoez2fFnABw4hQm4wpZfTvKA2MujH3Lo2CtheFmKuvdng9oZjqaeyKHENnG\n86MuKy9dCRg0LUi6KVKxkQgun1bwn3uYwbHeEys1coB+0h/g4IK7aLz6dUiC\ndwbD23gY8j7DA0s8fII0Nk0JRovUMpEgHOSV9rvQ3Z2jRDD584U4csSFcFJG\n/KP5gf4cLOIlDzB5BUoQ2WQvexJE8C+2sKSxxWNDZLM/ehTiSzN3YDdte4Pm\nkddQt0Oe7J8y4oSvKxSv8WnwyJqFqdVB8n8Q9hWWjrPulrd72E5eBh20Z9ft\n1p6M\r\n=tvaI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.16.2": {"name": "@testing-library/dom", "version": "7.16.2", "dependencies": {"aria-query": "^4.0.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "f7a20b5548817e5c7ed26077913372d977be90af", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.16.2.tgz", "fileCount": 57, "integrity": "sha512-4fT5l5L+5gfNhUZVCg0wnSszbRJ7A1ZHEz32v7OzH3mcY5lUsK++brI3IB2L9F5zO4kSDc2TRGEVa8v2hgl9vA==", "signatures": [{"sig": "MEUCIFkHU2H0wwRwvAaQMBVVNYjhCVW1JYcvzR4r+9rcsm4KAiEA5UTpE5D8xGc0aXzidU3HTKQQVhW0TuWXaFbCWpdC+Mg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2577541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6ngJCRA9TVsSAnZWagAArEQP/3iGxq8MOLVRglRRIUHr\nTOZoFhiUJCz/vTkkMHpT0+6SapdnNWwxykb1GLG0Kl9r4Ros2SSWmNTicMQV\ne8L+6cyGZWpN3Mv4HE4+FgwxdozN3DR/bAGzstxcMJ+9TvizYBVHEZCgGwhg\nLGBNeqyvQjaSZL38SpkVGwJ4VSpQu2BDBqZ2kPkgfBlG+5G/si9lTrFV2Et8\nHSczTK5hUBmqU/aLO1AUgqJrFNu86rH7UE5KUV7SuCmz0k6k9t0DHG3JX7Kf\n8khWbSSqn07Q+PFIO/dt14EIHVjo4rNoH8/gdIYihHTWsYmPof7IcpMssc+T\nlBVkEDFhkOhbczjXq9Tqb6xu5gJw/cXM9gxC4ZptDSdyUWqxomPwRrTBjN3N\n4AsSI3beo2J9HDQgkRn9TsisgJAT5+Azk+L4wohsdyQnL+lCbgLnKDwmG/gy\nofZ9CnQngWDzuOm0tfX6OUhYeiN7f2Q6esDTYXMShQe4PWirY1MFkzFR0Mqd\nlvfa73FpmJzIcTThQB+UAvBH4BBM0rLN2chm3NeN8vUJrADNhv3nF0WW72a/\ngJbA3oSz7nH/gsUyE5Idwo0LLT73AJR8leSkdH/tmH7w+4k9sXcFTYl4EwuG\nV1rg9Z3+HgFTKihz3Hx+kk8+Npo0EjC5NjIkBnR7nhDvtrZEynu+XpH/DxTS\nzZ5v\r\n=PShu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.16.2-alpha.1": {"name": "@testing-library/dom", "version": "7.16.2-alpha.1", "dependencies": {"aria-query": "^4.2.1", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "ff2e3c043334b7acfd10b6dcc66a9fa2dec3b104", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.16.2-alpha.1.tgz", "fileCount": 57, "integrity": "sha512-fQKRO9+oZqJ6uQ5tY+5ygxQFarSzVp2TT1XzptPVdsvO2b1YYB9vnwEZdapiDXUSFr/EzOyU/qPyz+6v5t4Q4g==", "signatures": [{"sig": "MEUCIA/3uFyCL9S/QkBOsgt0ITNdM7zT++VhJ53BGFcnXo1xAiEA+u2FJYbAUS1774z2lDrs6DROzPyawGn5voqcWko/Hb4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2701849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8YIoCRA9TVsSAnZWagAAgBoP/jIGYaQhJlEz0bR9ajrf\n7+TGZO3ucaI/tDasZ3Oq0Biib8wYdQ5Lc5hi6GB23XuQBqylIlXUmGE54dMG\nft980hUC48dauqKRJhs5ndYpXIFVGtDu8UtbhQvFHsYU43KCGoU/67/Oq4Eq\nOWIeElLkUuQHQGmDW1JQd3orST+NsQzV612iBnuDjeajFb2gEHS2In+DbhBt\nAcx0RYaJ0ii83MTHgHaIPEIQgUleIokM5NgmH5BTw9g/iZYtJS7Ci4wpTg4U\ns2YxliVpsKmBvJmuTxyxVR0bjAFp1rp4p7G4AiGnyttoHPOzqVhQEbyQAD/p\nlVGvwTdmEztos162FMRc+V4le0Oz/yRdI0H69NFnrN6ZRGGWJKiYsU1rhEIo\no04iKNzZRjt2Z4ek1sPWXpiTThZmxqR6Pr03XU+IN6z7Jfxe1WZBQo5XJ8Q8\nNkHiJwu9+JF01Dccy+FqSMixdRb846KEexfz+1wUnoAxyCGyDsyk5JuBSk0v\nglJHJ6ulqluA7CLQvt/Q/TI+I4Gu1dGqVd13v93MYU8v0ER3uxwz40Zy+8is\nMaxOkhjfdD9l3fJXse32e4sJ3ed1+FzviISfamU9d/Q/HpYrqtAa3ec72Bi6\neQ1PGniFnaRDunlXBdgIiedBoIru7cDHsYnob1a3mzyztSFm+oLOaMDLIeID\nyrEt\r\n=eSxL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.16.2-alpha.2": {"name": "@testing-library/dom", "version": "7.16.2-alpha.2", "dependencies": {"aria-query": "^4.2.1", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "5950460da92a3dbf2099bd5637e74fc64f33f854", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.16.2-alpha.2.tgz", "fileCount": 57, "integrity": "sha512-LvTLCA5TWmEEYOAK3XlMm4tGDVybpXSc7+viwcMp3Q4LWTNo6jF+Ks4271n0Yj8SG5Yls1E8C+waXYBjzewIXw==", "signatures": [{"sig": "MEUCIBsy8yV4AX72pcb5YnAohhOPfFL4E6m/q8gL1sXLC0YhAiEA9q0YP0VvWF6YGA4Vumb6g+4lLZE9qqxOIDkdPKlWBno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2701985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8YiXCRA9TVsSAnZWagAAYIcP/0j2HxfrDsO8fxiCMZJS\nZK1LcgBA2M4k34Yk0BGmElKNzrYLujl2klsaeivHSbeFAZRxeyH5ApOJ3HYp\n9r1iSuoOuQxtlDDlNTs7A7XDGR/TkwPUUI2IiEOOTpCWEpJNhMuKylpCZCpC\nPsIra7aznY4XEO0+sHJ4wyR125rFHpegBx4DE3QjDU/9HGDmRB0dH9ZAnmLo\nroq5ZVFEc/+9pBy+NKkg85X6HPv1kULwuxk4RtFhBHgIIiILrpYLdZy5qoUt\n4DkdnCBKgjHijfuaIyuWdi/U/kgfuHfVmjCfmd64GGu/fFoTirhyiLSt75I0\nBzrHRLsB9+pnSlTXmYnJ1Fji6B+zHLCd+hGZ6ZQ90H3fAwP8a1GrTg8JiFrG\nBFE8az2DukVMrwpQuZxb7BaS0Ms8fKBB8lMu22lt5FEoONqDp+P4S96bvLXZ\nFTdseU8aHfsnHo5n1XWYq+YFg4eVDRUMlq75b8ZWOsXyM83ZKYnElfs/li/J\nEllRhktkG5ELi4dcBKn1PDSxjyhlXapGepG0vl1CdemaAYM6Kq73Br8yYDOF\n2uf6eW5PMyhgjXPB0QkNuqoaE/Pj/wwxEpeCqrKvK0AL3RrFrcwUpOdYmaQt\nmwPUMxDf982xVkzn6kcpouFCQ3h1EnFQnbuBiwGigO2lZ7CyJOh1udTSid89\njUhz\r\n=1nmC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.16.3": {"name": "@testing-library/dom", "version": "7.16.3", "dependencies": {"aria-query": "^4.2.1", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "ef7fe623f337a9393e9a52b19960347fb22bdf7d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.16.3.tgz", "fileCount": 57, "integrity": "sha512-6s13JqQ8yYtlR8RmaoQpxlW/ssMmpWJQo4tAIypoWecX/4JSFCL3h3VLoLphbPLRmn18S4x/BHjT2egkCaYl5Q==", "signatures": [{"sig": "MEUCIHsCkypCy43jlI1YiBTXDXce5vvMAxT4pDLVFApnRE2jAiEA9D2SmXUfCnx7nv3jSmvrQCHEx0fP151aC/UIblLJW4s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2691310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8bSyCRA9TVsSAnZWagAA7IYQAJdzyY6NdXulnBTi3CqI\nsj+qoMOMAtYipp4OTjoPQakhfRee1/2OvFlX+WRpqbUcVdfyLlKa8iRnku5L\nIHeG59uHV0WdmSfFtVkoQi6cfqsItPU7/2S0hz+//hJDZqCVYMWPfDh8mQB5\ncF042itOad2HKRn9cyUPqsRzuH1I5tY3SfLico6LxC917xycQxNBVGpIcRs2\n1mitfTm/v8a20P+aGVedqaR+ZT8Xk66d3SJGR/i+NI0k0zHf6bYQuWsMKy/6\nXsMQfYePJ2wgsR1EDEnT7ey4JIXxUpIu5wOXCiTfxuH/cQDzzd87bS11zbJ/\njcmjaBFK4rfTeuq3WYe15iw0qJfL2wgA8iH6MOhK6p8ATuT8GgKQqCekN1vX\nhItErkGwn1e36KaU6kbxeresfa0tHbjiMd4WcX7DQOA6ekLhIXvVX78oNJvj\nGZb4r3P5EaEpe0KQm7kpH3zjYf2Alc2TeYVRCokP6DytKrhT3MNSnUzCzQVC\nyZGGRZjp93axZUnTL9uFkXab2iPQhaJCacGxJX8pbKp85K66MRcBGJ/4ZsIK\ny7+zfypFM82KL63rzTtHSZT88JtlG+YFNtqsz7SQ35zBtqHgh3kG3FzEDnNy\n6uyIamg8zrYZuH7al/v6+iguKGJubvQEFvXbtZWEMBKhrwW97DEStcKs6wYd\nwrsv\r\n=kQnY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.17.0": {"name": "@testing-library/dom", "version": "7.17.0", "dependencies": {"aria-query": "^4.2.1", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.2", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.9", "typescript": "^3.9.5", "kcd-scripts": "^6.2.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.9.0", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "42a359c21ba1cacdccf14d215a1a844011bb4e11", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.17.0.tgz", "fileCount": 57, "integrity": "sha512-GT8cRigyD9Qr6+ECQHFTzhgX3srdDqD05I47CtXUp97gkFQ2lu7ylJbcxjHm25PMZyxooOk03bY2jVj2PdoNGg==", "signatures": [{"sig": "MEUCIQCYb6kJ1ZRsFoatJuZC+K96YOIODDeIz2IGVacM3+HVbgIgP7He+U6wdSMMNlxlYgmh2PH22I7/h5Y+Rzj1KJloVps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2703576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8iuOCRA9TVsSAnZWagAAw6QP/1HPKdGASLv2M5Fa617l\n2DKy4+pcC5pYk8LIlZqIV6CrbUlupd0vbofVBmUwY+Ut3kEUOvyDmxMyAZeO\naeJ4Dpyz/J8C+SlCL6IbPGw+iS6HmwdXNeUmA6UtoCFXdCc9gCy/3B2xbI5G\nJVk9ZQCkoLzsPspwcPLC0ATMkwlAFKlTm07nbLmnHzz53qNX5zC4aflmX+64\nSc3RYnmUGlZ+sBjA98nkBpX4hnE6fPG4hLAjb1k4JqJHe86BkcPkcmIpDGzB\np6W3gruYU3dazKvbynRyq675Uck53avTYgcuI2/m2N7lymBhAxqNf99M/iI4\nk8c+kmBwcbhHbI3AWDzfBHDV1MJP5/EBuF7Xej8HdXvRhRSI+qr9v6k5PC2J\nxXe/I7mbbccc003iu5Gr8iL7pBZT/E0Fs7HRwtk7576NQnNtOu+VpTq4fDaY\nwnt80Jihx/3ukTOd9NGee2b7gAzwDYnkkIZg/BPDGOQplvtL+MjPxTARfdav\nDDPVmVRbQWFN0MavLFr09uBSNJmcIJXN8uh1Z4U9hSpuoBRKWbpFyjLClORr\nUTG+GTKeXk5SfaUGFfxNnzLm9xdds6ucOr/1yuRqz/L1o9RAfddROY9Mv3X3\n2GltuDudDrxBxdtEbofcqthTCylJXjn6MfqO/H48cjaHWgM+14ScMUoHeZp5\nBBhA\r\n=fMst\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.17.1": {"name": "@testing-library/dom", "version": "7.17.1", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "7ea1512dbba858940ee39e82652055d367b94cb5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.17.1.tgz", "fileCount": 57, "integrity": "sha512-gmORZyxqqMAM3TVOXQftgGxqISiCN7hQHAVtV26OqESB8x8SKbX0tlX4+VvagLi1WMK2TLd9MWUJfuHHN7fPQg==", "signatures": [{"sig": "MEUCICHznNjHiRKZzkT/SCU0SyFdnDyhTokbYjZhoZcWqV45AiEAxD15IRxSWCgcgPzM+wCv5JLa0+Bpn4VxUFugAq4mM7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2707857, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8nL8CRA9TVsSAnZWagAAsLYP/j0iS/e29GLaXMcOJpYi\nRix2pm56wCoCi1o8c68wgnv33ZxRVkXs49XC6o1r6IKgkgIr+swnX6tzSMMR\nkZIbrB0AWuorufpjfapjST3rejn5x7lLUtRAoa05rfbAJsXnrJPeJn9gxKHM\ng7hmZX4eLZEZw3eGUXyqnwGGsh8bspiJCIwVFc6T87rSoRcbm9WOxYTRcdaD\n5buL6xjad8Ch2mfCsTaNBehY2I88sPkFlctrlzH/dnFNGz78/0InhHCN8672\nCD6EdEdF+hPsHSVaJE51hmY1vxPRQ0hvjyi3tD7QnOO81YWo0t+kw24tP3T3\n78XW5G/xJkF7nIgyP5QzlOxd41olAghLUsGMAYjRPXoLoYJLYNhn24OqJxwv\nAM0UkUINRjo354H4UaOcOydU6bLM0bgqBBzFiZkJzEK4C2Ebtm8qHbKCWQWp\nkBXlfMVaHktNdjHwCEsgsFKPj3byoH5xVDRqkT/k/+07JIYc3aXKmQ3v/6ml\nlU4p5ACo9z8w6C8OerkREcu5xRzn+Fe0kW3OXOCvk2Dfkoyoug9xBSCO5ZYg\neL25Fru0eDH/ESlJSRAMjgrr/aMcwcCv08Ya5iJPjBkXl79jTJtSYbWn0hc8\nK7q5zrDcAw8IjuF5k+69jSoOk7iUbwrmZvKqawejwBOaAebpckx63iTDhd0v\n1iVv\r\n=9vlS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.17.2": {"name": "@testing-library/dom", "version": "7.17.2", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "d062e41336b885107bca8ffc7022eaf37cccaee1", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.17.2.tgz", "fileCount": 57, "integrity": "sha512-TQAoIzoI9g64oNVJ+13i4cCh/DQp/n7fJOyMqlFB1oQVusPtSgiPImyb52CwtvPO7J0Im0+/4YcmxU9a+cVxxw==", "signatures": [{"sig": "MEQCIHSAwrNJznLeIFH9QsJGvjT2riza1MQeUYXnUq7URMLbAiAS3lQB4i89ILYudx/OLV70iYpKjkghajEsF9pu7Z5c4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2707871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe81tWCRA9TVsSAnZWagAAh0wQAKFRIPas8wyfofgGtAyN\nRTifok2oOvouJ1qQBad7MomKkqKg0U/rVKqPV3QGxiXTkyNQDQX/70WcYmok\nRHMmOp1V2+PKZPSmJal7XWeBYoyKis2gqXXTBqEDQNQhaLX6GhWWY7ZxArpj\nDDgAuSeNj6GdGLuj0SkWBiGj5x4VwhTlADz1mf82muIaLFDTJCVQUKXj08SE\nl96ZDnF3OKq6FeYjdXBKpfSE9NPLBaFsnLTRPHBvNWqfSn07KyV6AfrQQRcq\nHdTJ10K6eAsB9qBwCvg+HUVImIumbMCFEAHo+cAEfLI4v+Rg4G/4kGr/2Fci\nvibl5+/moqhMA3EgRZ8zMV8p9O+FrqQV2YMmleYFxXTx7eNesUe+uCojeRZO\nEgXpGVxsgoGL0ausVqtJfJc2v9imvsF1hVdXNi2HJDGgh13fL7lRLPJrgqkf\nlLM7xc4oqCX+TdwKjldQ7NIYMBFXLzRu2yu1n5Jb+2fsx+15qimfFzJQkx9d\n6edcBDftY26EgSrvUqOwIMOWQFXPSs9XN/52IlwC/naFd7yBko6Ll1nSyeVW\nYgtLtJLHJaAGGJTCDwFo1anwoXQ0shqFw7VNMStClacRIUxh+zJ8XhI4tdTF\nl16/86ziOLTJAkKf6GIfgPHCcHUhZvWZ8EFgt32DOW7+s1nsCHuKOSYAuYSx\ncKnC\r\n=tImw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.18.0": {"name": "@testing-library/dom", "version": "7.18.0", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "fc883ba993f35cd00c863099cfcf314395f0a9eb", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.18.0.tgz", "fileCount": 57, "integrity": "sha512-OD6jgGc4nwM4GNXZJlVcI6wwP5hcJ4T8g752wbtK/Ub7jdATvAdnVSauODdoBMsEZejb1VoWNQsTDc4C9bgrsw==", "signatures": [{"sig": "MEUCICC+bJhA4NSyudmAU2iXrgQ/Cgs6VzQ3B9FS+OHZJ3MeAiEA78B3m1C3rk33MHJpiiu4/JIha9Uxjo9Su57ipMgtwd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2709586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe88MQCRA9TVsSAnZWagAAv1IQAIUtJaX+9vCsi5avhBPQ\nru284pDd3MuQxNXSAcDibIt4zpFxyhUDnb40qEt9XitguWVjG/DGXD3dvdKt\nX6fJS6kw6wjFbyhn1fF8u3oHOtVPQifdpbnRff4Y/EDFmrbdaCKffnTyS1i8\n9K9CWWn3WUAMpn/Qksm3SAhZ1p1+B192dvf+vuaat13OeCD9Qut4xsoMmdCp\nR9zkODdbUqgYwW6zrw8hJow54FomUAvp2y65O8ZYZ9F6oyDRRsa1ehrxrPFx\nMK7q6auB6roBzxTH1ifhcHxJJwqCTG29QogB8rhgsVDeSP5IoGdCbR8lfGg5\nsNS5ZQ07UQTMyHJi5t5FGWVGg9PgfRjQ7eVZvRdR6BDD/dZdCGztsVFO3Ewi\neE29sUxEZqCrON1nap84uYW3e8agDDVghPsg01HBnyIaJJnhbfFqNGflQYgI\n7kMEcvYgvUDiGG8MaTAzNjxSfETY+6+GaGteXOHzOPsVCU+81XW9qIFm9bYU\nmMQUFUR0ROsYVUM1o+uwlz5E8oolrbekSPABvp3gKJ43X5uGUwv2D9+qBhLn\njMz/1X4H8Qzq+Tdo3gQ6OJoMVKhBazK4cSb3hswfxgxZm+1q4Bqn1/vmpuAm\ntcfwqCIXoMeVBM0+rsye+7+C/QuQJjv4shIl79AzmF7PpRoU8pHcXgF2KHxV\nC2bW\r\n=+Bas\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.18.1": {"name": "@testing-library/dom", "version": "7.18.1", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c49530410fb184522b3b59c4f9cd6397dc5b462d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.18.1.tgz", "fileCount": 57, "integrity": "sha512-tGq4KAFjaI7j375sMM1RRVleWA0viJWs/w69B+nyDkqYLNkhdTHdV6mGkspJlkn3PUfyBDi3rERDv4PA/LrpVA==", "signatures": [{"sig": "MEUCIF7PwJ+ebIwfZOWwb0yVTLzH/WnKGXlcHEMCApfqV/DcAiEAgApInlU6mk6hi1W5emGYYA+HslOrHnr4ZDbCqI7gEQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2711321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9LsyCRA9TVsSAnZWagAAM5YQAKLQE0q263BurTG16Y1n\nwqqdOWBvIL6eknJ7LFvzKZ9i+9KUtl6Iivw+4Y+/9kXizMsGzmJF3me3RNBr\nlO4/F7NwsfwfHyNnGF+AaRhigrmqx3gxa5I7+yR8LbeI9YuyDvIorEEaTvks\n9kUSB2ilmZ6LwPePIgHISkYJMU57zuwCHqB8Iamq+KGtrv6uha4P2JmO2syN\nxWvi/D2/cxq/Rl20F0nSFtJQESNShvN/50U1VHL1lqP78YOZ/nz1ZKJL0qSl\nY0nSPkfYykaZawI7XLq8X25pm/k0Zx69JBbzTbC9Vqlqnk9K79YrIbqJrVD3\n1LpH/34ToTtKlMYhxFkzXPgxlIfivaTbcy4U1XdVB+ZcqaU88umcau9Rvb3H\n6eqkD8xuIwS+JLklRrWNSu6OI+myKU560meIInxK2QE70PCltyMXY/TbqG/1\nyzqksdkFR2/PPcSWS7JiVhqP+op8/i8P5m1FrTYCaonYEzXLzkBviSa0iqbN\n3Vt3ZuEQQJ1ruBwQMF9Xs/glK49LW1iSI4JLkaURGfY0zQgUV+0nUBZYyFSW\ndXnCPs2SMTCYUZqP4bAfUQZmU9GM6k5fo+oUeQEeXvuDtY/XUf3AwDgWIELo\nlHTFymLsJfYjuEWHQxR2IXm2z8WHB77Jfbx4Q2Y/zkRRLm6vQn83vQEzcvPx\nCv1s\r\n=Wney\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.18.2": {"name": "@testing-library/dom", "version": "7.18.2", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "e5fcd910e10de5d9765250b3440f167758b5f419", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.18.2.tgz", "fileCount": 57, "integrity": "sha512-4bPXA5298SmEvLJGYR7hvEh6FrC9JgU3QXWRPl0hmGzXRgQbb5EdmJoilnqCsDCXOAUUNaYogJOJEwhlTq6Yvg==", "signatures": [{"sig": "MEQCIGzI4jNKyMuNFKVjIfQ3sdSx+1a3Rqop/NbZi/rju0LDAiB+u6YpvIZfytideEFHsQp8CP2K1wDHaHoG5zFkWJ3wsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2713914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+LTZCRA9TVsSAnZWagAAaOUP/jeR5jGR7rUesS2V+qW9\n0fVJ1V7JcH2Ak/1N/60s+0QVBme5MvJeM0pSkwaXwc6CcaCoFc+Y45ZGrqEs\nSj59QHzjLUMpgT439Wd0D2SuMHiI0WgFDeNHh7wxmOOFYu4GBGKahwhpButX\n8ytzLhSHJO73l4VVzKghzJsPZ/qr8BTDdTu8PXbj7mjNLN2qwf2YjaC473Eo\nZHmdKdQT56KzO3TPP0SVR1bdRM7VmXjqzxQ7+EiO/l51+5G+RumIYSqDXu6h\nOneCyfYV67bnxOJ8V36qq+01ohh6LMK5JKE4fbEt/MyOD07GiWJX5pDD6eIN\ngeq/VZsXWhEqr8S5kvA55DfpuJrlLtUnHUlf88Tlu1Xk4w+mEzR+JC0HfLFC\nVJSs8Xy55FAwL2iH7ojd/Se4wL04PbTx7hDHv0Y5aSICaQp/2LAroZ2N0Rws\nT04b68i8J5Qg3NHBCNadhHW8+FM7Hxhm9B58de75de5cgwq4bkNZ4VrxwCve\naZNjm8vaMiiA43TawJa0BlnI9Yc080/ygvWSka0oYKTr9oNR3ixgN1JgjB7F\nbbI2wqXPaqoDDX98hrZ/R9a/hfwZx6NMYFrufgKXX7cpyzyDG7ZNv6ZsApMy\ntn8e039hJkpDhg0je8fEPxcJBJg73dsO7AXegdX+9o3E/U8+Tf9sEgu4g+Ij\nUNTm\r\n=9jOA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.19.0": {"name": "@testing-library/dom", "version": "7.19.0", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "35d33bdac587000c4bd19cc73f3c58a05a0f29af", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.19.0.tgz", "fileCount": 57, "integrity": "sha512-t4sIYKGJ8vCi+fEP3f+lnvomvhofHK8xXxhrapiRS8tnP6la9woE+d9hjivviRkwvNFAOAYaN7REKnQf5XCGxg==", "signatures": [{"sig": "MEYCIQCsBuwHiBidTEriff50EtZM0tKXbY+3b9wuL75Bnsf/PwIhAK6JZztV981yHiATkmP+U3QurcoMs5fEA4Q4BK86v9bh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2715380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+OWOCRA9TVsSAnZWagAACqAP/0cYYowTHYYoH+AHv8Wb\nocqi1VqJwdzEr4AyLe002lwksjgEOAyR09dFFQ+SMvH8lEzQnmRmlACWv8g/\nFPxQm2GViWAhA8wYBaqPI3Ni4qIdEuDy4xvPFeP2hlyCRhPNHRRIvvjfEBMy\nlxj1G6z+oI912bpGMQQa2ae6RvNm2ouNMWsXb9RrOx0HKht3uVY0KadIQWpl\n1pfKRP+hjPGUOvydJx+JyM5dne33npydzfL7WXVQyey2FS0uxEGWujPIIwMe\n6puKJsvMAYs7pKWLd7r6ZBD4MNV58eabJBiGowqrG8KGuDLHlKAqTowigis8\nZyn3v6YsmTW3YuzBAz7Xm+sdHn3zblvny8nviyXkd14ahc1aFmQsI7Yu1cBI\nyW94BAg5NOfBRQBVJmcAZXpEoprfrSVvFmvb5iKVbGb4DgLDCcIMIGWm83Qk\nC4qGGBlerEnO8vKMbq+/MsWhGPo6pT8jXYG6GvFoOy2TQN0Y4BGP9ABWhwOs\nYNiyn5XmeIx1WYGDXbHKPnqaJbzlnJh62nEwVhlmDe6KfW6WhKupxs12aYfV\nwdTbuzQyT3i2nDqC2NR+u41pqHDnVgkHb9VJidrd3Ozj107s2S14j4kxzK6j\n4yVhvIrET8K2f75maqvx5A02qfVv2bVRFMwZxZ/p6t1doz9PhJe7ic42XiUP\nTX2+\r\n=zht/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.20.0": {"name": "@testing-library/dom", "version": "7.20.0", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "2bab85e90f0221a56256c5d4741c2a36b7c45f4d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.20.0.tgz", "fileCount": 57, "integrity": "sha512-TywaC+qDGm/Ro34kRYkFQPdT+pxSF4UjZGLIqcGfFQH5IGR43Y7sGLPnkieIW/GNsu337oxNsLUAgpI0JWhXHw==", "signatures": [{"sig": "MEUCIAJB0p0AQSdCfGfMU2wVaryInuPAk5u2HU7Ff0y6MlNhAiEArfRN2JD7BoIULFzWhbcMja9aAMk8IulQL0DDC+5nCJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2716375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe/bJnCRA9TVsSAnZWagAAMLMP/RwdHigFb3OmAoZ9UmhQ\nOhQ9tDhmddfx4OjGUN5eaR9a54XHv0TxcvyVc56vcwiizzBS6n8uZkMo1DY4\nlCiB29fcm9pXFwfC60Z6fPGadhCgRO8U2BlZWVdzFsf+IpSmdQxwd1RpFJDf\nEp9+ehUQOscm4NEsPxuMOIYeeW5tNS8gi/JBVGacqpJSh3N3z9m75xZVxDhd\nZ5rTIgNf19yHK2zLGZHfo5SFa9dcDFpD6rXwGfPzeH/fQ+GzkSgUAGxmyG4N\nr0vnIBmaKXYyBu5m6ybjvbcbT0HxUPL0kpVm+TNi9+ymwH2ee33wc2Ss8VmC\nAp1QBpjMQvJ+d+sE2ITFcRQyPVjjnpTRxEG2qEeHYM4EtBDgqNYu8x8d+OOG\nOo2gi8vwulqDtR0WjL8od+2ZT8VjYv+HHfV2JRzQYOS/mWR3UJafsPbgV9yI\nofPQ87GqFaTCGngKe/5NAp9M5J5JXawvPZkLc6bTdLLJHFN2wbPTIo0KUKgJ\nBgzwdMuj3aX0sTCiS6ovDb9WxZXSq7dnZsPg+0U6LpvITPw0wkdyoB5xIgQN\nqx9qhMQ5CJTLQtdEUws3ZGXelGeZ5RANGA6IXOThjQkLgjAos1i9NafczVAR\ngdCeWOptzoFKHYkFha8dlg7xDeU1a30VbtZpVGElF59DpehIFbdNtqYlLdZr\npC5x\r\n=sZro\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.20.1": {"name": "@testing-library/dom", "version": "7.20.1", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.5"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "bd3fd92eed4a29ea9b2731a96f430566da9b8980", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.20.1.tgz", "fileCount": 57, "integrity": "sha512-kvAMxpPPPFHIC5vkuSXpx32Q/c1qHoVVuZEvB/pY4u1lL7nwWdM4P7vjzWlDfAEVNmSK2gHA4YrW5+hLxmFuyA==", "signatures": [{"sig": "MEYCIQC1OBf1/xZxerrcWXbTRqUF9DS+/yWotC7RMyr9efGujAIhAKOToDslmszTZTrp5WtaPJrs1SBUtPGq5hIcW+bEKkHb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2716598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfC1qeCRA9TVsSAnZWagAAdqcP/1lm744rnUh+0yPi+8Al\ntLh+/5XAMq8xa49VwLoCGO5/6++vlRESma+nE1pdZMzxsaqotXoSo+ME/9sa\nYBkBenyNQxg6YIUtPoYV8Cs2N2RRkgjgaLqT1do8QSf+BJpgHkElDf6mDg9v\naqyXv0ivcbUGyikSr6Itgvfx65DzRtJppns3yu6OGUoW7VJDi487dW654G9C\nkGMjbag/cu1FTVNaw+kGKHAkXl9PJKr3Ty6qjkLxN/sYwj+56O97WSPawuhI\nWzNGYzCCnxqnajmDkklmok5Itiz//Iyhj3F4DdfB5E+EsD8bN+HHYwQIRxnz\n5rRP1C/h3lePHwbLzHGrb9Th3YEdPwzZQHspqLMmE4+49nOfRe1KU5xfj8rp\nDgTrXwXhj8cwl/4sXbN2yjuKzC6FLaJmHm42ZiOEqmuc8RbDPykxAQuuNq4d\nh+fu6D88IQi9SC/v4SXinZU1sXPCN4xHy8QxoLX/lZiCgRFOgBHP1q8UOu41\nJadrGkMBY2KRwx5VW7MojvLN4wZsxnrekS5nyFyXg+b9r7c6SlTT85QPGvep\nALgAYImhOqrQ6mNm0bBn8siAGq6X7h9e7puTjTOw1hjRFcXpI5rdC8m9q8df\nHV8pGNZiG9cP0ga1C8cqZxQxI26pYRCfkuF2TBeHJvIwr5X/H0oAL/D404VS\nVeGV\r\n=c9Gp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.20.2": {"name": "@testing-library/dom", "version": "7.20.2", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "d598265248a6e16a2fe0aff6b96b858ca5f1a71b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.20.2.tgz", "fileCount": 57, "integrity": "sha512-9KOD0fFCTVFsT1EgB8C5qKs1nV7KdIGe0YIANAKeIDWWC0vwkiLXA/8HlrM2+w7YXiRXIeeHh0LxTYQpvaoGgA==", "signatures": [{"sig": "MEYCIQDo1kXSed8Ba2mdcoBUDp5L5d/7357HfBbGkOeIPtpj/AIhAN8Ulg84UZG7NsHafLd/gfVIJ3fOvoXZr21SMo9ZvAcO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2721731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDNiwCRA9TVsSAnZWagAAHngP/1TSUlidstRv8c1JITdr\n1XZv9IyCEEYqIFRMW3pyg0pWTW8l6MozFFkKG+crwLFTsxM+VNXIwZ1ipkvG\nHAGRVlxMFl+QaqpBdPyOC3/uB1hBwcLv457ZT7TQUw0XwiMitQOOew6sEQc8\nGU4U82iMvdUC3lkimocORK9Aox/9JbkoEGntJarB7/yTedbV88RXr5Dxt3A8\ndaByFtqDlLVPWZUEkEgUVfdYFTeGIbqq7Thw0OugswZYqBMJUxDEXX2dYY4a\n+3gvq/q1XcLUs8ndW2fqk7KjrZ5Fb730NJ2l/qXDNm6LLHrlCX0bLZzQHT1R\nyeLv0G5d9monF2fQXU0pKj+tSgjjDhCN1LJ+WzDijOVFHmsx7N45so4CrEsB\nQ6Z2jdKDlBZpNJ9LOPHMU3pkiI2dV7abToBAYugCErGJ7NA4/WP7WjMUNCiK\nsjPCE+Wtz9Km3Tar+/i/cbwxccFQIqk11YpL6pITe240WjVorQhAbbdu/LgK\nIjgwbSO/f9JuORAmCXAkXTLMO/apbe0UPw/Pn+xz5GPDp2PvZ8sY2wAq+3F4\nFhM2edX54aRZfgsOY+zrQvgUB9R853Zullg+Obud8z9wUVBlAHUpNIO1q0rS\ndLxWZ/hKYyTI4lL62j7k+xHzCqN0pFWoG21Zilh7n4Sv3eswcsC9x3B43KCy\nG/+T\r\n=eJWx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.0": {"name": "@testing-library/dom", "version": "7.21.0", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "9eedde0b8d6a2cf5716773e9f3170ad032ea45f7", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.0.tgz", "fileCount": 57, "integrity": "sha512-S8TTMCd7qCkLs6bhdh2+fk7/pIrD16UKvnwa6gJICalZzV1xoAxDY9Isp6qmelizYH4P1Tz+O5Y4nMmjx3x0uQ==", "signatures": [{"sig": "MEUCIDFiZid2RNMNpTcjK+VKJUKMo3EKllGg7x5VsDw1lZfMAiEAl9u4QwckgfGaIsidg8lWV8HjnmWrkXUyd3E4zGICMx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDv+7CRA9TVsSAnZWagAAG0UP/ij+LhpxcfMqUnvYsHZh\nETDIUlKHMrBiWezyhtoM9o0D1AflGFB22OwMR04t2ggyYTJk4yBQTFXRvWlI\nEiM6VMfD+fOu/3fnY7u4nMa/VAQNG+pMtevUPrQXXe3453XAZh47+o/Exsvy\n3pnNpWuEp9KN1PQFIAsd1es8QA0KIbsOA80aebVXG0+tRRJ4PCDFQtfsMgSq\nIliqDFFkjia/nm7Egz0qHMgY2EOLqqjRb2nOhDd+VtcM5FaG/+hWVs5k7Q5d\niu2WbxmmbTE0Ad5KRb3c4p9tpraxFqFUjVHqzYIeog5aezmJLzT13qkpU/FF\nYTwm3Hk5MWfrY8Y6QYF0o7Kx2GXDQ9Khd+aSHKHM7Ca/csqapND2b6nPrL61\nfkWL9o6ztp1swD0CpFhGJLjsRYQURzWAvOiqJr8V/k/T6QKDvcCpK3WbyMrc\nBFXhG++O8Qr3e+gMs08cj/1RlyCky89iTERLB+ifDHxMmi4ZSPypbRdXTMTD\nU62rehXhFkJ0Y6ps4reIhEYvzYmmdHB3JZ2rIgpShi6cRwn887Mhkvgf7VVI\n9o9LbLbj8GlZGjRGeNQmgpUfbowFfcHiApyoWv9R4C8gM2RfxJUGpUCweK7i\n1uVnOcaq+hzr0CPhhhnXlewriW4sFURmDY+6Vlg+C03IBnoDCosoz3AH8bT5\nthKN\r\n=lhpH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.1": {"name": "@testing-library/dom", "version": "7.21.1", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c59f50fddc33db34547a7860f969dbede110623d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.1.tgz", "fileCount": 57, "integrity": "sha512-BVFZeCtZ4cbFqOr/T8rS8q8tfK998SZeC0VcBUGBp3uEr2NVjPaImnzHPJWUx3A+JQqT01aG60SZ7kuyuZCZUQ==", "signatures": [{"sig": "MEUCIGTqmpsCa6zEe7XD1hetMlhIwPHEcNo8AuWpt/deBFLHAiEAqDD6hSZo2X3hAxQrKQ3AoPCASKNXfr9u9j8KA87/zD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2730500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEbieCRA9TVsSAnZWagAA9MoP/0ya9M5UkGgGPdCiY9zH\n7e4eajQA1WK5PappgnUYc2+GLhllT0i/4Y6JXLrXgw3R8Utl0WJ0cNgnY0o5\noZHK5oSPyMyAHypBV56CX0LyC3GzL4PDHYzfO8H5GPyWHtzliJMQWI2hiXD6\n3BUpbWvB8aEJJ5YoG2oqyKIkMgvS1VmDT4rSms771aT2AwhiTN7jc3eJDS33\n4hQxV0wkbpDgDcSCJSnIXMaDb8nS4QqR8nmBpaNeglNgKTC5/PktPRsG048T\n20CxCZT6KaRt07Ho3ZSgmJABJ6bK3r64HcTDf2p0uZG4m5aY+Rl3fY2EFBn6\n8OJFzTBEINhLkjX+EGTKwcJapI0vCZBGQ8bCXT5w6My3TAwYVsqjj9pwUuo2\nQI3dYK1IBhdIjsy16GcnJSY4wtC+YAMGsfGSzGner1vi+HMUAO21O3FSAEv1\nYq/bdkwev7TrGRmHsyFkX+oQVQo/RbjcYeX+T1Jsa2ngSEm6DeplpOYf6tM1\nw2gz67Hwe4JhbZKjbeDFud75LkuczEpRls0/4Imu8Gibvi6dFB34WAp19aJN\njYdyQ5xngZyV2Qs96tm3qbUxNaYLMFzvCYHfDyg0VxaTmp8SBkh1//IXaKnZ\n1C6S4XobRtN50BZW1NqvmQ0mcKmtfcRd51SrcikVxfJj4v5rJg0GBkrMqzw2\nhPRH\r\n=k9wH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.2": {"name": "@testing-library/dom", "version": "7.21.2", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "8521991dcdcadc984041dea702713257b86e71d7", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.2.tgz", "fileCount": 57, "integrity": "sha512-dHm2rhAW/7EeTo+C63lJe1+KUVSafHuqulI3OWLh/zlmMmcOiAIGTzRdrDnU3g8EJeRR0lKZppFFDD1r5vAZuw==", "signatures": [{"sig": "MEUCIQDzefg2Md53xpGbjhGkFh4Avic5anRHRDyKC5dpMEYa8QIgUVgqPmKAMCHlukWJEhvAwGSaYamz0MB1WgNxa1X+Bts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2736591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFfOWCRA9TVsSAnZWagAAvFwP/3MKZvVJxSnIHKXmwlWQ\nwdMiFA0ysKMl81U3y8XoD9g960DjwpQBK9V+lbJkkyvjBdnb4XS6tMc0s6gd\nonEppwpC3nrmtSadqkKZsAs5bChgHaNivItvK8daR9NFC8yR1xVzCynbERoL\n5s67zZYxZYSk4T539poEWuRmkfXiVVVCkoAe1d0WT1ZBW6ogW85E6r+rKqhP\nozTcHTDT8xBtrMT4fJxWxGvgmPwDXTTtwbbppuF8o2QmXp1hZxNBLvyFkMxN\nzHyIyEKfSmcUeOxi23W5xE/qn2Eg4WSAtaR24D2+mx3Tum+/vcJT6B/AvdA6\n7QV+fMW3QcNHBtEWxBh2xEJXZfCR8hfzMVT3w0OSLHK/ThMIgvTFd4gD2K2u\nR0Q9Erf/fWBmKAR/XM9lnDLdPKRVbCiI7pYKMWwwwdBO9nobPJlUQL+AZbzq\nP/QNPZguWXxMlkBd26C8iEjYjXBC/vU5PcbxwjbXAE4zRoE3TrikUWKPgqMz\nfhzRrBEBH4ZvS0+kNnHwWJwKPUDfiH2Hs+acn72w2zq0cYCFyuK32T/Ulyd+\nNbtB2JstbhkFPsbg021DjZg8TxmfQhGNx83Z0RZ+1VR3cn7/OAXK/jUi7R1g\nIE4sXDwb5GVfLWJFFB9JxvGvbrF30HhtDgnzTAaPMlyEUh2gfdaYIIz+tk7c\nQ8M7\r\n=ESP8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.3": {"name": "@testing-library/dom", "version": "7.21.3", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "09f96d89f06fbe49654993add11822904973668c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.3.tgz", "fileCount": 57, "integrity": "sha512-dZBp+llkl3iBGnJzxITWCZV3gWmFSe8I8CwHDrVWguJnwt44m6sCjIFMk171IaNbmDgT2KKrAra7Hj/eNrpooQ==", "signatures": [{"sig": "MEUCIQC3I1Z69PMw0eBZkj5TCICqtxyTwVLKAkxx1I7nAy8G1wIgcwnoXJtRLNoczQ3uKFCHI89iNPa0f0hzXuEEjY1yFMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2737051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFh9XCRA9TVsSAnZWagAAfLsP/RGtEhzKK2GmMxFkn9Bd\nZ5xB/z5ONQzNRbtmnXDrdmNa8uTrJkURG8AviUKLgTFE36h/0w0vOlmpwhni\n8OZS1MQhWlL/Cdhs3vCbUCORLk8l1wTvm3ZsPeTqbw2dCBAmcpd0go73zqHa\n9Vv2Dd/ud7Dt5HE8fUpuawwhR0xAyZe7WKKXeGR7hCCf1EaOWLx1z4uwhkxZ\nhrnylw9YW3Tt4soxWk0u6njsXcG74XCj9OfzXBjQDQN22c6/qwNTUFm9xN+U\nN/e//T6TYzQlevF8Q7t/qowN9vH3U8RwHjI3en1Wt/qL3b8GPY9u5xXLETGB\nwf6fr9hPfA/3I+gXKbz+NObRXWDGojf4X+xzYX7b1hc9Y8rVjj+7Qg7QLV3t\nopwnO9H+ForeLsU4asuvJTSKuoL2/iC+Cwws3NQ6TLlgQytHR4p+fmAKU1zZ\nEXpZbOtHNnYRay9nWuiGiWrezXRJ1aJaqK0eAVw8o580rvXP9Wilfziw3NfX\n6SlGXtRDoBmhwL3mxXC7yv5fs3zijD/GqRDS7gbtbpBd+/6FU96m5SkpsB7d\nAeAfiZmLES8WEvB6oCjZt84zIR45LfQk+X9P/1lXiVTGz41+OHJJJHtqPvlM\ntGZMQPXOuWgBTmNAODyN54qitRzH4HhrgzhJTh2A42bxshyXooh/87b2CIIg\n1RBw\r\n=Zbs5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.4": {"name": "@testing-library/dom", "version": "7.21.4", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "24b045f3161b7c91fdb35da7c001908cdc99b55b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.4.tgz", "fileCount": 57, "integrity": "sha512-IXjKRTAH31nQ+mx6q3IPw85RTLul8VlWBm1rxURoxDt7JI0HPlAAfbtrKTdeq83XYCYO7HSHogyV+OsD+6FX0Q==", "signatures": [{"sig": "MEQCIC4kLAZtZDXLdhf4ofDyekyRukixlMfPxP1npzwB5sYuAiBcUlHAPNEpdolE6gwYf0XGc/8jHzs95S7Qpl0XANTmRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2737730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFud+CRA9TVsSAnZWagAArkMP/1R8boaQTSgC+6JA8+Vk\nDQsWV1+6AnnrtaXKoQ3yPcd/vGnWXY06vOfadJPFvoehNunF6ytKSrh/qPi8\nfg6J2eqYdkFSHpuBd+kwd8qk5a3oBirxj5gQ2lFcr60c5mJEaSw6C4XNoSnR\nKc2XpZlCvGUVgkM3LHgikqY8k0MWAprJ1gMvGS+75D7wLsNuswLs+RQ6bqbQ\nnt4NzOJMFqSWgiqZ8wODr7X0u8BiJyUzuRN5YEkEi1OOaPuzmxAGUr6Nyd7/\nGKdLVWxGzBZMacXnywmHzUl9dGOlEo62XWgiwLbbE7wxcGPfTBm5QtPnb2lt\nDQHgGkl1mBNK6PnXQ3XuUbmniWZ4lwb54nxxsGVhd05Rx0qWeHTwZj8EuF3J\nkWJS8kQncZu2qgUjYO9Hn4v+0/i7vimhXWTVlFVGHeeYu6dV2xVsIqbK625i\nCw3Z28wi/EiKgU5UB8dqv6szGi8fitpG/wrxt4A1m8+Z45hLl2nCBa37GHms\njBnrlPkyLNtdu4vROwlUbNmDGpGZiqoObTPqeTUJgLMFVqb0DpqNRKml5VtB\nedTrpheg46V8flb5QIftXF41tygOvD3/gsLTNh4ckL/gcLgtCZHEh/Y+4K4i\nC21jp6EoozlCgBo6x1xadccOFoPRVIt2WH98Y3Meu4ZB4sqkbWO7HCQ9lsCA\n93V8\r\n=tMWi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.5": {"name": "@testing-library/dom", "version": "7.21.5", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "d87312efc5039313f9ea246ed722d808f2ffcbb3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.5.tgz", "fileCount": 57, "integrity": "sha512-zZqC5T/9Upjs0/3hyrYNpGxw75dr/bLLD27pUdb3WWJ50JHwutvnQ1FJNHbVth9f2hLzEnh7hBdZ9pD++8pJ8g==", "signatures": [{"sig": "MEUCIHf600AtrFKgLcQBXQ+xDiJP5drccBds9JW2b4zQTl/IAiEA1cnBo7rSA/6yFAlaJissHrkCg+MbiyP2SHid0L17A+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2740024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHI83CRA9TVsSAnZWagAALVIQAI9tS1wtoXOf7syP4/Yg\nFyg3dDMwAKWqrHGOeG/3fh6boITfLzBHbeY2bMB3WnIObZ/9s3WTNAEt2HJp\n2jEFCelhEgQN+wQVR/ToeomGoIOg4iOphy8H69zUUUgqMPZZWwPyLqVvLc/5\nLYuJQhFEdXmMAG2pCxJR/rYb06ybUFYMhSjkdvwhxoVxiEHgRfndg51Abt4s\nPIc5cOLk8gqzR5KmFCz6+fusZSz8E21DKdGXaX6wrcX2oQyjEl3uHlX1nbUc\nc+UHdG10T5aFKzjxKobxzH0QC0ltGWlTmV+J8s79FjqVJfyMNKVQafJqom/k\nsvjR6ICp5zvSIZ8M/wDN2OMllfzON+4QdMU/9N6NIYTO6ebQuL4b/musFN7+\nOyfmHFcqYRmfkVDT7tiuzeM8feQYKsL0MmZ4+NOo3s13tzGtHvAvMG/qzK3k\nNagGFOyFoMtjCZyEwd3NeXH8J0mTAvqa+zyhQFYvH1v2q228brd+MQHKga+R\niH6EGvgBd4Njn49nS84rblf9arDcN6YLluqXckC0fzeAMbQF2RDsnXuvtkLc\n2i16wjwEsalG9RBrqdLJdgbLS1hhz+arXHzFc978dKofIOE/K0BDxexIPtHc\nlTrnupXkjYO4WR1JeXATzHsiA7jvXbTi/dUYit3UBwVFUttFhkHCe7WCuvKG\nE7Mk\r\n=oCHj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.6": {"name": "@testing-library/dom", "version": "7.21.6", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "e69349184e558472fe3f54eb67e6960d080925c3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.6.tgz", "fileCount": 57, "integrity": "sha512-Xj6AMM9Xb24NSeann91fmXFuyJEzZ1lpVgiaPiy/KY2pNX9y4OeDrEm2ZYZt6mfZgFSGZW0eEWpF7YfcvRq5LQ==", "signatures": [{"sig": "MEUCIGn6HV2IIvb9E2rBBmm1igPDh5H9ifif3hRV8UtVCncjAiEAsCBubV3ur5FaDmkrDrvj2w9Dq+zuEOuPsy9PkML2iFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIXBjCRA9TVsSAnZWagAAfK4QAJNhSaKORPH2teh6UVPz\nBd6rT6foVrnCP/jTe02gfNe8Q9LT8q7xPbEqX3VEG6z3ZJOLmkXJy/rY/o9b\nFPRLz/kq3DCvPVdyKRcT9bBFuJiTKUm2Jl5OR4QrXOqrx0qw34+6jd07nHip\nlrMe3gJgP1NOUKAzAY883HkZqW1/W8FdwRQJuvBjmZnJa8JqVuMbSpsM9eeg\nahhK5S3dZpE0LHn1qALoaDWLmMZrXnwTo6jKPZz3lERfNW83EjyFnMUzMx4V\nTysFLYj3cb1yZYzBPwbxSO0+vHRqI8jsionKxIAjuV50bSaBPJZt3BG3bk25\nB4P95uIUyCzVCZVr0ZZMDfcaq7aUCEnGgb7Y74Vpk4umQp5tgqvPRDPZZDNU\nAI6CSkmm3hILv4vrqJJ+y0hn0f1W/GTwmyyYLWV/RiV8B0YTIT/FWyv+q4Ft\nHkRaFiApZCZxO4k9dRn4wMR7EjNMRM4O6i5YXfFJ5BD9G3lCSArYGpwu69k5\nGn/qNnXIyiULBRGqbdMGv3lArAoChsXM8Tansp9tGNDRPeymD6NuaRPoAkB5\njGg2J1RofN6jewuVoNuVXfnDhArVWlY0li51B5XCO21+oDN1bTDRJO9scFB8\nPyxxmpndOdZphQRRbrcDwkrM02HHksVtk+EufOixSgxHJxYABbBWpl9+3coz\n6tiE\r\n=GBqW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.7": {"name": "@testing-library/dom", "version": "7.21.7", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "23c57c361db5e961afa3e6f3f15bd57fbda01187", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.7.tgz", "fileCount": 57, "integrity": "sha512-GVNrLAt0yq7Squz1HrW8IiDVKP5jeWSv9cpgQJsfmXYXLFPpaFoRxn+H/NcUitVXyb0J62PkpVWjMe5b0fvYrQ==", "signatures": [{"sig": "MEYCIQC+2oenezm0B7UHKqZ/eMYhKZflmo5CSyt875T+TdNp5QIhALR7j/a9sVfKsEloVGPzS4LSJt2O2vpnumzXWMwHtCdt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2748263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfI1W4CRA9TVsSAnZWagAAr74P/ijRTTXiHrA9cTbWsA5i\nrUmunGCp9eD0kMqFIhQDyyUKoaEUdImhuhqLNEjoEw4mlcDaFsPIvadONyne\nDtOAaevsK/vWcqz1YSb37efZmrp1AvpvbjCgp0H02zi7SZhbc6mwGsqiU021\nK8n6F8F1nanAZ3OTBIEmjjTtLyzxa9lb9FkdprA+Z1alzwSi57jdr9yqG/Z3\nSIp+7Bmso4tRf+v44BzOj+42p3Rqs4CJTjQcuLw8aPtrY6FOnX5echmJ/mrf\nLKvjYpFmxc9sjdh6tNzIRkG2MrjOAHRCoP63nKmd495GWnZHeIifa51RLB7U\nQgV1p/TlsdyYWJnZQIln7wlGVjIq8C2Sc3sEY0TlAkILIHJydIDA5UpZFFhQ\nNJW/wr9T69sW4kWRw2nTaUfuPvpfAJcH+6EcZiduic2I5L2Ctv2WQ8BWbvlC\nrQz69AY7Pq3g2M4m2En5q90Pgota+uc0VwhjrO0KddVe5L9Ra0GCwPCggvfE\nl26Fst5tY8owYF/zRDIuxfWnJOAfunnqdDEtYfdhWHJl3c7zcT8FhvcQC88I\nfwLaLPjqBnqwDh5jHCz9fDXgMAX7wC6Z0iWR0ShAEYL+8WbeqvaQjUP9y1OF\nzcjOGfxnMKEQWEDF0DobbWXsDhHh2vTnHMNGMv+VjPd4m24NI/TSpFxX47w/\nC7Ji\r\n=ToXV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.21.8": {"name": "@testing-library/dom", "version": "7.21.8", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b64b266264bff9135eba3b5c6d4ddc995a3371e6", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.21.8.tgz", "fileCount": 57, "integrity": "sha512-iK1rJubFoeD5gxCryokwh09tnJa1Y4doNDbNFYYqOqz6ELwB1+kEAwlezA5xwMi8QrK7xg+1/aBMzb9X/A/EmA==", "signatures": [{"sig": "MEUCIGr5AsGs++6hgi/QKqsCBmlO35JKHJxiS89IpuKxZRyyAiEA3O5Xcb8SfvBl/T04quYtWIIV4779nIwcVDfBh2yFWAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2753178, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKjjGCRA9TVsSAnZWagAAHBkP+QHwtkWubHrNjgRBvhTm\n9c+ZNwM7YsIpsjh6k1+RV2frs3tm/AYnAkRoEr6M38vTplX96rVgwAF7mP3T\nSrF9QB0bIdHWHy6beiDpmtsMnX7luk4vcz7U19xYC5xczMrhbQN3SZb7x6hU\ntN9caFmH4pPAyQyhPbAKsN7fJLuTx9hP422j5wVGVhignlfE+rRWY51P7KO9\nbvU5jfGjXBa3gwbVwP+cozt7g47UoednYoCqBILUa5MuxGBsPOLhz/TPV1nO\n6No15sJReIcI5D1/uhJvtHsx2s1TpXmlWm3uELxg1Gj6n3Mnkh1EvP4e1w7x\nc1tQ0JsJk8SeQiikytedj8TAf5/JNCpeGpEKjQSWd0q6J37Wxuj28nRAEkn0\naETtcahLoplzwmYZw0cHhlIQkUzURRmsP4pNiICDk8fUuadao3vRBzrNfv9P\nCtw3Xu0dZ80sxT8JBgTjXPoyQfYtjg29yuuTXgFkd7XHl/WKCo7rVM17cXgv\npFJGrFhfdhbIC+rkCkzPyG4lwMgVnvyOsI2I89i9GDW4kvuzJMPlCMbT4/Ec\n6Tk7Y9Iu4uOxVkTxWMe6unlqR1GsM251xBYU4EqZtPamA3/KdHrVzrEovCLn\nRxWXOmZzJyunFjKFb564XyPGmyNWo2EPXXeN4vYZgbT4SMppi7zNEefkTcPw\nDsQA\r\n=vVGe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.22.0": {"name": "@testing-library/dom", "version": "7.22.0", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.4.6"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "07cc67f4670c2ce564b8d7f0e1826d7caf0645e3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.22.0.tgz", "fileCount": 57, "integrity": "sha512-soXVCM/F2WxMidqGlZsSvTkmLsmi72q5N2IrB7o1aTFpMCfEL+Kl0kzv+2Lk/dLxny/c7CWUDa+yjve5VEsjMg==", "signatures": [{"sig": "MEUCIBe1zJrGEDemLIqE8pbGAiDtKbqOqvJRsNNLRdCim5btAiEAiPgQXBaysbFS2rgudCM3ICERkUXmiJEuSEqcPaOcSqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2758991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLBBFCRA9TVsSAnZWagAABOkP/R5xOVygUbRv2WVXelIa\nK+R3hKWxrq1RfbF/l6YZZeXwfiNbPEfzsP2hG0fz7C8V+5M1q4XKS3tYS0mT\nFuZu2F5Duyv9kS+kS90gJReWRnZ8BmYpIBOHtn9Er7k2L2WVwKWO7qsCEXXv\nL5pXfaazNEsP5W3XJw+FydNBt0jd0oKROBYfaDqbNY7AuXxaa9lvvaECkO1S\nnL2kVWmAv8XwQDvMpyQS8078LXzM/PYlaF+5LVfLIhe9omj/XJAit9Ts7MB3\nZjJGNezStkadLSSbOiweWMuIopw+R8hYQe6RuWTdKrIL4eIqAQu1WlXP2x6m\nFtvPFFa26NiMvpDDbSYwn8QCesciTl2U7u6dlfkVpfaanbTAAdzRddIl4ex3\nv0wyzZYD2eSXtg6VszLOhE+0Euh6dXLC2w64Bq05lB9tRX0mySlUiP7MM/L0\nahhVyQgpmov/qIsm8wdj4kmnpk6YUM4nVT4tZCAn8J+aEGNBWrFuQAdk8jCA\nIK/J6ZTI7uWgGd1sXWYmOef2M+I76/gP7LCXFtz0fNVhQCSuX2xQJFqNAWmj\nQOaooYwSiYDI5Rz88Ny9jb7J9hvkwfiaRwKGxPtUhTeJWBQfiRTBYLshhj3g\nMhO6IREBioq3Bb/uLXLg+ADH34EsS0mET53ZYvv5eUSmPK+ZSBom5aOSPGxo\n1hhJ\r\n=kU9M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.22.1": {"name": "@testing-library/dom", "version": "7.22.1", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.0"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b66861fb7751287bda63a55f5c72ca808c63043c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.22.1.tgz", "fileCount": 57, "integrity": "sha512-bEszhvj9LcspaRz56mqGV7uc+vJTAYKCKPJcGb5X6U1qBysgTAgCexQXvKZ3BBjWu5S/TANP2NniOVsMWqKXcw==", "signatures": [{"sig": "MEQCIFSs36yOh18Kqrg7i20cxiJNBzG07Oy8G1yYt/1eAy4VAiBwsHgmn/XOUyyuo0JW5qhz6Zh35N4QH5eqphWo+DAvQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2777070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMcS3CRA9TVsSAnZWagAAbjAQAJFgIDm8gqmnP4LM74Qa\nlxvsER1ErAn0T/IKzYiKDyXGq/SzajMlKqOoEPreEzEc4UnJhQ7puvIGW/EF\n6HjfMVvg1WO1DhnRKVTBJxGVuVM+trARhlvrgu3uQrB4sPUHZYix/YEoqWCr\nGQbeAeWXID2x5JZqKEzyQgqQb+IIF2EqdkAPA0rUgRxJnXgUPT5ZvDfmyzci\nXZiVm8EqSTvYJGPjp1TuDxPLoXTJG/hcjV6cz6RnIORnUkNYc7+vhyET0Stw\n8u2SAQlrCRMzAYzpqESRdFTs6gXSoy6ZAlO7Fyt2y69TmpyxAQmh61iMHTpe\nTYIx3FG3VQfN/HLdo5wVHjoBLd1rwDP/ChaI6jKcjnyFoCVRT2+SUikozlda\n60dRXsfgowl5zcmDE5GCtGJ0D/+8Mk+4VDmnh2VoJMuhlqQfnlylWE4UxMT/\nFpGuWaIQ7J6T8YJ5qKCwDQ7UtYPBY4/pIVQndsS08YQZjf4cMoHrhq5Btq/U\nBWPz8d1YN/+RcLvFxsKTmCbG8Sh+0O183AXwFm8RLOEJ3/cwDFulQtHMo8u8\nU4k4B09WraaO0mYGH9ucD70R+gk/bmmtd/YCipQa1GE2Qt7UGzJ0gz4eHkUm\nXA8HnODJC6E9wGhlqOtHj+/NoMB7uaYaMhXTzlQoOaOJSi/MZqEibvfEUO0b\noZ5D\r\n=vbKV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.22.2": {"name": "@testing-library/dom", "version": "7.22.2", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.0"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "6deaa828500993cc94bdd62875c251b5b5b70d69", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.22.2.tgz", "fileCount": 57, "integrity": "sha512-taxURh+4Lwr//uC1Eghat95aMnTlI4G4ETosnZK0wliwHWdutLDVKIvHXAOYdXGdzrBAy1wNhSGmNBbZ72ml4g==", "signatures": [{"sig": "MEUCIQCkBT1HBCdCHedp5xkBX2T/yu/5yUItYd081FpLCQE+SgIgE4wM+qm0VI2/6lIZDhsg8IDhBTzSiWvynqARpX8u7SA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2779486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNUWmCRA9TVsSAnZWagAAxNIQAJxjwfcjiGdgHVAkCNwV\n2f3H9lDjV+1C8f2sxmgCpA87y+Wt75r4iQJSygdcN14GwxIrEN+nAu71MCZE\nXWM8pICfHfs3tOKuWaqLAQpLGiLbGoPaGMwiuRElQwiJ8Z0Z6KmyfoesivIJ\ngfgmcR/MLoCmf76rkv3Yoztf7mOLEXRUsNqeqURHeJYo545HY6GJhf0WYgTy\nG8FfETmv710jgfwqbq0Ryf+Qd5UhVOosONnCtpGS6esM43k3JMelfLrMW0EQ\nEWsB/E2nuOE+iGP0HoHMdZSrNnIoyg6jd1ZqO481Zd2GnybOrAWcAW7KRvUV\nGWijTe0QTdjRFS3EU0ua5/hQEZExCaHbbwNGUb3FyK5HKCBcWd5TtbWdzqDL\ntKcbzVORVhH+PvPkpsKz3scoFRv98njEhuyzr/9MO+IE/WSS5cVEC87VidAu\ngerFU6c6taULYhTXFTQYPjetDUl09FwYwofaPGUzaxLA5updG6VdNmOcaWfW\n2aSmZhAKkZ41NFuovbCKi6QOB5+G3apWfScuv3RvjcWzCb12f898be1ub6OP\nRY3+N5qefcc7Pxx+ItwK/Tzfaic2N1e9IMVCp9Xizww+ukj+QJ7coUaEi5Iv\nqtCYPQb0yc49KJqot56eEG4MS+vCtmOpEHG8C+q+OS9bXm3rrrzLrNVB+1Y1\nRIAy\r\n=N+Qm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.22.3": {"name": "@testing-library/dom", "version": "7.22.3", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^25.5.0", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "12c0b1b97115e7731da6a86b4574eae401cb9ac5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.22.3.tgz", "fileCount": 57, "integrity": "sha512-IK6/eL1Xza/0goDKrwnBvlM06L+5eL9b1o+hUhX7HslfUvMETh0TYgXEr2LVpsVkHiOhRmUbUyml95KV/VlRNw==", "signatures": [{"sig": "MEUCIA36SScw8Ph9Zm4m5A330n7S/9DW8qUbnhB3QC9lu6GQAiEA/TNsXxwGsCwg+zeh6Svtn5v1MEQTow9I0dLQaGuAVhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2779688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPsEsCRA9TVsSAnZWagAAyBkP/RSp3qzzsY2xpkjak1KB\nhqQlRAZ+9COcDmGtIcc57/1NxkL5cgHaFdcTepwr+EDtMcBBC30PgJGztZPP\ng0TeUDE0OTaTmP00ZrrUL1Youwlhc83vDJMmhMToT1Y+QkZBmavHgde2Ecpv\nR/TkpRM0kn+kiojaHbRO8uCHMlyTPQ3yzGAZ++FFRcyZW7z1HVmI/nACr1yP\nSTKKG8dLXHiKt2jbcbV7xPcXjrRjruX06jTHhRG/ZHLLWqB7L0x/lDib3QAO\n2aDo6S2TIFNv9Zn4kRqT3XHxIhj9J0rNpoaBlOqbvTMdiCikOH5I7c90B7j/\nqTbBITBWgbpsaPmckF9KlnTdvICclaGlC5ncfJKLRTB76Fx9aUT/Mm20+A11\nigX72WhgAib1cwvLYw5Ok+0N8vX119lpwoLyrFtZjwnN/QLg8K8opvxwwHEP\npNZVkc1Ytm1Gq3wIph0wT+KrrJ02w2uUyEyvDLK1U2uPCwlqFBQUNJg5WfPJ\nVw3gh0JiobeDshXj+QsjbH67P8PfbGkU9Cwp9sviN/bge8dpxCZHvpr1qpJp\nca1/zHNb9o7lTHkrnC4BBRWYfsyJfwIQfCzamEI6GKa2MtKzw1aPW3h7Bf/Q\n6k0JGow0juh22vCQFkzYJehEv5wnn8p48sh0Ps9zFd14mzizdYEMIAufd75d\n9YyL\r\n=rBwo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.22.4": {"name": "@testing-library/dom", "version": "7.22.4", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "92a43253507b2436199449d344d3ec4a29df773c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.22.4.tgz", "fileCount": 57, "integrity": "sha512-b+/z7/dIzEckmu7YS29xkl7JbI+n5XAnneQVUv3rXb19oVkxUFoYU6c0CTXuWEznNDU+g+eTYXB5CXMzbr232g==", "signatures": [{"sig": "MEYCIQCejCKTTUZ99F6tL/mbFTZXGxSJVuF2Nnvxv8XAnENBRwIhANpdMKVo3Q5odKhsgM3zVrI2UKvb/kPDlUCU2GUsYQ3Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2782478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQWHZCRA9TVsSAnZWagAAd/UP/3mn/IhUx6NjWRb5C/mU\nzc3+lFwh+qRFrwHReVyIL7Hx/vMCRujF32WM3J0WcQPjzQIt/f/0vTYszyYd\nHk9pmMStW/Y9NA8RL1OjLx9GkRTvxUGtYm1QP/+9s/tPa1V4FvVYvd9B08Ci\nuWF3r9G6rCBuKzd5RSFQoSm2yC1G4wbQ1E3aykYO2wYbX+Y4pGzxBDGXGFSS\n2TZOAhi2B8yfFOG3mpVs2yqsQ1g3nE81uDMKClAy2ktMmk8NcIsU1f2rUtFF\nOcBN5HJNqP2MiA8FwbAAWvf5HIDYmC7/VsPHZSCl4HaFs3GR0Itwh7IlbKlT\nwDt110mGNuhl2BYYPTweWgUYfOsgBfeSzWocog5sM64wqSNSRb9PNUgARBSV\nEdzBF/lGSR8VdJlLv2uWZEYkYz0lDNlkt79TeKOAkWkNa6cf6UB6TnpxV6bP\nd3ZwvTa46fqDpG7P8uYsTTK6awO1jw2y+FRFl1EbwBcijbXCcPIaCaFM2rS/\np7nL0I2F5baKAADlBWpgSEJ3Ol0c71j32b9YGXj87UTtJ/qQxYEc6e6EiTNa\nZmLu4O5DcrGkKe8ysdLzk2hkoLVAwqkRQwRG5pcfwXZV5HghJHIEnBF1ggr1\nrCuEESGBeyNK+fFsY7uDDwLa8pFo6lSVFqwO6qvTYScMO5cNVs/axfK3iWMY\nMZKe\r\n=La2i\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.22.5": {"name": "@testing-library/dom", "version": "7.22.5", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "178fb0bfb52540538667f2f72d4b7fb406a49499", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.22.5.tgz", "fileCount": 57, "integrity": "sha512-qALmosaNSny/JqQ+mDhdT0N5u1a76pEcOvfWFDpBQOchtkxDm/w/bCfe0J/K7nHrwJPHelFiAZksaBs//P9fsw==", "signatures": [{"sig": "MEYCIQCwJrNCtbr74gXLNX0XgR7WDSav1/1+m+fpcpIGMjT0KQIhAJZb5ehkXNoe4U2uG+lMqeK2DmTO0oDSXLNXopzkf9yi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2787333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQaRzCRA9TVsSAnZWagAA7t4P/R/cQDVVImdpzZerf6Yk\nH1XesLNiricuKqJVzjvQ1uhNtT3vyDI5GASu/B6T3om+c0E/xTQN7rWvkKXA\n0kkR3hr+GOiGdXddeCE1t9Hzt0JKke2gVlRs9BiYCXETw5OBeeqI9YIiEDBK\npd4vmAK1lPodA8huVm4V6te/OQVRQjKnNvdM2kAAa6my0acNGDvx5abhmtNg\nHFRsnRR7cCwka278TJ5qy65MnN4oKZUXu5aidZ4FClDdGKFMOp4X775dX3PR\naSZPpKAKkUF6Xg/4Cwge7jOYe3FhTpTXlmMI+xjvIjM+xxmQzo8fjGXbevia\n3CjByZIp3/wVSs6U7Efun8jAs1qdfuwxVmTc3MdPdQXPrkOiBTc+M2Ht+sDr\n0L1PqE84gYhrnYftZ92Bb5lyZqtKllTF9Gl9V/eQXbSmhv7bxghpq4RMukXr\nuENdy1M12fdJaVxL/KzzwACmIh3q7VhyTPuLhrkGNmJBuuH8mTs2dOGsRM1/\noeD+Tdn06teLi1cGmSYJJsSagwxo0kQQiehB/PwfcgumMvNmpYfZUEKs1CEz\nFkfQck0k0GlSXlASvVJ8YYZ76DeWO7eGB6TElYX6pDT6zJiY6Kg+JZw/RppV\nNsbPTIEE7C4vshdj/6L5fXZbpRq93ee6TtOYcmjc5mg42hqz/ovjVf/DmGZn\n/xVZ\r\n=/yul\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.22.6": {"name": "@testing-library/dom", "version": "7.22.6", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "a9466c564e9cccb56175ad2ff471aa4ba43ef0b7", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.22.6.tgz", "fileCount": 57, "integrity": "sha512-8703SWfapsEMwG10/c8AV7qK33zyy0NMDs0SbX9uEZnHWnxaNA5SnODuWS1PvFEjoNjBxfoxVZtw6uQAXO7tJw==", "signatures": [{"sig": "MEQCIAxHOCUl6o/LySC2lMDQSpgIEI7rK0zbgDylEUxlqdR5AiAjcJGmqazLTe6CBnO1UkG5czpPFIILFOngu/cbd5lz7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2789586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQ/94CRA9TVsSAnZWagAAAoUQAI6b+Nm8nHMicPcxwGSh\n2KiopgYnWazq4hDY4WPVE22JBN1UqNqxFeeB0gZ/hCJkJsxkY7wAPnncGeOI\nF5z3rivjezaMkP7z0Ta7gHyR1/+MPWb1TfgkKdwF1WYUBbR6uDVD8N4I7yqI\n+zLh/Eps2A0pBZlW+GqQqfjb02ohteYWxkV6No/sdlswLrAPUUrR4K3UJavj\nAGNmhqYZotOC3VoFTe2/pLFHDiiPLPle3jDA+lOHQqjI2BbA3UFvLCscK1A2\nuawXY0N0ov8OWezormfIeoFOKIRG/2ZGE0+VUMY4T1fbyO2yZiEWTzJgCPed\nANT46k8yaAa9MgvV6+Y8Fqgy4vwpp1nrTjlBmpOPeMonlO/GQdFZhU9n00jO\nPLJdchOSZY4ig4PnJlKlMCU8rtXtE5Rg2vNjYrvRbHSw9F9ZbnAlMSHZ1xGQ\nAElBdbFqolGqyD1NwZn15otRygunOWpzqFDkJ8wctEP0wlprExBQZyULhn7B\nWaaQqzGdKlnSPsatOAC9ssuL36t9tQf/AWH9ZQelYam1DzGwWVhxqBIp3ijl\nn4ATMp0nZSMvNbMnXxUORi6gB7vK61nkKqp3bAx6SnR4mNF9NlDwAVA931NJ\nT/JPrn1UdTHqKXGHseSmyUr/0ZRxdNBGV08S8sYChXrgbZqAUOvnscecpnit\nXCiB\r\n=QIgM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.23.0": {"name": "@testing-library/dom", "version": "7.23.0", "dependencies": {"aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c54c0fa53705ad867bcefb52fc0c96487fbc10f6", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.23.0.tgz", "fileCount": 57, "integrity": "sha512-H5m090auYH+obdZmsaYLrSWC5OauWD2CvNbz88KBxQJoXgkJzbU0DpAG8BS7Evj5WqCC3nAAKrLS6vw0ljUYLg==", "signatures": [{"sig": "MEYCIQCin8VH87YkqC53++WtH+uOoWj3h9CrrNFP7ECegAKS4AIhAPA2Eimk3OeJUv7DnqmePh0fAxZggmU0gmxm2ysPZQg5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2797616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSAFyCRA9TVsSAnZWagAAQ8QP/0SwBuFITYKidHLf00xL\nBmWRgvsZVJF7jJZ5kvD/AiFwIUXLso8eP6tpCdTdgrdhdVSbD3dKhfMqBgID\n/axsarHjApX8K54MAbQleYrhffaThvrZnw4FojDcjGNcBPt2id73Xpr8GODb\nEDugShb6db81ECSFv0BPU751l9og7WszFhR0w0gqp3L8ULf3s35uGo6EVlzq\n3eBtrDmcibE3tD317akYdiUR7iZ/mKWSi/hGDcPfNnhMFTUcgm4V2C40fzxZ\nmkbIbmdjaletMUK5fVChohFlYZFDzHULlvk29aLqnub5hAUv9PYNKBwW40XO\n8F3fPgD59AeEaYLM75GVnXgpMNVD56A90JD7UKrYNKIYj6zFR+kBEP81MZCM\nMzMgJklKxoBtutSXcLBSQrTLdxJnrt59oHmQY8q9QaOhFIZVlYNpega6tWRP\n5mgZuQ5o2jnCz2z3honohLROWF88xh2u8TU0RmzYScwD30HTGgckf4XmSPOx\naxAX34QLNXQuC9ZQH/QqnQqAS1adl6PMCMisbxszvySEKZSy4a9yQFL2uTRq\n5KuOkmnxMcS+ucKMijPARCV3zEp1ZJgbV69U3fXviiJKBxvMCe9mCrc7Lotz\nKUeRZyM/ZfLAhshiFoxqB8hw2RnRuswl9LiM4snMNSdg7wFiKvNRtRueXQaZ\nQPu8\r\n=6Pwq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.24.0": {"name": "@testing-library/dom", "version": "7.24.0", "dependencies": {"chalk": "^4.1.0", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b50066100947e4d1bd0f2f6855c7ff3f2a8770e2", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.24.0.tgz", "fileCount": 58, "integrity": "sha512-Q83bQctoBse3NbHrUHlLypSp+cIchmpLCtVTG2rgVL2RhzgNOjlYgqH/gmwg3ztHVPADSD3PwtoXRhgU6dfTxQ==", "signatures": [{"sig": "MEUCIAWiWF4oSOpUdp0l2XGNEyYXKZapq0LDEe4x11hk8oAcAiEAluYTpI4P04fgKAGPCyEm7uMuKAwiThRjFYowzFwK4Dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2813764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfT9lmCRA9TVsSAnZWagAABREP/0+jzuMhZ8GA2BUmhqHW\naApWjiXv+7kRpRiUsgzgfR6hykrLNEWroUlHkmrW6jfbfQd4dlUxgTxbuSpV\ncHDhM58ulvPlNwy7w5MLOW1hswZClc33aGC47M1xA/+UN/q9qxW3uCPTtynk\nHtEVWkxvV18tSvP3GUqqzxb7r5BuXeCIwlINmxYkoKAMylDy9duvoi0mvyFq\nUvI3DUO15shT2E4mvhT3FqeVKdbXDVSv4bGqjxvXZMD271EpgdH+SxnVBCth\n/LZAlhfvMtD7i85iBVSq5iZYzKxTbaaKb2BpoUer9rScI4QkbTdkGQ1MCian\n6+yeiQ0VpzOc9SFSJdImiiRLy/MdPObNf+u9XaoltFFiUqXkj3eHTULzTtDF\nAy5T8/yOlM2prTfdgByMhscKUzNh5zreYPqssscLlWvzJfpu9AvHVCerAz7w\ndvHp/ddM9qbdWkkDmux5eMglUFYCMoyBqSobjH7SqPDHRLiOaZkGiP6cQvPI\ndU7NQwu8b4MfanjNXtbPZprfFs7fRdFsa2c8lwDq+1j1Wd2KVHAc2hecr1j4\nKtjzhExkSloCyixjb9phcuXsyWqIY9B7DWgBLichYYVBSKsT6ykuPYZOiC2u\n3GZq/GibV4usLG0i7WfXVTczFX2OSf/cNHe5Tg682KzeE6LGpFz+Dw3ZdxP/\n1gq0\r\n=4wq7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.24.1": {"name": "@testing-library/dom", "version": "7.24.1", "dependencies": {"chalk": "^4.1.0", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "0e8acd042070f2c1b183fbfe5c0d38b3194ad3c0", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.24.1.tgz", "fileCount": 58, "integrity": "sha512-TemHWY59gvzcScGiE5eooZpzYk9GaED0TuuK4WefbIc/DQg0L5wOpnj7MIEeAGF3B7Ekf1kvmVnQ97vwz4Lmhg==", "signatures": [{"sig": "MEYCIQDhKEgJvy5TJQPcZs5PIK1FgpZqzNtiZ0Q+IQWfzJ9UHQIhANXu3MxrLsF3CX0Wu1jVokTIVaAPuQfObgu0WlKqNXUK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2814197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfUKI/CRA9TVsSAnZWagAAh2sP/iKKoN9jBqlwX9+n9WdF\nrmfHOJyRJSOrRgivv6eBShc7+EHnmVi2kRnf3t31bTHEPACILhRKR4Y0QX5b\nX4AddZWummEEEbw069MYIQZRtZsd3KVHK+KAVl1kH4K0S/9bib3m3/HHyMyn\nI4B7CykNn3Fccpy2MwGdI8IK1IjnrH2apuWu9gtD/VIF+o5Vqli79qFBp8VI\nM/MbC+f501mAS5Rw6vulvtJGxsfWeFkwoVK6t2Z8yrOacZ3wUjniLYpy+4td\nYBZkWlTHKy56Qjv26+pYAbMpQG3CADFa1P5iIGOKA62aBB+9WUgl4KLgA4vK\nOFUWmmP2W8caY+dztcslnVE/t5KcPNepUin9TCyGfpYu6zG8V0F08P1cELat\n2RWYg0eEcoNg3ZRcMIF5MWjyk9oA7p70O6hGC9oVR5rG/OZTajSQN0jUTxgB\n9A7VgZ4Ju00em1lBT41ykppmm8Y3uqo5n1azjoTo8hrsMtz5FmfQqIMTbzgV\nzwWkBt8gQyVZ2GwRicK/qeXyj3AGCqeKA9BVFAPsDMZp52F0ELEY5FrNk/HP\nq1GO/cS4Flqe1AuFDLZj5CkPqAhcn2oVFujkZ3Z0m0ucHFaWHdavf38rU/Ht\n4o3T1btqkZlFeQbIA3/IZ5IZBakkuaeYd0YoGoQ8ZioXH4nl6uiwhSHERQGY\nE16L\r\n=Kvd/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.24.2": {"name": "@testing-library/dom", "version": "7.24.2", "dependencies": {"chalk": "^4.1.0", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "6d2b7dd21efbd5358b98c2777fc47c252f3ae55e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.24.2.tgz", "fileCount": 58, "integrity": "sha512-ERxcZSoHx0EcN4HfshySEWmEf5Kkmgi+J7O79yCJ3xggzVlBJ2w/QjJUC+EBkJJ2OeSw48i3IoePN4w8JlVUIA==", "signatures": [{"sig": "MEUCIEmGfYIXIdiBghb90iXUOxrN09DVNtoruCvNywZzIW9AAiEAyFY1f5Wl692/nRFc9+epF2uC247up6vquCQwVWkgzFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2814507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXlQ/CRA9TVsSAnZWagAAjywP/1IpK2ppxoG9YczSOjTD\nf5Ntl2LJANdH+R76/aHXO6IWGxefkE16+gBvT6q5WxZNJYzdMAb7NYqLPXvA\n0bxQQsTN2Crb+ZklgSsQSTUnQH82T9vzC0Mzs4tv0mtSCcMFtconb6nTZxN6\nqiAiC5u5+up44UG63NKxKE08z/5tv1PnU1ukD5Uft1BYgeFBsumcOsqBtJJo\nba+ajDGrHxr6ZYrv4VPoG6yEGO3CWc3IkBEv24cvmJf3lESiSWtVXHJKUhaD\nEThHZsWrBuq0uExtdjEzjcTSf9OnASPuybUeK37xtRt49iaDA+bm64+xqGoR\nw5k5XZE0JTQQ41a+lvBMLNQxsMG521pqOmmPYcaQzqEgudV97ZXFLEru+7OA\n4YovrUYRqFK+vIPbJY+9CbXEGWB9OJbHgM/hYZor3AOPMQlIU1tY5BObCYXx\nWkK5AAWhI18+tu+zE/zRDCP9pm2QAIrMTnDkY/kFmVKCTre3R6dBxdUkzas2\n29C7x6ZjpU+9NTmPbjFrCVCoRpEXuEKD3+tRVgs8N/igc3NCXTOcNon7hSkv\nitTsuMVsWVsAnbZfXRjEgCLmmWmhFx99X9cEPtbCFEfmqDvypOremwPENokr\nt/2YqfEYky4O9l5/9FoLaACk3SYOdJcItWy4+SLNPhjJoaZheMzq+9nKAJGK\nzfrt\r\n=IUnJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.24.3": {"name": "@testing-library/dom", "version": "7.24.3", "dependencies": {"chalk": "^4.1.0", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "dae3071463cf28dc7755b43d9cf2202e34cbb85d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.24.3.tgz", "fileCount": 58, "integrity": "sha512-6eW9fUhEbR423FZvoHRwbWm9RUUByLWGayYFNVvqTnQLYvsNpBS4uEuKH9aqr3trhxFwGVneJUonehL3B1sHJw==", "signatures": [{"sig": "MEUCIE6/J+qwmZFi83aHjkEkbCkUN00IXXjQpnGiVxfwFrp9AiEAq/4jqXipDb/3HubV5xJUdlhG/xHBxYDAFNw12+2pQC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2815184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbbCICRA9TVsSAnZWagAALFcQAIFirY3u3bq1Ziji2Ck0\nAygYAUS5tWU2sWKaE1wk2nEUHlry9XSi9iH4MvPXGMm8w2zM5Z1zZaG5H2vh\nJQLDHRc9fbt5YkhGrUVh/N7heAitjq+CG9A5J2cbEtO6XIWqUGACehAiH0YT\nypev2DpeDW87TPbxZmUlRmSQoTS/617enZGVmG7cieFgbquHpLgTP38apE9A\nOlibnGji1iayUqusw8ZNvjMX1yQHLoyyGS7gm+zQXXQsed6pxuR7DWMFjYtt\ngPG5hlIoS9KNMznmWfgNJEbRlfEz7WExFObu5mz5455/xYCYIuxlgbAx2xS/\ncjLviYiAazbABvxFrh11rDyjC72dSjqDLoufDYfLR+oy6qZj4op1BV7aLxes\nUb0ZUyYyM3Tx0WEM2TRp9Iwpflp9VBm+66kCMmORddIz/pN9fG5Zqh7jIOBF\n2y45+5B7ngaJUe6920PFqGs9IRx1X2OUkFKvOdtzheZV0FwMWwN9TuYmznT2\nDMIgWgKI1nDHxRE5eIiNC3xt/pUZT88Xut/owCGu6Q2kY9HUyMcC+N3CL/Nf\nM5fFzNokU9kk0hT4Y8etC7ISywbtZgsRjxUFjlP+/NZnVzv4Hq750nKZtm7+\n612FT/rN0y02hsBUgMIsbnFz/nF53yzdH5MpbDT6ZmdTM+KOHJa9vOKUN5tj\nkr1x\r\n=HrW9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.24.4": {"name": "@testing-library/dom", "version": "7.24.4", "dependencies": {"chalk": "^4.1.0", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "95d82a42c8a7631fcff428d19829e0d8b90c4213", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.24.4.tgz", "fileCount": 58, "integrity": "sha512-KXUgJ3LIhuQUxZA6jA9Q7ACPM13ztb8GAy2zm6CNHYjmMuCX8wCM/hJcd+qBJMLQPiVbG9wJp3g3WN5OR7cPTg==", "signatures": [{"sig": "MEUCIQCbSuANXP0+2qa59QCP0nH2pHVsS7RkoAj8ngWbaMF7WwIgFtJElqyLc0XzHfMlv0I2cCP3sAnc3GEXqvSApUlUetw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2815646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfe2TlCRA9TVsSAnZWagAARpYP/jnY1wGpq2tfortu+Gt2\nKKIfrui4grIUK+BfcpeEqHrnU3NOuTBaoAWXPgqPQFHfpm2MIbi0aZw7HyG0\nvErRoZVoZ2eYvw6itRuD7O+9HabhNdtvvD8BBsW2EAmYY/Rt8ctYIw4PRlZV\nMvbljKZEaADP6BThALR8UW/cY+Ign/ffvqzBm7A8rKgS9gCV6+ZlNRAtgzgH\nT8YvXepSXZnNt0rJM3kYGzyMW5RMPBAr0uvW74IXJn0+T0FVzB+/FqH+/IY5\no/frGckEHBD2g/Xv99piAplG7Hfo0uGaIa980E+Sc+U9PbBc0qTH97zBl5RU\nOuyE7nqcTPEft/NlYL79PD//gBoV3kweq1zGmwp2HT/A8Xwa8XbM9ywsCJqD\njQvfk3aFWB1gh6Mq/aYSpbeCA1bD6I3FTblFjefQnRBGpdjIIrx7jy3a2Mj0\nC/5M2/2ln4yIO6bs+f8cXH7C+XhqBZs7Gx6Ww38EFGI0xE2V15QNyLPc7EBh\na1KTYhs6DYybCfltWz6LwMOr/SBmnT78fhBAw2LXLhx3DBwPThPrAChxyLvH\nmNjhwwfILX7gJ+vtt/kDHkorUJ8Q9uXpoKkipa8BnYyfNagtE4++rravfFtd\n2yUAwzMXqwcVq71TTIhpKQETov4W2d9Ind1bJj3TD/Pd7Rk8KUpbaDda1k+X\nKOnp\r\n=ZO65\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.24.5": {"name": "@testing-library/dom", "version": "7.24.5", "dependencies": {"chalk": "^4.1.0", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "862124eec8c37ad184716379f09742476b23815d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.24.5.tgz", "fileCount": 58, "integrity": "sha512-oyOp8R2rhmnkOjgrySb4iYc2q73dzvQUAGddpbmicGJdCf4jkLmf5U9zOyobLMLWXbIHHK4UUHHjDTH8tSPLsA==", "signatures": [{"sig": "MEYCIQCfFOLzkW3/B5QS/JNk+Ya8KNAovmGZWLTIeqmk+mywBwIhALAAEA3E+IGmywnDt0lFZ3Io71xRG5RLo8o6A2Ppuafd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2816237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffMqwCRA9TVsSAnZWagAASEAP/iEJal1bQwQLaWbCOHpi\nMPSIQdo6iTfqR1eqycki0tuXRkcSy/nH4FV52sqfVLcLGxnvGDAhgZB8j3BV\nv+XG12JlwJqRZU6uFTSjmbW8TRrt+HBtmvHfcBNKkVPfsAPY0azhsnEdLvHc\nc/kPSU7SX1FX5PELD53ejvrFYHfl54zrkbgzdGsBXEpCdpLnXvYQdw/Yr2Oq\nXlLrR77IiFbu3fl0XJuLdXKKwZbYVvFDIgLWleYcNZEiIhkLPr4jpZgVQ9uq\nbJBNs0ExzHa3wR8NpVUoa81pquQY+NXnFM2mn36lIRcvSyqv9sLzV5AcvqEp\nBPYtdX/s+VJfZGPT+pTUoOcKb8o5LqZQxU2qmGUVSfk3uWHlIujuxOPhi/dI\n07rcwoTmenBN4eoo3GQxt86PBM86V2cz1SJoILjhGp+MmJrBvsmDBlbcHc5c\nHUbAEoXtIj6KTDSuntz3o/mqe9st8OQ5k3z74MnFqD8/f7CpDmgIhXCaJPEX\nkbeu2kntniAIGSyu1TcgploVLo6DyzHnK6ZZWpdRdkn02/eYuxT7q27Z+a1J\nvdPD4VAeZn3kXB507ASVURSf8w/NJ20S8eL8ldSI2bC42r8ZxhDhAuEXZR6+\nLjX9DrlycXszbhN8lDkZLCClEkzidUxjCbaBKpJhMdtKPurPCL81KuHqtAxx\nefJR\r\n=/BGJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.25.0": {"name": "@testing-library/dom", "version": "7.25.0", "dependencies": {"chalk": "^4.1.0", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "f1cc5478ae1eb4cf93f7eed977776493dc78d940", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.25.0.tgz", "fileCount": 58, "integrity": "sha512-wUp3k/LOQTRJjcebJreWp4CYUUoVo8r86M760Qxe6MY913LKBLEBH4kTGZozqX8zjZH5Tw2GogX/fQGlXYWOWQ==", "signatures": [{"sig": "MEYCIQDlf35jjkmooqq9icC7mqA6rMY2tlUU5IYcxj6x74bh6QIhALZX9krSrneMraBgoZtj2+c5o0yoHO3jKuKRuBUlqNfo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2820839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhNzFCRA9TVsSAnZWagAAMr4P/2bZuTg+IuRvkt8hLyOQ\nJvcDaq1gE3U25lz7UyZTsO3BNiGUKj2eKjfuFkkE7y7GaYG6+cq0QUrRq/tK\n9m7wAYE628oYhj1+G5QtydkusBo/1QsUisfPjd5U2Nes0UKqep3Bs2Q1sLkw\nitbFV7dy5nfiEDE/IkrncYOnWZxCoaOog0A/vtKmxFhyKEcDfs6rjtsuzlQA\nfiMyzGw0Ta1dtKJWbQRPIs+3BAjz002BR8NGVj3eQuCNbtB2pJ8jIOOPHc8U\n2cN0Eo0yUzU31EBnHXAO3Glrv2s6KFLvkUq6quN4gmJbBm30AlzOlohWBl49\nS0qAZ5LJ2pJ/rVcjKNFPGKeIlcXvxDP0CTE9ahoLjQBF11aDLM1/duuLlopA\nn5mC+MgkpTVr0hbpsp6BzkDekV32xjT8FiKiuLcdyYOT5obHQy24T1wOUSxS\nVgb8r8RMCgIzcretEb0kGp1Jh1KOrBhEytDIi5RSa8Xpiic20L+URA+jEbRG\nxG5848WD9GBKIX6ETdiC2fvFeJx8bmJbv0IuTgfhdStipDH/KpSjlOn4+O6u\nHLaXh9Luy9fMDbJjTadGE4VCRRDX4lUk5a7zfysPCZRS63rXFz25pHtJTK4S\nSwdHJHevbHWjH+P9Kc/jQTpL8JMhTP5e7SYgLFYB4WqjIlB8d4S9SSMuqvf3\ngmxe\r\n=gmPv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.26.0": {"name": "@testing-library/dom", "version": "7.26.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "da4d052dc426a4ccc916303369c6e7552126f680", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.26.0.tgz", "fileCount": 58, "integrity": "sha512-fyKFrBbS1IigaE3FV21LyeC7kSGF84lqTlSYdKmGaHuK2eYQ/bXVPM5vAa2wx/AU1iPD6oQHsxy2QQ17q9AMCg==", "signatures": [{"sig": "MEUCIQDpYUadacOXuE80HH7ibu9yjbxgOjBHpi8ZyolzPkP2BQIgCcab7cyJnuto9CKsUp4SqYmhAQXFR3HZ6m3cxi53lEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2911643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhcfWCRA9TVsSAnZWagAAi4IP/Rkl/gUh6l7qvaM4WQEr\nUPDaaJfNSXPGLxhKZxQfFIuNYZGg+v7Zs3FWOH17f4xSU7+DlA4m7GmOtJxX\nVmi6A7MdWF6IrDtgVd+uKyCm7GwZ63mwg/aT4GoURjcAdJovxG/C5LtGy84Y\njIu2DtwrKSXpJgjMNKc1la0Xtx9iljGQEqBX+xmJsYx8ciSorLQbBBfNoah+\nOmYP6mKoqwoilxw/J48CkkBtp+wuKNucJ6bOQ2A2F01FCxu5lCEqKAk8HYKo\n7x0todRfMd6KfJ7xW3xpC4+42EQoOpwvO+WUBk0EjXwS0u3a94BPQ3ZuB9Tq\n4azWtfCcnidVrc6ufZF8c/dn/q8SrDd5UEYUNdNoj7X1/mzM0KqlQCMLvuxN\nZkx8u+1ktp/wtiJIVWt/UX81gIKUM9B2vRvSSyigWZa49lbm8UHX9YXdASG+\nW9Vxa1LJ3VQLzHoZBegzuyPQIt1JWwbNHOuTCvATH0VoEGTUfiGhmcMgOzE0\nqxFzN10tmByA5fvWWNkcYiSvMB+5YGbS8flyXqmtzOgbB7cSzN15S1CHfROn\n84rqOHjg2t8E4h+YWZZl+g46ig9KeIuabkVoDtGle18wkYuOH5xHkiCHC5/M\nZGpEB29IV/AcQyFZIOnqURcjz7pemTh3EgESlfptSX+1MTKzvSiPA/7bPSGK\nxBUA\r\n=h6xn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.26.1": {"name": "@testing-library/dom", "version": "7.26.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "6a0309525428f57903aa7fa881903df4cc1269c9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.26.1.tgz", "fileCount": 58, "integrity": "sha512-WVSkMxk6eJxGJnf7TPyct8d0NsC34FIg7SynbseroNDvMr+uDe923JJEVqoFROTntxLf2Eh6nTktM4pMmju+Lw==", "signatures": [{"sig": "MEUCIH2JmjLK7y9LT6nRELVaMSfYRbViW0IDEefn8h90QdfdAiEArrdQAF2/032BGAchaSkT2dSKsAq1yQEDDZl43fBecVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2909420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjgQBCRA9TVsSAnZWagAA3NUP/3ifZtPeF99IvL9F558M\nzetr42Y/24f2S3PbL3r4k6u3n6E8+NPf60balRBAw2Pd2MHQ5wV6BdyEP5AO\n/ezKUb0uHinDmGxJP/5iXHk8Ax6i0McXFE3pEiMh18ghd5vLKygaeDNwNhrb\nGyaWzS5uQnQibvMr+ezGIRhawm74S2ypU7JU2ahuAgGTbWABRtymsqbYTaTa\nAU6Fg1Zv4amDxNbk4UYM5E/UkP/TJbUzONyUM6hiT+cCPA3lJyXQLfXWUtIk\nySYsYXhbpZxj+cKaGt3/QfCFGJeHhFviRjc7GlDQzoHYg9X5R87CfsfcRkFW\n44jAtLQuibokD/NsROd6s87bzzd8Nrbu9zFJWz2vvkI0F3sRDKaajXoFI3YF\nDBtaLB8v16iVpySyF2FLqNAJeJ0Vjr0x3R6OouKHC0Q0LK3QMGQvLPR/q1ul\n1BHTlB/c/+a9DEzEDxWhWtWWD3Gi6BlO/FBUBcRMoqyNynxMcd+ZeV0yo6lx\nBuYxASi3dNGGxyKt86PZsUM18zqZoaYpHBclTJhACXZhFZE3+gtDARbi9Il9\nlUAq8ZfPFKUKv05ifrOFXmJ9jAr71SpPDQNwFvt3qOOc214s3hqTBfVHKtDV\nEXuBniznjcDIaGQSmd5bgmCMBFM1XxCGRjFbVnHfX8yQ0mTSIgBbbzMum0lx\n8pV0\r\n=bQBa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.26.2": {"name": "@testing-library/dom", "version": "7.26.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b0d95accb10c35734a8809b794a6ddcd94cc10ed", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.26.2.tgz", "fileCount": 58, "integrity": "sha512-pQI9j4VipZlhotwGH0jCIIkBO4HgJsAlH1Qj+/Hm3XWXNG1WLmov94BlTuSGEnu4A7s4TzdDPwu2+jaiWBCpBg==", "signatures": [{"sig": "MEUCIQDSWGyM3m8o00wddCFO3NBIdct6UDvrVkg5Jp7naU/BEwIgNSaIMWSfMNAAMHqJ0iVSSKWNtgwdQwSRCurWff43zEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2909917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjmmHCRA9TVsSAnZWagAAwe0P/1GhiRI3qldBYlyWkxaB\nX16M1PuiMmcrzv4D70d0cO4BiY9LyndkBCgNmOoHApFMJIuP8qlphrVgAgkz\nu3XJorf0lKHklXj2KLt4XzB+cxELSfwrN7l4CNEeHk38Ev8iM2bFWl6ELmMy\nhfcdcDSp7xnzVwmAew9+O9QLjK0D1blfEGxgkCPR6K+p/thZ42pEGfuK5vIN\ngmMf58WqK3e1ABbNuiF0wtBkjVQFLeXBAgW/ynXJDxwxMbMBEjcmcAUh2MW/\nj+vnQCtPiFurszQMLo17no5QBB64HA/efqLhKkr9VSRoIUAzsItDGBuM4XTx\nKfK82shheLEnl/WmkzboRuUL3E3kupXoS5k1l+Z5uSxOYZ2byngkWr4OeD0w\nJWh6+xThp+IvGFd2AZjCnZUy2beb4TPR2+9UI4nLB8gyJaVJnBwmRGRKkzk2\nRJZ5Lmp67/+NVCw/a7eAsPHr5XUCTV6kqWFPXh9lHLF/gC1vbJf5dqbpjVU1\n/yzmLh9rjxYFAqCgpUtiNTH+EcVrMbW9iu2pOunKplNrs4dmhRXKOgcNE5vv\nnr3oh/rFSGsIo4T+Hi4Y6vI5H5qNdx5bExAUtKkZpXjdCHwD2N+35ZkralFQ\nnGL4w1TfAnbUkZahkq2H9wB+mlAqxXxV86CqW/EXnd6Lrxs1qMnvh5xugkPK\nLpcz\r\n=Z6bL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.26.3": {"name": "@testing-library/dom", "version": "7.26.3", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "5554ee985f712d621bd676104b879f85d9a7a0ef", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.26.3.tgz", "fileCount": 58, "integrity": "sha512-/1P6taENE/H12TofJaS3L1J28HnXx8ZFhc338+XPR5y1E3g5ttOgu86DsGnV9/n2iPrfJQVUZ8eiGYZGSxculw==", "signatures": [{"sig": "MEUCIQDG1uipSj/MmJ2WqdXZUfX2UoQZWCE+KpJ+IfLOCB3AjgIgdYEXX1FTSNS0wglNodWYrMj0qRxr/Se5pUIykcPY2Co=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2909563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjrfhCRA9TVsSAnZWagAAEqYP+weA1o29MDJY39zMU1Pm\nJltZcn8nMjX9HQQ+mJVefV4l3pFd4OMTX5A+Yyn+uR5fsNtrrzZoOGFaJJmL\nN1atpOaBUf3H+I0ZDmdYM0iwNriCRxFQ+7EjIul+3WPd3ZDznmttJEmSF21x\nlCM4fBQLQTH0fg+7eHgbDudjFy59G4xUw+Pr6jnbRqWmHVaVB9F1P5mtbY9/\nnSHx1dl/9G1zQ59fH9dVZt3X6yUrvDqHKE4RAd9t3xEEc7rH6aZnKKka4fSz\nV08J3XZIjG4hYcM+n5YWJaoDJI8w2mshmcFg7NGUrTNP5ApIWzhKgWvTO/n7\nPN5E1PRAnQsbuNzHnUz1OXSaGGmpEoJlP1fnCoCroUuzV3Qn9RCh39YcNKyh\nlPOVQADSqmaVz7ybEh586s7+eKvCeZUKjK7DZwHkBj363tdxGc2GTt0SjdpH\nVXDavoQpMkpGuPVw6+EGBuSqoMJd9y+I8RdeKhn9rC5ACNxnZ73GpvPgYpvP\nmeUtx4ACRxsTvLrgVEnb8krEytp7jtsQIe6M91sotPTXOOUYaOCDKHj4HP+9\nrASPOej7WQmDNhdHh351eU50QY40n+UCseTHbP0h8HW1uFYTVsRNJ41OOHGc\nkrypFc3NDfl7eF3ekzqwbsPdh1e8hVc5tlaMpCksYagf3NpflRatohYfZs2X\nSAhS\r\n=h2x9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.26.4": {"name": "@testing-library/dom", "version": "7.26.4", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "226d7d27a7cb090e545a2b964dd15f67b9000aee", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.26.4.tgz", "fileCount": 59, "integrity": "sha512-3sJmqN9NSqISDjvJOhR6xtuiXTXdYagfJIQz+6UVWwRs+Yc9Et9dFQm1ODqR1BaZpTv3HntBzkp5ZWuZTU75WA==", "signatures": [{"sig": "MEQCIG4FPxRap4miRDZRv8lxAdohGMbJk0Hp12JDixz1Ap2bAiAZ08x132CstlA8JMWBQGtaZPpSRZUNOEaAHh/mtQg2Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2907987, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoN2bCRA9TVsSAnZWagAApFcP/i1+yxw+qDmsparvRI5F\nkexSdakebtReYUat99qMp/be2pCCdZ4ubyhHQLpju4AhDFoN/ckN1K1n8zHq\nED04s4FbBu8rDNGXiNfr/LqEivl7VvQrc10RYg4j5xzwOgpCu3NLKCSeVfYE\nRkp15cl1VRhkt06oG0NlEWE4YCLHUboB0laW+3AMDBmYXbpbiLyDrC0j462/\n4QibK6NU9+YN3sNvGrelmfISHJhhGR/Lgv5f6/oN86E+tQYB/2cMxmytK7Jx\nom8ZvwRSPDh2LTmVzn66ZAZnJNIjF3EaT2YNu2+A1Jvgsec4/g7snbku8ujQ\npIcGpIToa8iI5IvtkPIKm5+TB6PWUWk0bo0PaVzTLbIaE35vSC8wi1fL8VQz\nKmcdJ/SHq/+fNzIPf/uF3hnBlCLDbPOX0FEOOEz4mxsubRwKYg2OFMVcnhLR\nyLQtQP4VICND2mdwRfszioBjO0ifk0dwm14Weg4vrfJyu6k2WS1tfH8STzgr\nAO2PQZgTT2nZk8olXxeV5oOPhd6nvXpfF2eqeJOxOde173z6sIpNLWk4lAiP\nPQfmvvuOlexg5ZCf4QyWOzS3/Xu4uEbWE1H22zLyXCWNJoevERVp7W1aaWRU\ncMhFJ/6N+JRKpfULjWA09TDfLNqKwANJ13kcF+msanAGofOieBmAPLH3SEdq\nCkAu\r\n=qMv0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.26.5": {"name": "@testing-library/dom", "version": "7.26.5", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.4.2", "@babel/runtime": "^7.10.3", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.1"}, "devDependencies": {"jsdom": "^16.2.2", "dtslint": "^3.6.12", "typescript": "^3.9.5", "kcd-scripts": "^6.2.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.10.1", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "804a74fc893bf6da1a7970dbca7b94c2bbfe983d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.26.5.tgz", "fileCount": 59, "integrity": "sha512-2v/fv0s4keQjJIcD4bjfJMFtvxz5icartxUWdIZVNJR539WD9oxVrvIAPw+3Ydg4RLgxt0rvQx3L9cAjCci0Kg==", "signatures": [{"sig": "MEUCIEQTIBDOj58RsFehBTT9a8mRVLmPu1cfLFvsDyGi2ImoAiEAn0Rl7jNRQqactPqJ95rMu4PisC5DPob47cfZxTTzwV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2908789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoicZCRA9TVsSAnZWagAAxa8P/RAvPK9B7ovuou0XzOAJ\nk5YlhDUMJhvTgu/kRhrqwPgWajG8AOX/vyr07rWT7sSIG4krpEehLMUjRJBw\n0UXrL3sIBD9UYazn5xRqHPR+S4iZcRDIlZPJ9fgjFIJi9yhG/LWt7pMu7PHY\n64zX2eAwkWBuy77OeE0enyhGro4Epb/zW3fhgibDKfJ+Pk4vZDjHKVylrZiU\nE4jKvs9CtpG1YmYmQVXiEMTyNXKG2t5gX7bzfjp3/7zmxlqRPvTFwdPa1wsz\n4XtZnxgXHLyXtu0lT0IKG6QHO1x6kgQKA4sueEXug6PjTx3BRnncccxeFKbc\n5axFHSkG83MQHFSUiYAak3JeKhU2FEipDACRtiHQmODYDcdiZY1AxhAcRol3\nUQZ7R6VvcX2P+7Y5YcG+3LVEeCrKkNvoebDgOz9/9Gt8PmdQfYU+0MyNugFr\naLC7edLR0qOv/JH1/6t3IAbFhq9yDMVXrqmzIAW+mkFcBAWcquR+anrLXueN\nm9gWe+vhD88rW26GKG+B3wL+fYrNaxJeKen+bfQy0m3N3AC7tG6fvvR03ymj\ntiuWDXl5BccrdGH9loryiVfc5jZTNOFyrxcCh3GeGPxe3eIuhgSCp1rIqjec\n7Nl5+v3cuO8AZRoUhhJ3BC0D0FE9KS5e/aBwq4o2ZKqhiOy+XhBMJNLhYmuA\n2Qwb\r\n=Gi3/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.26.6": {"name": "@testing-library/dom", "version": "7.26.6", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "dtslint": "^4.0.5", "typescript": "^4.0.5", "kcd-scripts": "^6.7.0", "jest-in-case": "^1.0.2", "@types/estree": "0.0.45", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.5", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "d558db63070a3acea5bea7e2497e631cd12541cc", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.26.6.tgz", "fileCount": 59, "integrity": "sha512-/poL7WMpolcGFOHMcxfcFkf1u38DcBUjk3YwNYpBs/MdJ546lg0YdvP2Lq3ujuQzAZxgs8vVvadj3MBnZsBjjA==", "signatures": [{"sig": "MEYCIQC+vHD+wgnMkVb5VIDH29V311c/FSRYGg7Gpaletwx+8gIhAKF33c9oqt/MPBM+muKjyJ1Oe2svfnKcrY3g6mgdVWBB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2916753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqtevCRA9TVsSAnZWagAAeU0P/ipNQcLvik8fd2P6nfqd\nRS5ZwlXFKgbeUCbtiAJ610GyLjyxb56OaeQkHyxnMpLorysbavbxv6qw9qi0\ndmvXu4laZoruJcmLC0L13CotaCpVPqpuNfxvufoB4byuauLeuohrnKB+866w\n4Ag4PO7E4Fv7IXOOp0H2Jpxd92q2f1yKw+aVuJmkQQPCEJ2V1KQRHkuQXPEF\n5sMYO4QQmHxqo4g9d4e7W99bHJfPP8f892d8xHdV6RgZx0p6C44I6wl8Kpip\naTaQ63GzvIOLoIYsy3pU6ovGV5W3S9m/31ImLdO0TK4ScxyDJ1zPIQ4Oij9+\nOviqem+/D9WbtNfMRRaOhWxZD+R2GIZyGXcpKeEUwTUqznS30AUyEwuuMM1f\niPM0ehMiRgYJWJ4V93BZmM6qi3kIt2aCpW/Z7Qc4HQ/fSbeuJZjIKxLSQ7H2\nq6BflAGUKdT0Mpaj+F9Hy9dblBI0Q1KfFRNgOYT5oqhM31TLRzSYQ2EXq3LG\nu+/lSQI5n4MelrnvzeTTLW+Dn+pzaBlobrkGMsr0X5J+M0szt9Qs6Ue8ePPz\neL1wnAZ/T1/x2xdh++O81STixeaNHf8dXqUsJk3+QIVQWlyak4fTTLF9JOsc\naJFwMwegw/elVTXjDfnvNETySIL76UwmxM+xzB2hy1fBhe6oUzGsCwFCQFDY\npGJF\r\n=OtSM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.26.7": {"name": "@testing-library/dom", "version": "7.26.7", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "dtslint": "^4.0.5", "typescript": "^4.0.5", "kcd-scripts": "^7.0.3", "jest-in-case": "^1.0.2", "@types/estree": "0.0.45", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b9008b21524288fb09f7bc20bf366a3aa1f0ce72", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.26.7.tgz", "fileCount": 60, "integrity": "sha512-Ykb+W02Q5lAAuwwANq/LCxMqrLKF7S+udJ0tFDwyuHyr84kQ2YKeYdIl5B3HnLKSWK4mGqA+Xe7dbU25DXhlkQ==", "signatures": [{"sig": "MEQCIGtSmzxS6iCBtEqo5g3B+KBmRsbt7TrzposXkjiW2rJAAiBDDsOleNewfor0gkFsxVNXCrZjvp8fafYCjBZGZFAJww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2922442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfs/5FCRA9TVsSAnZWagAATacP/jbSkFLpHR31IMjAfjQc\nUHsge80Y/iaYqLOiNXSDQv4BekQO8xkQOXmSXnqPdRy83ymmXugyQ45U2Dzu\nC+IMS2yBhNRJV8e5/emXfzR5wh7LwyCmggDsGEJISFaa1LVuCnfL5wVe5bhS\nu0n8xa1zmm3qC2OgKU3YemjmWHTD9RFhysrlxo34i0rHhn+1axzotKE1gpaF\nThY2vkpSRlthYhDfuHcSkFhmIeD0t4Hn6cQLI9UABMTjRiddVxAQp0sHYG2Y\nYHjDcuouM7hrs2q1BdsYxiqASoEZzEJ7HtAWDuI4rtehNcpbyYzxkn8Oz5rl\n9Gf4sse5Bsw/u8pYk0pGWHMmz/hXCHuxf891s0+ihwVlBvg1BQPv2tEhINmb\nGoLiowlZN6t0PF3NdxCV8ZrSXKk9wzHJLSHDg/Re+BKqOvqv0c0WFLExYJY3\n/do2j+rzwqd+Jevxz9kL2sDdRkNlE1mqv8u4tpZMkySw69GH59YSLtqRj6Va\nuZEZ7064WCm4sXoB+ssO9DHa5g4Qcthc7h4+47IK055CQO/G0zcJ6/RC1MjX\n6SydUiOBUREd8CPnWadzYaKJ3bRhAV5p+yd4rqIkhmo8kn0DHWXI2cPKEZNz\n1dMyTWHhsmJS6xnkhcUsW62RO+VPs4dm0ud9tNbg/ugV1/yMSjkWK7b/m4p3\nlX32\r\n=1sWc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.27.0": {"name": "@testing-library/dom", "version": "7.27.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "dtslint": "^4.0.5", "typescript": "^4.0.5", "kcd-scripts": "^7.0.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b4c7393f488db6de18c6cfa619390e7d6da57a3b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.27.0.tgz", "fileCount": 60, "integrity": "sha512-<PERSON>4lK9R0DdKdePG76cKQXORMpXLahqRLmyIau084TdN2sM1sfjxnGEiiTbJe/PDiam7sMOuf9uBXsTByH3pPhQ==", "signatures": [{"sig": "MEQCIGa/GM3qf2/TQOj7lP1ugf8xLDu0c7M0HniapFI/Fxu+AiAY5XNANARd0fXIlau5LBcCUAB7Dzmq4T7qcju/J3JykA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2922495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftK8gCRA9TVsSAnZWagAAZ2IQAKQOhzogm3d0oMqzHtMF\nwxeSIjuTLbzd3Dk1FGvsuh6qoajKC0OirT5OEFTZTsWAZEWa9SYn85H+7vwB\nO1+dJSwt4R26lBmKHEtv6LXciZL2v05AfoQsoompLjRh6jzs8BmTy0iJ2D3e\nA6FQYT8vr0YltGIKsNlRlYsvLNq+Lh20KvjSgZAE0uXfWjvTwvjdlbc0B1SX\nPPwG1xEWSwbB2K1qAdGtKI34LMvA/Xz5wqQUd1VcdADfNQ4sW2DR5B9N6VAI\nAOgDymVUT0dDz9VDH++onqxrvbuWx9gnvMJVWh4SHrHU/P30gPf7XIH5V30w\ngwkl3LzptASJZxs/zJFv8pJyq2F0xXDANuGU6K0JwZHFXSFm+J4A+b7vK2ly\nFNaX7B005DXjvO3Ud9z7TpXOX7LO99rhSx/sSqDiLM86GFBXfTj0frvHe7qF\ndFdcjPNqHVBeX912ZY3R/gx2sBQtGk72e6FcQZBNMl/6TQSR5W3lesXu6eYa\njoNv9m44I/jYNE/QY0DhsTJnNQYNSVrIUY/LcXFf22poPfPfKR/wPfzG6eaG\nIsJbh7Vt42d2HeWn+oY8VTI6Mm3rZIoVvp07DhCCLg9KgHv/8BNeHqQBsyqT\n4+gaQXCitIKTkeLPpRoYfXMeumHVz37RxILiC/U0gwvxlfW5HS7Iz6bEQH0d\n4E1l\r\n=f8fd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.27.1": {"name": "@testing-library/dom", "version": "7.27.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "dtslint": "^4.0.5", "typescript": "^4.0.5", "kcd-scripts": "^7.0.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "b760182513357e4448a8461f9565d733a88d71d0", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.27.1.tgz", "fileCount": 60, "integrity": "sha512-AF56RoeUU8bO4DOvLyMI44H3O1LVKZQi2D/m5fNDr+iR4drfOFikTr26hT6IY7YG+l8g69FXsHERa+uThaYYQg==", "signatures": [{"sig": "MEYCIQD8qvezJnbZMfIgMdp/Yf/nNImfRKG+97ikXmWHBSutWQIhAMF98giN0rGWbwJ2guVEX6Jt+/DLu+vjfGgObBXkgZ0H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2922747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftTHWCRA9TVsSAnZWagAALPcP/3VzLxWlRhupmbE4HfT4\nlsKEk8i8ZmqswImwqxQZLJ7a3DTMi7uGv2YfDz7RpsGgxYGC/CyXYHC/Bml1\nzNfZqg6YjFE9WINIiCypfePAUCsPBXDvA5dkJdYPAn/XG7zVSLk2LSmRh6+K\nyo2qOIp2UJMES2rs0V+Dzl1NLQgTrNzMYdRI29Xy6vBftea37H5zX7b/IBnG\n3FZt84VI2wD1VhdHHYRdQKoQcop7G7JgU1liOzq8rXNLr49JrWgQp2vKK65K\nqAOpBa2Ey4XDfNtjQFVA2Xs8Ti+SiCehYrTLaTWG8WJ8ClOP2RWQP4/So/AR\nh41WjwzX3pRmFbvsPSqj26b0Nn6HRdQ7IDSkHvlpyGd/JyMEZraNd4EWAa4p\nYlRGD0uvp9fVaJohMGBaA0DDZviH+z3e7lFCnV+x1YRX1bgs0D3MaaL7JNEe\nhZikATSgAIOCMVUt+JumUXWLvqHlWobvLZEmz1g6q/JLVscQhAT9GEc45zGb\niK50s54ZxuTAkWfsmtRtOwA2tKp+cW1HzChG+wsYeb/0JcgTILD5kAvKxYMl\n+vCRF0WSQvyhp1BPDnyEScOCgcejWEs5YPLEcuPVWXzik0srtClwFSO0Y6VA\nilDCw3rntXjxXvXWuCWQc62ToYpVCrpMqWlqOSWBGvHWpm1oSf1D8TIVKip+\nnnXV\r\n=obYQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.28.0": {"name": "@testing-library/dom", "version": "7.28.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "dtslint": "^4.0.5", "typescript": "^4.0.5", "kcd-scripts": "^7.0.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "4d68a39675dbf0fa2f3c53bc2b9ab9e1dd1d55b2", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.28.0.tgz", "fileCount": 60, "integrity": "sha512-jY9wE3eF/fjrxUCC1VTCnMWE/g+aCP582Df4H6H9wQYY0yLglyevTO7TET9pgg0w9Yzm8n7ck0Hxzi18pN5+4w==", "signatures": [{"sig": "MEQCIDBPP9L09WURitb7ecMny2gNyR7K+Uycl8twLQqz4QgvAiBIue8Xa6T5Nr7zKjlOC3vLDhubjjg5tlzDORg/25Yl0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2932875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftoWFCRA9TVsSAnZWagAADXsQAIQ6P84SHP8VK0elqJ5T\nfj4QwSzsnzjfkNHToVdarMx7a17EEHvlKpE3fsQxhLfb6VJtf7clFiqKNVex\nxEzD0xXND+gJdW8dSsfJZJklFwrHndw9Cu5HGy+mIm9d64luRpRpfLBMK6PK\nfksnS4AB0dCEL6r1W3jxGD5/9dkhNhoImB7Pxvq8Ox2NQ5Q3pOVJwTHmnKzK\nyJ4YUwBJP7C8kHc/78NeBdRRj/GBXNcteYnG52zqcYgK4dg0hmf5kfQ3pgiM\npVbmqXS+nsQM00n9ewPnVbwLtxWne0e7qZgYzlHo8RieKTZpBLkYeY1H96gK\n/ALObej6QoazT/MbSXpXKTt6mzdDVZp+HnY6W6lVfQ8onTx5iobiMy0w9Dwx\nnUJUgtcDx9L41PbPjZ7rsWXWSJKXOPyCDsGvPLVE5UbB9s1xKyzLNTGkzegn\neYNdWARyEA8YfwiGSnuU3LZREhg8rdMIgN1v1ScfhlAVrZjyqgFaX77KXoy+\nbfP2KwUVOMhAZQA1cWX2+YZ0mo5pIKazMI8Mujq/kOCuPgKVeIHr/3/ZIc/G\nLRyR2Xz1LVKGFD8+wiZgafgKjKr8Mgfi/XeeTsUT7EjBRt2r+CqxnOXoMVvS\nAto/CYmy97iGTlCUrmetLf2cCJjjYPA1FM7c9iWan6An6dDSKZlpvWT0d1JY\nXlIX\r\n=wwat\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.28.1": {"name": "@testing-library/dom", "version": "7.28.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "dtslint": "^4.0.5", "typescript": "^4.0.5", "kcd-scripts": "^7.0.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "dea78be6e1e6db32ddcb29a449e94d9700c79eb9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.28.1.tgz", "fileCount": 60, "integrity": "sha512-acv3l6kDwZkQif/YqJjstT3ks5aaI33uxGNVIQmdKzbZ2eMKgg3EV2tB84GDdc72k3Kjhl6mO8yUt6StVIdRDg==", "signatures": [{"sig": "MEUCIQCmsWB0rQJrgLIDg2fHth6bfDvzV29Cgg2kZ0LtfkYSpwIgS4SrjdcGSTKQzmPL5XoTYoVuGj0ZKP8u58VjnX0xV8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2932896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuE0xCRA9TVsSAnZWagAAnYAQAIhnKK1P6ZbyZkAPe2aP\nqCbCYpV2yRzlKUdKWqd18hNd77snC35coWKZGEyCsMPevCiEnWVpmUGUDJWT\n2/XJvaQdWXKd+XBUbs9NbORQKCLk9SMVaXOMWzMT1Z4ZAyp2P7G7YazvmDq4\nTCCEvFM+WK4HEUnVk3D6+WS89iWTW7sof9P3A+PRCeEnsCefyyyAREBqs6IN\n9vlGD8OuUUPGWB0JPvOFVVmnRYNvLpbCiDZbfRBUp3EJFUJYerbAQvrluwjq\nPNWNrZ12fHn4ZvwZnM/Ii19/yel/Ix7JfM0YjmV54A5bggWnlSRPHBAitaeb\nqgMUOqeiTdtlyYXZcI8KdsZHjlGODX0XcFt/XkGR1fSfXwfASMFnEluGoi0Y\nesomsdKHgDHSaPUoJlUrLvmLRhCafW1cCDuiTNlgeqHRf2cGBoeetGqwOjm+\nwRI5Gg/Q5sI3zp4S2mzMfpywTfKYrrb7uoaSolft7G2Q8mNk5np8k9ZrSFM2\nCCXhnKQPGhBODg6o8jxSEKMipjN0TzB1XkBwjawSBcjoRUqGD+vm3YL4l92h\nU+yf+R1/7Mb5YJCCrGxoqfNdA1acwmG17JTNfJQUmmrhw0MKeicTk5B9LNXf\nF+bzT5aSTt3vu3of1mGzi15EwjXuwzYhOCFPVHz5CpSK3+Jxr5ZXTJCSu0WL\noyrU\r\n=xSFJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.29.0": {"name": "@testing-library/dom", "version": "7.29.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "60b18065bab50a5cde21fe80275a47a43024d9cc", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.29.0.tgz", "fileCount": 59, "integrity": "sha512-0hhuJSmw/zLc6ewR9cVm84TehuTd7tbqBX9pRNSp8znJ9gTmSgesdbiGZtt8R6dL+2rgaPFp9Yjr7IU1HWm49w==", "signatures": [{"sig": "MEUCIDTXb2/jluk8InAgVa4QI2zPP79hwiqNkNsiB9MtW+aJAiEAo4LqlPKU7tbrZ/C5kuQ4f/wkrN1d4fk4ktDsOvLfz7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2942142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf04DUCRA9TVsSAnZWagAAuN0P/1zGL0E+kPDz/+FlqOYy\nfW56fczIqV+9LBEgbOwH7kR/EmLWCsX+AmHrQ2WrGjR4+9xBWu9d50tH6vsY\nzGBW8qGcdeZH4DsoIszdmDkamfYJxswaYoY2MGDKgkoSI6U9Y5ZWLhicJL83\nF9vUTVLK0qaw3rH06LtrIkmVD7o5mUK6S8k+7D1yzX2DyMXM5733+u3bQVGs\n1QOAWs7APEiobRZjdFp/egNrv0yJnhh0s0eu6gFyL07+eGgYU8U3salLfaxF\n9O84i397T2nHMQhrhJYohHtHYqmIM8LhQVhR71SlLHM9Kkpdj35U+HY07z5J\n1ZdC255HMk3C0d1vLr7wHwS0K3/wrodvppsivgXEMGVGsTMuG//foFtFInvj\nBilNFdI2rxAejS7WVK+GIRNH7MBD9baxqdqkCm7/J0b5nV21IBl3NgpxnAuG\nMbFe+ZUPEg7BwL0G17RQ376or1NzmsMAB52fQjSnFqjihntoHa8gle94kF17\npxhOaSwd6lfMmBruB71krAAgQSfqG3vRvXd5HaRB85Ypy8PkQ+egDUQjBWqS\nauD3MTuq2bqLBHFbfbJKZS3MQuxX4LJFeNK4U0B5cnZuBIl6Zc8utZTLIvLQ\nhV7GPYZEtuI1NVRMmYsw/wEBRXKUfaaexTR4CmzusADC5wgpuyxkpIyZ/WRk\nwQ9U\r\n=nUFf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.29.1": {"name": "@testing-library/dom", "version": "7.29.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "a08ebeb26b2ea859b1621ff9642d114c1f04fe3a", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.29.1.tgz", "fileCount": 59, "integrity": "sha512-6BU7vAjKuMspCy9QQEtbWgmkuXi/yOSZo3ANdvZmNQW8N/WQGjO9cvlcA5EFJaPtp2hL1RAaPGpCXxumijUxCg==", "signatures": [{"sig": "MEYCIQCtJp4FTZ9OjfUBQwbsWBzTuSBymBmgTn38w6a847/nywIhAJYVBr5tXASyapEteE9+/737beAWl5A256sApNUEZH35", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2941387, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf8tULCRA9TVsSAnZWagAA4icP/32rRhnHs/RN5B5Qtpyg\nPVutX4RxdoxIeuIvr6gn1qMJaNVKwW9yQ34VOCFzhv1Q0qtftUxgZpsuZZmX\nGpCaYQBdMEUC/7TtJ67T9jKI4XmlK9xCigeXZQR1ZkkruhVRZ8kyJkbGExD/\n3uAUxEgDf/Rg3IkIxqjusmzwv/pBX7Hu5V1z21oKIcBJqXkkvDRwCXyjfmUH\nOF32BBEm8vFe1KXeOlVXUSw60W4vzXTH5lXf3aTcD8DBquylcNJxBAz1NTqj\nH6QsFSDYPN3Eyv4p5mWLQYRfJjg/L30LpkVGUvUWLMIWAAdGvMWca2aGFGys\nC0LVqPUj1WT7q4jTUPfoHA5lgFISke94PCQvSY4WvRgMf2vF2Xgs9XKIwbLY\nee3E2GSGbXkw4NueDOfiwzcy7EKqoRPSpmGShZ5USQmToN2r3ggqqXN6kqGq\n2reYOWkRXuVeI0F8jct5D9k7U9rVKaV6HjHoyDHsCR8PNvK4zOux1xmebas0\nU4Pgtsi8BusBr/1xBVRII6YDp05V70JDVbgbV+nWd9972XTL9rbv05gfH15b\nHrBvCqUsHH0tEq43FgKoJiDvKEzn7Xkf1zLrEyjzQIhq/gR5k2wnENiOAs1T\nMZdtJRy1r/DopW9YJO1a1hmZNhwv5Fd8+CCfgm7MuimQOQSrK+OHPoGCRxXL\nzC88\r\n=HCKe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.29.2": {"name": "@testing-library/dom", "version": "7.29.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "6cba65d961d8b36d621a98caa8537444075fb42e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.29.2.tgz", "fileCount": 59, "integrity": "sha512-CBMELfyY1jKdtLcSRmEnZWRzRkCRVSNPTzhzrn8wY8OnzUo7Pe/W+HgLzt4TDnWIPYeusHBodf9wUjJF48kPmA==", "signatures": [{"sig": "MEYCIQCn4zgws3HSWsqFoDReEGGaBvP4dx+Qju1L7WASvkN/iAIhALa4duKrtaFR9MTG1/bto45kiOnCDyX6oM6FFB097YbM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2940629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9ugvCRA9TVsSAnZWagAAr6EP/36l8Tbkon20gmhOUfWg\nc/ORNdr2nfIom8IXewgq5QQN9+6IgiUa0zWKBTBsk6NsjomWGUbAsi1Uiy87\nxkQOtHb70bfM1xSiESZwA+d2c+4VvnFhRQ812jJS3kLChAVW2DTVRcagpgXN\npZufXpTbmoV7rePMG5EDShF6qJbz6vUczup5a6nuoCB7ZZgWf2cX6i8EW48j\nlXCkXLkdSv5NAAfTnFXZ0wHUcqp2Zj9y8/9xIgfcw/69lnRikaVvPlg9ndoP\n8qPG7+FHEy8IwpPss+8XRY7Ud5ftv1qLwZeos8zW4VNvjLoFrz1jhHzMmCxM\n8lPszoelCqjyNOOfLKKKqMD9kwzrgRP1VsvuYqp3t2hJDNWW53ZEQjC1jc0a\nBzyKix6yyXHLkimAtBXbhjAHIFlYg2vyrM3nwSy0M6YoJwu3oi4VywEDM2bd\nhD1Crd0d+N6NvBGFtlxzePg+n04ID5pC8IRnGfiRX0zMfk0D0aSFsikCys42\n8JaOm44ASB+pnaYVT2fCuVKOgjscBjCu8QciaGGDLIjkZLVsu6yJ1aYtnFF3\nwB5CE69QNNpbQHkJ+4z4LPUPwxekpKXCOi831zqTWFhAE1U5wPW+VzXL0X2M\nenDhMHUcJDQuXrufvIJSK7nMPAfdfds9xSbDq4N/ZooRUMnGlFy9SAhXu+o3\nI6T7\r\n=TjHV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.29.3": {"name": "@testing-library/dom", "version": "7.29.3", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "f98ab80f978b26105729dfaa5f76c01122d3c3bb", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.29.3.tgz", "fileCount": 59, "integrity": "sha512-72rGD14/FL/U3y6rvegr2x2+IPRr3y0Lk5imnA93wF2zLnWwTrXe/8e5MDlIHRj0JMcIS29YFetvQKWcb8VmLw==", "signatures": [{"sig": "MEUCIQD+bhnELb+m/gmZ4/ehhb0VLpA8ktWVDzQQ+iusSqG4oQIgKkmOtDroPXWe7msJIEJgRaHgzjX8Nq+SUs7ULiMSPF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2973675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/asrCRA9TVsSAnZWagAAnisQAJwRjDXHFVBay9E1se0Q\nUxO0lccYwv9IDSWAM/56cSBEUarBLnk4zfC/eBxgdo8KpDMmPLhDUyXUDlSC\nEMzMff/HQbTBwCgjVjUVwKUUQAH0oHYZZDztDf37oWblC+N2WRPluVQ2qpN4\nK1kWcFO2fT2oi6ztHPD0iWIvodPfXAaIpwHeS3Th8K//qRxekonYvTchGF/p\ndtQjCosLad36K/LZrRbgrpOxX9jjFVF/cIkWrCzktn9f9NMWVPtNe/p2Srh0\nuHSsX8HEy+LfDjc85YC5g/69O4ZoogYZn3dLJw3jy0fIi9PFtcLOVoJGDOMK\n0g2AS/it6fPsy8b62DHaJUn58n6N7y5C+8qTdLh7NSnOj8B/yMIA47xHWqRV\nmOoditel+wfon4Az/PRiyrAq7WfZ3OtKlF7iFr+9GfU21O8xruV4UmkJcqYm\nUL7mceYCj+43jKrzTVuSJsEYN+z869L0R0BLL77myz5E/eahxU84HWB1AgNJ\nS1Zd4HAiWfrnwss5nQGYPzixh+G+7ymJdAHn7b1FwYbtcCaifFBaGWjsdkUB\nF1MK6DTna/WsYcDy+azMON9EIpsJcQ35lCgctgluX09CedXe1uIi+zPFbC6N\n8iBuHEqrp+DjG45nqTp9YH9ioJtLNQBAOjQNBVJY2DXMydfYqaZxj4xFMO7g\n/2ZX\r\n=xT4E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.29.4": {"name": "@testing-library/dom", "version": "7.29.4", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "1647c2b478789621ead7a50614ad81ab5ae5b86c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.29.4.tgz", "fileCount": 59, "integrity": "sha512-CtrJRiSYEfbtNGtEsd78mk1n1v2TUbeABlNIcOCJdDfkN5/JTOwQEbbQpoSRxGqzcWPgStMvJ4mNolSuBRv1NA==", "signatures": [{"sig": "MEUCIDG/8xqw1C4INUJ2beniORMqKxs0ltHtz2I4V3UXiU+GAiEA8wIJKcUtaLz2ymLZIMgKCrIAhBSzk7pl4lnCrKLSWLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2976182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf/bBiCRA9TVsSAnZWagAAHR4P/1kSWNdFNnfB3fsM+49x\nTNMnLxHbOBVA9Xh1HJsy650tcKDSyMiabFEQJCoU0FRlvZZvf73tEIPkdjCf\n+bb7BVPLBmbQc1bHatRqniZWpQDEJKCFbTWqGZqekXZt3Q2oGZaDCFZBY/uX\nfLjlMoKxVyQ8eDlh9oNC74Z8AZi3htme4rY4L4Y1cuCpPEfOrNJG3fnKuyrb\n40kd9TI05hQd1cnb6A9Xp8fFZ3t3BF+uxHgGr7LXjO4IgWK3O//leVmUjZoQ\naQDbskBo8RIgSlCIdB+HEgX3I3+yXxCQRUqEsofOnV+sjlUipk6+2/IsWtur\nGkaqyKQt77XmrNuUBWnBENWfnQqTawQLLprB2EctZev+nUrCaBhLvI41r5CF\nYpafFbC/n33zVqhCcKAEWQjVi2QFfepN7GCbVx67IsSVY/B74SpuxkJ1Y3Te\nJQgoZ+5nZTuU8AsjEsiRBwZIA4Ds/KReQ7T3pQJG4SA8nsPMJVYttUBI7H/1\n2039cmKcEKGKbVU3Dlh91t2dNoiu72DUNveTGk7moArkZ3tU1E9ZoHFMO199\nJlfDeGNYVS3mRw/WD2msBLarF0YQhqqal7BVsaRTug3iGSxPTpa2Ad8jlA/J\nxLG+xbU/8nJC2fMd289XtPKqbMCexObDAgBgvj1NeDLhG+PPwLTwqxyJmFHi\nsIQQ\r\n=mvRi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.29.5": {"name": "@testing-library/dom", "version": "7.29.5", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "976d5a115c558a71bc688f1895922a5bdded2dc8", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.29.5.tgz", "fileCount": 59, "integrity": "sha512-0PPIYHWkMZpwTSvSOHG4LfYQUeICcQEmXdjINCnJ/4v04iTKG71SS2jj+fg8bNP5nq/ppKPRgwDQsdByQg278Q==", "signatures": [{"sig": "MEYCIQDByZ5gTdZBZBanTUL7VphY+y1aBF+CNKsYrYkM5tfWigIhAP2+67K7lwVERwAPXSKUQH6QTUfi7y7ORrfnDkF8lx+J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2972222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgL9wVCRA9TVsSAnZWagAA7aMP/0OgfNfGOe+vvN+bNM+Z\n5zjOabKNoJZOmBBzVkJjUOFd9gc7ZShWAYtb7yit4pa5BD2UKn6HUqAT8bW0\nFULOfzezJicxvhZJt/eGrJPz6FIWan0UlbGep+jrUisUhY6C5kf2t8wvYXYn\nI7XIWc5R90eHFqPTptX3/4V+NGLPI06+rpBDA8fc25HjCDc2CCGGpflMvQ0X\nUJ0DTJzeBh8ZV8kKj3VCMOqObE8afCS7s6bAc6esqss/AoMw/yxbyC0IEZuc\na+kQsZxvM9iMS4Cow71bD7Gq2uWraJl5fwpo0SzGjQDmYxId6IyCFFPD18Nc\nbMezKtaLyoQ1fmd+v8IxXKa291DvwybAStMa1XEtB20MbA2YD6mv9/BMvoHU\nxFi8KhSj8TSmVsRyjnIuhrAHiMB7rS4rUcNm5NllAPYs36xD7KegIlIskqHk\nGxsYT45OOpvJDu+GTNtgYUlQZf9L4ExWMqLHVxasObHF+Cw/lgqSoGuwtR63\nRSEGBoGjPQuy2AFPQ+srRVp2/umsjFRzzCqJIQ5KrjiDBCAlH5BDqFwQqcSw\nO79AsEx/9S1Ib+5K6f1YcU0tG98mo41C0OQbw08L6x7pj2XMtw+y1N6NQTLx\n+HmkXSHDFmd8RBdt1HMoORwSiwuDlUh6BnT8k32I3E9ghFb6KnZxsRWdGzov\nzgqt\r\n=NXer\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.29.6": {"name": "@testing-library/dom", "version": "7.29.6", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "eb37844fb431186db7960a7ff6749ea65a19617c", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.29.6.tgz", "fileCount": 59, "integrity": "sha512-vzTsAXa439ptdvav/4lsKRcGpAQX7b6wBIqia7+iNzqGJ5zjswApxA6jDAsexrc6ue9krWcbh8o+LYkBXW+GCQ==", "signatures": [{"sig": "MEQCIDCghQFvPG7C0DWkzaGaQTO3u0+kLuSvwiUYDNmztxLwAiBcoJCN27A7fmlHSUTAUSuzT3X/Pjhlu0/d3C070kGXMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2973122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMCLQCRA9TVsSAnZWagAAOuUQAJB7ASIPXLYEJtzyaKig\njEICr8n9A1O0KDRH8+7yrltnQJf3Ji374iou1cEgDHTtLWQhl4rj/47EsV4z\nWhYCImjDC3fjgIeimVpzEtuJkDI+y2NH9qO56a1yvX0PcmvdNMcC0KQLIKVo\nipYUmVwwbCbkwaYS51ThNl4lZ3QpeienOQQva+D9auV+fCiZM6qKjDQs9G4d\nk05+jTNkc6bZID2ZBQtx1zwQtyrK214coAdEUSg9FCA0I6g7YTEJ72R7kFWs\nKVJ/wZBpoPmxRpbI55QCjk6kqC8ps8GPBa8R7wtQxtF9pdPoZ2MVh3qJgV+R\nzOTRDhyMETBlZ9MWlF6TeivHO3aSqnr2RnrYnu9C9m98b/J4ZGwJrNOBlGBM\nBL0ylrVsOo/xhGxf5Pnh9ikZOlj9e6h9/Ztjt1VKQvZZY5sgjWa/nPWKJmIZ\nUDNg/x13QiO3kkiNCvTjZNxUFJ4eVCnJEvy6zWCMT9Ahh41t+4jGNg7mz+Ak\nnL0Qd1GEUb0d3EGF4fCRdfNXP2l8HXNHMQ8SqgbB5kn90WovI1551Wv7ZLv8\nK0EjgM8e6q9209Y37dbXeQGaY2dXxUtullQQij65tB7qTKkRDzraLlgzoSkE\ngWIIGoZDFtX3rAdT7zpLJORkL76zdMrrtO6wLolMV0iFjf2KTDPJTJ15v5zG\nf/mf\r\n=TR3C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.30.0": {"name": "@testing-library/dom", "version": "7.30.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "53697851f7708a1448cc30b74a2ea056dd709cd6", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.30.0.tgz", "fileCount": 59, "integrity": "sha512-v4GzWtltaiDE0yRikLlcLAfEiiK8+ptu6OuuIebm9GdC2XlZTNDPGEfM2UkEtnH7hr9TRq2sivT5EA9P1Oy7bw==", "signatures": [{"sig": "MEQCIH2UBY88Cfc+AYRdYhOc2nOMbre6c4S189KXBUK4XguIAiAApfvLU+3EBhMmMBnY5P2tYKACe+7gZIjhxzE9HnNkTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2983388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRK60CRA9TVsSAnZWagAAPrwP/1pN4CnQW3R0U26hk2fJ\nZjlBJ3/hOHHOEDNJahs3LeIAIYUuJhWDvxbfFXpuD45XcNVc6n587qnhjyoh\nisLinx0mU1R0ZQaIzJmBvIVX/zcPWBGGDT5KeQkqDtQKAtYRJc/l5GO7fnTy\nSLuIr8VX0448CSNJsZ+Dertnd4tTDJo5y1YG6eUmbHDIF63UsE9mxz2UC8ZM\nM/eE6u1jzCT1Dn+d2Uy/zRXODCRXffEcAnbw/af70Vzio3NvEpEuFRzRlnZO\nPqKP9RNCJWyr0E4ykvvsqVBdm/hCKrqPZK9LqVq48jv01LE/9WCgnZ1+T3xw\nbM04zcPxHS3H3P9kW3+lmUlHp87QpRuJjKUWmZG6te1IwcOqL9P3DbmXd2nA\nqVEcGD6YhyH3HWP3xgHdbLpBX2zXiIloGSJp95hRf0gAUb0hAi6joHLJvoDN\nxLG1Y8BcUdpedwL4PgBZSDq6UWRJEwZ/j+89AjUmoDuMAWFUVfzvUUIjNmgO\nTcriKIwtYSsIEKm/Cd7cR+ZajOVazsB2hRaaaxWTLf9otaaEXSouAafJFeZB\n2MLc+ysPOjqnFkl5tC3ektVq7x88b69ETiVZbBHdMJNHBi1SpBgroL2EG9R8\naYnmML5uAnHBkQrM33vMbuze8Fs+5yTjkfJb6Ww0VMqskq5iqGjj/bhr91nn\nc+RP\r\n=0bDz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.30.1": {"name": "@testing-library/dom", "version": "7.30.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "07b6f3ccd7f1f1e34ab0406932073e2971817f3d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.30.1.tgz", "fileCount": 59, "integrity": "sha512-RQUvqqq2lxTCOffhSNxpX/9fCoR+nwuQPmG5uhuuEH5KBAzNf2bK3OzBoWjm5zKM78SLjnGRAKt8hRjQA4E46A==", "signatures": [{"sig": "MEYCIQDzD0SOuurnyTR4oDeo8GUG6flctXx3ffWftyyBSG64nAIhAJ4jSUs5bNdJPQOyfqSgUie6XVR/o0yR0h/2/RykgVTp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2986567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXNRECRA9TVsSAnZWagAABy0P/0O3MZ2E0ujrDS0S5y4v\ne3nhXfZIiXJaGFsx94BAXkoYa+xslOg9vvwXNzb2UU6GsxsqISl3IJNhMr+P\ncGwZzyLO+48p0/i5bZqXtadZ+8zxY3z6hAvu6Y/Ug9MDJCXPOZDILLmBV3Cj\nl2NTjX2Hf+tI7cf6wynLUK+agsyaRODsRzTqxXNS1AlFkWXn0ilz/bsvDtI2\ny2Mz2/2W8UU/Chr4By0H0EkbMibS3YjEskjsjI4R12aSwA3koqbIexyzNZ/O\nOtTafFB1IQ1Q6ITfcWqyKyXiQih3O/rLaZSIEYzx1gKrTjH74/PndRZQEJ5G\nKIBgLgV/GtJKZHDxRSm1tkHIIllhvIuftxLVRbfe4TvzsojeX8FBhsJmkL+h\nzbR2BlAz4yA7Fe7svII7KEIU+F7h4kbXeCzXpz8YQuxSxis9rfsCSYUGI01d\n8Mb0nYxD6OhgJBcBgvZl89y8myLLQVL6Z6o1GwWQ6wVdmz/fYt3T3eDUTAAT\nrKsI7BD8YJ4zrfNDEbJsGt0glC4dlDs7rkHumTJv+/DKeN4d7LzWIOjZ4ErA\nWTTOblwNpCFWMn+NbOJdUaTYH2Wn1GNF1dd/9thBWBYo/6pXpuxvOXvqJfof\nmi/b0uD6KmfRQUOhWUTG5qqOz4bALTP+oig6sxtJVW5WeJOEd0gDP6eGPg2w\n3YYY\r\n=OCSY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.30.2": {"name": "@testing-library/dom", "version": "7.30.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "a63ae7078235ec6248a8a522b04e696ab49896cc", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.30.2.tgz", "fileCount": 56, "integrity": "sha512-NJzH<PERSON>b5De0J0varzT0W00qDTLcbF86etfAyx5ty7iJhpR6eCs+JR99Ls7AMSUG2bWRYIG0u4KTPH6PMcZhlWQ==", "signatures": [{"sig": "MEQCIDfY/CdI5Mbjr05v+cPTAyQkYUUcFYdMjgDt7DQDQ0t7AiA5HHDAcwT9XJzUqNxL/aI1Moh39wzUTcNbzvaEPgBqtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2980961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYv8YCRA9TVsSAnZWagAAqJ8P/iS2JEDokEx30rcaYmVX\nRXIKM4A6FRanyC7Qy/Qf37c74HSwlkAJqhUBb0asTlQmkjU//UGeto/wji8e\nsvhJ3wMJJuAJ1DpmDU6dI3mY8JOGSdz+EGKxGRCBas+rLEbEycjvr96YXZWr\nfHMdB2p+D57tIo3+xjFQ1T1yS40UOTGfjwtZTqy9CBKjku5i6koySizhEOOz\no8UbxRdQYlovuVi6If0G4rIFdJdf7YlfyojZhfQR/WJFB06/A1N0QgQjwVeM\n2xDMSAyx/fJoPxzrKxFSFskIwrHZjS2VqcYC0GhS7LzDDunY5Iku7JvKn5sB\nNdu3+YdXbUCF9sZfXf388HepblmWMVVJY1ocJGME8Cvaxy0u3Zoq3EEhFd9m\nZj3Fra0o8Yjs8dRXiZTnoqVaVBirNuHcoBIoX8JCJcIDk7msQyDpuh5iEXCz\n3MdE6kwEUSZLc3xeIHyLgrtRQgT1AeH2L8SAgtsR7a5DtoVw2pPkJ8NR0OqV\nImX7o3/ezt9Mn6kShhyDC/gBP3qZIv9X/jNtD8ZEp8IOVFbPIQwyFNxmWooH\nusqWfF0w1IuSOlDd1az6zbdE3Tj6ZOCvQDsEssn6StcNimPBLlQKN/bRpZ2W\n263t8jj2BusLW2wZAGV/uEOKnG2FF8B9LYM76nM4wT8YO+C5fOr1DHCzjoCq\nJ2bV\r\n=XBCZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.30.3": {"name": "@testing-library/dom", "version": "7.30.3", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "779ea9bbb92d63302461800a388a5a890ac22519", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.30.3.tgz", "fileCount": 56, "integrity": "sha512-7JhIg2MW6WPwyikH2iL3o7z+FTVgSOd2jqCwTAHqK7Qal2gRRYiUQyURAxtbK9VXm/UTyG9bRihv8C5Tznr2zw==", "signatures": [{"sig": "MEQCID7h8BT/yPCd+ZiiMORnSGJ6tWth5uuDU41gnu93K7/bAiA+QSB0k6cRWdvQqxbhka1BYduiA8ajI+jBut8T9H5hHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2986431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZDGFCRA9TVsSAnZWagAABL8P/2zQRyFZ6FCyvGh4G3sj\n8djIVtjSGewRcpX24Nm2lTrIhUvEQtd/XbwgQ+LEj3Qqt6hJI5wk/8avCc2A\njV3gejAhG1/OloCtsxEJHM8Mvj0Ar39CeIMxVS2karXGqYeqfjgx/uhvuiZp\nBbFMjqjXDxYX+6GNICks4x/NQFX3yTf+mCHysQY/OwYClz1d7pWHr9OLpBTa\nJKhFmrdseQqcNUMyE7j4A0NyiIJDcBD1oKgNg2aFqb+U6B7Mtfq0o+S0Mx+x\nxtiZcfPdPWhxLNsuAapFTaGlXJrsIzjhzvLkc9tJ09H8T1kv62FUFMiwwUWh\nkoIsuFz5IWPn5Fwefmaq4WNa+oKRD1yJVlC6I8OtWHlyUV2DX9NVuHfphBqn\nA10uexK2AtnU3OZlWa4gZqQikk8zWllyAszgsvRarRpPChBOSmWKyoyisMl1\nUt5SjfZ7knSKZCN37QLqjed0XuXdDgL+dwyiHIhY93L1+zrOWjkgovBtHENd\njNm2NFaznek5DuhlwozbCCfIWJDrKCw/wN8Xa92qPxx3jpC65JUdEW8hJcZA\nUyynyrWEXeroPc50zzglXazL/jzWB7X3Baj369CN5Mb8KKw/9LLB4PISv04N\nKD8cH/pvQroHFfSeWdyrZ/RjajdxqwMyTZEnlbpD61efe5Bch2nAKIWe8elV\nY4UN\r\n=pD5w\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.30.4": {"name": "@testing-library/dom", "version": "7.30.4", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c6a4a91557e92035fd565246bbbfb8107aa4634d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.30.4.tgz", "fileCount": 56, "integrity": "sha512-GObDVMaI4ARrZEXaRy4moolNAxWPKvEYNV/fa6Uc2eAzR/t4otS6A7EhrntPBIQLeehL9DbVhscvvv7gd6hWqA==", "signatures": [{"sig": "MEYCIQDrF2cQlTiQNe04o5wXi7xcmFNdtqexT/Ji0ikizqRhZgIhALNIzVEHMIJ61C3C6vCuY9g7mKNdEQBDM75wQYHlQfUM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2986879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggW50CRA9TVsSAnZWagAAO7AP+wbwHAFfgDG1HtqbgUM6\nM3ea5hga6DeM0vfDcTdiuz6B+CECkjN2wcudSSoXKtawl3TqVLYr5J935f6i\nfgsQtxSxaLiO0bXaMChytqmcvND1Ht54yxXFa/8MBqPkLNZzq92baESbpwYu\nAMAD4gurNRnwAKEcvoZb7U9KdavCNHnW17asHbVGGYu+cYadw2P52gb4dKvD\nYiK6MZcj7PmKeblWzAEQgyw0rVBgGa+0Dsj/Pb/AelupCZoZ4oLObGvIzwah\n7mT5CPCuPt9wOfJeu5srHxzFGuhGX/Ueny9ry/+Q7yvWjugqI+CsiIh1hyCZ\nwjt5FLD9WyCIPOru8WlPnqqshGrubjR5eeUxDRXp5V1zgGJujxP4b4c0/R60\ntijKwSvFedoIh7hmpKeko6eqeVNS71qmObBUAUZ4WPW0SK7aFT5JHqvG+TQP\nJCtZ0q7kQyaWAClg18V2vc5MrT4NB5n8y+WswxpmdwJ3WyzTpFxFeb6Oyiku\nB4ynkJedRCzYBS/NtBUZkfQJfVnXOpK+d8YVbVc6umupLxEUHjSaHal6PsE4\nlmwwBxVqSNUkG/aZDb8eRl5puoA5zUQBfZxQymD4/A0L+P/vB62f4bQ6CEOi\n+98WxR4ijXiQ30MAll5yReG0VUc4IAEcGomjKFGvBFYwB5sZ1pbc8RfNKeti\n8mnn\r\n=ymqa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.31.0": {"name": "@testing-library/dom", "version": "7.31.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.4"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "938451abd3ca27e1b69bb395d4a40759fd7f5b3b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.31.0.tgz", "fileCount": 56, "integrity": "sha512-0X7ACg4YvTRDFMIuTOEj6B4NpN7i3F/4j5igOcTI5NC5J+N4TribNdErCHOZF1LBWhhcyfwxelVwvoYNMUXTOA==", "signatures": [{"sig": "MEYCIQDMNXgwoqjqwRDKy/AOdzdMa1UE7pQFl5uls5D9cbXCvgIhAJ81a5oUyjMLSrgmO7VRGQWQdaH2CvXgrTGcL5TgaIBS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3000308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmOt2CRA9TVsSAnZWagAAWYUP/iv8kHHkAJ7LcQPbJNJx\nEeFS3l4r3fPPUcsI/lxDl/vRQtijSrm1OAjmONpbQa2a9FkbIGI9fpNrP2EZ\ngWYUDU8nk+ODwj8uIvDBroQmOy6o4DELDlJx63wW7duRpJgFRlYlpXG90J7B\nFSzqfRWAeQilfcpugGEVXwY2HVVMpJMLw6z5bPb4JzUdXexM7PpxxLeQX4Jy\nSyV5oPfkd3wv40A0mP7KwXxp2f7VZi3SbU9rPSpLY43ECWLdNOXQg2vKKmcS\nzzs7+fklJQkPv3W6CzECeItFnLuXvPLKpeiz3FEX5Cn0mjTmf21TX7T40G+z\npm7nJehEFaGuwoqeZ1IZsU0/Nh1Rz83MDzrEms0UyhhN5wRvwM+YfzfB2g/0\nb5h4MWunYVeKa6uMVq6o76HdbrHJy8ROhcFgjkRIsaKX8iZmw6WpwbPijg3t\nNb+Buj1LDa6aUNTsPwfnFMU1bYJeQas7VVSPufH3ytXlSWcDPlrVznk4HFZ+\nbadS5mv0gpCoYt9m+QQh/gv+49IjJjnJtHcEUwryrrqiulxUeWePkL7Tluxp\nf12/Uq656Tc+WsmHSGKAY9qnNHRdKT1YJqBqufS2TLQD9CeHD/yY7b+du73f\n5SbgwqPAPPfAyg0+TTzxfFfmqCjyiadg6Z0zXe43Pe5iL6+OAH1G7SbbxTwN\nd73Z\r\n=QWo3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.31.1-alpha.1": {"name": "@testing-library/dom", "version": "7.31.1-alpha.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.5"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "11004d835b7ade5a5762fccded00b5c53b241583", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.31.1-alpha.1.tgz", "fileCount": 56, "integrity": "sha512-Ur8SNzy+DIBmvKnHC3k5u7tuLQqNDS5x1ZNoW0YX/DqaaEjCAz2SA6M6he1L8ACga5ZULpFUvZPoGeisxXg70A==", "signatures": [{"sig": "MEUCIQCVArqcSMOmVghijfkIqFbWvowHPXBQzvTxwtvVK3E06gIgDd5Mal5EI+yr6V2SY9UHHH3xvL0M1uo1wFJ5LjxLBFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3000144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtk8JCRA9TVsSAnZWagAAqasP/iaDBkr7JPpzudQTS/ZW\nqdBJxYNz5uc/3j5zqaVzkPEPAoHzC2bEoAQ3fdLJmOm+0aQxc9sXf6EjOqoy\nYlyRYyJrOdIPfNAeKDZKcFG1STlOZI5dAEvaeJMZif3n1xePHmQ80eHfHRsD\nlShDY8fru6e82OisWdsmw0KYeWNVnakq22uYV8Y7XRxvMCl3cfBXIw2ek2ZM\nVHzr20C4uKNR+cgesBJJu1cm4YeT/Pu90c9Dth79mAZRuQYzg/ueceTZRwTL\njqqvQ3rY/n4biOnSO0OvfHYOBerEvAOrLH9aPeZpAlE4iCYGdGKes3ZLIpUC\nRqsgyk8Q5gRM4HwDt6im7SLDDwOEuCF4qa8i6vgtS82AtjVOQlL9AoYS3aTZ\nvkmSE/eThy98HcoL8I6Itsm3fGIadR7HtnY9MJkI7wK1fEhLEbaklFsqb9Me\nT98KGvfnIQ9um443xqMwMrOQqRlDgPDdaW+V0VXCWlxLkSkOnqQXACJJvtI2\n6o1J0ufTJcnH24h1kDetwoJtGxuXPl2Sk4m3o8PmwVmHoy6RcyV/poFDZh5u\nd1E31vJWexNkdvkwZQM+pMRwdSptqiUebnBmzCMQNagx4OfdHtm1dQLyL3xm\ntlv7ujrlT9bJku2c8jQ/SOLgLxvuu/13+LqycFsXNy8BRDqBOkjB79kSNYe6\n0wiR\r\n=grM9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.31.1": {"name": "@testing-library/dom", "version": "7.31.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.5"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "805a40581ff76b91a5b32bcdd26f737c914da902", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.31.1.tgz", "fileCount": 56, "integrity": "sha512-u58mYe4Ebzc9o7LGKaehRbRpuqXj64hD/6lbS4Ubwohcwh/xu+TSjgG2u93mW53No1mkusL17cOV+bQiuD6U0w==", "signatures": [{"sig": "MEUCIFmdE47RTnD2ht8rKNrwuWiVDpjLPbMBjNlFWGImIYG2AiEAkGjb3n8VkuZfpjx6/OF2+jO+LsxejeQlo+vrPZGNFCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2999382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguKBCCRA9TVsSAnZWagAA/loQAIwyE0UnHwrQ4u7Kly9u\nBWJQTNcceKFKAOxG4SCe8Wht1BWOwHnYf2bJ5anvdMW08VzLjuuaBITjmhis\nYNQVMSb4EoSJbf2zklFXJcChWnODbOjbW0FgJfvw6rqwJs/USifAwqbd52vG\nFjho5DLlVWJIQRxUh/BtgrapzwPhdc44IMo2C/eVaeNkV0n0GjX2N1Mqfn5H\nmRsUFtZvIwh6GtbgK/+mbUtILLF8C5cyC6QGGu8AN28XYP2Q4LrhRq58/qeS\nn5nV24BCzVOJ0vjmkv4foVTUwGBs2IC21icE9w9ixxFNPM5fbNOzP2TNudI2\nMKbLbetvkvy9UcfMIoalIgIIj51JNouIO/xtlSBtwIkS8NCAoy4/XLvZIKGE\nm1O5ZiYd2EhyvNNpVZHUz3desIeprxlcvo8eIDYRnykP/OafQlwl5LU+xNaM\nOqwqwYhuzSDok54gFqJflrrKVKTz0cSwxJ+/3U5tROUgKMNJNGSEzGxXxqv/\n3KIq1ou6U9TDJUiJ1xbMB3zaAmmWUmXy9bqye39YXp1mikhJvaXWnhnnQQrX\nC+WkoW7Yd5QqvLvbIQojHFAGCdiolTYQC8jqRu0oeSHBZORApvSgOgKLw7Lx\nnK5G6r09CbY4dcI60HDAJGTSOVfyePKfDvBjMabEFTo/2N7qdtI8GWizxcz5\nOQJO\r\n=5vya\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "7.31.2": {"name": "@testing-library/dom", "version": "7.31.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "df361db38f5212b88555068ab8119f5d841a8c4a", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-7.31.2.tgz", "fileCount": 56, "integrity": "sha512-3UqjCpey6HiTZT92vODYLPxTBWlM8ZOOjr3LX5F37/VRipW2M1kX6I/Cm4VXzteZqfGfagg8yXywpcOgQBlNsQ==", "signatures": [{"sig": "MEYCIQDlD8j0WbXSnC8HNJkDE/Wsvd0RS8a0WVOuepIQAb2EPwIhAMUvMxql0kto4YhX+GSP/K091Jrm/7/UB7IAvNGxTYAe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2999382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguKE6CRA9TVsSAnZWagAAko4QAIJD9uP5Mh4DfDYWw8AT\n0cs7LeVydMMdJz+n/dC5oW+0pJX2muxyQdalIgRue4NKLNgnLpXhba24KSXJ\nNc4lY8R1Lb83zCBqdEod7bsgpxLY/F3Gpf94cHGMZShI47kiLvIeedvs1hWM\n8W6NiGJqbYmvG3IIxxpmDu610U7LJ0m626X5YD2vz5JKIsrWFpcA8aswkXaw\nQOv1WFZNON81gFDBR0SxkBhqHxPVpChryjVlgB0hWUMh86v5SUypuoLHzqTl\nLuY6t/9pUa83mobFT4GOUyABzqNCAtDjQFbZ3Np9WbDTZEm/6gz8+Lbq3gCl\nWh1HhHfvLNyGSKrAVE9C+qI+na4uMEbkVEqABuZpM2AVW4K1U5ldng3gaAsC\nuKlfU3rHlBtGOnUvj3ufLdZnurS1hsYzsFLdj+1IeJcec50LONiMWhFjJvK0\nRzxvtXzRc5DDTHG68OmYfBJVzY1n/kwavWfEjxDxbNdXGQFiWrKGqghuPMmo\n/Fj/og8u7bWWHdwIfPp9+isCJ9gljVsC7PP3vZBMmght/dq+uCzpbLAhYfmI\nItRwKkeJsW0x5/7jD0WSHpafM6ttWcyc3LHZPIglLlfF6MrTrLol5SnQ8BZn\nIyHceyWnH5NW79rxCc5eDgtOLva2wQoVRPqdeJ5JPlft0z78FspAoorlti4j\nwMwv\r\n=3W7H\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "8.0.0-alpha.1": {"name": "@testing-library/dom", "version": "8.0.0-alpha.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.5"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "35ae1044985463d1ecc53a70c2f89b767a5cc418", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.0.0-alpha.1.tgz", "fileCount": 54, "integrity": "sha512-Iq7R8XDrsiTD3IMg/gk+KUrKVcahFN0k5c/Gz84qCCUljMIUicPxv1G6MZttX8cbkAdBGH6EcADRc9u4hAHnxA==", "signatures": [{"sig": "MEQCIGd6ZPYx92QoBE8AFkEE1tdThbswUTQfLvTVHWGVSVAXAiB2eo8XfGOb5sVjPuvnNFR0tkWLLzhg44/aD6sqtZw4tQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2963822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguMupCRA9TVsSAnZWagAA/pgP/1/GRgPT6AWrsaa9+Ueb\nhhRUb8P5diGUgWRCFp+/nUEiCyNevdmaAM5RBq9eLeR9fACCdQ93Hj6Qscrh\nbNie803CLK9pjgK6N5wh5XrHFXsGAyJDgzG2qpT85iCEkWXV6whcQTpVyjjo\nR7B2nCl5Fmat7mQAJrjK/6u8xE9Zbg9Jf5rwE5JLecXaqO50mCuPJuNK4Iv5\nRfLvO8XJ6yLE7XSmx1NtzWVafo3QnM5+Yvq5E8eH0upvFPNpAzFKGq9GSZou\nLqdd19lM8kwUlsJzNKmc0p1RIfspSTE8k+jmJDNeoldEEXnKzeKwfsxEfGyE\nrsOjk562rsnFs2SZhI+Mj0l2FujAKOjPO1w+LU1hVdFadXjBbBAeD1swwod+\nsd367eQMMHtpVbFOI4U2q5qnpe1nYZDZDqVyTVrPTT/SaqHMRXXKuP/Tg2Jg\naU/SHvzDdbjPac9jsZrbx6cjD39qfxFF449BaKNxV3O49UHJXL0B7+LSKXZK\npMW/HUuiGn3mh8VHg20AoypHxH16EKmRzRJ/6kpmT7ceyQYz8l9wmcyDOC2S\nl1UCtdpKhIyvNAin2HN5P3NhkNgo3f8C2HDTlgLX44n3eYvOrEEslb7BofmY\nsQ6CGik5Tph3UTRucm5Prvhl/s19/22dW9smtJbXqGtw3Tu+3LRpMDZPfV1/\nvnxf\r\n=Nuof\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "8.0.0-alpha.2": {"name": "@testing-library/dom", "version": "8.0.0-alpha.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^7.5.3", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "fc5c1c381a18fea58b5b1884551b27d745b81aa0", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.0.0-alpha.2.tgz", "fileCount": 54, "integrity": "sha512-3MIvGVt1NQmGRJ3zm9onyFM4HjpE/E5tv1lIkTstACveHEnD2eUuFfOzfAVB6z2Q2pVc2zFsr3tJKAjiVzuFTw==", "signatures": [{"sig": "MEQCIHxa5CXXsK292XBBj4JhXs9sTBer0MpUg/YOaFtqc2ThAiB7Ett8cvVsuu0EaDxPDeqqctfHuZPeTwFVx0faPGOEww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2963822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguOCkCRA9TVsSAnZWagAAMVQQAKHRdXkml2aTez5IEtKu\nT3B3cGVVFj05kpG0ScOy/tDEil/bdoU7mhz+2/Iw0QjjEx7eMIogfSPe2VeL\nZ39AVXr2ejFEL9yCP0i2RgP7NRAu07mQIEnK0U1+aP2SXyu9QFLChfTDtsre\nreXjVU/HqwibAbihW3G6nBHXf9AN4fwmqc3dbjVo6kk/NODyDnNgOIkPWUK4\nuWCs2DWkSlsnO9pl0fAoLeIURGSWbTNVCSGQAvs+Z8EmnuFmKNRr/QWlvKC4\nSHmRdjZIVh1fnt3TRfbhlj+5tBRKWTPOvpQyeO0heTL8d/81c7icJNycN5XS\ny/0bfDNTzKZW28xou7Id/dCIWadJ6n1Heh7fcbl8zwrh4Fmu3HdTRCDQHxED\n4W2RakyqP8Cu4p3jvzbXozuuDqgRhlDaCuE4yLgM7vqLTb7MHBSoWocFKUpU\n7AHXyyKCt6Bg8vLLpTwOI5Y4PsG98UeJQfXQ6WZhwXpEPx1dEUBpIxGxzPEb\nGLXTu0/RyaaraRnhsm5DHn+T575ICct8Xdp/gEEEk7egoHrVWeuu1hxNSJ16\niESZ6ZG2YkFk/u5J6aIzK9pFcvEOJH++PogJa9NDtjZUPtf1qhrOpHajc90N\nZD/I+KJ8NSJEdQb1qp4Gik4mPtE/Rh1+tj/3FeVK9VL0nwRLPrNQshpJB+LE\nuil4\r\n=uSc5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "8.0.0-alpha.3": {"name": "@testing-library/dom", "version": "8.0.0-alpha.3", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "5cf2f629b29b928bb1dee9af90cf832200659ad1", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.0.0-alpha.3.tgz", "fileCount": 53, "integrity": "sha512-gh3e/c6KengVXGZ0lvRWibFDEK/dcEbjJUVuFFzrZFPRPuJeGXHWGDmKrhGgT+fZ8ezNRAeoL1Vlxl2JUsvktg==", "signatures": [{"sig": "MEUCIE/WpmwVDarAC/cH/WZjt0JkJDC8vFQ+PV7xSewHAtRGAiEA+HYMnWH3g6MBg2pzTBJg6sjsdc7s1V/Ix20vuadvZok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3017595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvx9ECRA9TVsSAnZWagAAslAP+wemXg7MhOxOkXtAWjg1\nEeLYX5T4a4XqKi1PHoujLLqulia/Be5W7pLgHuGkxugg27gdzqBRJI6Gtphd\nDFqml/Nj17R6XVfXa8DvNS6wWWleE5eAOzty89AmBgUKidn5AYlW8vHanZ0Z\np+ulqU7EVRcNQ5/+Gy+kYb2QLWTXClnjvGChoIGrnUgQ6XgH/bBLYpxm3Iah\nCenIyjCl4fCV2ae9GaJaNhT/8/CGkRwkOWU7dg2DdcBA62v98kpv7dX5ZrKF\nl/jx3BDaOlQGK+HwA6t9YESe6kAR7btoMEBGHfhkEycQNDEbRhY0jSPdsJLk\n0TKBAHrrIToqYQa2TUOBrZOHNW3dLuoAzJNAx3P3gDvdGvVavTvRfw9aplAl\n2DVf8lXEt9S+4kSHb0/jj4QrRv+Zd9tvVgbXPn9/qua0caUDWDhGRUQapftC\nzrmubCIBXHcFVzVCokY7O3DE0Q2/T4ncxKrYznx+5RErHW0JGB7SKWgBs/Vz\n64y6OIf0fx4yKysL9hAL1ssL7zq6ARZlx52oy53C+j+c4/YyU6a9Z6UpDLUL\n+XKOUTz0s9fkIrS20zyZH8MnfXXa/2sByM5JyIL2Pbd7bOfeQcCdaClyFGXL\nDNVjsLZ2ZXuEhDCf3WWXfcr0Sy5oDA4QQIluihDHCLyBGZydnbfgbpfEOwY2\nwons\r\n=Mbt5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "8.0.0-alpha.4": {"name": "@testing-library/dom", "version": "8.0.0-alpha.4", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^26.6.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "8def877f09ef7450958aa23fad0a1d71e45aee73", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.0.0-alpha.4.tgz", "fileCount": 51, "integrity": "sha512-vOQfLux/cY4XEn1V7BADI1cVdJVfARBLAw5bPWSX8GuYifh7dYvdwJeaTWjl9pfmrrhljp/pDBqUMstNRBXpCQ==", "signatures": [{"sig": "MEUCIQC8wnS2PKD//7u9e6qE3YRdrdwzSDuWxtuNvOaQT79V0gIgPcleBvGEtYyjVKqhe3aYeshZqIp9QOXFXcHCoZoEWsM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3006862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvz1BCRA9TVsSAnZWagAAgLEP/i0yKik2I8cQwa6YMzMv\nUQNQ17ARVaHX9Zax9aZ0cS4Yd6RpcsOZbTOJwcaHo/QOO1AseYtHpg8WXrNA\nbw9wEkPQpx8NwrdH4j86lHtGTaP+bBkut19Eaa1IbHExxwJ35OkvFz+QqpmU\nk+zOS2dlyWq3DjOBZGXQvsU6wcmmfsZFp+uRmEwzWCwADp/uUwDi1gUtUv8t\ndvpaiE1Iyl/1sXDVR+DjWF5q/NhQPa6AMiqgunlwc6DuFw+lJI28WsrMEMUh\nMJpoJ2Qdo6f625pPINC7srZBb+hcz4h3gzGwEFrBDgbqnF36h7VWOdcwFN6W\nCqjvcwNfu3uWaeWhLTwujwsSGGH+rJmrTPrmZ4wRkuclJ/cyROgBP/ces4fR\n7cG7uIXqTL5mB/f3/HOWdnhOZYjyEc523ZERgIlFcZnOUmXBnvRMHDWlthbL\neNk7uxqna+RGK1JDBZTt9vCop8Y5b+LkE3J/0LV4HVkNGcf+Hl8DvpQM9FJU\ndc5WShYkLQgzsG+o5NdRTI8bPlvuoHcYWPsVrrNvVbXJbAkUkXb3PZsQj1Ob\n+rWtQMMhWOStMKVJPzXPjG1dP2mDz1EkC1nulw1kKXY718a9FrMVFpcQfob9\niAAnbmcRQZUBvKA3JWdNKxhFDZ1VjePrXu91p6PuCHohsktomX8H9icmB5Ea\nA6eT\r\n=sHGZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "8.0.0-alpha.5": {"name": "@testing-library/dom", "version": "8.0.0-alpha.5", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "7786fa6d560b6f31fdf78f78d30b325e86b3edd6", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.0.0-alpha.5.tgz", "fileCount": 53, "integrity": "sha512-og8g5+37n099VvD7nRW/Fbkkhzcvjso8MmGduTCfOw1hvAF63LMQeeFK/BlxZ3DlRwsSKXKHUQ0hGGUynN8Uig==", "signatures": [{"sig": "MEYCIQCI3z91jMP13Gh7M+RXcM0Q2b6yk3gaKIhRkaLxjL29OwIhAPrvVf5euvNkyvhRuMNneg0WYe0thwLSrIwZRh4aHQ9T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2907375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgw8y7CRA9TVsSAnZWagAAp4wP/jVAU12sda0DKnVW3QJQ\nV+voW9KxuELeAVK1Ss5V4/qR5vDlGsh/a72jPXprTQAGah3ECfguVsw6ghvD\nF0OREqFDEMavh4K2Z88aEAfp0hEXHZN2dhir1LSicP8yuyVYlMMhsUSKGdG0\nZq3IPO1/Ebxn0szXTCUigDdbi0Veln8iLLFqbl623sBtiOFJWAZUq56JPLY+\nLgiUcbk7qRlcD5dD1cg3YI+AJW8p1GPUwHoHONt4vD7wn2sMF/bkfToB0Ztq\nkNwzrlERqICoBEHx52wzxIMWweheDUs6ERgR2YTZMkkAdwAXnTsDIhdUdGvc\nPuqB3ECHy3NWIGNVFRThwQXx8F760x3Hwvs9KovfzL0jsCTwBCMrFh4qmyKz\nTN8HmJV42hUQksk+WmwGecKmJxBaSYbBcTPs4HWl+0JiE5eUJX8MqwowT+Wc\nMTcggquj9hwRYVrHaKgfE3ZEUFpoV/6A2Jpcm7TxdeCY7wx1AFQFu3/tXejp\n1/EFnEr0SrljOrhfAyO91jx0v5VdrBScKKy5iJeJopMjMysXQuf6bQvR9iox\nEm9B/fW8MJIiU0kngfy6d6JQ8D7w7LVNDRHNrECo7VaDCubCLmpMcFMsZnNf\nXzWqIgoPD5z7iyPHxdnDbn6O7gWClcr0CCp/Iagmy2cF1598NaI7W4ouXK4L\nB9E8\r\n=lwDq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "8.0.0-alpha.6": {"name": "@testing-library/dom", "version": "8.0.0-alpha.6", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "197676d31646faa18bf87e7e805440b093e5db25", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.0.0-alpha.6.tgz", "fileCount": 53, "integrity": "sha512-j1N6GcTViG1Kezh+/Uu95rKL4swLlufMuv65nqGr7R5SuTwgn0Rz/7zk6lARHz85rLZ7EjLmtmjXhuF3ceGgPw==", "signatures": [{"sig": "MEUCIQCcVI+ZHSqMETKYiwSx+haFABlDvhWVk2YtDppEG3p9bAIgV2v5u6yToaueO0RI+y2jRxvNhRzo/rUQYa0+ubLS/so=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2907375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxIGUCRA9TVsSAnZWagAAviAP/RR5+GmsOgSHyX39Bozi\ndTeSaYW269Tcz/XiTsyA7bv/TUNKq9r9mCd/h8HssFeWipxeE3a/4++uS+j+\nyqYSzxiUm4b7awsZXbJsbxhKJXFAzwlBewKRVsHeJxcmql/EaCPBQaBgv8Pz\nZTkL4y7IxP2uOa4iQmTaGoJRBikeMzanh82YDv72oqDPsCuHIDtbVmFruISD\nD10z/kWDF8tmfpo1unq1Xs9lKBFRSpFlEv1V/FxTVLGXMJYGAmtp6WC+QSkX\nb+oFeMl2GCsw2Lx9AJy7jAYDUWoW1Lzbkp5Kk6VAGxREW+Dd2lQSv7EFsBN/\nhZSFUr13Nu5EFDlegQwQ1/V54Tp+MAipkLM0vnDo5GYXh2pDPzpm3C8Kl46y\nSlHwxGcLfWF+vxDHX4iD3zK5eRR1inlURZi7kK0LSVbosWUBYkjXUeoizfkD\nj3VagqV6hkd6zeHoJBGzDWVFnwUuEdlgCC/PwFCDo8WtPWfwVltpRSkpns+k\nByjpLl8Xm1O9rzt0K/R2WMDgP4UPIPramEdN2o3tFoxsR1mYVTYrjYvQ3rzY\nNPhQz3aJ8uCXKII+Hfs0dhK2cOJXcdMYNJnhNWkMBhUWZ2aoFYNwJTRuWNi1\njUj131EFz1MpvE3QXV0nohkly2Dmm5UWTZI2qBjJAl1xe1dcLiiGKpKalVo6\nD3FO\r\n=21R3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.0.0-alpha.7": {"name": "@testing-library/dom", "version": "8.0.0-alpha.7", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "21dac9b925c384361b3571de27149f406678dc08", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.0.0-alpha.7.tgz", "fileCount": 53, "integrity": "sha512-K4uO6CEfBcpOlDR8nGaIc07UjIYDUdQsZmODmPpjgsN9MKZ9Mb87ex3hdOlYtzO7xIMK23e9y4SMIk09GTAPLw==", "signatures": [{"sig": "MEQCIFbmjB9iAIwp2N+0y+WGtjH39NSbyzt1kjSepbr82bCXAiBHaRyEthQJpNnj1UQpC5SHlWDoKYe8GDRZHH8DJYvQyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2907436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgzvi1CRA9TVsSAnZWagAAIx0P/ReN92EsmynD7PUf4Tdx\nagWYrZqE1z+3GBCd+m9dLkza74DXGoZ0RfAKUitn5ogca8PRHPbhFrLYKtJr\niUPWbXTcKT+m/OSGO7EFZIJBLSM3pGe+2OUZVqRe0Hg0LEd11orcdklRSkBm\nqZOBKFE9AsoQkdA+WfrV1FyX9Lf6u+Op+42kP32AT0HH0a0R/CE867eWOfIM\nSVFAtqis3SPdSZfnGGMPx4A2HFi/rcBCKiyfBoXS8hzdG4KOXV75M/Ngi9/5\nUDI8ruGxq//+LEZi8Eky9k1Z8EPOME0AK6YYT3GbPf/iXBAN19xM7JfVQ2H0\nR4vVP4WDLGaK23LZF5PwuEb0NqAI47hb6CwGCi10AHKvC6TuAQlJTHG7QhFa\nk/JZPEEqOI7vFyxWJwh3oJG2YklGPFrI+HBTyCSY8QgfIt8pHz3SrFtBMvOZ\n/bWnnOthKYcMrwbDdU/thCik3beineeg2T2WUDJ/pbdKSa8DaUS2Cgg8vBkR\nP1xSZdJr1RxMUrsQf4pZgkGYOmgfpi/GixSYnUB0t1vcfJrq+WeCQ4wQsN39\nG1scM4tj6sWp7jb5jI84UFlHhPCxmQkxMo5qjds8JswxkSNWjG5YG04RU2Pl\nY4Vk7om97gNg+6cImsZn91HMKKcHK69/vXlNgQETCNfsgAsDAshLise6heb9\n43iz\r\n=a1AM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.0.0": {"name": "@testing-library/dom", "version": "8.0.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "2bb994393c566aae021db86dd263ba06e8b71b38", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.0.0.tgz", "fileCount": 53, "integrity": "sha512-Ym375MTOpfszlagRnTMO+FOfTt6gRrWiDOWmEnWLu9OvwCPOWtK6i5pBHmZ07wUJiQ7wWz0t8+ZBK2wFo2tlew==", "signatures": [{"sig": "MEQCIGt45twCBW73YpPHIjRBMEetnUDp2q6+sMTqxYRI/XzFAiAu5fpyF+aFJHWMlOiVMtJOlvTV137VfjVt3Hhiw8s9wA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2907428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0tK/CRA9TVsSAnZWagAArv0P/jttv7ixpy3EVHqlRinU\n3veFjrbaqQJQFRXviAprzXtpox/Nv86i3nJu7iU5kmvgP0lC8Zs91Z+Glk7i\n7e0w5/wfTFu7BwsIBftBwdx0z10o29qK6ebU4f5qUQ556IIyK/X328X+m4bE\nIt27LCNCwkVCXyXqfKCvmKsSLl/S6mfXfoyv5tHkTTerNhs62Uy26L28/JeZ\nzX7/J9cimoJcgh+4tosTmys7RCpawndIcHtJTRQ6IAhB6Uvl/+TKmSd6fAVF\nf3ewwU7VBLfCTH4/XGxwAQxFJgW1Zro+0A5R4dC8eYRyFugZugZ7d38LFQjL\nlhsScshsEblRg+TD7Bzcxru6eBhbiPdjNMPvV3NURg9VfxPRs2KGVD1LX6+V\nbtMXjfDkcrDVDe0Uw71ZhLezjbQefEqsCEMqkpBXR7Luu/mIIYO5vYD/TWkw\nXA75A64jIlnS+3UfkeEU5e1KfSB0sBKPr2zLEUT090AmUpnD2Y/QEgWCbTY0\nUL44XN6MkKep79NXl9Ixt51lxYBi/EczltKs0UmYQKx/eZU2U7rYdRjKgrBg\n/eCWkbrjvA1Q29NGHvBvO8GG5ZXTvY0KoaItEMHrRSOa4u1bfs81jM0Sqh7i\n/IP3q5CymPd4aWRyjPH1qcI8sDz3xXZJYz8X34L5vxnR281DS3jmVS5yQx+u\n0vM0\r\n=/sgK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.1.0": {"name": "@testing-library/dom", "version": "8.1.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "f8358b1883844ea569ba76b7e94582168df5370d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.1.0.tgz", "fileCount": 53, "integrity": "sha512-kmW9alndr19qd6DABzQ978zKQ+J65gU2Rzkl8hriIetPnwpesRaK4//jEQyYh8fEALmGhomD/LBQqt+o+DL95Q==", "signatures": [{"sig": "MEQCID0mH61ywyF3T8UoEhFcuqQz3E2RHIBk9xH9aeLwCoPlAiASPDQZ1Py5cIcMfpmKnFuG9IpvsljNnFXaHR+ghgvhkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2911814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg3csGCRA9TVsSAnZWagAAUWUP/iTSyaTJwDtsK4uoj5UZ\ndtihVC2YwIuyT5LdNGDMWfTlin4RAKLAOdoEWspSuUEgtcvEuMB2rtp28xtC\niIYfcwHrQHGl7kxCUxor9rZ1GfDAH32s6t4diJ6/T96NDaNvW+C+hI+kHhOh\nJifO9HkEmo4iEMzyCdkafle/pRDYSoFF90azvEINuOcmx/fBYOCGYmibjBt4\nn3vf6bYtgn+rYucNfGLOH+tNTYmPH0P2QE3N0y8PuHbGZrTgivFUr4vUWBJJ\nxavON71wIBV+U8NUlUQMubgmA5vBQGPU8O4Y3qIUy1dClcuNiDJv+BvuDLPr\nAAJZS5DfnZXCpP5yz1lyzMuqcgtG3s+lUOPHU32UX8z+jcR0KoU2sT3RoaY0\nSDlNIsAfrqgPRKZdwIMREJq87JV4MwpCyGDmOrHfNTkY8wMcZWcfNtbYQPZi\nq08L/g9PKJlkD7Vhu4PNlpdrqWvrqf3IOgHOci7k6IlXGUvFnFBIgNdItJr/\nJbtJBwg8E0OZay/xDr+J+rDEf0V6iQvfwsaAXXvgdg+gJ89CQfjJmgQ6O8JI\n6eZfgtux61LtcR8LE7lciRBx0I4wPgYDD5jFYU6Vl3w2TQRCx/uxk/q37p7v\nJ7Pz0bKubznVq0k98lNQ/UX5yZcx17fSBUph+2TLz6e00tTNtxbI74BPlsgZ\npueD\r\n=0Zvx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.1.1": {"name": "@testing-library/dom", "version": "8.1.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "44308211c92cc5c82190e940e101d452b684f62e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.1.1.tgz", "fileCount": 53, "integrity": "sha512-PkyaKetl+at2XWhTVUL8Lpjdl0rFMZ2DCEOQexXFjg1TumMXOtxGTVFf1AIxdh11DFu9w/AO8gZcP3XnVwR8YA==", "signatures": [{"sig": "MEUCICfRV3jenndu7NUANhtTS+Q0478UuhPEQlyVnGt5pSqYAiEAv+EktRcGnw6GxCN2WZPAz2L/VX9ct9NjmIgfR6L0A8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2931124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJgDVCRA9TVsSAnZWagAA13MP/RtBvTGlUBJbP8lCp+E/\n7SLBRLRZOlxaca5W60Ag0byMGGe5PU14AiKBh6t/T3W/v3pgGuNVhDgA5+lk\ngh1hEeuxwPGtaqZSmvodNettza1vycTdALppSFAzFXrBl9XIAygG6KHhaSIn\nANKBQr6eoFpanFwyqsLPJnSHXJnkq2MCp9Eqz31B/JI8ysLqb4Tfcbu0/8RH\n62fyI81rCrBLNEICWoRkC8t6kKFKxU1sywUVR8jw0LaNnzUHOdVoILF26dvj\nPdp7Z3x6rh7+KNObu2CSzbz+RhZXoMycwC+CBQ7fX12dbF5I7ep4y81Rgebc\nhews/aAnGEV/y9tfYPgF4aAzdWFwfUsX5J0Fp/AFxl9CJWopx40R3ANXbLS8\nKUtjUlJGBWE+jDJnwVO72D2Pp9JtnqzYnQA1mAE8J6ERMKujVNI1mmr32b9x\nfLu5kqY/kd6ADxtNHM6k2foJnHrjvAju6rhZi5sxYYX4E1cxVbDE4ZDEZI6M\n86oqbGhMtzDwoYSPwSj7q0ZXIqIsgmtgA6XnA9p4TccesJDc9OV/BHQ5N4DQ\nohR0W7ZDxygoojLaxIcomiizkmageknnWOXMAlr+7yHXK6Dr4rMtgw2/mJPR\nRBChDvBvFjGuoM39brppnA8N9petqwIh6tP2GaBOISqkcoLI2rCKF4o68hRH\neAP5\r\n=QKgI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.2.0": {"name": "@testing-library/dom", "version": "8.2.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "ac46a1b9d7c81f0d341ae38fb5424b64c27d151e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.2.0.tgz", "fileCount": 53, "integrity": "sha512-U8cTWENQPHO3QHvxBdfltJ+wC78ytMdg69ASvIdkGdQ/XRg4M9H2vvM3mHddxl+w/fM6NNqzGMwpQoh82v9VIA==", "signatures": [{"sig": "MEUCIQCZ7ud8Xmy3XIrISKale49SvPgP3s+xriFkZXqORPb1HQIgMlnrVhEkSFVkN5H1kpFUwRabG9+f4twC5Ojn2exU5uM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2943075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJknjCRA9TVsSAnZWagAA5/wP/jO2hcN6iXyx/z0lvM8J\nVZ1Ght0z1dOe7cybe2/1a45Z9i26F0aZk5Cqpi/2u3hdMTKTRoNqiMXzsyr2\nmu8bHpxKhJO+uFOsoPfxPFHb8P+3alt2S4xERImdHc5W7LITDFA6R+jlKmOX\n81EkvNzHkEqVs8dE78e61PGkLL0MAnEBAHk/hsmEs2KHUgUV97x3Z8R5VvNY\n49YK0PgJLrRP9R36ntdJPaHypj9LRfkE9cyN/qjWP2XKQB9rR5/4HREp+zLK\np9VuucdbIRBD3Mp1cZTAJVKa9H1d7zQpALGIFlq7SXNTw9b2uzTVmP6Zr9RY\n355VF5+wO3gg1WSsJWs8pEZsf7Z12Erd3zbTY8sEAa6jHynJcqfbb5Qk4OX2\n9uY8syhOHrW3jnXVp8oBM44Z1aeEM2X6pIdsuIgWtAgLz7HdjRrcvAlgCKEO\nKzsVowoO5skvdO84VTdvCC2b/UvuDfbJq3szsOR0d/H9PSE6w7gSagdcMpvq\n7IcL95e224s+9Q63VU0deNTPWTIReskamMYrOcz659E6Ihd6fpokdXTQyFFa\nKGvHG25K97+I+OMbPRIaeUesNnwUbt3mvSsI4Xlsuk/wCgI0qWpJyRqWenX4\nnjjFfcrXCMWKqc3f6KGGW2nZC7jixC/Ct1fQYfAVAraR9JvOOD9dK8DYS/ch\nPTBN\r\n=Zkmv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.3.0": {"name": "@testing-library/dom", "version": "8.3.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "62eacb67f071df8dc67981f7011fe27ac2334804", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.3.0.tgz", "fileCount": 53, "integrity": "sha512-4Eu+ExT7UhesxcRjumD7kAXv/8xhl5j2wgp2FROReXDg+Y+ArzPpU+GEN0+0m0SYlOu9hBtvNuBrzTKqyEB1Sw==", "signatures": [{"sig": "MEQCIFGeihVAK0i6SGBYCTgF2Q1EWl/9KDiHcGvgDt1Wre9QAiAEY/hac2411l8nomHnmNH90Y9LYV44ws3qk8hFhq2pPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2949562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhN+2qCRA9TVsSAnZWagAA8tQP/394kr4zBE17Uua5vv9d\nQzmN8WScpVqqIiyJwMtk9/jI/bNVw5qG/6lEB4MazmHgjEag3CvbB37Ii0Tt\nPhxPN15c0IYFAC0BQwKZy+LE838eJtZihwgGeXPeATB9Kr6rLKqs8Reaib5G\nJn2yQ4xx1v/e+9QrxPOtFqXOZ3571Za1xVKNaQRkZeuNhskVU6/t7EUc7G7X\nyUe08tFYDQQwqLvUlM90ucFKrBX4ezFxrjoN1iSFRYMEYgaJ23lErw1mYQaD\n7HsGAlJhPjEYtGGSwPvNodwUpll5qJTpCCPkSdIPFxJKn5opN/IFsItczNah\n7isVm0nBX73u/hCY8/RJogXo3omhPzj/2Qz+5mI2MdVlHRMfY6Dr1TW9FzDv\namN0WYZfBwp6BxWxlXPrqSDmglddF5KQpTuzSrfb6thPbb3QKqMDexLsOZiD\nozDaPcJRN8YkoaFYLBONui0zPa+y9ARltqSP3gzaa+IkARJ9PVBCZ9YOrWWV\nd0tRvteSC6MBOihJF8XWg1fTj1hQYDa4VK91zDhuo8P76s2bKdwM/UPCAUCy\ntLTtQb3a4cc9lOEUBNl9ClhD0AyOwmc04sTw3wjkjQqwIvAgcZseEuTUmHN/\nChMTttltShnQLRsMzhQsjHOz17b251p+SO5I/95hNHqtQOBYEVVm4u2fNwO7\nAO+b\r\n=Txa2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.4.0": {"name": "@testing-library/dom", "version": "8.4.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "8d6bd6387366c141b960583e2ab4663332d78c23", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.4.0.tgz", "fileCount": 53, "integrity": "sha512-TQeMdULGOhRVIOuDfztW0AsT7ceCpUAPudKz4NL1M4YHYna2woI1iGsM9IFtzgIwcXl3hIdvd8FFWUSFykW1SA==", "signatures": [{"sig": "MEYCIQCbAP82iLgjwhwIXLMcEHJ6vedRaUB89c6bP3CXsnXvmwIhAKXZIWl1NvZKNXBSo7DDQhTwQ+rwJq/XHaX3FPETTS6H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2954740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPHEuCRA9TVsSAnZWagAAG4gP/A7lOWtGZbrMt6ZjysHf\nfRRWWCjzQSRs6DNNq/aK9pCIaBUkPlJejx9MevL/NBDzESHCOf7EJWMmd/tP\nomCgiWJqQgI9IxDuizoeAN5EJ43zoHTSjSdJqWQGOQwcBOox/S+AtS6lHU7T\n5o3VOSBQ+nCK6f+HMM8oAE5iTuPfwMzUusAgK8R8zxR8EnFVRO5p7XR1/En5\nap9h+UM2D5x1SVSfJxc/XfhYh5l32S5Xxx3di75oAJlH5+nM2tZdnz8d6ROw\ni0Wv6L6baQkaKsOCyeo7Wv1HACfjutAHX6FYDw1gDfJJTwVgWp9AHpet61Aa\n0+KTc8qEwyP4azv813A0z6+9mU4GGLjH+zjQXAIXQ4p4ALK3zH77s/GbEvnZ\nDIyuYl3GKZ64y+LvCokJPHMZKdnP9LExSdl22b5LmUuZP6Ky4XQW6nWXlTFn\nPYW28NZZO6lL/vDlPErZ9KUkEQqsrXWcQEACMOJJ7BurSqCD9RknlulPfCUe\n7Ci9N65O74ML/hLNBMDMNsEomVH8LhdoiJvdyKwYzEQDHpyN5N1NLpeEE29x\nk8Zos/+xIQTEenjqUgrpuAcociB3dp0PbeBROwWOrawBcViXz1NQDWoqs78C\nIEohu12o8UAXSyfYARFDks3PisyBFSZK86pXxv8hRPeGwbLXXZHSpwtAZ+ui\nE8s8\r\n=q9As\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.5.0": {"name": "@testing-library/dom", "version": "8.5.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "56e31331015f943a68c6ec27e259fdf16c69ab7d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.5.0.tgz", "fileCount": 53, "integrity": "sha512-O0fmHFaPlqaYCpa/cBL0cvroMridb9vZsMLacgIqrlxj+fd+bGF8UfAgwsLCHRF84KLBafWlm9CuOvxeNTlodw==", "signatures": [{"sig": "MEQCIAEMgpzti5D3o3FZrBDPp9vnZPVjCNmvsmvbM+bpzRL3AiBx3XfFIOY1tsXeEP+JSCIyBLj0crqHOcGyBbD2+nSPXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2959876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPHPlCRA9TVsSAnZWagAAV4cP/AtWd1ieuOVVnR62DQrB\nCXdprxJv4uaXbjL0mnePFpIH4wx5RTgvdZkrEwkRxAxPwJOtmPzTz8v4mP1j\nbtVd4W6vZTyw3ilrEs9sQuvhxomauqeNLO9ewkyiHqi29CMGL/OV/B/ahamk\n2LAEMwo0DoaEJWaRv1gbTod1dnzAWlQs6O8s59JCJmj0QpeVeuPEm1PRU3M2\nlLtT7GZ2Cb3hFGbwt5/z3VMIK4YvYOBTsLuT6E4XDKccoTzKPevuWmzhN/mz\ng7XnDF3H4kcnmvOC2x8QSKbltuOC5tFyyoyHoydi06MKpf56RQRgPqfkj0w/\nIEiTMbG+S4x/qo+arzwMVRSdWvB/CxrZQhDoqZ1dqswELXfWgIkr3nSSuyi6\ndTEhTF4iQn4tKY6b4yYtPXZ9ZpfxMqLMRrFD/+UbxIrSy1EfcyAvLYpqh+Ve\nipdnfwB6T6Hje4i1/shNcsj05nJNPVQe16H3ZZt/vIsYi039UQ94ooSM6Sp5\nKS3RmuiZ2N6eXCFaO8Ss64XiMqBv1ND15kIGnVBUvG+Vusy23BhLDJ3xtfOs\n3Vzd/bsfId1mIjQFgsI3A78sbRYe22pC6YCIOE8tNSZqg6GLJz4XUTyqRjHj\npyfvJW/ue7rO03ru75LUteugKWB0tz1p3S5Tm390TNnIbyGW8tlRZSSNabH0\nKJ0p\r\n=ZVeW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.6.0": {"name": "@testing-library/dom", "version": "8.6.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c92ba5b714882eabfd7f25d8f5c59d4a4b0bfcac", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.6.0.tgz", "fileCount": 53, "integrity": "sha512-EDBMEWK8IVpNF7B7C1knb0lLB4Si9RWte/YTEi6CqmqUK5CYCoecwOOG9pEijU/H6s3u0drUxH5sKT07FCgFIg==", "signatures": [{"sig": "MEUCIQCzOCfN+geoRsX7BSZ6Ue5jvx0s7Fu2/PNUgaSnq8oIagIgKcZ9BCdocc4dodkYmPHlk0yyFnkAQ/W3QWDwh82jA6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2983633}, "engines": {"node": ">=12"}}, "8.7.0": {"name": "@testing-library/dom", "version": "8.7.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "9fdcc1bf34dfa49b63815a3f76491330cf115724", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.7.0.tgz", "fileCount": 53, "integrity": "sha512-8oOfBG51v8aN9D8eehwzgnEETf9Lxv/3dZyPZuar1JAp9OK0I9d7Y2MR6TEQyj/E/iN1kCIeYaCI445s5C9RDg==", "signatures": [{"sig": "MEUCIEOM1dhWJbkTKx2+c8Pus2ZWoC7DlISbObjgl/jZgMY/AiEA41eYcVvMaI54w6qwiWQ4H9giJaSlZBO7+uPw+3w2RJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2992090}, "engines": {"node": ">=12"}}, "8.7.1": {"name": "@testing-library/dom", "version": "8.7.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "jest-serializer-ansi": "^1.0.3", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0"}, "dist": {"shasum": "c17b1e90e682c7945b2d88ce2f078e522c775ce3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.7.1.tgz", "fileCount": 53, "integrity": "sha512-nMqxP8qPHgYAiIPMVb6zko5DInPJ/LdILgrkWB5HBJhBCIU5avso8pU9IafnXVGLRq+LDtbvo0LuVbjEo27BgQ==", "signatures": [{"sig": "MEUCIF65HrHCrJzKexdYmSs0wglaXtnopJgs4WlXc1qwdQEEAiEAza2ERzTJD0f/+bRBmt/bd8MK/nwg+H0VfIA0aQ3Tuok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2992112}, "engines": {"node": ">=12"}}, "8.7.2": {"name": "@testing-library/dom", "version": "8.7.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "234315c6971be380fc9cbf0b031ada3e9f0bfe09", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.7.2.tgz", "fileCount": 53, "integrity": "sha512-2zN0Zv9dMnaMAd4c/1E1ZChu4QrICyvWtkUvHFQBPhS1oG3VYGcM7SLGLYdda7187ILRXzIUOvOsbXQm4EASjA==", "signatures": [{"sig": "MEUCIH9hMHpnNeMqe529JIWrlnjNAcWzJWzTVfdbgEQ79pjVAiEAjrIHHJMFx8+ZDUD2qArA5ZQqqjPoPhjbNx51k3KK/tU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2992514}, "engines": {"node": ">=12"}}, "8.9.0": {"name": "@testing-library/dom", "version": "8.9.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "608ee6f235688a27f8ee180c0d81ff77a5363d59", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.9.0.tgz", "fileCount": 53, "integrity": "sha512-fhmAYtGpFqzKdPq5aLNn/T396qfhYkttHT/5RytdDNSCzg9K/0F/WXF5iDsNBK1M3ZIQbPy7Y0qm4Kup5bqT/w==", "signatures": [{"sig": "MEYCIQCrri/EgEqM7mhaQS9ENg4kkSF6B+dB9DmDIXf1hmW5xwIhAJTCaFauZQ1dSEPg5nYFDVR2iOsgwMdvRyYNzANMyqPi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3000487}, "engines": {"node": ">=12"}}, "8.9.1": {"name": "@testing-library/dom", "version": "8.9.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^4.2.2", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "d6bf8ad9e1699be0e0e0e818fc211b41e511ad08", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.9.1.tgz", "fileCount": 53, "integrity": "sha512-jXM+FKo5BZA/2WtcsoTGuDs810J3ewQ2yq59Gxd2heux5jr8lo2DnTAl3JWVwAlg2/DcWSYqKY5pi9v0g8hvhw==", "signatures": [{"sig": "MEYCIQD96NvT+t6Xdv7IHMof3qqlxf2Lr5RWH6nR2pl+uVQq4gIhAIeQQvR7j7dE6bis5xMmy7SAH8uag3nHQY5HaLsi1kVi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3002806}, "engines": {"node": ">=12"}}, "8.10.0": {"name": "@testing-library/dom", "version": "8.10.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.6"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "34160a59b257322b9f83fb7d1a20d5aaa2b9dcf9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.10.0.tgz", "fileCount": 53, "integrity": "sha512-iZyFrCXYztY02tYQBp/3zEcfppAWqTzvC/epedm9Um/7qbWK8G0/c/nEyeOM3VNtr7GJd0BstqBJeiElLarqKg==", "signatures": [{"sig": "MEUCICII20Z+UtPCqhJ3ysazumBzEG5jY0IS1S84lnmUI01vAiEAiiIlTUcl4/TqCXkk9a7gC4qUPCduHuK72ElD1Ysaefc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2390409}, "engines": {"node": ">=12"}}, "8.10.1": {"name": "@testing-library/dom", "version": "8.10.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "e24fed92ad51c619cf304c6f1410b4c76b1000c0", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.10.1.tgz", "fileCount": 53, "integrity": "sha512-rab7vpf1uGig5efWwsCOn9j4/doy+W3VBoUyzX7C4y77u0wAckwc7R8nyH6e2rw0rRzKJR+gWPiAg8zhiFbxWQ==", "signatures": [{"sig": "MEQCIHe89spy0G2UOQn4byWrWqSZKegNS7NYriJptpvM2IrQAiBmomQS6xlO7BNaBSn9Isidyb0FWp59OstTSD9kbMb3sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2391060}, "engines": {"node": ">=12"}}, "8.11.0": {"name": "@testing-library/dom", "version": "8.11.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "3679dfb4db58e0d2b95e4b0929eaf45237b60d94", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.11.0.tgz", "fileCount": 53, "integrity": "sha512-8Ay4UDiMlB5YWy+ZvCeRyFFofs53ebxrWnOFvCoM1HpMAX4cHyuSrCuIM9l2lVuUWUt+Gr3loz/nCwdrnG6ShQ==", "signatures": [{"sig": "MEUCIFrL19ckloA5lBld3qoCHPxHT5X/uBInswDft5SAaYh4AiEAwjQCp70fqiE2s9gmoIe5S1R54+phtEu+uvQ7eJqS0Jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2391107}, "engines": {"node": ">=12"}}, "8.11.1": {"name": "@testing-library/dom", "version": "8.11.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "03fa2684aa09ade589b460db46b4c7be9fc69753", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.11.1.tgz", "fileCount": 53, "integrity": "sha512-3KQDyx9r0RKYailW2MiYrSSKEfH0GTkI51UGEvJenvcoDoeRYs0PZpi2SXqtnMClQvCqdtTTpOfFETDTVADpAg==", "signatures": [{"sig": "MEUCIQDfQ1BY+31okKP4OnNGAmT2Kc4dfXREiGoydlu5kqySoQIgCwzAOqDPTi9cdsW6YoH0033RYlUHlrrEUsWdISstwgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2390966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2dqZCRA9TVsSAnZWagAARKAP/0H+s/NeQzfIL3K/8t/f\nXNuEEPNTON2T9/7QhYRxn3oo+0dlN6VaBxCLtJNAZViTvjcWZj8dFUGLOvTZ\nXYvwazzQcX0vIw8/i2e6YfHbE+uEu2296VXHVVHodhKwIpWYPJWmQ7VcaCEj\n/AoKSEE7VEY9mxd3hMSbaeHpVH101pVHAPS0uPd7HAw9nls07v7BK/aAsQyX\n7WhIfzsAy4ZG01e1b8AarLfdYRWpzlMxLlqWYHAUpxk2vninl5KzqwiNOW6A\nLulFDLLESihkI1YxAT1CMa5IEyXdgW1njhSZ/dsSWw45nStgJcQKl0MZr+p2\nSapggx/xhuKlmQvt/fmgtYgJTKCZA2v1g/thT+CSz+p60iQjzm9jzgXd/8Et\nGSONxU1gQunjQNGodGQP0yZrbHa99q2FzotR3CJPJGO0q0jAoHKi3Ael3kWO\nVtFyFsB3CY6gddhjIVuDoL6V5kVKgguIX5a/gAcANbYi4EQZp0ppMhy9VWIy\nzv3q7kfkRnZgIcvON6nUHChuTP196KCNm2wuwcz0/vO79TYH3YoxXjg55sMd\nhHgC15arfC2wozfD8/Gg+U4rCikrk7iIqhgnF7fXNjIpwU/vn5XSfx15MIft\nM4YZnmcgYwfMg1ADEuklnYBwqRnLWqn9WvinpQx84jRiq1sJJzj91W6FU3py\nqVrv\r\n=shZf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.11.2": {"name": "@testing-library/dom", "version": "8.11.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "fc110c665a066c2287be765e4a35ba8dad737015", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.11.2.tgz", "fileCount": 53, "integrity": "sha512-idsS/cqbYudXcVWngc1PuWNmXs416oBy2g/7Q8QAUREt5Z3MUkAL2XJD7xazLJ6esDfqRDi/ZBxk+OPPXitHRw==", "signatures": [{"sig": "MEUCIEJzEfXaZwPcwC8fveP/QOSqo630EFP7NzO6fojptRUeAiEAu6+80wUQKb/GFR5+dHBrNRHcPjnFAe+D5FwiGKaQOpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2393541, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4wU/CRA9TVsSAnZWagAAaJUP/0mV6vQHMYxuj1JMumbT\nZHdjmdqYC6JEIMpDxquDhDKmGrvrK3sqLTTIi7F5wrZl26uGMaZE8Tm95ujz\nhSof6+FbEeqTq7pZ2NPEWfkuJQC5KUgIIuI69pH8VtTOE4rvKEJWcy2XoXKm\nOBF6xTodIxzLzYcDP2O0wrjZZlT5U/R2/mS7bvS8n/uG9N4yLukMH/GtnsPG\nbDtUokoZnxGddILwG8m8Xdpt1ZGnlXXJw0TTItNYXGPgld/AkQIq80+tR1rf\nQbOw/k7ED+hC4FV8MLx/W/FXVEF147EX3MHASMOhSA06mSr+blkvjjRDsT0w\nKfJ4dwp8gKBXr8aPx4BJclwsuo9nkZ4QvPvQtAxCCwQFJqCj94dtyfWJQJYk\nMz/22EUPuoX4TYN2KVSeQpbyOrKLZMqZBUkR+AikNS1ydOG6vKw93ASfZlL1\n1QU6qhmX+1UMk3Iv52wpmnVEu5PYqvG4YuQ2Exbfz7n6XpmXNs2Iip/tjxhQ\n8uBVMlts2EG2NqYyVpS6oi2qa/31TiRu5FC2w7d2pKu9e1I423eNa4WomwQM\nEhzsQ5xbvWjmaDQ6Up7OH5rw7u2a+lQlIfG37I4abS7hYqwnHzd0r0iFJCmM\nOqdorL9/kb7DWWqKVYA2KdOYeCt9RyCfNVwyj7FPR/xXaFF0so2x39hzEdWJ\n000+\r\n=U2B/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.11.3": {"name": "@testing-library/dom", "version": "8.11.3", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "38fd63cbfe14557021e88982d931e33fb7c1a808", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.11.3.tgz", "fileCount": 53, "integrity": "sha512-9LId28I+lx70wUiZjLvi1DB/WT2zGOxUh46glrSNMaWVx849kKAluezVzZrXJfTKKoQTmEOutLes/bHg4Bj3aA==", "signatures": [{"sig": "MEYCIQCjL6GwwaVUsHgsrzMTJYCIRevALFblB8QrTU/7iYFwfgIhAJNKnyhj87560bJ3ExfjBuDPUJz11pK/i/nqcU5M6n/F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2394085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8DumCRA9TVsSAnZWagAA3/YQAIZImAs9jhJHbV6oHRHk\nEwMgJWY2qay5trTT1tZyOGRrBCAnFr7In+Viu4+JQDZl285PyvSlCrxHCOFx\nT7NLNSdQQIAR2HkdRfH5ItLNtBmJYX80m8x023TPfR42vGK+WA7NCQ6L1jog\nNhanIkxcKDXWU+20aydX1mHrSWszKKmFn89947BL2WIktmQW3jdL86f5l1Zw\nhqdHWn69EATyc71AvPcftgr0KhF82KzbJfDu+xbT5IDNULpIb6OIW0HpxRBa\nt6IId6tY7Er2OK+Iif2pmMXjmGNElgWKaN7rbiXdVrgMPSm+8y09aaRYybGP\nJUIUMPXah5E8gWUTbNp4CurLyaBoPJW7UtOvFblfeO/tLrWpJzOCJLf3oUys\naHR+KPnNHk7lXe7WwH+Yckuw/pWtlIHFmxJwlfbKCxZl/eEhg6IX3JshY2V6\npJAlIG5PR0nfvPTic0N1f2R8bbIhnSx52QCn5M8ckNWKobZIRCCPGuKkVAE1\nBQay4cj9k7tW+N9lb/S47HRWuAIAaZlMCz3DaJ/3bICZb1oZAcwSLVtdso9J\nB0guV3Yf3Wb3c/KHYNXslZbZhPyZArs70+LWBlbyiZEqwHwRPd8slYvj8GEl\nDft4E5ZjbTjIACffAcw2lx3XFS+ptIYo8V6Qe/iWZeUkSmVaHY/JHXAq9HBU\nbXyn\r\n=Afr/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.11.4": {"name": "@testing-library/dom", "version": "8.11.4", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "dc94d830b862e7a20686b0379eefd931baf0445b", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.11.4.tgz", "fileCount": 53, "integrity": "sha512-7vZ6ZoBEbr6bfEM89W1nzl0vHbuI0g0kRrI0hwSXH3epnuqGO3KulFLQCKfmmW+60t7e4sevAkJPASSMmnNCRw==", "signatures": [{"sig": "MEUCICLsF6LFPN1C1cmn/wSZqfQS89E1dovIMS9OuV2RrDHUAiEAxSZwFOwssoQXIPL8xBC2T57u/JCZR9jgEj5T19ejdM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2493939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiO5d/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWLA//QwwiArqvGu+XmluCP0mnZH5VAM00UDGWr/D3aZCz7QGYwNcO\r\nOJHwwOlpdqs3AiwGBiW3r0yNkwbQIboFDnkS1eiP+tTAnBg7CbKM8ZAViroJ\r\nfG6YoJu6Dwtk83a1K4cU/7hwO95x22C5eGbHPH5CV/yOBHDQ6YlgjfRSIoG/\r\n13WC53oF54ioqV4te13XLC+84pq2ZoN7IcdAuZvIrKNdqY3tBYTDRtNdd/nG\r\nj6V8UMtNBEERwmhd9jzX8u6ybNuN6KXiaKPhd3sXF2nNE3G+SvmDLLcunMRM\r\nwoa/+BlkQsPXzGEDMF42ullpJFnBJoHQC70OqnxyavWbGrcFRzFlfnHJev8g\r\nZveTivqhVv/hrvVrdoKQyV0xkoJGtOmLoHVQ5I8wI+09OzzRVpSRk4soOVDK\r\nmSYNveParMOu6rk/cK75ZY3sUF3sPFVdY64VJsDGUE/Shmv/56yS4/Efglh/\r\nxR/etAySEIb6oYcOgwNXkcvJ2rnryJcgh0k4fwh2YDMN/IxEgEmaInbCgoMp\r\n+IXFSNHJgRQZpPpXxvLgJTeigrXLYvE8hFiOeYBJf6aK5zATKN8AoDrGrImu\r\nwmgRaaCaL809lttEk/n/Fh50OhEsMLniJmUwGC5Qr3MNYbuEg0AmdavNHa0j\r\n+Kw+UmL2+TEqB4xGVs2dviIeEdcWoewjuX0=\r\n=xGZH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.12.0": {"name": "@testing-library/dom", "version": "8.12.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "fef5e545533fb084175dda6509ee71d7d2f72e23", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.12.0.tgz", "fileCount": 53, "integrity": "sha512-rBrJk5WjI02X1edtiUcZhgyhgBhiut96r5Jp8J5qktKdcvLcZpKDW8i2hkGMMItxrghjXuQ5AM6aE0imnFawaw==", "signatures": [{"sig": "MEYCIQC95EFj7ebkHW/xxzPj3LuEUNKPS+qyw/o4D/hEVOv0mwIhAOQrDHVzIzQ4k7hBeGGEQVLYzzJiQ80mZe5QhC0Bgwet", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2497971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPzZwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjlA//Rkv0ujU4ZgLy9XylO6XLAMHUXoAuMZt6iccRBStjoY0kbmNs\r\nDiEiw5hyk5fb62lMW8XGLGw3T5BnqQGzDFKWgRqXHxEmNxXBlZkegoEfcApT\r\n0YxhiXM9qpWizuEwHnXouWJL+unNcBXG6WnHPVX56iVqwSGjCghx6S1pLyj2\r\nKWFv2owr2tEhTebgZzHYN8dLVqoD/WKjQQp3AecMODZPZO8GBSpxsV6qjEU3\r\ns+mKHriV9Pp092pa8rztFeitCVKcSqoz6ACPhGzg4pPTeiOQKD99SrzKsY72\r\nTz3vYCdzzAGvic4tQyaJIDf8m+PZxzuM8Ass2pLINOQwSl0mymbRs+Uk+nbB\r\nnVmL/EiJoUiXUrkgoAV0BOqkVOW6hqL+4Oi4KkMNVd63p8NAUBGznBUXtfwg\r\nb16XglY5lS917YS4iAwvY6d+7ITddHN72IgS6UbWgtt+OQM3UKyj4WN2wjQM\r\nJPRmqRkxcqfjvXlVN6rCfFjr1/1dgC7proVHbVbJiEqqtoif6g9N85GENH/N\r\n0/jIN+VWUNTNe5Uy/b5I5HxyPvlPzOy1RDM39oROmQnFHz24e4Y7Ne+y6sfA\r\njBwdiJhYgfVCShxiXmWoaB8b6oEe6we+DKXAOzshP1FrxCAzizGJJQNhh8Pc\r\nG+nrG2Fx6yUAlUHtOcrmzlx8JDJsbOPijNQ=\r\n=3vMM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.13.0": {"name": "@testing-library/dom", "version": "8.13.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "bc00bdd64c7d8b40841e27a70211399ad3af46f5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.13.0.tgz", "fileCount": 53, "integrity": "sha512-9VHgfIatKNXQNaZTtLnalIy0jNZzY35a4S3oi08YAt9Hv1VsfZ/DfA45lM8D/UhtHBGJ4/lGwp0PZkVndRkoOQ==", "signatures": [{"sig": "MEUCIQCfs6DSDhyNDrtVhpNHeQoXd/6JOAoX8Tstluy3bA9HpAIgD9UXRUlVaug03j+GPr9jYtnpMofJTMXE+JAEHcYucEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2516556, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTDneACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFtA/8C0aLLrII7+KTfY/vMaupEHqNIXmVLkEsn53k6DC/80dB0/3m\r\n0isKqpM4Ao4EnPir7sZ7i2jnAfUoMuxOUNHIROO8At7k+rumO+JSfy4AmtMD\r\nfzmxs3bKl82eTPgpCfZ/ysFYXBP67zlD5TLRMNVpVtGHeVz0PK356smhQMJp\r\nd8bhTZJTiQ/cBhjACFlOl+/bRB3FHFpRgysPvJB+wLdYRfHshz9RjwJpCj7f\r\nHc+uvw/Am/Inu2tCEGXuEWD/uneYTx74YCrD2ng8xLZ/UDHH8+4HZ/J/Ib66\r\n1r5xVjCDGOuIneLrjCGk1acZuXG1Ypm1mgw8XTvKpgFS2OFh1mSiCZ48oGWR\r\nTS+iT5H7BYQTw7iObWo7v3Mj3T8rcDq59OeDhpe/5ePpnnMrH3cTeky+hBIH\r\nmxJpie6vCDmrjojdd6GPIei880YQcA71qqBn38Qmag46DFxxc2pIZkESz6sh\r\nMwLEjxQiscHZQn1eoTIT1YE0cXXQPTh4OsZs1YQVf3CfxLDZTSOYT9Ft/cqF\r\nAcN0gjz3ftJlptJ2nTi3GAYwORDazS37Xws+XJnrJcVpsnezcGiigFO+8KvR\r\ng8s0r7SHp11Yk9kJHrpMU7rWrwtHbTAHtnoSt8sapqtUsOqSgxf/mHNSRExq\r\n+EQgfaiQzjwc9DCrOua7pQVwaCnXSpqvdYI=\r\n=d97B\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.14.0": {"name": "@testing-library/dom", "version": "8.14.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "c9830a21006d87b9ef6e1aae306cf49b0283e28e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.14.0.tgz", "fileCount": 52, "integrity": "sha512-m8FOdUo77iMTwVRCyzWcqxlEIk+GnopbrRI15a0EaLbpZSCinIVI4kSQzWhkShK83GogvEFJSsHF3Ws0z1vrqA==", "signatures": [{"sig": "MEQCIFT1tzLlWayrX/01GBPuRT9tbqySabzCsqAk/icUxbTzAiBXACmd2Lz9+VszCa9pNw/xSVpaA/ofbYB94jE9uCfOuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2473346, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisATQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKWg//YYqJRELbk0cKJ6IhT6fMQkDFefT/aF7WmXxRkNhBttlreYnx\r\nwqUlMEQw5VujLI+nbu84zoM/dOBa90TYKPPxjA2PxFKicNFwtPCAW1GNKqQz\r\nqB9MpQu7uZm4uX1rI58wdQjf1J5uvKJcYNo2jaL1WtX7rOjez9PPGRPZ8eKj\r\n4nMzoolLuZlDo00U7xOHLwJFb1MHUJMq+dxjdVojChY4F5QuPnQZfxYs8uVn\r\n/bLR9beVLb5ACuK0r0ksAk4jmB4QvR0ywk+OUZlkJqR8Vu6jAm2YsLpufFuW\r\ngycRqdGrK2Q6maOxO5QoXzWIH28Q/kmZledfwMNw6Jixs7uNUztW+pCu/HvD\r\n8o3EP4ZCp7562ZzC1yivU8Dymk/ctWP3kVSeNnY8BK9L8e4wdIWDeqbRmyle\r\n+ZfYetgFCiDo5zXuAljMpvbfRa+aO+6yOIhaVvsS71DS0STygn10pWWZaZ6b\r\nCdZVucCmilqrA6Eu5pJVruQNn03yqqeV9VwfXJDPgrTGj0Zlzs+fwv9GxrF9\r\nYbQULyVBVWSxY4zjOF1UNxiampJmvMg7cB3IHfpMD63T2+IlJ9aDFoT/0AuA\r\nOx51zMLXzkLURm9VaX0dOaXrE9obUzc7VBKWnwm/gesuCdM29XKrvw24TR/o\r\nv1Haq38qMgyu0z4CSj/Nq8si70pWfYgATgg=\r\n=rQHl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.14.1": {"name": "@testing-library/dom", "version": "8.14.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "75b12617807a2ba7db71dfda8527caddb06932ae", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.14.1.tgz", "fileCount": 52, "integrity": "sha512-S+E5jTThFnTga+LrX9Oj29Uz0wyLcu0/fD6i9+BUaoJpYtcW9QZ5rJ9SPq4CkKXGhz3Vz9w8poBkngq6Xg7NLQ==", "signatures": [{"sig": "MEYCIQDsKkZpveJmic9UGhvJsH9XjkwPBSki1ClPD0OIb0RhUQIhAOIaOzr5zx/B+4oUBu8Ai9jw74I4oFSdjtqxmCJ2U54F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2273647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyWHHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMxBAAnKdV2LHxZOrTs/cYzTqqO2JfJvoWIQeK96y2BZxBNkvj6Si0\r\nE1B0kW23IBmeCy29pfWLBe1jjX77oyomkQyXxahqT5Rhwk/Isv7tWPIPufdT\r\nAKxZB5GXMkhgAnnqCVWIgviKEi27avJL5v4VDWcVBDVl9CY+7YpXJIntt8h5\r\nt4xr3EZgrRHRyZKuUHiEBHpgxP2iePoZAMGCWfv+z8hMYHZkwc9LcCm+3QDv\r\n6oqJHWI5FsgGdxJhYpuw/9PCPFX+izfsFdDEeVe0lwjEZpBX+iLfZfBG60/4\r\nMA8NwsdKUv2o8PgZMrukJO7/0jmOOSgRkbkKNg8m6a+Se8MKz8/iAacDqmv3\r\nZlB2y/SBNDSYO+fnmNGTdR85+kNU48ngLzkNvfw8BjtrYsLO0yL2ayULOIJM\r\nhllQmlQS+G6PzfGey0FWGtSD9Ks3ErV1oaG6+6QbiovKI3ouOWcgfClVPNtl\r\n/LBEjvVzvxYA4omzcVAoQpQ5sFMAuK+HoOoSjIVUDdbdJUhrOjItPJFt5yLG\r\nH+8GlKSt1f/tLjIVBga6Uc6b650kseKM3AcauWvHZuFneFBKJllgwl2H+i2B\r\n6RSAZruDIT5wlTwNIZE64+5HIQC+6jAKKslm0+dSC3fFDbQSkn+Tg9Ybk9NO\r\nVE91m4UssrhvjORBVnwBZGWBnUvpoVKeBGQ=\r\n=BaZZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.15.0": {"name": "@testing-library/dom", "version": "8.15.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "c7f8549dc4d094786a5bf1c241fbd0e0df9d1e74", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.15.0.tgz", "fileCount": 52, "integrity": "sha512-KtMhnUST7iUvZPl3LlRKH9V/JGbMIwgCxSeGno+c2VRRgC21l27Ky8rU8n/xRcfUAsO4w9fn3Z70Dq3Tcg6KAg==", "signatures": [{"sig": "MEYCIQCTlR8BESpneaC8DeQTAnMmXbkT5ER21o6m+iE5Z2cFOgIhAInkInfHiF78NJpIJiAX5gdFpHwjBVFJN+z3wdvmgrrm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2274732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyp3BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHchAAo3lyfyYFVQBAnpmhI4DAKaBPkPzXhpV59U1Q6CZb5M19A/Rd\r\n3u7i2pZAMK+Lkghfvh3d8bDrJYuFic46fdPHimyXyDa0Skr9YyTQC+OK5ZC2\r\nq4G+CxbZ2gfDXkTqDsH5CwyIX486NAfCULSzCliLzVvHHymMZRoFD0C6CRwX\r\nA8iyXpqcZU0movOBsftUU/lCOkA7NkZOF/A9G5WLV6RRxiU9uc8kBVlQbqOI\r\nY73wnuLm5zVq2T/hQThpQRS81zRSTY+cUold88GehwHnE8O4vlVOd+AzpZ7s\r\n+24edeBjyH9GKQ/nEGvUONaQt/KdBeej66yyzWXWx3jmoQv4707BoBnts1Qe\r\nYgzUXh5tSn3cz/5pUC9KOpFFvxLuKteWWLx/bvJZNF/DUtaz44TPEwB4lj7v\r\nHagviQog6uCK+OCkYFAn3QfSfPcOV22FCRlTXPqjSzZ89niK4TsKaI3iMOGx\r\ny7bwlOwfUXxZMjCOVcJ1e7thylyfOzGFStlIaWem/PErtyNzp/6DTGiPGH5P\r\nQP4p99EhQTH9DvAF2hw3coh3u8zRCvI7Y69Ip6fK3v8lnn33j0qom0EaBvML\r\nEK7cUodtK+vTuHJl1qnNSzqp26NY0fSFvNU0gkUrdqYJtj3XmApdqpKMiVzV\r\n2OKfUtqlka1aRYCKTFgKHkMywNrdSZihZIM=\r\n=JQTf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.16.0": {"name": "@testing-library/dom", "version": "8.16.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "d6fc50250aed17b1035ca1bd64655e342db3936a", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.16.0.tgz", "fileCount": 52, "integrity": "sha512-uxF4zmnLHHDlmW4l+0WDjcgLVwCvH+OVLpD8Dfp+Bjfz85prwxWGbwXgJdLtkgjD0qfOzkJF9SmA6YZPsMYX4w==", "signatures": [{"sig": "MEYCIQCxb/Mzzc9ysvyiwI4R/n9wKeOMcBig49i2tO6Y5AO9FwIhAMtACPCArDW+yMNFj7xroVZn7ssnKW2AR4dcJI92Vdnr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2275624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiy79TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwWQ/6A2qh4GVFgrAUqL2jKVrgc4/WzmqHARQ3SJMFoWSaO39ecNab\r\nWWvS4j3gW/L9YJXdQhBl+GlgMON6dgEmwvx7I5ax8aI9Q4rKLR6NSgM6GMMy\r\nb5kqQTxpoGVCHSJCNQB3HM6B1IzXUy3rLTDcFgZNQkJMiBhL/xYIX61LqZux\r\nSZE1EhM7dAPGjpiXd7x+kujlNjXrzWlDAHJWU/rmFoHQvVkUtyz4jtFi8nBQ\r\nfJTLWxGKYIfH1l9BPmaD1d+WX5fj7IhaJ/i3TmWU0BVcCeAZ6uMf8n2e0Lhh\r\ncxnIE0znDagmbdbSh/uhx2JK7i2S+mWbCrJVzmGpRcB/fNnOEy3deOEcOT+E\r\nymVRJMU87VY+rbt3bgt4/20kNmSnkOG+K+/9Uxu1TKGTHi+6EsxLnDk+AOWf\r\nL9DjN2WP2key2/ZuZE5E9h0zoBff1vWak9xlS5OZUpcC6qO9zg/DA1juKjxj\r\n5n0cCrSusp2wjq3Cx/KWKDqU8VXQJEfcFTadq086IHSqFd8nVAt1qpZF0TWl\r\nrPG7zMkVmjyHR6QCnoEwLkn/f5wFpYDtE9DP7uNkV2nsMLBH54u+BChfa9K9\r\ntgbMm/IvikPwk5C/GAwUvOu2oFDAIDleF2Jvwkbs9qXRVNoRP8S7UQLGYUQT\r\nI2yKLP2+/Yrq1tvGsPhVBXLQlKcuQGWg19Q=\r\n=f2+q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.16.1": {"name": "@testing-library/dom", "version": "8.16.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "96e528c9752d60061128f043e2b43566c0aba2d9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.16.1.tgz", "fileCount": 52, "integrity": "sha512-XEV2mBxgv6DKjL3+U3WEUzBgT2CjYksoXGlLrrJXYP8OvRfGkBonvelkorazpFlp8tkEecO06r43vN4DIEyegQ==", "signatures": [{"sig": "MEYCIQDpQC7VtUfoXFZzRQa1NumK+vjpS+e0sS9JYgVDEev7IQIhAKBOuUHD3PzIUGpoo/4Y5r9c+ovXyhcraXIYTqHE+NzF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2280947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7CfNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoa4g//aRv6KZGlHaAGyiE1Fq/vumad7ay9gzEile/sdJp49hgYbAlH\r\n2RI/W74/Id9frxxtQaz+mRTb2PJv6Jl9eRwsOjp3Hc2w042YX8bfJ+IAO6PK\r\ntHz+uP3hxVf8bjPw/gQo+kDiciWdZeE5idVWrQ2+AFpKaJnl9CG7ccNv7NUh\r\nEaUvciADCGIB3i4TGa78tNSx6u7Ty6ffUDCp4jGWIrtYTncFXyo3b2FTRJFR\r\ndOv7/PArJvE6tY02TBzRtAUxsdf22KJa434lUl6AAwjsRQGo2vwMWR9S+zT1\r\nw5DrFo/lD0L4G4GMxAQnjG8fy+3OIwDI7E+LoaQtj5AAoiIcHKGaNJaJhHjF\r\nEk0MbpPprcAMajj3Zqv1tu4lbnRRYGGHThQ0vRrO3UWl9gvRyTbLK7pgwW4k\r\nBqn9mSMO3WPpX0jw6HwK0po9nwqu+qNeMTNSzcQvuK5EdCVY6MFYE66Ac1Ho\r\nmQHOm1An0L5dfwGTh6oORf290yRxX5KFRJh9h9J5/nuR/xznaSKAEEFfwHBZ\r\nzh/NhRUgC4s5K9ZIa0C5BL3ByB5k2k+mvZGscOlx70odgvBt+VNpmc+Ji4Eq\r\neiBc6qwsJaLDyVZAFtoz8+48KIg5sVhLEa/k6xJi8AOesZs6//Cn0TmyrGaA\r\nOYGoVx5Y8XjsDVHLZpCnngnnWinCRWxWXsI=\r\n=Hsnm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.17.0": {"name": "@testing-library/dom", "version": "8.17.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "618627241d3e687350dd36c049af9995d2990db2", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.17.0.tgz", "fileCount": 52, "integrity": "sha512-Dq42l6uIM4ND9mp/r+XOBEGbRnw7zqnYqdzIzCGAOm6VRlTw2YefQP7kTXMZtzqk1R/E5WdH5xka4+0jfgiltw==", "signatures": [{"sig": "MEQCIG5gq/JZQLiWOL48QIGleGNnWJWEAWR7YG3Nx5s5nZv4AiBUUsGxhQpiSl7MN6GAqh1iOWBKcS/DRrWfyTzG7gwMlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2281515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8mkMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYhA/9HR3sTW6bH3M/GmIWiSAsg5Y64apxplGR7sGvwOu7C3WlCJOc\r\nXQq+2YXKXgWwPTLoAzlmwTJIyDruCzaXRsLOPzeHuyvIsQPM1bhgcfGrqWKK\r\nJMStZuzy9ydWo3oxyKchoCtINHR++l4KlfY1GfjQI5gnf9Q01W6mB79p1Kvj\r\nwdqFfHuIXmI9dFYwkX2IgOClC0o3KWDf5xSkr7BthzTcKS7llfrI5GhSqAag\r\nkvPNPlvrkpLlWnY1LsVoObrx/ps8eJo6cBqmTxIwFaQ2vvdNZwgbGiZDGJty\r\nSRdeSUPOOqM28eJpMMTu3kUVJXlkfbs6IbJVGJ5Ze+E4yDbHyiaOA3QeK+XZ\r\nzula71VKHFVCp91g+X+gYs24crYsFxYTbNdPEHmyaLMddXe18YWFxXxIAeEG\r\nH55mZNZ0M2E7ifRLM9AqIkAT/1rt4s7ewJ9E+M1UUe7L5tmcwXBS/UuqbAYA\r\n/rUvlQwFJXfQvSCy0xydmVZTpWZM+RMSqliv8fx3orbT3KH56tr7H1LbqVZw\r\nhNhkOrW7Wn+X4cBqgSXeurMYakqe9eP5EYvY/VbdNiMKl1n3PS+ALvuAb2Q5\r\nzHwgKh72Z1jDPy28y+1rRepjwiLxJSmGxan+WXnxJ8UUNcjskQaA4a9wT3WU\r\n810yRDxcUeU6c0SxeCBqW5SL3xEnVEd+hVw=\r\n=0p+Q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.17.1": {"name": "@testing-library/dom", "version": "8.17.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "2d7af4ff6dad8d837630fecd08835aee08320ad7", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.17.1.tgz", "fileCount": 52, "integrity": "sha512-KnH2MnJUzmFNPW6RIKfd+zf2Wue8mEKX0M3cpX6aKl5ZXrJM1/c/Pc8c2xDNYQCnJO48Sm5ITbMXgqTr3h4jxQ==", "signatures": [{"sig": "MEQCIAiAZ0g3vZC3SNF14O1FY7kIdcwy6uu+o0sLWRo+Q8NgAiBKk9G3CpgZdZrYnSgngXKXNuXZW3kd5Tt1nLuqA0mAAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2281572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8mt3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroaBAAklKQhNhppKo7n1ztutZ1rWgjUH6rLFZzrz3tMF/wRG8Rs1cY\r\n<PERSON>j<PERSON>l9lgbayXS6LsJ1x7Hn4G/Ya/hgAY8LsoyQSwckgXYMz7qCr6qFcnda5b\r\n59YsW6WS4wRH4KDSYVn17qrFh57vllxaucXZtlXdJY6mW8oEB/7eNg0+s5lU\r\nkEliaJpMTOpq7dfBQgTkH6sehrRoXJ599Xsbg7upbr+LhDfDdsMbnrTYtaVz\r\n+hmNa8MM4kQY6yUNFFSDjhBNpPX6xmF667IrLLvBKsIaJIl8n6C+EybhJM19\r\nfpMmwF3f0Z8qOcfP0mh1nh4/CjvtW1YuGrR/ixIk6CVgU3MgTg+nTO7QCDAL\r\nd6wU3GBUV33odDEY3b8M+6A5wRrOZyyqJSL9aVqLeUVWzlS0N5R96KqvYsL8\r\n4zQ4Je68jFULe4f3dNFaLJ3K7IWEfOWlxItavgPGKMinVceRe58VCFbKQsn+\r\n/ynscpBSrZaFTDgewsuuafjgVLKdDzJTy8VOp9GimTebTgmnnK+Tonc4bjKd\r\nDCwImAs5f+mozl4Wl1eEr/gAOLDMhHila1+JdiU7za3MmyGP+2gNtHkkDilI\r\nIFEgukdnlD+lWgJzQh8aDbpwN0WNa0f251L4RiJlNVRq9HVLZpJBW63xdIUb\r\nkQkOkoGx2OyygN0rRCNcnndeZ3Qku6Z2qnM=\r\n=pMBu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.18.0": {"name": "@testing-library/dom", "version": "8.18.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "fe5903a456f2c2b64770d91e50237a031c382121", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.18.0.tgz", "fileCount": 52, "integrity": "sha512-hP7v7Ifiq104Lb4dTuQzUtlkbX4sgsmxVwOTnVSDOU4ntApvPfp+eFLvasS8GpMP6pVfdbNRXNF/bSK7LTriEg==", "signatures": [{"sig": "MEUCIQD3FK2dgEK1cjUAYCVAyDrD5L4adjQKQ/rB+r553BhZOAIgbmlJBP9YU1nMVdsfyJ3Z8nGdRzt2YSwiQ/DBhN3u2Qk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2285175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjJXHLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7WRAAoiYNU0fH7aOGzdsnmLoRm7Xd5C0r4gLnFfDNyLaqNNj4qB75\r\noCSTfI1PUoQaXfamzEyI86D7nD4rFZaaDcoSfispD9Jb0o7vLadCo/40sxmp\r\ny9lddaQ5VpqOMiv+gySiI8xImr1bA9fnEiDJ1AdWF7EbcU6UL7NKVL5ujkfR\r\nQx//xwPSKUCVmFC0dhpMomjESH3mysC3sI0j/EFtJk14TlqSBqiOl2A7Rtcd\r\n1B+RLqWND0rIJX0qG/dtf03UfgMtCCpTNsuSogd5ub3ydN+f9jVdGO5TCxDj\r\noq580SUqaPE/AnyuHUdVaoVRc3QMuvpeUSfrQheKDdZedDuIpUPmzWYZ0wwg\r\n+sEWE4hI1uNEbRd+7au8aAivkALDJh2QotE07q/3qmm+2W1Rs0Kblyhkpt+g\r\ny8uZs5eJEGUxnS44dZmLrPeGpn7X572cXl7DpMd8pHaBXJ9c8Mi60rlui8r2\r\nPJum2nl8Em8TW804tJNz70E7Dv0y/b1nI+ONENG6ygctf32phuEbjDxuDO26\r\nxHfUwAzcekS0+DZRUxqIirm6KOgwwEk4J7Xtph5kV3pS1MnhHqaFyzW9eGFy\r\nnUuH3q6S1SletacPZFMX5evRpxFF8Ky9TJV1zjShSsPR1nwy7OEW2CwEVXen\r\nFip1EGfTlLTyA3CEOL/1AEJZ/URbu9W92yc=\r\n=Fsba\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.18.1": {"name": "@testing-library/dom", "version": "8.18.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "80f91be02bc171fe5a3a7003f88207be31ac2cf3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.18.1.tgz", "fileCount": 52, "integrity": "sha512-oEvsm2B/WtcHKE+IcEeeCqNU/ltFGaVyGbpcm4g/2ytuT49jrlH9x5qRKL/H3A6yfM4YAbSbC0ceT5+9CEXnLg==", "signatures": [{"sig": "MEUCIQDcU/XYFTvfdIolIPscVWItsGrBERjaLDUl9Dik4zg3kwIgQ0GI6jO29uPtuEloApZZmTJvh1jWmmQVLCqvjtELsyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2287480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKMajACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqqA//RQdTS+AxulnDyQvj5xxJe9kafMsyspD2iddYW1J025JSUqCu\r\npZhYxg1n5zbPSLjMMKaeq/xiHG3XEVxUiov6Wczpk7R+O1Sddfy7URRYM491\r\nsVfu+21oIPUTmZgRp6ihWG26uSi3TSOxmYRTqD+sTxkcPOgly1Ayo1pXYLJQ\r\nXQUo7EsvcCSY72+dZm7oovn7iKCEEv1fd8SYaWBCGK+ENFNd/k7x1u4YoxSB\r\nLAjz5vwM7Uw+q0yApKnT6d00MX3zWs29+yIY2Y63R5y3ALg1oSajzEIgc5Au\r\nDD7WFsx7pvSUUehIv7FrTwVQbNSgnktZctBMIvzJdrP3s8gIp7EK7lRa3b6r\r\nNAHhjv/QRkL9yDFkVt6Diqf3S2QPBF6Ig9To+Z5s8lMFb/f/TvCWJe4wKte6\r\nQbEVKtnJstO3faq3ZgTrkDa9bWcwo7Dm5t03nbDnSggHF0diAZ5V0FZ/ci84\r\nYkums4pR2MMdxEKutrXk23WIACnKyuxwRMQ7y+P09dzTukcgPN+4nrImDpgp\r\neqMuS9+EUVCiIBVufCw9InKzYn5EfJ+BMyBQlWiBcQ3kgJKCbbOXwacYbSNV\r\nJn/C1TaK02DdW+aqqlstaaUCmzGPfiZp3GwmYGO6lQQhlqS8PapZGdJYw89k\r\nna9MGSOu6GkH7tWbArG9BgI/gK7A3XZ/Phc=\r\n=GTMC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.19.0": {"name": "@testing-library/dom", "version": "8.19.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^4.2.0", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "bd3f83c217ebac16694329e413d9ad5fdcfd785f", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.19.0.tgz", "fileCount": 52, "integrity": "sha512-6YWYPPpxG3e/xOo6HIWwB/58HukkwIVTOaZ0VwdMVjhRUX/01E4FtQbck9GazOOj7MXHc5RBzMrU86iBJHbI+A==", "signatures": [{"sig": "MEUCICgup3kP6Oa4rowhEEcKmzEthQefpo4RMM+YU3RgpHN/AiEA02dtNqsf3cAkglHBf2u+5arh/AjMfm6t/dBKE8s0K/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2288726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQUcfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQYA//efKGyuWDa37fyjpS1y01CBfYojHKghgVwgbXJCMP5kwrTY7t\r\njUIyx1dMy0kiaq7gVUZG2NIsPppbS2FMElfWN6w6Y1lfM+trirNHXblbyo8B\r\nS10qNl24BtOVwP6Ey/8LM59L+OjiMe04Jcb2RTHKQjY0+50Ng30UG2Wo5A4S\r\naGyYgtpZClIcg21bubZJY2DlpEyoqHhuicNuNx9IiW9bRfkYM1Ovq3c2vDCH\r\nMNclwmkKpyBpewMblFk4i2LaJFZiwcdfDCH1PtCTKYRhCVSBti5jWLeKFGuW\r\n2ZUF8NNDewTptAjtQ7HqYIJT/rDcHkFLMG497nKxx65ob1YX6W5mEHh3EI2a\r\nklvgExNyTjU8O86BybIW/3IFjqRargkmJDgxsKO8J+MEW+iN5YweOLQaclT/\r\nfa5bvsdfvTgGNMy+FBNHS1P8YTWXgFfzExJn/sBAvA0WoTd7/JcklFUgeOya\r\ni1QCZ+VHPu7B9/E0y6JeS8ipYI+F+wi00Dxlvj6xtFQif1x9MYjg4oCQPqJu\r\nWY8V4HdcYiGX9dKOdbXsYWO43NpvSTPzstBiDTGYIE0tyTiHr7bHyrZ0xNXV\r\nReLyfdHUmOb6Ij4WQPdX3FBhBMBHSyzH8UDcXfvsvEvWcp3PJz0irF4/GGL6\r\nN1/Wxsf9boioD7Y7o63QHYD4rQ77VpTj3H4=\r\n=P3hb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.19.1": {"name": "@testing-library/dom", "version": "8.19.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "0e2dafd281dedb930bb235eac1045470b4129d0e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.19.1.tgz", "fileCount": 52, "integrity": "sha512-P6iIPyYQ+qH8CvGauAqanhVnjrnRe0IZFSYCeGkSRW9q3u8bdVn2NPI+lasFyVsEQn1J/IFmp5Aax41+dAP9wg==", "signatures": [{"sig": "MEUCIH3QREWGZ9/Mg+iGdrAjIqao486liaHH0XdadSk0cbQcAiEA181ZkBMisjVbEcVFgsGDp2fwocKG9/gInm4D5J+KRJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2881053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpbHyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqVw//S/AIzkm3oZEgq1aFL34+RyN4/nng4YntY9aN7zI8fougRRNm\r\nBdukRwXnuBWFvDfAqXk024UDz9ik1LP3un/M+d+2O78b/Hkv407jwOwV6kgo\r\nbrFuUQBOeFXEFSXNfCR4dwJVYBuCbRyXBIke6a1RcapH+DsjHTaENQiQUxNB\r\no4MprMPM36hl5FOc287Dd1eQho/g2/9KLXUJwNr3T1jVF3JZ2pehTDWEwlYy\r\nZ0y/N7bTOBKvR8wCXEZlvS2v9iZbKeZhPA16fnLVSkAS+uI5Z84pEDSTqSfA\r\nAD3rCzW2VMpJ1zf5qXqMwIWUgLP/Pj0YmEfLkkiaxNwQBqx78dDhmpPXiyDb\r\n8ho3czECgq01QUYPld+27gg+B9nZEIlVkYAxrU+jeOe/UYCrEIHefIdx5exD\r\n6eAOgscZ0VY0qXLMwZGfFXqKMeEK1/5Pfg3Jh7heXKAAgnST6SfhHE/EaHWI\r\nnvgo9bC53V+KAIFltSnLZmyG6vqBFI/RpMOCJ2Mj86S1Kpr6eqWLANLlaMif\r\nD2gy6RbAMx/kWeM7/P5sUoB6cjxb3prrXy1NhkM/5B9Fud19o70525GhyC/b\r\ncdG3dDS4EjTza0vSNfKfWbehKAs2BMX9AaJAu2CbjVPZLH03hw/0Ork44wcV\r\nbddiUIeP4rfDpPqHDcPo24eXegN7aBHGF5A=\r\n=RRIY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "8.20.0": {"name": "@testing-library/dom", "version": "8.20.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "914aa862cef0f5e89b98cc48e3445c4c921010f6", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.20.0.tgz", "fileCount": 52, "integrity": "sha512-d9ULIT+a4EXLX3UU8FBjauG9NnsZHkHztXoIcTsOKoOw030fyjheN9svkTULjJxtYag9DZz5Jz5qkWZDPxTFwA==", "signatures": [{"sig": "MEUCIQCtav6jkaRgG1jogElE4zzCy40l33vja0wr6GIj750RBwIgcZ4w09rC+dUfdNGy9AdCtlFqCYvjy1c22wqN4gNma6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2916048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxadhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwVA//VPrm960+jXzPzVT8N0pY+SCpiGaLSTZfRbc9zQoCe4aQ4G0r\r\nIJN4bf8QZQz5eAXhxDS/3FtpJZ5JzyY4wHVendKQBWciEi5dy6Y2LDvIUi4F\r\nWwZXVUJtxlmqdHcJ5IlKHIfHBBWxO6UrtDF8632wfxerYu8upVhsbhIH0Mgv\r\n79dUexjzbrkqVVrwbM3fcQttpIVK+vjT6yzfKQpbGNvRglggj4FEVjlwYLdL\r\nAhjb0iuGxTNKQ8v+U8EdP4ECyiFuyrx5qSA/NTvKTjEMXDRYV5ZyqLlGCzW1\r\nHwkZFbcvrSjAsgIyUee/Zm2oFpQPssne1zVvDLDmH1EeA+JSws3cIP4JcG3b\r\nxIhMqFOXogUpzB/5F4t5Uekor46dsGyfV7L4K3lf4UmtPxveCDpSCBqysum+\r\nUOvgKGKVb+3fNW6zkJjpu6jrLriBlbzQLkwQnk5jpjmAJ9Kktu+e6BpwM6yQ\r\n+ZVAoIGQzhJI/5sIS0m5FYXIood1l6MZhArTr2QVJo9FHuug3dBgnQW4+89R\r\nCjpQCBEfLGjXpi5/fa4/btLSXoO4cWewAmuoC9PMMvszf2O8wK3KlZuU+Xfp\r\n7YFvj/0AEHzhPPgFQBYBOEgRNU6hWAPFO7bFJm72+Jam4d9N695S79T5QwOf\r\nmceBgx20XZVgIOC3bTfdn/UU9a4cI1BH40s=\r\n=tb5z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=12"}}, "9.0.0-alpha.1": {"name": "@testing-library/dom", "version": "9.0.0-alpha.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "74bc3d352a30516284beb68b7dbbf963e71d3fd8", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.0.0-alpha.1.tgz", "fileCount": 52, "integrity": "sha512-agSp2TwiK8CSc6IoKSGYAQIVV4NVUi+IkdIPYzjFcLWPYpzcFfAPmR6uC4nK4v3muxsuhJgZg0p3TSFYyaM/Sw==", "signatures": [{"sig": "MEUCIClLmJgg1XYDc9uAbaGRuXhX3XKAehYi92urCKXjs3xrAiEA2WOIWLklwGq6YFw5gaHtrurVywpY2fPHOqyH5YkMpAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2920173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1j2YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5Kw//clAXcyR76Tnj0b9+HryfcskF6u0+Ylhnl9tNqGXj2MLTLuD7\r\ncQAFRRWeVF8S7tfsQveibgQS0ASgohTf7xbrM6v4sD6ihe/eL9y/9f3RA8Eh\r\n/+iScx4SUSdCRDPQo5XBAqT9JKsuW0oxx4CPbpd77h4Vd6P99Npd+YZYvxHn\r\nNJWXyw24a6AH/36f/cXYq5ZIfoACzstxeMoTxZ5hmfBPqgJsdJrBMk5TPzdO\r\nd7FBMUuWOlwpJDlCQv/oKsCobObmAMJNz6m06LtsMMwEiNnoy8bU0xr3kiQu\r\n4Am2cVx9AhVR3m93p+FTT0IQipNEg7lkmqsFwviZQVudYbvjDSEOPdOke71B\r\n1POjd/qhcT5ryO4ShoA+5Fa6cOVhmRsO4Cv2N/tT7AiE2J1wjKFK4ZueyesO\r\nnU27Wpwr7IHyq16ySZuvfwkg6eTWYGv39WClhYgF5URoMTL30J2XoScH9INe\r\nFizz8fRLtXdSHWmS04t7wmygvkjTYLA/QI4C6Y2knJHuhR3r+lrV7NLmywFO\r\ngBZg86quZtTOg73R3KvfgVk5Bmesj0YM9mdiFCiMRAqBVkTXG6acjEmOD41w\r\n6UVuJ+6FayjEd3mBaYMAIqk6TCGodHr8szPVaVCCEqqZZF9pxq1RXUbifexz\r\njQMSbJID8Gvm2q659AOOarUxLA6PYv+6Mcg=\r\n=B1YR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "9.0.0-alpha.2": {"name": "@testing-library/dom", "version": "9.0.0-alpha.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "18685d2639020227a1b1fde4b6735564a4ed09a6", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.0.0-alpha.2.tgz", "fileCount": 52, "integrity": "sha512-8ewnFvt87nz6XUzaL8jJbcCyF46OpSVGM28WIE1KMCI2oqfGTUONzioGlNxqtM5FDc78+vfwpBNPgQASgQsgpQ==", "signatures": [{"sig": "MEQCIBJWaK65pfOPLWLjhHtzxiVNTRFkTXbQB68EbthV2zFPAiB7QxvJwCCC8qq/VU2e6jo/BAcMxsA7Kbc/97TvUkoQRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2919220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5Ol5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQGQ//eIHcCp36lS+H/HKHkoV+sqbbP5hP4j6kJ9oXnVYIJozKD2A+\r\nb6qX0WjYyHROb6Btl5oxc2yJY7UR3oVSmuUKhTaUIhTkFNpnen48ReRNE3l4\r\nqz1Mg3f2szK/M4nF9byMZWDK7xU0p9N3nFcc5MLAq9lWYx1jh2kTVm1SZ+qE\r\nl4aFSw6/GoNYPyEiq+Dkyev9LhjpGrzQgW2+33FtNDjn6WGPj88g95bw8J8y\r\nwlDKCmHUMNC4Z87QbRWwLz0JD/Y2TjclBdTqR6jmglqgNyJdoWaydEKWTorH\r\nAaXGJmdHrb24b5qwN0RI0Xg44DkFGwF4KMy/71Sfhv+1Dvc0qQ/hogX4tt9W\r\nmgBdLQpFpkVU6/HS2D9HUoXvhY4bJ3M5c9shmgK3LDE4AwKYbDqHOBEFZMPh\r\n7H7L8bT79NoRSOQUOAgmUiC4Nq0F/oT0HjR4sROEJeHlNC4/BJyt32YIUG3x\r\ngNaiIj8I0NeRgOdRTrB+95czyJaOqCsqt6BJdsl3CiURuaWQRSrEK/kvyflz\r\nORKuvUtByIhyYRb/t/tXknaOO7f8GYvLbzzu4iIpwQMNey2LRGt0Xl9COrU5\r\nPsSi0wCrdAaQUX4daiobMhOlTK19B6/Y8432e4NvP8Jup30pbW/DtgDl8OoJ\r\nA5vTxx4tJgBx8k2oJ+PJP0wKQzlzAxLcvLs=\r\n=jFkT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "9.0.0": {"name": "@testing-library/dom", "version": "9.0.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.4.4", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "jest-in-case": "^1.0.2", "@types/lz-string": "^1.3.34", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "cc50e8df2a7dccff95555102beebbae60e95e95e", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.0.0.tgz", "fileCount": 52, "integrity": "sha512-+/TLgKNFsYUshOY/zXsQOk+PlFQK+eyJ9T13IDVNJEi+M+Un7xlJK+FZKkbGSnf0+7E1G6PlDhkSYQ/GFiruBQ==", "signatures": [{"sig": "MEQCIEYz+UYmIyUcM7qRwJi/doQ0smwLQpwVWyF4/PEw9r1+AiB2A8q5bNZ7lIKr0NbH13budGKcqUYQfZcj1iW3jTb/2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2919863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7q0zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmplpg//Wjf1tOfyQyko4ymmVO3jE3bAckdR1LupqVqSjzu0oiwLa7PD\r\nlPNQYdQPwOqhOn5pwa50G0l9R0WOA1189gPQF9VBgKE9MUwETrIH8NSWPoc+\r\nC7JcR0wIP/rq++YzSOvFl57VQDf0V+lIQKmrw1Y7k1/O2ktjrpTDw+PcNVDI\r\naDuoFV5ijJt/GRcqmEJQJodkHcLyhQWi91Ydv0SoVTxh3Afj7SozbaCVGjWq\r\ngbD90UYUAJ90whXem5foNNbjIQ8SanUuYSXcVlsnaBb+B3y57t/Bwn1sG8Uf\r\nKJV7fsoWaRZOOU7uAOde+go+agb695daqWWI65NKoSa/jSQoE4hmITRVtqqv\r\neDDPoedeEK0H4HwIKynK8xoKdeXzXSU1g71pVKBg7ofgPkkReD1EzuFRHyyq\r\nVy8GtKYmLBCuOx4ahh3Hn/fKG/Q3vV2Sv1R1v1oBXCbkCY9Zvqrkb8Le0aNS\r\nE+ma5+GrujM9DoncZTyncMmcG4JkZcUPpCb95Cb7lJNC/nCUOe+XoIOQxwmw\r\n98yZdl19yq9wcyvDV7BkoJKI+wZhZulEME/t8EC+ugKGz14JOdRuUUkx/CMV\r\nbbXJtw+XKMC2XSHPikO7MZTqBH60c6rQkP/5SoPSAfGZFgKuIo4f0ngQwzee\r\nBzRsqMtQo1nNDU0IeRRsUCxa84ajhRrurIs=\r\n=g9Bu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "9.0.1": {"name": "@testing-library/dom", "version": "9.0.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "fb9e3837fe2a662965df1536988f0863f01dbf51", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.0.1.tgz", "fileCount": 52, "integrity": "sha512-fTOVsMY9QLFCCXRHG3Ese6cMH5qIWwSbgxZsgeF5TNsy81HKaZ4kgehnSF8FsR3OF+numlIV2YcU79MzbnhSig==", "signatures": [{"sig": "MEUCIQCf23pk5yIOQO+YWFDg5Qi62jR0mGJqc7gw/mLo18YobwIgJv9O35R5CXQi3dPWP14qeOq8PYnXBj2aT/qwP0r7vHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2923119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCHyOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6uhAAhH3zxRwZYDTUx13fJ8mVLvSKDwPSw29Z3ccFqgQy8Yeyd4PW\r\nH6B7dJHZqAe9Cqu0HaWHH98lLCuV5UIYjTtFx9auzMudJuEZEFGSCkLHPXK2\r\nKQS/BweLvZ13a7j0AwcFlxDgqFUXs4lt78Gq5B0gwuUlVQ9sHNGdKmhmZ6Cs\r\nNrGbN7kke4Awe6rlRPuI7uq6vS6tuLQONYgok8eZHMp5Yx911sN0ui+NJewa\r\nc1m01Z3VHtbkXoulYPRmSsqYuE+Cm5X6eWKxHu+lOI8BA3HC6T1fEApxvg6e\r\ncmYqWTM71NGBV48jCMp/yxg5nNgAs8v05ujJf6IDVICMgNbABjjXMp2W32GM\r\nylJ5hHD+NkrG9KBVs/yRdCVQ9riFgtq1GId/lZUN5TFaMZl6/yRLxkxSbhs5\r\nJRgj9LMZ/oxDAwzUuMOWcpVa3Qb0XL7G2X8QIb9DY7qONUD+krJGpMmTdVSA\r\nghbVI5iv7rEW25ItgqT8u3OPWbKOlL9DiBAtqAqzKtuXBsP47AIo+558l37g\r\nTrRaZb0cqrzCN44m+ubq8ko38KCvOFxYpHk8bBtjI+qd4uVJ3MQec4luH88E\r\nsBuuhwl8fz7WTzPBlBxu+1jdhvHXA2eBULNMw4EG5MXliDrdApquW4QikOmw\r\nuaazxNGJbjkf3miRFKaH3Pql3gb+ruWyLxs=\r\n=SL44\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "9.1.0": {"name": "@testing-library/dom", "version": "9.1.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "0fde60d2199e02ee92a9ea9e84de1e89d8ca573f", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.1.0.tgz", "fileCount": 52, "integrity": "sha512-AMfu6PmhktZTA1eKGJ9jQdzHhhJx/IRHhVYRI79O3/oI1mklCyx0+qQhM9QaNlQ12FoHAKV6Gbjcvr3c8L6LoQ==", "signatures": [{"sig": "MEUCIQD4qX3m0fwBe7pdAMYGkHoa2NMfRQkg+LWpKOs6pENq/gIgNCXJQj9FbIZRugupeNimHHlhi/XznWp3gjBW728jeFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2927990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHec2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDGg//Qt3pH9IvFDuOxJIo6gO2ZEi9KFXLkxyAahyMRBcG95x7Tkeq\r\nlg1UnlSJ4HjRvafBmmGAD9JwhRXxl7tp80SKlNhfJBCYuiG0XqS+vnu52hel\r\nYzcT+RZpDV+dd2HZWmC8LBO2d3iCLanVjxktgBMXnvJDS6gp1+NeFnhnwu7J\r\nci/VHxOlfw1CPPYOMCfWGvch7Hvde87mL2E21TVI66uOgDbirDOZ/8ZhOIpf\r\nw8FhWgMIOuyaZoa2cF7p7wAX485PuD6QMCVY0A3N6KLKiJViy1/vqR1tFyUJ\r\nR9DbLpchsEAjKy3tlGT5TWLzeCIddG7btyjS52+zGFO0lBkhjkuQF0lcMbAn\r\nIikRJqVRMT2KK19Q2aLdFvjtEu8gL9LX60l77Zb75ej4xv7ID2EVqfFC8/el\r\nQ0t2fHfDRmAtXtjY+MXgBawaue6DYrkV3Fk1Lqw+rd5CbukvIBXIxsaBbZwE\r\nrNKcSLg31Lx4oD6dZRZLkOpBxP18SL6z3J2IdXC4SRnsStpOd5IdVP1Adw8o\r\nos3Vdu3IJIyqEZMvfw/jwFbZ41REQ8RHAQyOTRDviWAxnMigOrL5bG2oVQvv\r\nqVwu4eLxgZ1eN8SR7axsQCU/3mYyCt/dUxINABWhzNadfv+xTqToLuVgMGta\r\n9r+B9w/18FVLrKnLoR3CfWYZAD0FOTbq3Rs=\r\n=V6Q4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "9.2.0": {"name": "@testing-library/dom", "version": "9.2.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "0e1f45e956f2a16f471559c06edd8827c4832f04", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.2.0.tgz", "fileCount": 52, "integrity": "sha512-xTEnpUKiV/bMyEsE5bT4oYA0x0Z/colMtxzUY8bKyPXBNLn/e0V4ZjBZkEhms0xE4pv9QsPfSRu9AWS4y5wGvA==", "signatures": [{"sig": "MEUCIQDVlkjMrRHSD/X2LesK469s9bc5ty/Ya5+Rf91qNbiJtgIgKixUB/oqKnaVgFRYs0xJRekMd7gqu5Pj8wrSQwfBnTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2953710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHfAjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsDhAAmw1hFANclBsoD1S7SlnzYc3y9aRFC5QNmqCcreEAI+m/wU3I\r\njmQ1eVKx2tsq8jAHBzwOORT8W4pecGV4RijgkzXhBVixzVR4phJQIhyE5POy\r\nwIRrUwZ7LpMIV15k0e2/jt+lYfr1RQGKwinGc/rtHcgJlbGoySlNCV8Y5C/I\r\nlMo2f/T10F3JyujOVPftYCasLpMgMuHyWYRg4b/R8h6Pb6AZ5RDw6rK6l0t0\r\nXInVkJoVsOJuvMB9W00NUjTXpzL+FWZ3vUJz3Nx25ViWsH2cwPoI2t2QJ12I\r\nvXPmD86s8XW/BR43IgOuHgnSrrWhhwB3d3dwGCluZl22A72gtW5P2LXD3Edt\r\nOzzdpTa9MpwxqYDjraYTmbScuE9OKYvTofhcYkrDSvaI9laVPyHMofcbSPY0\r\nEUainRg3XgwIFRthHV8DCn4wOV25LMhRv/pQ+Oyc3j8592egb+4MOd/HqLEN\r\nUoKAZeefSnv4ODoRdCjiBZcLgi23ZsbNAa8cqJp+sILzLB8g4EPwePm0wAw9\r\nsDLvHm8GAmSSwBywrRdBUIpUDTa86AshgecL9gn2vT7YI1gjrzC4/0dF9aoc\r\n/2dhgvS+OErmWODqOCIz5JBRnChLkpAZKOhsbPBuAH6cs65wWiWKP7bevw2p\r\nYC5AjBPeHAv2KPnnRU8mQ6DX98BtFk7uF3E=\r\n=yw89\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14"}}, "9.3.0": {"name": "@testing-library/dom", "version": "9.3.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "^5.0.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "ed8ce10aa5e05eb6eaf0635b5b8975d889f66075", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.3.0.tgz", "fileCount": 52, "integrity": "sha512-Dffe68pGwI6WlLRYR2I0piIkyole9cSBH5jGQKCGMRpHW5RHCqAUaqc2Kv0tUyd4dU4DLPKhJIjyKOnjv4tuUw==", "signatures": [{"sig": "MEYCIQDpYRHjwiHG0KlGJ4tTJqjyeDqPLH2MFl6VbTY4MWOgCgIhAMBbup574eoSwmtjsiOf/QV7bTIwDMzKPc+/sp8Bu8V8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2953866}, "engines": {"node": ">=14"}}, "9.3.1": {"name": "@testing-library/dom", "version": "9.3.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.1.3", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "8094f560e9389fb973fe957af41bf766937a9ee9", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.3.1.tgz", "fileCount": 52, "integrity": "sha512-0DGPd9AR3+iDTjGoMpxIkAsUihHZ3Ai6CneU6bRRrffXMgzCdlNk43jTrD2/5LT6CBb3MWTP8v510JzYtahD2w==", "signatures": [{"sig": "MEUCIQD0RtvVCKEk+CmslDtDEj1DTSBpjgAsUZsd0ceFi8G1dwIgRMAcRv+3TQCPTUwBjdEKWDhVRdFZCdzy03JD42zhJAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2956041}, "engines": {"node": ">=14"}}, "8.20.1": {"name": "@testing-library/dom", "version": "8.20.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.1.3", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "^16.4.0", "typescript": "^4.1.2", "kcd-scripts": "^11.0.0", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "2e52a32e46fc88369eef7eef634ac2a192decd9f", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-8.20.1.tgz", "fileCount": 52, "integrity": "sha512-/DiOQ5xBxgdYRC8LNk7U+RWat0S3qRLeIw3ZIkMQ9kkVlRmwD/Eg8k8CqIpD6GW7u20JIUOfMKbxtiLutpjQ4g==", "signatures": [{"sig": "MEUCIEFfFzvJyN0w0AD6NAtfmPoaNxVaNZtM0Yy/h/swgpMNAiEAkQWV0jJi8KuJOmdsGpeBUhPAwS/9fPV9mOcU+3Xt0Uc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2927642}, "engines": {"node": ">=12"}}, "10.0.0-alpha.1": {"name": "@testing-library/dom", "version": "10.0.0-alpha.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.1.3", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "525b12eb6f872f19ab381ebcad568edf92c44947", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.0.0-alpha.1.tgz", "fileCount": 51, "integrity": "sha512-3TKTM56f18DCgGdayfWdMMp4HrAkvrOUtfR1BuHtjZVR1WGgN+mdyN4RKlQzY/qLfO+Ug6I4zv7gKcbXN/Gz9A==", "signatures": [{"sig": "MEUCIQDq7vHKuiv19L2k85JbDWDmKy8JRTdrj6HFGEr472BanAIgRAGTf4BAfNDLZkoc1EdR7dBaseR1/uNku0qgcWBY1Rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2951764}, "engines": {"node": ">=18"}}, "9.3.2": {"name": "@testing-library/dom", "version": "9.3.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.1.3", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "36fac080730fc853d4ec6ddfbee54592c7eba398", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.3.2.tgz", "fileCount": 51, "integrity": "sha512-3D5uEQSmzBu3pTF+n5T5bylkbQ1G5xqQpoiJN5zl1CeH2PNP2dIxkftqUm7MEx2Rm+bLRxUHYTyvTUyk/b4M2g==", "signatures": [{"sig": "MEQCIDV4KoEvp9EG68Cm83nBOZR8Rul8lVxWnpWBhVQTR481AiBFgDbkQGZTsVWzvQTFxDWwQ962hKWF2ooKQ8SCJvAksQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2968343}, "engines": {"node": ">=14"}}, "9.3.3": {"name": "@testing-library/dom", "version": "9.3.3", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.1.3", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "108c23a5b0ef51121c26ae92eb3179416b0434f5", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.3.3.tgz", "fileCount": 51, "integrity": "sha512-fB0R+fa3AUqbLHWyxXa2kGVtf1Fe1ZZFr0Zp6AIbIAzXb2mKbEXl+PCQNUOaq5lbTab5tfctfXRNsWXxa2f7Aw==", "signatures": [{"sig": "MEYCIQDVf9J1o8mC3of8yIRmMPBXlfl1EET0coj/znAetUSZhwIhAJqjKabQelbPQFzh/prd6VlnmubMy8nOv4NRvKuQfGzK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2968712}, "engines": {"node": ">=14"}}, "10.0.0-alpha.2": {"name": "@testing-library/dom", "version": "10.0.0-alpha.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "9a74778c08f3f68d316fdc4a3ba7def7e33144cc", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.0.0-alpha.2.tgz", "fileCount": 51, "integrity": "sha512-AbfD1ajW37+4FgShbgQZYSxTEs3A1O7ecqIgT5UHg/OJovXExv1VeeeLclyzPzy9nLckORQ/XIdb8y9jK2ZxwA==", "signatures": [{"sig": "MEQCIFx0w9KCpkeV3mg9lWgwnG8gh66rj+hS93hkCgJts6ODAiA6Qj1D5WvOa3wDG4puapuEVFnFe7AYH/IkwEjlGEqdSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2422707}, "engines": {"node": ">=18"}}, "9.3.4": {"name": "@testing-library/dom", "version": "9.3.4", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.1.3", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "50696ec28376926fec0a1bf87d9dbac5e27f60ce", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-9.3.4.tgz", "fileCount": 51, "integrity": "sha512-FlS4ZWlp97iiNWig0Muq8p+3rVDjRiYE+YKGbAqXOu9nwJFFOdL00kFpz42M+4huzYi86vAK1sOOfyOG45muIQ==", "signatures": [{"sig": "MEYCIQDMT9o+IQTT1vixSJk6s8YjJx57DPhWXzSd14x8j38yswIhAInhrWR6JdRNLZBgqbKu7Cy8VkUNrv/YW0o5w2wmG0ka", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2981001}, "engines": {"node": ">=14"}}, "10.0.0-alpha.3": {"name": "@testing-library/dom", "version": "10.0.0-alpha.3", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "38290859fd9d50c41e596909b4826650a81ac70d", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.0.0-alpha.3.tgz", "fileCount": 51, "integrity": "sha512-8JqicNzhVd26R4+0KlKDhhpXS7GU2j1xO2pJCCm08GAiH8L01QT1ApSGAZfMD6JVE+uqX/0HbWFSMjiB+z9mmw==", "signatures": [{"sig": "MEQCIDxByN3265GDJ4C7wWsxukpTMFop9GKvHMgxEWmZC9cpAiAJcsZ44nhSGRcfo59o+Wno/DZT7nChwkOsE8Fh9aVVcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2422514}, "engines": {"node": ">=18"}}, "10.0.0": {"name": "@testing-library/dom", "version": "10.0.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "ae1ab88aad35a728a38264041163174cafd7e8dd", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.0.0.tgz", "fileCount": 51, "integrity": "sha512-PmJPnogldqoVFf+EwbHvbBJ98MmqASV8kLrBYgsDNxQcFMeIS7JFL48sfyXvuMtgmWO/wMhh25odr+8VhDmn4g==", "signatures": [{"sig": "MEUCIQD+1qqkc2N1OYzG75+BqFXx0p+q7c91unM58Wvs7nZUVgIgHtZnRjVR4hzeEckomw2z9HsgJ1gblbpCXwul1eDUMTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2422506}, "engines": {"node": ">=18"}}, "10.1.0": {"name": "@testing-library/dom", "version": "10.1.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "2d073e49771ad614da999ca48f199919e5176fb6", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.1.0.tgz", "fileCount": 51, "integrity": "sha512-wdsYKy5zupPyLCW2Je5DLHSxSfbIp6h80WoHOQc+RPtmPGA52O9x5MJEkv92Sjonpq+poOAtUKhh1kBGAXBrNA==", "signatures": [{"sig": "MEUCICSENLupcnV8kR5BbKJ3G+cb9n8R2VWd2KIAtj6vIx6qAiEAso+K8CR6S7aY725VQcpHcpRUGevnZG8YMya/+BpAPcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2424713}, "engines": {"node": ">=18"}}, "10.2.0": {"name": "@testing-library/dom", "version": "10.2.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "d3b22515bc0603a06f119c6ae6670669c3f2085f", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.2.0.tgz", "fileCount": 51, "integrity": "sha512-CytIvb6tVOADRngTHGWNxH8LPgO/3hi/BdCEHOf7Qd2GvZVClhVP0Wo/QHzWhpki49Bk0b4VT6xpt3fx8HTSIw==", "signatures": [{"sig": "MEQCICGrn7M40teNZNogeIlVWa5OMk+jisj/DKge7b+mVKx4AiB+5WHh3RmRlrnXHUDFEFIvCD2/R1xzEmhMCYxMPc4nNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2425569}, "engines": {"node": ">=18"}}, "10.3.0": {"name": "@testing-library/dom", "version": "10.3.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "f871a4776432c1bf196e643945800ea65c277aae", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.3.0.tgz", "fileCount": 51, "integrity": "sha512-pT/TYB2+IyMYkkB6lqpkzD7VFbsR0JBJtflK3cS68sCNWxmOhWwRm1XvVHlseNEorsNcxkYsb4sRDV3aNIpttg==", "signatures": [{"sig": "MEYCIQCKKt4bNffJ1DKrphvQIYyFPfy2z0StTrBbHh0GwyiTpAIhANvRxKwiEpIbH0FgTwx1Gd13tBqYbjMH10N+crRZdWn5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2425499}, "engines": {"node": ">=18"}}, "10.3.1": {"name": "@testing-library/dom", "version": "10.3.1", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "c960204cce7e969ac03ae5f3550e420226c61a21", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.3.1.tgz", "fileCount": 51, "integrity": "sha512-q/WL+vlXMpC0uXDyfsMtc1rmotzLV8Y0gq6q1gfrrDjQeHoeLrqHbxdPvPNAh1i+xuJl7+BezywcXArz7vLqKQ==", "signatures": [{"sig": "MEQCIBCJz66KANef8gxmcMKfp0m7TWGdPtTMkXwfDIfeUZCtAiB+KUam77XYYVR/kZPeJt63ad3BuhoXbqCxnhpjU6xASw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2425569}, "engines": {"node": ">=18"}}, "10.3.2": {"name": "@testing-library/dom", "version": "10.3.2", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "0285f643510d5ff4a0b12e4efa7f734a78d80aa3", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.3.2.tgz", "fileCount": 51, "integrity": "sha512-0bxIdP9mmPiOJ6wHLj8bdJRq+51oddObeCGdEf6PNEhYd93ZYAN+lPRnEOVFtheVwDM7+p+tza3LAQgp0PTudg==", "signatures": [{"sig": "MEQCIFo/vXc1uQNEN8yaXdBnw9FNpxxoLS2Q9tZGZ4MXzqQLAiBG4xrmvXKIdtXXQk5kSbMFfZWWutyqJDsSz88jda4FiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2425952}, "engines": {"node": ">=18"}}, "10.4.0": {"name": "@testing-library/dom", "version": "10.4.0", "dependencies": {"chalk": "^4.1.0", "lz-string": "^1.5.0", "aria-query": "5.3.0", "pretty-format": "^27.0.2", "@babel/runtime": "^7.12.5", "@babel/code-frame": "^7.10.4", "@types/aria-query": "^5.0.1", "dom-accessibility-api": "^0.5.9"}, "devDependencies": {"jsdom": "20.0.0", "typescript": "^4.1.2", "kcd-scripts": "^13.0.0", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "@testing-library/jest-dom": "^5.11.6", "jest-watch-select-projects": "^2.0.0", "jest-snapshot-serializer-ansi": "^1.0.0"}, "dist": {"shasum": "82a9d9462f11d240ecadbf406607c6ceeeff43a8", "tarball": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.4.0.tgz", "fileCount": 51, "integrity": "sha512-pemlzrSESWbdAloYml3bAJMEfNh1Z7EduzqPKprCH5S341frlpYnUEW0H72dLxa6IsYr+mPno20GiSm+h9dEdQ==", "signatures": [{"sig": "MEUCIQDWSYg+wE6cJSFXQAwUiVjLy3FsUPSyGGgJuCeoE8W7wAIgaMAg2yGNtmMCVKGDdF0BB4n9INAEiRATdW6ysZGVg+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2427761}, "engines": {"node": ">=18"}}}, "modified": "2024-10-11T16:11:51.962Z", "cachedAt": 1750172039939}