{"name": "@stagewise/toolbar", "dist-tags": {"alpha": "0.3.0-alpha.6", "latest": "0.4.8"}, "versions": {"0.1.0-alpha.1": {"name": "@stagewise/toolbar", "version": "0.1.0-alpha.1", "dependencies": {"zod": "^3.24.3", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.4", "zustand": "^5.0.3", "superjson": "^2.2.2", "tailwindcss": "^4.1.4", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "^2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.1", "postcss": "^8.4.35", "typescript": "~5.7.2", "@types/node": "^22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "workspace:*", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.4", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "workspace:*"}, "dist": {"shasum": "431eae799ec186127f86e5450205f8d9d26a6f77", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.1.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-Y2Wekuw83HIuVHDkxsxB3G8VilaedlEQjkUVxLo4XNQpdhXr+1p1YjrzPYbrB1BzzQksRU3Mpz0GLlx7s1yvMQ==", "signatures": [{"sig": "MEYCIQDQKLqvGusYBxVefwfjyg96iwlQ0ifS5JJIy9KHsEeAFAIhALAG+oiTcxC3MMHbYt99bsM10PB5GUcwEuRpyAOwPX3m", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1350116}}, "0.1.0-alpha.2": {"name": "@stagewise/toolbar", "version": "0.1.0-alpha.2", "dependencies": {"zod": "^3.24.3", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.4", "zustand": "^5.0.3", "superjson": "^2.2.2", "tailwindcss": "^4.1.4", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "^2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.1", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "workspace:*", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.4", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "workspace:*"}, "dist": {"shasum": "c615a8dafd947d1a940983e4579a86e65303afa6", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.1.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-fr5XqJp2HVq9mqRxfwp+ccgkmnR/+Isa25+x4TUbbYxTtiuUnrD/145Q3RByKSfhMgssn8k04ajbgNyHP2o58g==", "signatures": [{"sig": "MEUCIDsytrL1LvNPYTfzsrK5fnXJBlmRq9VxeDHCg669w+myAiEAn+cp1RWYJB3EOjgiRiJlypGP8aFevKwI6CavnNUAqJI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1349785}}, "0.1.0-alpha.3": {"name": "@stagewise/toolbar", "version": "0.1.0-alpha.3", "dependencies": {"zod": "^3.24.3", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.4", "zustand": "^5.0.3", "superjson": "^2.2.2", "tailwindcss": "^4.1.4", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "^2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.4", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "workspace:*", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.4", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "workspace:*"}, "dist": {"shasum": "b95fffa1bcef441d7825a131e7eb87c4bbaf9f23", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.1.0-alpha.3.tgz", "fileCount": 7, "integrity": "sha512-EnqxiAHeWMwqe+XJvqbpEg9KIuYMh6DZ5J8mR72Espc5Cu8wLFcZPrtf3+eltSSG7g9avcpGWyk7TU7BrATkww==", "signatures": [{"sig": "MEUCIQDxhT7v/5X02qnEFAsr3IVe0j9aSgK0xgcC3fSgDVp3pAIgP7maaNCORs9aOhtmkqXQ8KCYEOtLMsco4cXJxeQhVm0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1358671}}, "0.1.0-alpha.4": {"name": "@stagewise/toolbar", "version": "0.1.0-alpha.4", "dependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.6", "zustand": "^5.0.4", "superjson": "^2.2.2", "tailwindcss": "^4.1.5", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.5", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "workspace:*", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "workspace:*"}, "dist": {"shasum": "378d79125668c7cb068ad62100a33985833f32b9", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.1.0-alpha.4.tgz", "fileCount": 7, "integrity": "sha512-+HhDsPsZ1/7DbBFdkiKFsh+fBfLRD1V7gpawhRTEpXKYyfAYP9fM3buvLmyJb1X9BWRIIyanLNfb/d01EMh97Q==", "signatures": [{"sig": "MEUCIGivYvolfezQpHBY0ThQRu84juSQ2dEaXFeXE95fRZyYAiEAlDhAG6PenmkPOqjYcB0FA/DrOxNzNpFEHb8NY6wdZZw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1364797}}, "0.1.0": {"name": "@stagewise/toolbar", "version": "0.1.0", "dependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.6", "zustand": "^5.0.4", "superjson": "^2.2.2", "tailwindcss": "^4.1.5", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.5", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "workspace:*", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "workspace:*"}, "dist": {"shasum": "874cfccc7934b16bae0de3606f30dc5dadf59bce", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.1.0.tgz", "fileCount": 7, "integrity": "sha512-YWWqtlpYjROoh66W2/BMST6IXE6gcrLFaJVI78sExXw+/7i7aj7GZB1D8tNyOzw/dsavMEAo4h3y726PQbvq7g==", "signatures": [{"sig": "MEYCIQC0AHltwz4MUBlT+QbriFdrVJUM2zN98nOMLsukfUq3fQIhAIYrlQllLwJDAWCbUe4SI0QwL+yRTzYKdUYu7dNzV05t", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1364808}}, "0.2.0": {"name": "@stagewise/toolbar", "version": "0.2.0", "dependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.6", "zustand": "^5.0.4", "superjson": "^2.2.2", "tailwindcss": "^4.1.5", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.5", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "0.1.0", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "0.0.1"}, "dist": {"shasum": "3a79a30229c57cf7a3ac8b5c73887efd84b3572f", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.2.0.tgz", "fileCount": 7, "integrity": "sha512-FU9ZVU5RXBA+WIm6VEzDEFxCi8fio+OjQ9syL+9fkWhARQgsvKaC6VtzRScTWZm3KlRNS6J9dSeXfUFRj036wQ==", "signatures": [{"sig": "MEUCIGop5yMhD2EXmPl/eVdSA6bHV+F+mqLh2Qk6559RQYouAiEA/UWX1Cmw/9vI38Vb4oTophHkiyC9A4d1b+SlurHVdsM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1411568}}, "0.2.1": {"name": "@stagewise/toolbar", "version": "0.2.1", "dependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.6", "zustand": "^5.0.4", "superjson": "^2.2.2", "tailwindcss": "^4.1.5", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.5", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "0.1.0", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "0.0.1"}, "dist": {"shasum": "cd56a21f884e5859392e1aab746527c1e89152b2", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.2.1.tgz", "fileCount": 7, "integrity": "sha512-ITA68sqtXRklV6TDDyhT+3GDQqdtZFcZY9WBdPv6XUoATBHcOjpblt0yvQoOuMXAohbBGky4qgbDqxYgLsYbGQ==", "signatures": [{"sig": "MEUCIHrkBs8rmgjkVxH/1GJ7TqIzzLGarU0FsuqDpZkUtwmxAiEAlq2LvpylEBIY4+Cq6//pAm0vwA5WSqabBvwDeCBdPB8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1411566}}, "0.3.0-alpha.0": {"name": "@stagewise/toolbar", "version": "0.3.0-alpha.0", "dependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.6", "zustand": "^5.0.4", "superjson": "^2.2.2", "tailwindcss": "^4.1.5", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.5", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "0.2.0-alpha.0", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "0.1.0-alpha.0"}, "dist": {"shasum": "3ad02189a9f24e7408c86eb0357f51e5816ce374", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.0-alpha.0.tgz", "fileCount": 18, "integrity": "sha512-jprRgi2HzX8EbWyBImjmq2TP7usVYxdm2ZmmMrgviRiQisXTG3witxJnOLKYMcmz/hJRPqLYJQ8aS53/mzWZ2A==", "signatures": [{"sig": "MEYCIQDIkt/WHF+G3UaM30knemZ7G9D2Fl6bmLojNUw6iUKq4wIhAKjjBtCjN4YNAFNQHgrh+FtU3pyEoq+JOuOXoTmzeDxT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1156772}}, "0.3.0-alpha.1": {"name": "@stagewise/toolbar", "version": "0.3.0-alpha.1", "dependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "preact": "^10.26.6", "zustand": "^5.0.4", "superjson": "^2.2.2", "tailwindcss": "^4.1.5", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@headlessui/react": "2.2.2", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2"}, "devDependencies": {"vite": "^6.3.5", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "@preact/compat": "18.3.1", "@stagewise/srpc": "0.2.0-alpha.0", "vite-plugin-dts": "^4.5.3", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "@stagewise/extension-toolbar-srpc-contract": "0.1.0-alpha.0"}, "dist": {"shasum": "75bc481d0ad3033cad693a130ff0d31deb8b71a4", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.0-alpha.1.tgz", "fileCount": 18, "integrity": "sha512-Lci/oSc/on27i89Rh5CHCrKiaDg4LS2QOS7OXK1Tm7elo+PQ/7O2uIulls5LGZCWJtg5QKOl8h3AETk4lsY8ag==", "signatures": [{"sig": "MEUCIQDctJWVxwPwRe3M9nYk7T7Jj9d8dKbbO/5wr3ToK/rB1AIgXO3Bw5R1SWCcXlXohx0CjB4bBVOa0j3Ml8pDrOPeQyc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1156804}}, "0.3.0-alpha.2": {"name": "@stagewise/toolbar", "version": "0.3.0-alpha.2", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0-alpha.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0-alpha.0"}, "dist": {"shasum": "6bd6bc6ae3913372cd625181573b843edb121bbc", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.0-alpha.2.tgz", "fileCount": 18, "integrity": "sha512-sFDoEYa33sLHiky0wGFzGkpOpotO+69zPrllPxSL4Srl7RmzcTplL+/yaOeF9DOIaF+pvT5CDln9L5zciDaccA==", "signatures": [{"sig": "MEUCIG5PaQopXvcDDdwKNkknrjupcNg+n6DadGf/u9OblsB9AiEA4RBvblo0BRNZiQeJKOObOYoROpTBwMYRDaBn9OTg9w0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1156770}}, "0.3.0-alpha.3": {"name": "@stagewise/toolbar", "version": "0.3.0-alpha.3", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0-alpha.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0-alpha.0"}, "dist": {"shasum": "645439f9efdd238aa93ea4633810f050a8c68811", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.0-alpha.3.tgz", "fileCount": 18, "integrity": "sha512-zvtdCOWYduzS+OgK3gy/oslOHimt1UBA65O8v6wEpTS96eCDV0DH+k8fgsYZEP9OJ0VvJ3jUzl3ykFeQ6xjaAg==", "signatures": [{"sig": "MEUCIQDIgdqsYhieW2koGNc3FFeB5BOYcBIQJdV21R4MS1XqmwIgBHkKL8vCTBoBV+YTxFAiT1FYkFAZbZwClaFke0oxgBY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1155742}}, "0.3.0-alpha.4": {"name": "@stagewise/toolbar", "version": "0.3.0-alpha.4", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0-alpha.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0-alpha.0"}, "dist": {"shasum": "d6096cbeb0cedca9395005888fec761b1b8e60bd", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.0-alpha.4.tgz", "fileCount": 18, "integrity": "sha512-37GVYeqmg0YPFPTVIXgurmjuPuieQPa5GcwpH0/k14fIP0FO46scIfgZx6NnwmZGv+15ti1MvJ8pzDwwNjv7dg==", "signatures": [{"sig": "MEYCIQC9kx6BlZi00ATErJl4G4F3HsWMtJoRwd17EyBPA4B+qgIhAMhC57dp7Wrtf7eMeEwQ0R+kY7QZsgJvsWZFRY4LEIU3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1160768}}, "0.3.0-alpha.5": {"name": "@stagewise/toolbar", "version": "0.3.0-alpha.5", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0-alpha.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0-alpha.0"}, "dist": {"shasum": "2919ffc7b2ab83aec303798d5b93ae5f3006c9ac", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.0-alpha.5.tgz", "fileCount": 18, "integrity": "sha512-7dWqWVnFpcFpLWD3fuxxuGRPPGVEau0z+M+L+jYSKXbKBk3Oe/o9oqdEav9dmfLQCWJjFclEqDRf1uoEPTErEw==", "signatures": [{"sig": "MEQCIDhuCNZX5FKZ56Tu8v0/LWokTKncvbUZGWLQnBbLx/FCAiBZhM/x5GRj8eByDZuzVftR3och6umIDB7SXGd3Bl3xhQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1160230}}, "0.3.0-alpha.6": {"name": "@stagewise/toolbar", "version": "0.3.0-alpha.6", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0-alpha.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0-alpha.1"}, "dist": {"shasum": "01cfd2ce7f3b07efeebf60feee1402a0474b5d3e", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.0-alpha.6.tgz", "fileCount": 18, "integrity": "sha512-8HscuIBGOdSNFQLwrHfo5D9sIgjzBTQwRcU6d5k3BsgIoxr1GIb61lM5yLm5kfquDwxpfszZ+7StZsSc514FjQ==", "signatures": [{"sig": "MEYCIQCN0gYdksd2KMFNsJLN06vyrG9djeGQ80OMMg+sBaoxXwIhAIgcoYHcFtnM+arIwbDixV1uJ8FiS+eLfIZo2gAoqNNp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1242942}}, "0.3.0": {"name": "@stagewise/toolbar", "version": "0.3.0", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0"}, "dist": {"shasum": "f4e6e985877e14fe7b5a9e8b7b900f8eb02964b0", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.0.tgz", "fileCount": 18, "integrity": "sha512-NqbFwTAVukmxrJ6euGNZ9C/T9HTXx3/vkufsBqsjm1VC9XEo57JXrLMX4M62ZPdVJHFfe40B2nOVHAaOSKIAdw==", "signatures": [{"sig": "MEUCIDPerCef2ZYHqgzcUc3zRvrDpBWFRlNvKf9vL4xnLbETAiEA8lyeTKe/Pq9dDRthtFSpGUJTHLg/28xkmWkFL+mHl0c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1242858}}, "0.3.1": {"name": "@stagewise/toolbar", "version": "0.3.1", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0"}, "dist": {"shasum": "c61ad740efbcc9d1135f605920c085614f52df04", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.3.1.tgz", "fileCount": 18, "integrity": "sha512-D08oik8tAnUXsA/IJIYsmvHD4TJoHKDsMyqcBX+NjaunqLvY1NVru0oQmJc+9cfxN5enhSx7Aqp2XQYlpXAGIQ==", "signatures": [{"sig": "MEUCIQDVWqOdwvFchfDktVaWcmMzzkryqj8PLeeT4AFR2K2vowIgS+TH8U6SEEB67CEThn1YQ3/ssY41meVTxnKhJCAPEno=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1242895}}, "0.4.0": {"name": "@stagewise/toolbar", "version": "0.4.0", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0"}, "dist": {"shasum": "08b84279b4a5fec591fcb00ed7b3d811ee881e96", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.0.tgz", "fileCount": 18, "integrity": "sha512-d63Z7D4yOD2BQ/UbuCHp5BuJLkEX+r6KKslX3J2f/KdK9EHh8K1HqOE0ynHp1XWEaxTKUSvU93gpVW2V+ycS4g==", "signatures": [{"sig": "MEUCIQC49E45TtuVXzzRQ4BE3YVvUB4uBan5QYU8sjb3S+1/+AIgM8X8hc9C05ZwEbxi3si0CeOWuKd+dKDTqklZnAaklcw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1244927}}, "0.4.1": {"name": "@stagewise/toolbar", "version": "0.4.1", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0"}, "dist": {"shasum": "f4c4fc8cd87857919fd40a70b443cc269787d3e2", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.1.tgz", "fileCount": 18, "integrity": "sha512-8HE0Dxa9XriPS8N2T3xf7ceJ2Zop6OlZLVM2tOP/rSGAD1XuEoRCj4DMB7H01doY//qltifnxihatGdLiuSRpQ==", "signatures": [{"sig": "MEUCIQD6GjlPUFj02dPHQnGQnIfw4kdeeoH/CO1qY0soyF7OUgIgUw1oVLTf9XHn+z1YnKOcOUDchbeyMhgOl6j29kHQsBE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1244461}}, "0.4.2": {"name": "@stagewise/toolbar", "version": "0.4.2", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0"}, "dist": {"shasum": "17899f880700d214241829f96af8b326609c6c4d", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.2.tgz", "fileCount": 18, "integrity": "sha512-MxbuxLLFhqe4+HmktaoWotlj1GlCosl1JFLCo3S260K0tC3nErI+3KzFkkiqh12ewz8QF5+UmnfWMVX9JG1WrQ==", "signatures": [{"sig": "MEQCIG1rySq04Lsu6ziFBJujddZjMqi9KDiX3tsdxS1IgJ5kAiAmpz4ZzTZFI1xaUNDo6V0NmN5DWBMTQ3OPRPK9uO6H4w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1244645}}, "0.4.3": {"name": "@stagewise/toolbar", "version": "0.4.3", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "ua-parser-js": "^2.0.3", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0"}, "dist": {"shasum": "2a41fb75253e400530d01178645869ecb580a34d", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.3.tgz", "fileCount": 18, "integrity": "sha512-e0fTH4rANukY0dHa60C1Yle4oVDkcNoyenQTtiyEXVmwOXMUs7BeAJSPk8Q4IL0X0mOws43U7DGGTxt12CD53A==", "signatures": [{"sig": "MEUCIQCVl+txEZXrHMQKiy1hO5hJ9y1o3fXKvQ5Ua0eWxlKEXwIgRQcnFwIFG8TJ66T+1D7MBb//fv6p2mBl7IukaN/CnUY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1243541}}, "0.4.4": {"name": "@stagewise/toolbar", "version": "0.4.4", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "bowser": "^2.11.0", "preact": "^10.26.6", "postcss": "^8.5.3", "zustand": "^5.0.4", "superjson": "^2.2.2", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.0", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "javascript-time-ago": "^2.5.11", "react-remove-scroll": "^2.6.3", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.0"}, "dist": {"shasum": "e462d36494a144a0ac2c46a8b832e758675f066b", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.4.tgz", "fileCount": 18, "integrity": "sha512-hVxGqeYFx780m9SIv+YqhHx4o/fq94pMwr8OoemOCyOKozCGkvSIjpqkONvUyY5yWR+AVcop2p79LsSTdA6Etw==", "signatures": [{"sig": "MEUCIQDJW/P6hfXx0GyuxlHJcA33gFABlIIWnaV6aaHTiecoHgIgKcF74L8E3Ix77GrAs5P0ayj4RIkycCqa7dNQcAvwauw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1237827}}, "0.4.5": {"name": "@stagewise/toolbar", "version": "0.4.5", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "bowser": "^2.11.0", "preact": "^10.26.6", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.1", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.1"}, "dist": {"shasum": "5f3abea0810265468cb4a167e06cb0fae1d4afd2", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.5.tgz", "fileCount": 18, "integrity": "sha512-FFOuuBnfcSKwSBnfuclgFygd8mvHnJZXgr2KCJofyww5CZxr7sYl0IuSKLngufK+880o5mR0C8IV9lQV9JY6bg==", "signatures": [{"sig": "MEUCIDRj/YZANnpH03uo6OwWdNU+GNnERUOTw3NE+1YKB8jnAiEA9vu/qx/FoGMqzAY80qsTD5ePlRJLG1wNox0xjXiGBBI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 984030}}, "0.4.6": {"name": "@stagewise/toolbar", "version": "0.4.6", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "bowser": "^2.11.0", "preact": "^10.26.6", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.1", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.2"}, "dist": {"shasum": "7fd03ab79c415129b899807f879f106140719939", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.6.tgz", "fileCount": 18, "integrity": "sha512-XMPTuj7yhqjwnr7ap4OHJHA132fRgTf7uU936anlW6eINJcFRx2hWOiHXnc7d4ZssBXiuwJv6mMY3K7UJDaAwg==", "signatures": [{"sig": "MEUCIQCrZRXgwPXZLXexmMzElKC9M4IWJYFtkeDpUrPdp2smOAIgFsjxYtBCe3TaRxK4ZILcfCBrTdHs/2P56iwyz1yqB7Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 999317}}, "0.4.7": {"name": "@stagewise/toolbar", "version": "0.4.7", "devDependencies": {"zod": "^3.24.4", "clsx": "^2.1.1", "tsup": "^8.4.0", "vite": "^6.3.5", "bowser": "^2.11.0", "preact": "^10.26.6", "postcss": "^8.5.3", "typescript": "^5.8.3", "@types/node": "22.15.2", "tailwindcss": "^4.1.5", "autoprefixer": "^10.4.21", "lucide-react": "^0.503.0", "@preact/compat": "18.3.1", "tailwind-merge": "^3.2.0", "@stagewise/srpc": "0.2.1", "vite-plugin-dts": "^4.5.3", "@headlessui/react": "2.2.2", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "vite-bundle-analyzer": "^0.19.0", "postcss-prefix-selector": "^2.1.1", "class-variance-authority": "^0.7.1", "vite-plugin-css-injected-by-js": "^3.5.2", "@stagewise/extension-toolbar-srpc-contract": "0.1.3"}, "dist": {"shasum": "15438416fc5822ec269a5bbadd2076b98e018a67", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.7.tgz", "fileCount": 18, "integrity": "sha512-xU+lDPiGEhFs6Vx5Xwy1y43VQJIul9muN5u0eXnMoFKJYL+ku931at3gTRVP/+4BEgAC4G8P5g3pACyYa0ikOA==", "signatures": [{"sig": "MEUCIEBEy1zX5lSStQiIDE/uZoA+bLLTItnioECYMDcwzvHJAiEArVZEG3sGZ9RMiBBkKpzfBeZ64xpufd1o4fqmI0wjGic=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 999295}}, "0.4.8": {"name": "@stagewise/toolbar", "version": "0.4.8", "devDependencies": {"@headlessui/react": "2.2.2", "@preact/compat": "18.3.1", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "bowser": "^2.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.503.0", "postcss": "^8.5.3", "postcss-prefix-selector": "^2.1.1", "preact": "^10.26.6", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "tsup": "^8.4.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.19.0", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-dts": "^4.5.3", "zod": "^3.24.4", "@stagewise/extension-toolbar-srpc-contract": "0.1.3", "@stagewise/srpc": "0.2.1"}, "dist": {"integrity": "sha512-0ByvC4hYdHHf3rK5M+xSR9mipHYr8naNn2OgDBtv4DE0SoSCr08KfQtZ6VpsBNbOW/Mh1Y4c/AoWcyCTOc2ocA==", "shasum": "744aa2aeccfc41ba1be5e35e63373226ae91ce9b", "tarball": "https://registry.npmjs.org/@stagewise/toolbar/-/toolbar-0.4.8.tgz", "fileCount": 18, "unpackedSize": 999206, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCwANOrtbmnFua3lsnr1bek86zIfFKaORKK9rh6FPoS/QIgUsekMan/+ua6x638DeLj9SGbayNjSubavXlQqNzHiWw="}]}}}, "modified": "2025-06-16T07:34:13.244Z", "cachedAt": 1750172035933}