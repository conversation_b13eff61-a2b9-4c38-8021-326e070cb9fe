{"name": "@vitest/snapshot", "dist-tags": {"beta": "3.2.0-beta.3", "latest": "3.2.3"}, "versions": {"0.30.0": {"name": "@vitest/snapshot", "version": "0.30.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.30.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "462e5fe7bfa009b03f4b53c1903ce27d47f249f0", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.30.0.tgz", "fileCount": 12, "integrity": "sha512-e4eSGCy36Bw3/Tkir9qYJDlFsUz3NALFPNJSxzlY8CFl901TV9iZdKgpqXpyG1sAhLO0tPHThBAMHRi8hRA8cg==", "signatures": [{"sig": "MEQCIGqgkDP+CqtTTDwaBLo5xRleDaMF3njAIxRTgNVCIU1aAiArdQo20b5x1ibuGmA4gxDXuGY5TqdYNwWDvDp2SiLknQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMsAMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGDw//XHyWYUQ/oIL3c6hv0av7wqG67scbp8drtA47OrETRmP2qMya\r\njCrZJEMq60B8BPWRg9QWZ4EOEeZmfyPfODHc/rRTPvB1lRsk5Tbx/xCRozBw\r\ntVpwBVvup4mbAUypna+Kxb45PgkfGCVqg2Ew6L1lx6RPdzzcKK8dEUV+CAJm\r\nHR/aybVyxHliqSz7y/NyP5Vp4IBXnmlDg+EomNKEMD3DeYLBPwyLzt805ItG\r\n/Fu5FpCaFC1E/RNC4dh4S7R7f1pFmN6UjgVKKVHxJIUCAbNCA8NB8QdOI6Xb\r\nUTRLPhvZbg4bETfOLgAZ2vP+Y2FnH/99TCLy/wHxXsXdDdpkjxPzyhPk1nIg\r\nEsoYU4zccAdR2ici9fTl52YCWbBZi2gxT6X0EBrKTAa+VE7qyQtXBSI5mc9i\r\nel1DxpEWT5B/BsnrUz7ijRwuEw1StuJgqVK+7kbc6+A2sSVSVaLv0/5BtYnj\r\nij7Jvg77bFqsiXdRJgcfjXXCeF1ICL+rTGNEikO/Ak71Ee12Pk7LqVQKmNqr\r\ndP7y7hoeUBNqTNDkvL6F+LNc5ezfRYi0r2lrAuXid4RjWR85qlqogSqz04wq\r\nmkOD+3I/RggkEFYU6rpHQfGhIKR7DIQTINTu5Lm2yQispTaeNZcR9DnvruKx\r\navtNc61E8QSAfMfDg4+ky2ikJ4BmxcPJNpw=\r\n=XENv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.30.1": {"name": "@vitest/snapshot", "version": "0.30.1", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.30.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "25e912557b357ecb89d5ee35e8d7c4c7a5ecfe32", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.30.1.tgz", "fileCount": 14, "integrity": "sha512-fJZqKrE99zo27uoZA/azgWyWbFvM1rw2APS05yB0JaLwUIg9aUtvvnBf4q7JWhEcAHmSwbrxKFgyBUga6tq9Tw==", "signatures": [{"sig": "MEYCIQC4fdr7ays1/dZsrTVqtQBm7GqwvGym3F+4w8uZLotQTQIhAP2czyQ0U/oD4LjqNjrQB5D9SJweUaR3eN2zh2dqapTM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNUQEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojpA/9FComV4SasNn3Ky4ooggJlHVFKBwv7atuvZcnH+ksk2jezQkS\r\nQKZuYChZThBGkhK5sJzkUK3PmUR29UCTqRDnOTw4q5JgITb0YoJrQ+4dcVSv\r\ny1RUtvHHQo6G/t+4BZzoB4Q7/BGBZKEzYyBIVS8q0qflXVJI8oU6fXSsPTq/\r\n3gdKWLHqKPXWIkJmoekVZor6637KNoq1FF0bhopojoZAPSYDGHooHG51xMlR\r\n9x1GVLYWbFsUgUoSfjFSf0s1ecbezu26Zpbfoo29r5K5QVnisCmRyxsXwW4D\r\nfmehHCQGylWp7wDE2AQXd5LpaPiIFlJ98Yf2MYNlFRxiUJ6jGsXxGrUG7xPO\r\nLUmuImqX93ovCK0H6m0N2wCesCaR4Q3GqWcax74IE1glYYEiW7LcNXpUhnyW\r\nGzOjvi9HAUOb90yR+P0En16r8vE086cKj1yy+S5xFLgFZ103IkQ2iBaC3Tgi\r\nAJBkMtXkB0J/AsQVQlAmzgdUSb0WQ/Y+htbJuJVfdUu/D7YNucq72XmQcAtb\r\nGWyQdQ4vKwxPvQkMHUKHC1UUkIPPcQdHUeN5m+9wQGsy0FmS2AVX2tNrUI0L\r\nFbWk1M6DOYZoOlmtpWu+bcD/gOX4tzkXc0X0bMXMLKO3HO85QEXVFiWLQy4B\r\n8zrHgZGP3BitrZ9LcX+FsWR0aNTE9Mjmbcg=\r\n=xMtK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.31.0": {"name": "@vitest/snapshot", "version": "0.31.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.31.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "f59c4bcf0d03f1f494ee09286965e60a1e0cab64", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.0.tgz", "fileCount": 14, "integrity": "sha512-5dTXhbHnyUMTMOujZPB0wjFjQ6q5x9c8TvAsSPUNKjp1tVU7i9pbqcKPqntyu2oXtmVxKbuHCqrOd+Ft60r4tg==", "signatures": [{"sig": "MEUCIFAJTMwKqsl80HnDTV4Cp+9L6jRclAa2WRmeXACxLO7IAiEAwSJrQfcUhaJjNv0RT4YEtjA8Rcfvux2IGhQizBXsCe8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUqMyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIhw/7Bukf+gGVq8yZOKZFTzwofgoQQGI+yryStDn49nEQeBII21wX\r\nZ14iJZtzp/JjidqsHa2u1Us7bxoPx5L6XSp1wdalbAuGhL591d5jhChSlSkh\r\nTSF6/a00oaQkDPGEgzLTVHWob11FBnvUwY36xXY5ojPrJz4Koovm4h+i+8dg\r\nqzaWYuEhkvk0+prI78WUjZMSxs66FdGZOO1VXMMnAB4YKnoxvq2Ua9nliSK/\r\nZefqP9fGLlk1WpI2iG/V5YMQ8rukTVT7RAxfCi5KEuIQAojcgWdTjABEX9ao\r\neLRtjYAXU+2Srdf+YhH0I8j4J8P4U2V7VIICJBMxKQJvgHJmerOngmx+v2N8\r\n+/nQm5wsKiJNlEMoT1qIDJjaGWD/yE7boltlooR/ZkLaK4CtNEouiHHSBKk2\r\nwdxQHMh89lVV90DflCFZgmKfP/1lpyjHRC2XXLNXEvoAY96dhJs/kOXzNVG1\r\nHtIB/yyTGA8dno5Cn31ba0/7H+CWlwdRpdVtY0nrxLXlviYs4S97wNyAzhWR\r\n/GqTuQaGGjjSRwxG7XQ631gmPrs2KOYz7YEH/4P9C2K/WKpwBhvI9/U9/D7P\r\nhbjr5cpM5fxLqicjJ9y2M6ZXyZPVTKWmDYc9kJsMuT6lQbOvgNQxz9YJdEIc\r\nI7cudxrnYGXWWD+gShRgQY7TkoiCdbrmqq8=\r\n=nNxk\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": "https://opencollective.com/vitest"}, "0.31.1": {"name": "@vitest/snapshot", "version": "0.31.1", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.31.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "7fc3f1e48f0c4313e6cb795c17a2c1aa909a7d64", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.1.tgz", "fileCount": 14, "integrity": "sha512-L3w5uU9bMe6asrNzJ8WZzN+jUTX4KSgCinEJPXyny0o90fG4FPQMV0OWsq7vrCWfQlAilMjDnOF9nP8lidsJ+g==", "signatures": [{"sig": "MEUCIQDIJ+TdpA80O4/OgswLR8KzsgMhSVUs/nTbnKb43i/+8gIgBNdZTECu1cJkUL3cWcr93EwCti60h/Yo78t+ehu8nmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "funding": "https://opencollective.com/vitest"}, "0.31.2": {"name": "@vitest/snapshot", "version": "0.31.2", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.31.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "061d6d3ec013e008c5e895b60ca5e1bde09b6151", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.2.tgz", "fileCount": 14, "integrity": "sha512-NXRlbP3sM5+KELb8oXVHf7UWD+liBnSsS+4JlDVPD5+KPquZmgNR0xPLW5VEb5HoQZQpKTAFhtGf1AczRCbAhg==", "signatures": [{"sig": "MEYCIQCVRpCaLcuaqCv0C/5jQBzGpvGNJesTvAEyy6WRDGZohwIhAOE4cjOowlHnJgKzH6uPeDH2XB+okNam/hRx9JUJbKnI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "funding": "https://opencollective.com/vitest"}, "0.31.3": {"name": "@vitest/snapshot", "version": "0.31.3", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.31.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "cddaf637c07ee232303da8003f1f2a718eae656b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.3.tgz", "fileCount": 14, "integrity": "sha512-CgcmlZyP083oPUgd83rwLZ+qoT4KdvLNsyWRiJNgHoJjR0r7StVY+LlLAfdC9qA70G8O+hJeug+Yn1u4H51OzA==", "signatures": [{"sig": "MEUCIQDPwwuuPYej2cfOzLzuLQVSqymSKhV6l47dIsZSaDS1GQIgKy0SKJ/BvpzwWoUJT0tV2Sjmnlv4kF7IA8uhwTrgaQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "funding": "https://opencollective.com/vitest"}, "0.31.4": {"name": "@vitest/snapshot", "version": "0.31.4", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.31.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "59a42046fec4950a1ac70cf0ec64aada3b995559", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.31.4.tgz", "fileCount": 14, "integrity": "sha512-LemvNumL3NdWSmfVAMpXILGyaXPkZbG5tyl6+RQSdcHnTj6hvA49UAI8jzez9oQyE/FWLKRSNqTGzsHuk89LRA==", "signatures": [{"sig": "MEQCIFscyRLIZjDYYOp5PQywAPPRqytR+oVfRdzIDPh7F/xoAiAdoE1FEA0XuAbcVF6bD5+m+kWx8rlH+yiN3hlIlnXkUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "funding": "https://opencollective.com/vitest"}, "0.32.0": {"name": "@vitest/snapshot", "version": "0.32.0", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.32.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "49c6a08da12ab903ab4582386c97d70b293bc644", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.0.tgz", "fileCount": 14, "integrity": "sha512-yCKorPWjEnzpUxQpGlxulujTcSPgkblwGzAUEL+z01FTUg/YuCDZ8dxr9sHA08oO2EwxzHXNLjQKWJ2zc2a19Q==", "signatures": [{"sig": "MEYCIQD7YON5HrJXO6qBUgTP5UwIAP2bcHJujDyo1Vxb/2BuaAIhAJoVGOlgN7HFc3lrKUcql9H8v4EBaVvnLvcnGnBcSekW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "funding": "https://opencollective.com/vitest"}, "0.32.1": {"name": "@vitest/snapshot", "version": "0.32.1", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.32.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "e2f6722bdc3b18615cd4f9ff1fa6ca3ba624413f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.1.tgz", "fileCount": 14, "integrity": "sha512-6QsxowDyv6Gm/McSD/6eXBgL3zMSIxBvxC50E7ZYJ+DNfJACJ1w6pykhbnBh9L8igwlFFUPNC1hj+3WD6xF4QQ==", "signatures": [{"sig": "MEUCIQDW4ZST0WJcq3yYpmET3KrOssOBSYwEE4kHNrkpKtMGdwIgFF3M9nCJ846XWIAkO+D2Qoe9v5xBcii2n1xIMBbgadw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "funding": "https://opencollective.com/vitest"}, "0.32.2": {"name": "@vitest/snapshot", "version": "0.32.2", "dependencies": {"pathe": "^1.1.0", "magic-string": "^0.30.0", "pretty-format": "^27.5.1"}, "devDependencies": {"@vitest/utils": "0.32.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "500b6453e88e4c50a0aded39839352c16b519b9e", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.2.tgz", "fileCount": 14, "integrity": "sha512-JwhpeH/PPc7GJX38vEfCy9LtRzf9F4er7i4OsAJyV7sjPwjj+AIR8cUgpMTWK4S3TiamzopcTyLsZDMuldoi5A==", "signatures": [{"sig": "MEUCIQCRfa/EDX0cCcPYCplXeKwZZejm/LhP7WJNkTuFWuJo7wIgZB1QTbJ4cp/xoOAi8W7qdOCoDb7/DPVmPKakmdmHd90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75363}, "funding": "https://opencollective.com/vitest"}, "0.32.3": {"name": "@vitest/snapshot", "version": "0.32.3", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.0", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.32.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "4e43ac739de37daa14e7912f7e0c814ada623126", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.3.tgz", "fileCount": 14, "integrity": "sha512-xNm3aD2gzkxmIKQbST7R1PZoJ7XG/rsQYKG8I8p5CTth2749w0cQQ/45b4dMf+nbwxNI0EEATu1r3szds2Fo9w==", "signatures": [{"sig": "MEUCIQDe5c2q2w/v4L1lSwgFwSs2QAzvm6T7ujdzkEN0P0TbGgIgZ15WFYxJGGg6g6Qe8jXLaPOw+trgl9x/JPSp9pQGqxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76431}, "funding": "https://opencollective.com/vitest"}, "0.32.4": {"name": "@vitest/snapshot", "version": "0.32.4", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.0", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.32.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "75166b1c772d018278a7f0e79f43f3eae813f5ae", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.32.4.tgz", "fileCount": 14, "integrity": "sha512-IRpyqn9t14uqsFlVI2d7DFMImGMs1Q9218of40bdQQgMePwVdmix33yMNnebXcTzDU5eiV3eUsoxxH5v0x/IQA==", "signatures": [{"sig": "MEUCIQCCfArRdtjrKAn6Wy44xrVO1H4VXPGAq9tb4jwRGCyLMQIgNOxB3+vMyU/nWCNtcmgJcZoG1KUUnwhHELnbHNuiK48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76431}, "funding": "https://opencollective.com/vitest"}, "0.33.0": {"name": "@vitest/snapshot", "version": "0.33.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.33.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "4400a90c48907808122b573053a2112a832b3698", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.33.0.tgz", "fileCount": 14, "integrity": "sha512-tJjrl//qAHbyHajpFvr8Wsk8DIOODEebTu7pgBrP07iOepR5jYkLFiqLq2Ltxv+r0uptUb4izv1J8XBOwKkVYA==", "signatures": [{"sig": "MEUCIB68oR7o+0Ln/gTKI3nAe5FK6ZQVTxXgLBvRpBoBAboiAiEAgX0wAMheRpJXeQZ/3iCAVFuGVTkVfoK8PQjWahFrUcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76431}, "funding": "https://opencollective.com/vitest"}, "0.34.0": {"name": "@vitest/snapshot", "version": "0.34.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.34.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "4330c67f1686810b5657b20931d21dbe0b7e2577", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.0.tgz", "fileCount": 14, "integrity": "sha512-eGN5XBZHYOghxCOQbf8dcn6/3g7IW77GOOOC/mNFYwRXsPeoQgcgWnhj+6wgJ04pVv25wpxWL9jUkzaQ7LoFtg==", "signatures": [{"sig": "MEUCIQCAuJj9AuNldn+VV5zf/tWzeWO1sjsNpaVicmYP+tbycQIgewzO0jho67tCUD+ZH9O9DKi6JL76K2a+EBioY4lQC1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105365}, "funding": "https://opencollective.com/vitest"}, "0.34.1": {"name": "@vitest/snapshot", "version": "0.34.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.34.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "814c65f8e714eaf255f47838541004b2a2ba28e6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.1.tgz", "fileCount": 14, "integrity": "sha512-0O9LfLU0114OqdF8lENlrLsnn024Tb1CsS9UwG0YMWY2oGTQfPtkW+B/7ieyv0X9R2Oijhi3caB1xgGgEgclSQ==", "signatures": [{"sig": "MEUCIQD2yzjCQC4+Hn2Eit3WI6y16SmbWO3H9H52vIEf6dJWfQIgWimKoyGTZkd9DnhvGLQZ5SO8LskSnUlWVhYjtX2Euew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105365}, "funding": "https://opencollective.com/vitest"}, "0.34.2": {"name": "@vitest/snapshot", "version": "0.34.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.34.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "fce1b89aa1e836e3fd0229c03ef4ef2f69cb1409", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.2.tgz", "fileCount": 14, "integrity": "sha512-qhQ+xy3u4mwwLxltS4Pd4SR+XHv4EajiTPNY3jkIBLUApE6/ce72neJPSUQZ7bL3EBuKI+NhvzhGj3n5baRQUQ==", "signatures": [{"sig": "MEUCIQCkMYBL6Zn3AF7PGoC+lFIVpBT2czkvFg7Z2q7JUtkhDwIgYO/+1f5OHZNyTDhwM8SMPg7XhZ2WUM+lSqcZRCDw9aw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106179}, "funding": "https://opencollective.com/vitest"}, "0.34.3": {"name": "@vitest/snapshot", "version": "0.34.3", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.34.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "cb4767aa44711a1072bd2e06204b659275c4f0f2", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.3.tgz", "fileCount": 14, "integrity": "sha512-QyPaE15DQwbnIBp/yNJ8lbvXTZxS00kRly0kfFgAD5EYmCbYcA+1EEyRalc93M0gosL/xHeg3lKAClIXYpmUiQ==", "signatures": [{"sig": "MEQCIC5oGR2wbed9nu1gmmSmE30dgQ5Aam8PwFzygmQ5VK6pAiADY31jPyI0Ny5cYFGgn4PGl0+cUA8pvHrtRUREqQvJaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106179}, "funding": "https://opencollective.com/vitest"}, "0.34.4": {"name": "@vitest/snapshot", "version": "0.34.4", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.34.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "ee2c732e5978438f96c669aabb9eb66eb7f3ff46", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.4.tgz", "fileCount": 14, "integrity": "sha512-G<PERSON>h4coc3YUSL/o+BPUo7lHQbzpdttTxL6f4q0jRx2qVGoYz/cyTRDJHbnwks6TILi6560bVWoBpYC10PuTLHw==", "signatures": [{"sig": "MEYCIQCSlX0D0AkNtqO73n8hjndafNE1ZHPafhfKkuRo6YYVUAIhAOHmf9JEQzv+Fz2PNceEx8bcDqErMPlCcK93nk4p2zvA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106179}, "funding": "https://opencollective.com/vitest"}, "0.34.5": {"name": "@vitest/snapshot", "version": "0.34.5", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.34.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "1d81fce3cdc9cf6ad57e86eb5e5eecefc71d1e02", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.5.tgz", "fileCount": 14, "integrity": "sha512-+ikwSbhu6z2yOdtKmk/aeoDZ9QPm2g/ZO5rXT58RR9Vmu/kB2MamyDSx77dctqdZfP3Diqv4mbc/yw2kPT8rmA==", "signatures": [{"sig": "MEYCIQDTICE5qmpF9bHR6plo/5E6u/EgrgMRcItuL9TZRaIdAQIhAILEhJQUpYovW94hcbo48OGWAu5o8V3CxYW2HmkDrTdC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106683}, "funding": "https://opencollective.com/vitest"}, "0.34.6": {"name": "@vitest/snapshot", "version": "0.34.6", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.34.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "b4528cf683b60a3e8071cacbcb97d18b9d5e1d8b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.6.tgz", "fileCount": 14, "integrity": "sha512-B3OZqYn6k4VaN011D+ve+AA4whM4QkcwcrwaKwAbyyvS/NB1hCWjFIBQxAQQSQir9/RtyAAGuq+4RJmbn2dH4w==", "signatures": [{"sig": "MEUCIE2T7NNA4lVmarg9ebRJcGxzNOFWRAamAg6n0CUPsLjaAiEAscnnsh5t3veEHZOFYlsaalJSTP39478XWCH4JSl7tqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106683}, "funding": "https://opencollective.com/vitest"}, "1.0.0-beta.0": {"name": "@vitest/snapshot", "version": "1.0.0-beta.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "1.0.0-beta.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "177e345c670343d86ba66192eb21a26e1ae29ff1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.0.tgz", "fileCount": 14, "integrity": "sha512-yjBZ<PERSON>+DHpqdkZpQ0LDYUCSSTlF293HsTKKLkLAIqmn0Y4XdVV7qgVEebDA612sqsZ+c9lJZ5JP6n/YkWwlGaPQ==", "signatures": [{"sig": "MEUCIH1G9ho16ZH0WGexl+RxpmmRlR86pz/NLIDDdfa+wpHoAiEAwk7hnv6E/kk3xQ2IJgmmRV0K8/nDy0DwJlrycRiQq/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106658}, "funding": "https://opencollective.com/vitest"}, "0.34.7": {"name": "@vitest/snapshot", "version": "0.34.7", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "0.34.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "05af0972e7cba232a88db499f97accdbdbd8d145", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-0.34.7.tgz", "fileCount": 14, "integrity": "sha512-deb30aJWcASuv3H0rHIve9U0y9fKrHXFWIAsjroW1eq5X4+ue6nkcrRCxmh1CHL3nrB738PZXotZdnVrC79j4A==", "signatures": [{"sig": "MEUCIQDzhYpNR//Zl/zN4899/uvfZeSLEAfqjvM4V9OjbwV6ogIgEV6ji6kqNwa2OEyxYVrjzK2IlcDQYqPneteKOXmJWkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106759}, "funding": "https://opencollective.com/vitest"}, "1.0.0-beta.1": {"name": "@vitest/snapshot", "version": "1.0.0-beta.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "1.0.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "6d12e8323ef993a05709771c93dd0414d812f489", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.1.tgz", "fileCount": 14, "integrity": "sha512-3ZBkBJ7O0zLvvAdARD56Srfdk/sgROdBFCUnayGv1/L4ZkwB4GRRl+zuZdT/GhNDT1Q0NMBntbwyIrCSASpY/A==", "signatures": [{"sig": "MEUCIAG5SG303cisdeKrxpJQ9+wwxs89Axh10X/xMBtBHvkRAiEA/kCBGCBSW4YWTqPY1M6cHuGRkmAPOvuTfOf4pZe0x50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106658}, "funding": "https://opencollective.com/vitest"}, "1.0.0-beta.2": {"name": "@vitest/snapshot", "version": "1.0.0-beta.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.1", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "1.0.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "086c2ff26686b839afe394f51bbef54501a7351b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.2.tgz", "fileCount": 14, "integrity": "sha512-ldL0EnnFr8Pkh7j+sY6ffWXN6UlQCEmMxvxaq/REgf7SKy09L2aJtqnTH3PvcCt98JqOESrd1UpcCRvQml5gVw==", "signatures": [{"sig": "MEQCIAmUKb0d3sX7FMFJhXZf7sL8JNAR77A65d4zbiRNxrMYAiAMj2gOfi3PcrFhfgW+mzBWzB8LJJ0B1R7uC9lwmTZNVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106668}, "funding": "https://opencollective.com/vitest"}, "1.0.0-beta.3": {"name": "@vitest/snapshot", "version": "1.0.0-beta.3", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "1.0.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "121e5f926cece32fc4f102509b91d1e4eaffbae6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.3.tgz", "fileCount": 14, "integrity": "sha512-fun2daxMGuXo2wBi6ItFgUFl0xnN4v2hVXvIYGvoJG9SoPobfPlQ79MO8gMKOt++rWlUXNj7J23KHcoOyZ2Wwg==", "signatures": [{"sig": "MEUCIQDAoL/LWQ9VupbXB9Xhr0lMlX+oVuqRe7vYM4VWvisKlQIgP1AO29OecNaebM8fGpTCU5k5KrJBmQXVebTEERq9O+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115014}, "funding": "https://opencollective.com/vitest"}, "1.0.0-beta.4": {"name": "@vitest/snapshot", "version": "1.0.0-beta.4", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.5.0"}, "devDependencies": {"@vitest/utils": "1.0.0-beta.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.1"}, "dist": {"shasum": "ea9015bdd099b48f97ebcd06001a7f179dfe6731", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.4.tgz", "fileCount": 14, "integrity": "sha512-CzmHLGo4RNEQUojYtuEz8wWKp9/p3hvXskejRRJB1iCRH48uWROmoyb2iEQUhgpQw/+WwI4wRP7jek5lp48pRA==", "signatures": [{"sig": "MEUCIG+jutq0pqG5ewBGRO2bLVYEgPCq51zERvXZ4y6khUOTAiEAiNXqfb2SKwVpWyWk5WiSiwjq1nDCMExNaelpGpo/dYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115410}, "funding": "https://opencollective.com/vitest"}, "1.0.0-beta.5": {"name": "@vitest/snapshot", "version": "1.0.0-beta.5", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.0.0-beta.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "ce18b75979003f28a19e472289316cb3ae43ffa1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.5.tgz", "fileCount": 13, "integrity": "sha512-fsWoc/mokLawqrLFqK9MHEyzJaGeDzU5gAgky2yZJR58VSsSvW+cesvmdv9ch39xHlTzFTRPgrWkNsmbdm2gbg==", "signatures": [{"sig": "MEQCIFTPK3Tw5V47ygqFzZS1xYwM/f9OxhtdJ2H7ttrPPJhLAiB7RedvEFBEQLb2jENdlIT0UQNvgxJ1bvHVMcohWLRhiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65445}, "funding": "https://opencollective.com/vitest"}, "1.0.0-beta.6": {"name": "@vitest/snapshot", "version": "1.0.0-beta.6", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.0.0-beta.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "f43ab9481f0a879474f0d1ae1190ef735fedf69a", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0-beta.6.tgz", "fileCount": 13, "integrity": "sha512-znkf7R67aanWHjAfx+/Yj5VwIdUHU1I1akJFL8lguiu5C6C12D4ICHeMjRBc7e7BrIguBWZFfqPx/ZUSxqMTfA==", "signatures": [{"sig": "MEUCIEckDYreJYVH8lWjT/DRjU4oQrZLg0N2upjVo6Ev0kbNAiEAy1DSSzyg52U11ta0zTe8oOstsTno/DcoR6hZXu2Tnk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65445}, "funding": "https://opencollective.com/vitest"}, "1.0.0": {"name": "@vitest/snapshot", "version": "1.0.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.0.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "2a7104b85b932a2dd7cf99afc3a2af33983cd5ba", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.0.tgz", "fileCount": 13, "integrity": "sha512-kAcQJGsaHMBLrY0QC6kMe7S+JgiMielX2qHqgWFxlUir5IVekJGokJcYTzoOp+MRN1Gue3Q6H5fZD4aC0XHloA==", "signatures": [{"sig": "MEYCIQD+g9FlgWAIJYVtefTmyB4AMcpjGwPs31YM8hcImYuhowIhAJ328qqrOLK0J25smPufutqxHAXC6PZB7lTMyga/cu0n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "funding": "https://opencollective.com/vitest"}, "1.0.1": {"name": "@vitest/snapshot", "version": "1.0.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.0.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "9d2a01c64726afa62264175554690e5ce148d4a5", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.1.tgz", "fileCount": 13, "integrity": "sha512-wIPtPDGSxEZ+DpNMc94AsybX6LV6uN6sosf5TojyP1m2QbKwiRuLV/5RSsjt1oWViHsTj8mlcwrQQ1zHGO0fMw==", "signatures": [{"sig": "MEUCIBacrrQF+5XrgZdEn/mpGoxpoZm/pxXWzCiDBnsiO5dTAiEAsnli5+70UyfQc6btDDI6JWteZaFuBBCBQsTO3/E/NhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "funding": "https://opencollective.com/vitest"}, "1.0.2": {"name": "@vitest/snapshot", "version": "1.0.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.0.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "df11b066c9593e3539640a41f38452a6b5889da1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.2.tgz", "fileCount": 13, "integrity": "sha512-9ClDz2/aV5TfWA4reV7XR9p+hE0e7bifhwxlURugj3Fw0YXeTFzHmKCNEHd6wOIFMfthbGGwhlq7TOJ2jDO4/g==", "signatures": [{"sig": "MEYCIQCGbVnO+suk+6A9Io7FUvdlFbjmkMXgTXUCudL5nzHtbgIhAPMFhGDXoCA9Jom5B5luFqrg6OsGgfJ3M2UCRD82kYYR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "funding": "https://opencollective.com/vitest"}, "1.0.3": {"name": "@vitest/snapshot", "version": "1.0.3", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.0.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "98524b3494a0a50db3a5b5effe82ce5640dcc01f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.3.tgz", "fileCount": 13, "integrity": "sha512-2EQwVEuHusEXr0SKuFiI1JVlysSrUceejtusr6vK254tusAz/g4//QrAiD1b7PMdcUKM8QmdgWvqCMaYDsWyNA==", "signatures": [{"sig": "MEUCIQCrqeFPTEyz2lqGqybmZGfv9B+nn8fMC+VRpY6u/tgpxgIgGIQm9mu8ZTUhNHXlyez3HXrDpp3nXa+EeFdyqOy/WY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "funding": "https://opencollective.com/vitest"}, "1.0.4": {"name": "@vitest/snapshot", "version": "1.0.4", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.0.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "7020983b3963b473237fea08d347ea83b266b9bb", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.0.4.tgz", "fileCount": 13, "integrity": "sha512-vkfXUrNyNRA/Gzsp2lpyJxh94vU2OHT1amoD6WuvUAA12n32xeVZQ0KjjQIf8F6u7bcq2A2k969fMVxEsxeKYA==", "signatures": [{"sig": "MEUCIH4wrkKAWzHM1KlqRxeeOFWCLBkG3aqnA0xZcDY0c556AiEAji2YL8FJkOQhI5JrPmzaGiFLrEeZe6ZU6cSBJBwPnLg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "funding": "https://opencollective.com/vitest"}, "1.1.0": {"name": "@vitest/snapshot", "version": "1.1.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.1.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "b9924e4303382b43bb2c31061b173e69a6fb3437", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.1.0.tgz", "fileCount": 13, "integrity": "sha512-5O/wyZg09V5qmNmAlUgCBqflvn2ylgsWJRRuPrnHEfDNT6tQpQ8O1isNGgo+VxofISHqz961SG3iVvt3SPK/QQ==", "signatures": [{"sig": "MEQCICnIne8+b+lgzOtM8c1SrS8ZoDDxC8JMadHrf1EIuH+jAiBys13dgSRUnpxvLms0sVXIK14fcq1/+zEFeXqTnBPBug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "funding": "https://opencollective.com/vitest"}, "1.1.1": {"name": "@vitest/snapshot", "version": "1.1.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.1.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "40261901102e131cb09f23034884ad2c1c5af317", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.1.1.tgz", "fileCount": 13, "integrity": "sha512-WnMHjv4VdHLbFGgCdVVvyRkRPnOKN75JJg+LLTdr6ah7YnL75W+7CTIMdzPEPzaDxA8r5yvSVlc1d8lH3yE28w==", "signatures": [{"sig": "MEUCIGN0qXzfDyJmHfq94ykpRAZMZDyKPepqjPcZtX+cmM82AiEAyoQKxMCkR9qqbGfN57qBOnaTXuaMPBi0soHB699kX24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65740}, "funding": "https://opencollective.com/vitest"}, "1.1.2": {"name": "@vitest/snapshot", "version": "1.1.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.1.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "5628084d21423a55fb0be8e402ecfd1fd1240b51", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.1.2.tgz", "fileCount": 13, "integrity": "sha512-hXXd5KjURGt6GCrmw55A+PNIlrOaE6x6KcdEANXac76xmvVbJZXSiNVJ1JuMCiyvLLTzdpPnrgWyCX9/CepFCQ==", "signatures": [{"sig": "MEUCIQCnLoSB2TWO07fdaFH9Q4CLGelQ4Kv/mdNT4PQGU51XkgIgItbC5vPaVmbGNJZzvz8EfwEBvppWzpTE4V0ItFg7KaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65629}, "funding": "https://opencollective.com/vitest"}, "1.1.3": {"name": "@vitest/snapshot", "version": "1.1.3", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.1.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "94f321f80c9fb9e10b83dabb83a0d09f034a74b0", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.1.3.tgz", "fileCount": 13, "integrity": "sha512-U0r8pRXsLAdxSVAyGNcqOU2H3Z4Y2dAAGGelL50O0QRMdi1WWeYHdrH/QWpN1e8juWfVKsb8B+pyJwTC+4Gy9w==", "signatures": [{"sig": "MEQCIFcuVHO5l7ygeZUBTDXUBSZJAzbbbOHFpZ6tLcHy+OebAiBhNHVSC6rrjN7dLwBR4NDmU4sGTZiuy+oEovmgOkSuvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65629}, "funding": "https://opencollective.com/vitest"}, "1.2.0": {"name": "@vitest/snapshot", "version": "1.2.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.2.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "2fcddb5c6e8a9d2fc9f18ea2f8fd39b1b6e691b4", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.2.0.tgz", "fileCount": 13, "integrity": "sha512-P33EE7TrVgB3HDLllrjK/GG6WSnmUtWohbwcQqmm7TAk9AVHpdgf7M3F3qRHKm6vhr7x3eGIln7VH052Smo6Kw==", "signatures": [{"sig": "MEUCIQC3x1FeN9d5nG5wEd9volI5eeUOMRTzS/Jigg/aFVQCUQIgS/DKnOiXXp0plHNB7gkchEMv1f/96tKoj2jU/Tto82I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66579}, "funding": "https://opencollective.com/vitest"}, "1.2.1": {"name": "@vitest/snapshot", "version": "1.2.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.2.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "bd2dcae2322b90bab1660421ff9dae73fc84ecc0", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.2.1.tgz", "fileCount": 13, "integrity": "sha512-Tmp/IcYEemKaqAYCS08sh0vORLJkMr0NRV76Gl8sHGxXT5151cITJCET20063wk0Yr/1koQ6dnmP6eEqezmd/Q==", "signatures": [{"sig": "MEUCIF+T+fdDd9DBG2xSQEyOyaNkDOaBQQQP+4dUlvIjElfIAiEAvNX4ibjwOVR/bTJW3j1H0WJj6dCqRd5tI4Cm8o63y8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66579}, "funding": "https://opencollective.com/vitest"}, "1.2.2": {"name": "@vitest/snapshot", "version": "1.2.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.2.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "f56fd575569774968f3eeba9382a166c26201042", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.2.2.tgz", "fileCount": 13, "integrity": "sha512-SmGY4saEw1+bwE1th6S/cZmPxz/Q4JWsl7LvbQIky2tKE35US4gd0Mjzqfr84/4OD0tikGWaWdMja/nWL5NIPA==", "signatures": [{"sig": "MEUCIQCZzXVII7OSDQcRR7D7VrtRbHNUdctYlhKEAYcDce0WHwIgCNX+eUBDGsj84rTa+NbsjZmgODsQRFJMah0VhNDFDGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66579}, "funding": "https://opencollective.com/vitest"}, "1.3.0": {"name": "@vitest/snapshot", "version": "1.3.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.3.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "016b34289d87ef0c64f4cdb9173086c2edf1db7b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.3.0.tgz", "fileCount": 13, "integrity": "sha512-swmktcviVVPYx9U4SEQXLV6AEY51Y6bZ14jA2yo6TgMxQ3h+ZYiO0YhAHGJNp0ohCFbPAis1R9kK0cvN6lDPQA==", "signatures": [{"sig": "MEUCIQDO5AI6gOpp542C/u/vvw/bULNYE9JjRpZ9hqvyTmL4WgIgedbYxQlkcW7NOlRUc0xQwDtFNEdb8cmbreSPHtIaU4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66700}, "funding": "https://opencollective.com/vitest"}, "1.3.1": {"name": "@vitest/snapshot", "version": "1.3.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.3.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "193a5d7febf6ec5d22b3f8c5a093f9e4322e7a88", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.3.1.tgz", "fileCount": 13, "integrity": "sha512-EF++BZbt6RZmOlE3SuTPu/NfwBF6q4ABS37HHXzs2LUVPBLx2QoY/K0fKpRChSo8eLiuxcbCVfqKgx/dplCDuQ==", "signatures": [{"sig": "MEUCIHV1zpUwsVNSYcs+KeVZ20Fblbr8wEuOu+uUGGP8YPfyAiEA5at23sI1UQes4UTkRKDzWTA702g4z4Ue9tGmuQNsZzY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "funding": "https://opencollective.com/vitest"}, "1.4.0": {"name": "@vitest/snapshot", "version": "1.4.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.4.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "2945b3fb53767a3f4f421919e93edfef2935b8bd", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.4.0.tgz", "fileCount": 13, "integrity": "sha512-saAFnt5pPIA5qDGxOHxJ/XxhMFKkUSBJmVt5VgDsAqPTX6JP326r5C/c9UuCMPoXNzuudTPsYDZCoJ5ilpqG2A==", "signatures": [{"sig": "MEUCIEEAIBI3MaYPmMOzyF3HrRNIe1RZNs2XGTj2RG4bB0/fAiEAuz68ILqJeVJhtqIovmqhH8Oim3361XtrTOt0+4NvSRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "funding": "https://opencollective.com/vitest"}, "1.5.0": {"name": "@vitest/snapshot", "version": "1.5.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.5.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "cd2d611fd556968ce8fb6b356a09b4593c525947", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.5.0.tgz", "fileCount": 13, "integrity": "sha512-qpv3fSEuNrhAO3FpH6YYRdaECnnRjg9VxbhdtPwPRnzSfHVXnNzzrpX4cJxqiwgRMo7uRMWDFBlsBq4Cr+rO3A==", "signatures": [{"sig": "MEYCIQCgEh2nd1mdqudVxgPY8kM+iLdG/1vdHjCHNS3HQfq9VgIhAIkF9FeWd9tnYCBeoaAvzPlN0ZjiXvzdo05l0LEk1/oa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "funding": "https://opencollective.com/vitest"}, "1.5.1": {"name": "@vitest/snapshot", "version": "1.5.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.5.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "56f459f9327567378ee111384fadcda91d8e6be3", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.5.1.tgz", "fileCount": 13, "integrity": "sha512-h/1SGaZYXmjn6hULRBOlqam2z4oTlEe6WwARRzLErAPBqljAs6eX7tfdyN0K+MpipIwSZ5sZsubDWkCPAiVXZQ==", "signatures": [{"sig": "MEQCIFY4uYL8ljwyQiDN+yiUUCortI0b+jcVctuzlJ/hES8+AiAmnx3yFYedrZkVl/v3QsYJ3Yh0uD5exYZOzpc4c1nrUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.5.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "funding": "https://opencollective.com/vitest"}, "1.5.2": {"name": "@vitest/snapshot", "version": "1.5.2", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.5.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "d6f8a5d0da451e1c4dc211fcede600becf4851ed", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.5.2.tgz", "fileCount": 13, "integrity": "sha512-CTEp/lTYos8fuCc9+Z55Ga5NVPKUgExritjF5VY7heRFUfheoAqBneUlvXSUJHUZPjnPmyZA96yLRJDP1QATFQ==", "signatures": [{"sig": "MEQCIFcNCfQC6CnJJeK7sKj0YRNZI/ZNkHF4huKilIFkMbpJAiAeTnM5q+p7hUNVoAqfTX5d+EvIKLo0acRIhuN95WZi/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.5.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "funding": "https://opencollective.com/vitest"}, "1.5.3": {"name": "@vitest/snapshot", "version": "1.5.3", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.5.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "ffdd917daebf4415c7abad6993bafd5f4ee14aaf", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.5.3.tgz", "fileCount": 13, "integrity": "sha512-K3mvIsjyKYBhNIDujMD2gfQEzddLe51nNOAf45yKRt/QFJcUIeTQd2trRvv6M6oCBHNVnZwFWbQ4yj96ibiDsA==", "signatures": [{"sig": "MEUCIQDwmif68crclsukmJSwAnomzoxvo0mjvHOZXoXoBEZ8sAIgG11+D5E/ayV4/V4LXWrf9KGw+cbVY+AAaKtar4hoxHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.5.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "funding": "https://opencollective.com/vitest"}, "1.6.0": {"name": "@vitest/snapshot", "version": "1.6.0", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.6.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "deb7e4498a5299c1198136f56e6e0f692e6af470", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.6.0.tgz", "fileCount": 13, "integrity": "sha512-+Hx43f8Chus+DCmygqqfetcAZrDJwvTj0ymqjQq4CvmpKFSTVteEOBzCusu1x2tt4OJcvBflyHUE0DZSLgEMtQ==", "signatures": [{"sig": "MEUCIFFUAtS4rXM79ZQDi9I7oIGXhy/ebLPrHF2UC+DQhBYBAiEA4wluq5K4ltyKCbzZLq7mP2R+WDmNCtLvrpQcJDPQA4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.1": {"name": "@vitest/snapshot", "version": "2.0.0-beta.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "18f841d58b9c0014a6ef9ca394f97874386b5fa6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-DbzrV6fQtB1jPvlL/eW+bEYWY5MMGPcTr3isvrJHo2NtTt1pntm01kj9IbLIFyp5ew1gxqFQ19M0YowwiWv8Yw==", "signatures": [{"sig": "MEYCIQC+JolREeOFiOrsvzTibJVe2lXGuFKyxwGDcW6DkclQ7wIhALMAaTCpDURAh0RiOxWUNTxWI68MpkOkaJfQhgPgWjgS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66873}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.2": {"name": "@vitest/snapshot", "version": "2.0.0-beta.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "9510cf3681913ed5463b263998aef71ca4b7e4ab", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-011YsVfsi1i4s4Csptj+YqDmt4jr5tZlhe8uOVRdbICfuqg8y1xe2d5QnBVgxrJTXa+pX16z6aSUjU+oUTV/ZA==", "signatures": [{"sig": "MEYCIQCytaqb0POixRyZhiDgnOKE1WvWuJvbm+pcTQCyTwGC1AIhAKg8jrFa6G1nRkImEeLoQh/CQTRbArwYkEmlOxYb/GHD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66873}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.3": {"name": "@vitest/snapshot", "version": "2.0.0-beta.3", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "99d384bd90bab46c74edd83211d5356078f6a77d", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-39Clmuu7TQjcOBMs+gbWt3T93Ud+gxa3iTaI0DaTgXjQZLu03qeAhYS6iOyw257A6Nt2+TSGZhTlqm3wyYbl2Q==", "signatures": [{"sig": "MEUCIQCd17coR/BpomJnrMgOGecl4IlnsLY6TEibqKrZQawBJgIgB057fxVn6Kt1Tc+m+gMzP0yIiK1SEia6bGdd95gvhSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67029}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.4": {"name": "@vitest/snapshot", "version": "2.0.0-beta.4", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "f16dbc64ae63496a2f110775e15ba3f58cedc5c1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-enIHNZ59Fgj75jrCrFnvn1dSm2yybkEsW0PboMvEWDAz4JO7CdzrvOzqSns7zMWnkPqgLB7//Ts372me0aGLOA==", "signatures": [{"sig": "MEUCIQDGLqe3gPjcZFkX8wqJa9RBv8nPTHFkU1shu1A7J+1BIAIgM9PHStZeJ2pjD9r27Sh+PceZx+iAx+Kag4kCe9eQrsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.5": {"name": "@vitest/snapshot", "version": "2.0.0-beta.5", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "ed2c1ba86a68744726e13a91b0d7e5684fc669e5", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.5.tgz", "fileCount": 13, "integrity": "sha512-+KkkOX/WoUxUP1UNwhPLvIjNJ5YFGODS8giJWQQ9BnAp2kotuEsmehhUia59rzLlq42Ql+6YTohACj+0fVCxbw==", "signatures": [{"sig": "MEYCIQCYZFboAd1tLJOzAL18BdKErxJ7pRb+mVdLd+JMetnr1gIhAOYA++EyKWXAkPAifvjodJZcqYzdGjVKGJbLLswknmhU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.6": {"name": "@vitest/snapshot", "version": "2.0.0-beta.6", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "229a5f04816a9c83faeb85b8ce4330032762004c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.6.tgz", "fileCount": 13, "integrity": "sha512-fHtm24q05OLGI74z2ICRfJ0FvhdXlnFZlmzwDV6mdO2Mf1t2GQLrZ1+622X5uhSGEcwKJm9VSuD5jJJ45FyqYQ==", "signatures": [{"sig": "MEUCIQD0HE+IDDeV77mMQA396ZPP1KHlzZEf5vh4KmiG+o2X1QIgCFOSPSzaP7ZgR+KgkAeT//9VCmQBxgQJ9E+6E39PcI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.7": {"name": "@vitest/snapshot", "version": "2.0.0-beta.7", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "92b57ab48a07f358c336f091030e283ff8fb4ebe", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.7.tgz", "fileCount": 13, "integrity": "sha512-ilFSwedpQ3d9Ue136a5iVRboZ7Pyhwm1mSRHeUHIkVg5gAW/XqgeewQKvn1Gl7MqmFL+mfCnon9r9o2wC7pfWg==", "signatures": [{"sig": "MEUCIFJKfS9Qf58RoU7Gow7je9H5g2Lux69k1yZo56SS5LqdAiEA9oDP5BrTRbWzSaXUnnX7FOThVb3c2fGp6CagSzI1uCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.8": {"name": "@vitest/snapshot", "version": "2.0.0-beta.8", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.8", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "c2cfb8cb4b0b245bc34d08823fc9dd2b0b0e490f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.8.tgz", "fileCount": 13, "integrity": "sha512-dUv6FE1qlGvLhLiLqTr2YcxohX/eevobaRsGJRll/NqUot4ugB46N1pTxx+VCiyVETuTkr+hjOWJvyxfWpXtdg==", "signatures": [{"sig": "MEQCIDZbcuu6Uu1RYvif/MbBrP8RTCerpqHk2aDWCF03c3GPAiBJOUH86h4S7LcaK5MlgHQ/1N4TLYhzcF/d9Z+mkx/bUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.9": {"name": "@vitest/snapshot", "version": "2.0.0-beta.9", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.9", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "e2eea75e7aa6169ba7b46cd26d0ebdb47b86b313", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.9.tgz", "fileCount": 13, "integrity": "sha512-ZVyTIN4WnflZs5GEoWdU7/24zRNbzgLH+mAVvANs4wNRMAcDhdn+MkqXTQlKSzARN3WhGrMfvx7KXwO8pK1pxw==", "signatures": [{"sig": "MEYCIQCjIAAmL+kCK1pFZc/JBweC7aCIVZ+tER25WsMzYmivjAIhAI9zb0p4Uyq7GSRzkUq/90oVYYLAHl4Q34XlAMR6brXu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67142}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.10": {"name": "@vitest/snapshot", "version": "2.0.0-beta.10", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.10", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "87c72e8cfb87b51bc653997a4396ab089f8e38f2", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.10.tgz", "fileCount": 13, "integrity": "sha512-SvvnBwcGsxI++T0WF8Py5LGCzJE2s5x2Bs6hCTiGVZPqmCrmCLVPeRQCLzYyX3LVRUHgkqgfmP7grUHDKusMJg==", "signatures": [{"sig": "MEYCIQCGZhcOtReSPN3jJaAqhY0iZfxbYOAOSR9IwWYV7aAy1wIhAMZrIVcpAJdi5JhkK2Euo5RHGLVldB2eh1v6V3RHGDAp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 67221}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.11": {"name": "@vitest/snapshot", "version": "2.0.0-beta.11", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.11", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "b77326f1e19d48cdf2e0b42a1e0e72cb1f40c0cc", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.11.tgz", "fileCount": 13, "integrity": "sha512-F3LRgdEy0o3mFea4E9KpPyrYVoHhaXjOyMHz9f4Ie2OFZ89BH+750lX1Hp/69MgLwfaIziQP1NHbxOuAmXYU6g==", "signatures": [{"sig": "MEQCIGfhp6Yf00+x3mQuowm3nHF9XEFfhfh7JPY+9y0fKI+7AiANhOL+gRLyPK5FN84uOVhqeTV2MID9QymnaZii5gqLUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69169}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.12": {"name": "@vitest/snapshot", "version": "2.0.0-beta.12", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.12", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "510f24f9ab72a98b1c7fab3b556650fe11da3a9c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.12.tgz", "fileCount": 13, "integrity": "sha512-NBqn1rTNQ/e3Dsw8LnniHgeZslgIxg8UvSfje/QV3hJLSoLMLbKLopHmK9T2FQA0hcibAaq/TZVyVrBoX+6aig==", "signatures": [{"sig": "MEQCIA1W6T21ND7sY8Habc9XZ8Sonn5OGJkuaH/jvMVtEIhQAiA71/oGCDeLz988eTfrXQt84d7RQCG5Rgjz3PpFhy4iXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69227}, "funding": "https://opencollective.com/vitest"}, "2.0.0-beta.13": {"name": "@vitest/snapshot", "version": "2.0.0-beta.13", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0-beta.13", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "5dd8997a5de96238a917d8e08e7bb14e68ecaa2b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0-beta.13.tgz", "fileCount": 13, "integrity": "sha512-avQ7e6LoRKjmA0Fv4Iw9fxvFf6I+XtoCObTl0EdCMdrzGUMSPjFx1fYz5NaCd7iuGW3fQrBpmm/TV/Sd9i8gtw==", "signatures": [{"sig": "MEQCIGSp0mxWgVayXbPCgMkWlbN4y3Vdrg0YugjdXLDnZTOCAiB1Hpt5IS12juXKYrLqxf77hQRoQbARH/gPX57ewSbkWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0-beta.13", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69318}, "funding": "https://opencollective.com/vitest"}, "2.0.0": {"name": "@vitest/snapshot", "version": "2.0.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "728d70f22de9e32e82b6ac41ebbe54d8574244bf", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.0.tgz", "fileCount": 13, "integrity": "sha512-B520cSAQwtWgocPpARadnNLslHCxFs5tf7SG2TT96qz+SZgsXqcB1xI3w3/S9kUzdqykEKrMLvW+sIIpMcuUdw==", "signatures": [{"sig": "MEYCIQCmEMGMY+EN+lf51zRxg9hYN7Pd0rkdzbTEZAPu61tc+gIhAJ1XQ7JHvW9CZjY3QxxH9J+VmyhDXBooQGtRn6XVZZfn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69302}, "funding": "https://opencollective.com/vitest"}, "2.0.1": {"name": "@vitest/snapshot", "version": "2.0.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "2.0.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "67112ac546417657f7802456d329f62ead07deab", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.1.tgz", "fileCount": 13, "integrity": "sha512-rst79a4Q+J5vrvHRapdfK4BdqpMH0eF58jVY1vYeBo/1be+nkyenGI5SCSohmjf6MkCkI20/yo5oG+0R8qrAnA==", "signatures": [{"sig": "MEQCIDamZnXVah3q+PDHm0Zp3f/btQYvjR9MyL3N0AoYimIQAiBgX12JF2fs09YJ9OjNO1Uyl4HvKv3F/c1GCG3xpND50w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69302}, "funding": "https://opencollective.com/vitest"}, "2.0.2": {"name": "@vitest/snapshot", "version": "2.0.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "@vitest/pretty-format": "2.0.2"}, "devDependencies": {"@vitest/utils": "2.0.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "91a8b847d82d92d06b9bf70b72bb9f21a4a416a1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.2.tgz", "fileCount": 13, "integrity": "sha512-Yc2ewhhZhx+0f9cSUdfzPRcsM6PhIb+S43wxE7OG0kTxqgqzo8tHkXFuFlndXeDMp09G3sY/X5OAo/RfYydf1g==", "signatures": [{"sig": "MEYCIQDVpswh+eiGc3XOedh97AzKrUsUVL6rBFiF2jSnDzkIMwIhAJE+QMDhHgJHdbGPh20uSsadliAXt+UAGj3GJS9WBBkl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71427}, "funding": "https://opencollective.com/vitest"}, "2.0.3": {"name": "@vitest/snapshot", "version": "2.0.3", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "@vitest/pretty-format": "2.0.3"}, "devDependencies": {"@vitest/utils": "2.0.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "31acf5906f8c12f9c7fde21b84cc28f043e983b1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.3.tgz", "fileCount": 13, "integrity": "sha512-6OyA6v65Oe3tTzoSuRPcU6kh9m+mPL1vQ2jDlPdn9IQoUxl8rXhBnfICNOC+vwxWY684Vt5UPgtcA2aPFBb6wg==", "signatures": [{"sig": "MEUCIQCZsI3it5DgbKx509IJ6+CyTwq6SikHd0xpo4wk4geBiAIgH0EOq6z6e1ELZoZL7ydjiWVDuXcOj8fPFlbmRVe1g+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71427}, "funding": "https://opencollective.com/vitest"}, "2.0.4": {"name": "@vitest/snapshot", "version": "2.0.4", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "@vitest/pretty-format": "2.0.4"}, "devDependencies": {"@vitest/utils": "2.0.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "7d7dea9df17c5c13386f1a7a433b99dc0ffe3c14", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.4.tgz", "fileCount": 13, "integrity": "sha512-or6Mzoz/pD7xTvuJMFYEtso1vJo1S5u6zBTinfl+7smGUhqybn6VjzCDMhmTyVOFWwkCMuNjmNNxnyXPgKDoPw==", "signatures": [{"sig": "MEUCIFFE2Rob7S8bKTozQfF1n8THsc9PSfFwEyO8WEv2Aw26AiEA7oage/vA+xuW/zfDAnqWCtaVFJ09a0JE/Dlr2FGQ30Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71427}, "funding": "https://opencollective.com/vitest"}, "2.0.5": {"name": "@vitest/snapshot", "version": "2.0.5", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.10", "@vitest/pretty-format": "2.0.5"}, "devDependencies": {"@vitest/utils": "2.0.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "a2346bc5013b73c44670c277c430e0334690a162", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.0.5.tgz", "fileCount": 13, "integrity": "sha512-SgCPUeDFLaM0mIUHfaArq8fD2WbaXG/zVXjRupthYfYGzc8ztbFbu6dUNOblBG7XLMR1kEhS/DNnfCZ2IhdDew==", "signatures": [{"sig": "MEUCIQDx9z9ce5026Id9etdYlro78UtT5M191wfrjIQLaT5WkAIgBTAmQO8VI/G/jPeSzHHQC9sgypTUyk26np2bYV/QckE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71427}, "funding": "https://opencollective.com/vitest"}, "2.1.0-beta.1": {"name": "@vitest/snapshot", "version": "2.1.0-beta.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.1"}, "devDependencies": {"@vitest/utils": "2.1.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "74c356d24f325f3b76b5989f8a8ad6d956448844", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-O+xAqshSFXoeLfzlKkYN+7CwWJwyujk5awjuJOpXrceF5RMgoIy7GZ/o5KKbloVoafOIokmM8WHGbszGBe5Csg==", "signatures": [{"sig": "MEUCIF9R6/vxqzF6mNndQ0Mg55CJNQy/TgJRsxxWphrvMYgAAiEAri674bzTEIfay9BzNKGa36tRt99WxzijV/mpxeRH5XA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "funding": "https://opencollective.com/vitest"}, "2.1.0-beta.2": {"name": "@vitest/snapshot", "version": "2.1.0-beta.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.2"}, "devDependencies": {"@vitest/utils": "2.1.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "74f883b35983a9f87ae750b2ffec19a434e19ce8", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-J48yhZ+CFs7X7mGeyzSY4SLYIkZpiKCgUkG17WFdV6dZaNKVV6aLKS3E040d2+kv7YDY9gJVuA8Pe32s2eiq0g==", "signatures": [{"sig": "MEUCICddUq5yxAGxwY3UiSjehJWli8BfzQirlWez2qqyf4X/AiEAlZQy1IcD5ERy8Injk4u+YAOYlro0jJev4hYz7MMcjF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "funding": "https://opencollective.com/vitest"}, "2.1.0-beta.3": {"name": "@vitest/snapshot", "version": "2.1.0-beta.3", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.3"}, "devDependencies": {"@vitest/utils": "2.1.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "4598861cf92adb39ae9ecd852772e3fb5441628c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-r0a3lMoDCjly3C+ep3BXcymDVs4KmwaWSddH7LQup9zuL3LmwrPyOq9nFCyyOMgsyPSRuzboGJkOsZlMAZducw==", "signatures": [{"sig": "MEQCIHpXpd0KnmYHUtsi13nJEzAfLR2d8DkXRXHaQVfvs8WdAiAQGTWRXvG+SU47CLGBWYYjD96RHMSc4IST8QODdWk4Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "funding": "https://opencollective.com/vitest"}, "2.1.0-beta.4": {"name": "@vitest/snapshot", "version": "2.1.0-beta.4", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.4"}, "devDependencies": {"@vitest/utils": "2.1.0-beta.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "5cd9c8d1bdc10d3818a3b3c1a962ec3775f8463f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-wVMeZOUenmWBGKLGqcVFupRJtb0Y1SGafyQtDr7JlH5D/czHlPye89C4eb83Jp77luQFJKghmRtmr6B/oHokLQ==", "signatures": [{"sig": "MEUCIEmkES5Egg0af6nXeGwQb1FZnYFGj8GOhQEvIWtQIgNgAiEA8mF7wlUjDHjFUFe4rZ644eDnkOxyaPC9m7hx+HIWQZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "funding": "https://opencollective.com/vitest"}, "2.1.0-beta.5": {"name": "@vitest/snapshot", "version": "2.1.0-beta.5", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.5"}, "devDependencies": {"@vitest/utils": "2.1.0-beta.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "047cc73271ab94b698a1e6f58651feeaac8e4355", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.5.tgz", "fileCount": 13, "integrity": "sha512-XlDzkkRN7o4QHHF5/1R4HL82wbzQWvKBpRedNSvSr0BKZCZEWOXdOacOjOTDh8IHfJ+ACD9N/gaIObbBGop+0A==", "signatures": [{"sig": "MEQCIAiQqYt8db/0jJXmLr1TV/CJNX7ZKTiuAZ0JHz53jmi4AiB1ZIxKzIe6Ph3yMdULS/nR0th4sSOyRqk69HS1K16IZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71448}, "funding": "https://opencollective.com/vitest"}, "2.1.0-beta.6": {"name": "@vitest/snapshot", "version": "2.1.0-beta.6", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.6"}, "devDependencies": {"@vitest/utils": "2.1.0-beta.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "402d5eb106b40482388a332d5ea5b75d3754f1b1", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.6.tgz", "fileCount": 13, "integrity": "sha512-AuQc8NcbJhhUWxrVWllQStsN0A7cA5P+69d8ImszzOtInqtr6yHFziu4n9Xt5e0dNggS9edNYajkOS24m7YsKg==", "signatures": [{"sig": "MEUCICZelfrDhQVC3l9AQH5sLzuIkt0tW+qjhSN7GA+7THBhAiEA5jhOaJmlPC2XfSVcd88YZISZ70dLeEta4k0n0TRQ2Zk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 71944}, "funding": "https://opencollective.com/vitest"}, "2.1.0-beta.7": {"name": "@vitest/snapshot", "version": "2.1.0-beta.7", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0-beta.7"}, "devDependencies": {"@vitest/utils": "2.1.0-beta.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "e4ee571773fbc4ac42added09a6356da646aeda2", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0-beta.7.tgz", "fileCount": 13, "integrity": "sha512-hxVlKPGFgEjJgWSSX5dH4ip7l8dFRB7o+7iruvssQIi0WmUbCIQYgz75FflFO9H3dIiuyafdAT/Dh38sAFjX/g==", "signatures": [{"sig": "MEUCIBc3pne41t8y5VvNvR41jryJ4dmPoWfbzeZJIcQn8HrRAiEAiwVDmxruxwkzgbjldRNUGGhrKywiwCfTF1mzgdzJtOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0-beta.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 72310}, "funding": "https://opencollective.com/vitest"}, "2.1.0": {"name": "@vitest/snapshot", "version": "2.1.0", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.0"}, "devDependencies": {"@vitest/utils": "2.1.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "f02a970874d8669b79bd44479b1ce5e5c547a2a3", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.0.tgz", "fileCount": 13, "integrity": "sha512-x69CygGMzt9VCO283K2/FYQ+nBrOj66OTKpsPykjCR4Ac3lLV+m85hj9reaIGmjBSsKzVvbxWmjWE3kF5ha3uQ==", "signatures": [{"sig": "MEQCIFEeiiUP5N120iUY/gnGpVKobeANhCP/SykzUguI0hX/AiAEmCO7TElJIk7yRHMnfB9P54NzXq0xUAkJeNsZNUiqqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 72289}, "funding": "https://opencollective.com/vitest"}, "2.1.1": {"name": "@vitest/snapshot", "version": "2.1.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.1"}, "devDependencies": {"@vitest/utils": "2.1.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "38ef23104e90231fea5540754a19d8468afbba66", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.1.tgz", "fileCount": 13, "integrity": "sha512-BnSku1WFy7r4mm96ha2FzN99AZJgpZOWrAhtQfoxjUU5YMRpq1zmHRq7a5K9/NjqonebO7iVDla+VvZS8BOWMw==", "signatures": [{"sig": "MEQCIBOeP22mqz6bRpUbusxH2tuZU2a79gc0xWvZVnm0dQLbAiBaO6EfQwj8AUlv3fDgRkV1wmXc2a86onygZvAFbmlPcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 72289}, "funding": "https://opencollective.com/vitest"}, "2.1.2": {"name": "@vitest/snapshot", "version": "2.1.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.2"}, "devDependencies": {"@vitest/utils": "2.1.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "e20bd794b33fdcd4bfe69138baac7bb890c4d51f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.2.tgz", "fileCount": 13, "integrity": "sha512-xtAeNsZ++aRIYIUsek7VHzry/9AcxeULlegBvsdLncLmNCR6tR8SRjn8BbDP4naxtccvzTqZ+L1ltZlRCfBZFA==", "signatures": [{"sig": "MEUCIQCr35BOlmPo7D+peQdPqNV9O7cv95BWtc6/IWttDtfSDwIgCd3Yy72b5XkexGrRShhxqwro8290i64i5lnvpYxZyKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85249}, "funding": "https://opencollective.com/vitest"}, "2.1.3": {"name": "@vitest/snapshot", "version": "2.1.3", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.11", "@vitest/pretty-format": "2.1.3"}, "devDependencies": {"@vitest/utils": "2.1.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "1b405a9c40a82563605b13fdc045217751069e58", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.3.tgz", "fileCount": 13, "integrity": "sha512-qWC2mWc7VAXmjAkEKxrScWHWFyCQx/cmiZtuGqMi+WwqQJ2iURsVY4ZfAK6dVo6K2smKRU6l3BPwqEBvhnpQGg==", "signatures": [{"sig": "MEYCIQCZMsWDvJXE9rlPka5pBsvfURJueVgJmufT5p7W+ozxoQIhAKlTLcfRmV5a5Y+lHTbJskirzbvx0RyRF8x7tuOVbBMB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85249}, "funding": "https://opencollective.com/vitest"}, "2.1.4": {"name": "@vitest/snapshot", "version": "2.1.4", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.4"}, "devDependencies": {"@vitest/utils": "2.1.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "ef8c3f605fbc23a32773256d37d3fdfd9b23d353", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.4.tgz", "fileCount": 13, "integrity": "sha512-3Kab14fn/5QZRog5BPj6Rs8dc4B+mim27XaKWFWHWA87R56AKjHTGcBFKpvZKDzC4u5Wd0w/qKsUIio3KzWW4Q==", "signatures": [{"sig": "MEYCIQD3nVDDa0936I977dGhPoViZ/nwHuvSpIREXgW24qnLmQIhAIu0vqCWjePoz7n9i4qbzxuH4T4RvYXinf+LbkWX4n+T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "funding": "https://opencollective.com/vitest"}, "2.1.5": {"name": "@vitest/snapshot", "version": "2.1.5", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.5"}, "devDependencies": {"@vitest/utils": "2.1.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "a09a8712547452a84e08b3ec97b270d9cc156b4f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.5.tgz", "fileCount": 13, "integrity": "sha512-zmYw47mhfdfnYbuhkQvkkzYroXUumrwWDGlMjpdUr4jBd3HZiV2w7CQHj+z7AAS4VOtWxI4Zt4bWt4/sKcoIjg==", "signatures": [{"sig": "MEUCIQC8/scFXBiQNhgsoFJk/87Xk+yS8y/mrv393s2HlCyVvwIgYDImYYH+NiDGw4MA4MBpqfV7cxCbRZmeRAMkefe35PY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "funding": "https://opencollective.com/vitest"}, "2.2.0-beta.1": {"name": "@vitest/snapshot", "version": "2.2.0-beta.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.2.0-beta.1"}, "devDependencies": {"@vitest/utils": "2.2.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "6dddab14f81b37d4f4641fbde9fbeabbe9fde4ab", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.2.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-Pa208PgAYUmEK4+MKrDNbXEf21lly5OgS5KPemZYHhFaUwQXcCBndJikgn0N2JPcsf07IjPekIjzenes8ezNhw==", "signatures": [{"sig": "MEUCIFgok9NnsHYCN642gQdoNo1l+nppFwTqGRTUbeEd8q2CAiEAsbHuvOz6ghBG2OQwEoIiHp5b58vsaXaNc9ykVpeHCbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85343}, "funding": "https://opencollective.com/vitest"}, "2.2.0-beta.2": {"name": "@vitest/snapshot", "version": "2.2.0-beta.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.2.0-beta.2"}, "devDependencies": {"@vitest/utils": "2.2.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "89ff8d9b729317645016e41237424b3b2d4e2120", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.2.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-fOXZlA1enCyeGvTiV/ZFFDaI9vPadKu3E/Q5LKVcXBqCPHDnRDpa6UNP6N7l74CchXj8y0LaIewkFxsq3FpBSg==", "signatures": [{"sig": "MEYCIQC6X6FFfEGkdE8gR9ksXRkHlbutsMFKF/6wm0p6DUaL0wIhALs3owGhe2tmQMk1iYOf6A6uo+5e2TJzVMiryO0bLG5l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85343}, "funding": "https://opencollective.com/vitest"}, "2.1.6": {"name": "@vitest/snapshot", "version": "2.1.6", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.6"}, "devDependencies": {"@vitest/utils": "2.1.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "21740449221e37f80c4a8fb3e15f100f30e7934d", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.6.tgz", "fileCount": 13, "integrity": "sha512-5JTWHw8iS9l3v4/VSuthCndw1lN/hpPB+mlgn1BUhFbobeIUj1J1V/Bj2t2ovGEmkXLTckFjQddsxS5T6LuVWw==", "signatures": [{"sig": "MEYCIQCncb74Z2PMV1azYs9OXeEsyll/N7kN+H8/Y6Hi8BSZYAIhAIHUA9+RcPKWseK/MYKHW2IoNUXh+zh4vTZKuz6K78IG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "funding": "https://opencollective.com/vitest"}, "2.1.7": {"name": "@vitest/snapshot", "version": "2.1.7", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.7"}, "devDependencies": {"@vitest/utils": "2.1.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "5f7afa9e5fd5c8444e376ccece31def1cbc8ec0c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.7.tgz", "fileCount": 13, "integrity": "sha512-OioIxV/xS393DKdlkRNhmtY0K37qVdCv8w1M2SlLTBSX+fNK6zgcd01VlT1nXdbKVDaB8Zb6BOfQYYoGeGTEGg==", "signatures": [{"sig": "MEYCIQC1rGER9AP2AYqBi58689GKnaYeo2M3Uqn//SZ8IlNWyAIhAP1wa5Q53XGpXpMXzWUkPfso9+t84fl1pYSnd61NC+Cs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "funding": "https://opencollective.com/vitest"}, "2.1.8": {"name": "@vitest/snapshot", "version": "2.1.8", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.8"}, "devDependencies": {"@vitest/utils": "2.1.8", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "d5dc204f4b95dc8b5e468b455dfc99000047d2de", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.8.tgz", "fileCount": 13, "integrity": "sha512-20T7xRFbmnkfcmgVEz+z3AU/3b0cEzZOt/zmnvZEctg64/QZbSDJEVm9fLnnlSi74KibmRsO9/Qabi+t0vCRPg==", "signatures": [{"sig": "MEUCIGa49qVKINSSX/fRRnjIuRsjeO2LAEX5B2X4UJTSMtNJAiEA0DMbJ7pWEN0J884c8EI3BONmlIiXpKd6XzqhXRrfsHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "funding": "https://opencollective.com/vitest"}, "3.0.0-beta.1": {"name": "@vitest/snapshot", "version": "3.0.0-beta.1", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.14", "@vitest/pretty-format": "3.0.0-beta.1"}, "devDependencies": {"@vitest/utils": "3.0.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "b4c45a5005a1bde0893e65cb9b218def4b5bba1d", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-ejTp3wZrEdj1kBGfrrY3stJNy25b4+qJvIkVKfzo7xgorsC+qI3r36RmqMQoAxUKwniNN6eDHMG01txUZsP71Q==", "signatures": [{"sig": "MEUCIQCHm15PdEKx824UGK1iOsQt5NKKpQSSwDYw7tsoBr5yPwIgOvkfiF2tYadU5O/AKWqfEfbIk1Sqp4QtEJupKYyaK7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86592}, "funding": "https://opencollective.com/vitest"}, "3.0.0-beta.2": {"name": "@vitest/snapshot", "version": "3.0.0-beta.2", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.14", "@vitest/pretty-format": "3.0.0-beta.2"}, "devDependencies": {"@vitest/utils": "3.0.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "a77471e969e9cec2ab43535b0c7bf8bdf100c7a6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-6INaNxXyYBmFGHhjmSyoz+/P3F+e6sHZPXLYt2OAa6Zt1v1O91FoGUTwdNHj2ASxMQeVpK/7snxNaeyr2INVOg==", "signatures": [{"sig": "MEUCIGOp0M+lb5dAmuOi/6BGh3k0bOwIyWEQKcm52IQQj2pGAiEAmGQsCnbfLVRCCWziCsjAz+pswAKj2MxG9O1hh7Xx4H0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86583}, "funding": "https://opencollective.com/vitest"}, "3.0.0-beta.3": {"name": "@vitest/snapshot", "version": "3.0.0-beta.3", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.0-beta.3"}, "devDependencies": {"@vitest/utils": "3.0.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "b508ef51fa2e51fd4cdcc4876b67e00ac8067081", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-jsWdfQWRcbI1WIpxi2X6jUAAjJY898iK4P/ZzxgkBPFrPK894HXkCm3xcF/sZHF9nHNa61ZIgfgddnH0blkiFQ==", "signatures": [{"sig": "MEYCIQCItaR1bYxpjkT5WaffKPZD+WMeuzEs3R4C2ntGXU9T1gIhALnHcVhYCbUO9OxAwMUS4bkJAq+/MGXcL+CRLoA8AmQg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86583}, "funding": "https://opencollective.com/vitest"}, "3.0.0-beta.4": {"name": "@vitest/snapshot", "version": "3.0.0-beta.4", "dependencies": {"pathe": "^2.0.0", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.0-beta.4"}, "devDependencies": {"@vitest/utils": "3.0.0-beta.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "06456397159df3328f20760cae87b4431d2a48db", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0-beta.4.tgz", "fileCount": 13, "integrity": "sha512-z8WLahOEDpRkPf6OvOYhjK6Mhl3Z4U4m536kAxUeFD5If0o1e2rdvSzD6oNPfs25rKs+UT7dla+4MSFU1JVjPA==", "signatures": [{"sig": "MEQCIEi0JYIw2HWe/AtO9YJ2T6C3KcxMGl+Mh6G7y0DjBFa7AiAlYgxNryK901H+VSekTAVdocAuncAulH/Iyac2LHTatQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0-beta.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86773}, "funding": "https://opencollective.com/vitest"}, "3.0.0": {"name": "@vitest/snapshot", "version": "3.0.0", "dependencies": {"pathe": "^2.0.0", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.0"}, "devDependencies": {"@vitest/utils": "3.0.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "e11797c3bd35a57fccb2a2667fe36ff253e620cf", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.0.tgz", "fileCount": 13, "integrity": "sha512-W0X6fJFJ3RbSThncSYUNSnXkMJFyXX9sOvxP1HSQRsWCLB1U3JnZc0SrLpLzcyByMUDXHsiXQ+x+xsr/G5fXNw==", "signatures": [{"sig": "MEQCIFNzFOoMO6/vcAM123D2f6l3h3SaUFIbGgwn+OcwaNa3AiBaMYW951DWfO+06hl3RMWy+XsYPZYPO5ZEkcKaGu3Q3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86732}, "funding": "https://opencollective.com/vitest"}, "3.0.1": {"name": "@vitest/snapshot", "version": "3.0.1", "dependencies": {"pathe": "^2.0.1", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.1"}, "devDependencies": {"@vitest/utils": "3.0.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "02493577c708ff9c90b9bd77c4055ca348e8d196", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.1.tgz", "fileCount": 13, "integrity": "sha512-ZYV+iw2lGyc4QY2xt61b7Y3NJhSAO7UWcYWMcV0UnMrkXa8hXtfZES6WAk4g7Jr3p4qJm1P0cgDcOFyY5me+Ug==", "signatures": [{"sig": "MEUCIQDxJDS2S3kIeh+t1pSgKI7DzVvXoBiKoqwArezqvJ0eGwIgJBKyy0yaWKW7ZGi3tEQtEaobKrlAEsXYpK2UBlP9IT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86828}, "funding": "https://opencollective.com/vitest"}, "3.0.2": {"name": "@vitest/snapshot", "version": "3.0.2", "dependencies": {"pathe": "^2.0.1", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.2"}, "devDependencies": {"@vitest/utils": "3.0.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "c3029d298c905f40ec3f238cab1130c87175a47e", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.2.tgz", "fileCount": 13, "integrity": "sha512-h9s67yD4+g+JoYG0zPCo/cLTabpDqzqNdzMawmNPzDStTiwxwkyYM1v5lWE8gmGv3SVJ2DcxA2NpQJZJv9ym3g==", "signatures": [{"sig": "MEUCIHMZv0lhBYRcwClTZIw5SFjXl+XpbQ1JnX4qLqgheQ/0AiEA+36mJi2Nb9kPBeLwy5eQFrpQtlYe8PlFaBEHQ7W+lgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86828}, "funding": "https://opencollective.com/vitest"}, "3.0.3": {"name": "@vitest/snapshot", "version": "3.0.3", "dependencies": {"pathe": "^2.0.1", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.3"}, "devDependencies": {"@vitest/utils": "3.0.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "a20a8cfa0e7434ef94f4dff40d946a57922119de", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.3.tgz", "fileCount": 13, "integrity": "sha512-kNRcHlI4txBGztuJfPEJ68VezlPAXLRT1u5UCx219TU3kOG2DplNxhWLwDf2h6emwmTPogzLnGVwP6epDaJN6Q==", "signatures": [{"sig": "MEQCIEcyxNHilSSqEDvUawETpoR7ZqyvFbOPRhGakqJOAKGuAiAmEV3U5Ed2mtY2OtdzXYcS3qftyW2D1S/s93gjnRdCCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86828}, "funding": "https://opencollective.com/vitest"}, "3.0.4": {"name": "@vitest/snapshot", "version": "3.0.4", "dependencies": {"pathe": "^2.0.2", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.4"}, "devDependencies": {"@vitest/utils": "3.0.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "7e64c19ca1ab9abb2f01fd246817b5f0404798fd", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.4.tgz", "fileCount": 13, "integrity": "sha512-+p5knMLwIk7lTQkM3NonZ9zBewzVp9EVkVpvNta0/PlFWpiqLaRcF4+33L1it3uRUCh0BGLOaXPPGEjNKfWb4w==", "signatures": [{"sig": "MEYCIQDKyp0P+uauOrFqgqkdafiI3foCHlaNUsinvFJv9gcdKgIhAMkR9DkPhYaPct19X7d9fB2aDhydXM1seGw3WfTInTlt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86894}, "funding": "https://opencollective.com/vitest"}, "1.6.1": {"name": "@vitest/snapshot", "version": "1.6.1", "dependencies": {"pathe": "^1.1.1", "magic-string": "^0.30.5", "pretty-format": "^29.7.0"}, "devDependencies": {"@vitest/utils": "1.6.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "90414451a634bb36cd539ccb29ae0d048a8c0479", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.6.1.tgz", "fileCount": 13, "integrity": "sha512-WvidQuWAzU2p95u8GAKlRMqMyN1yOJkGHnx3M1PL9Raf7AQ1kwLKg04ADlCa3+OXUZE7BceOhVZiuWAbzCKcUQ==", "signatures": [{"sig": "MEQCIHudkzY8XxOEA/McshfGVkP7KiefpbCnwiwdzc0uDb8yAiAhPF5rL6LmRvBfJTdDifho8Vvazw1lX/qaRbv0F/0W+w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@1.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 66599}, "funding": "https://opencollective.com/vitest"}, "2.1.9": {"name": "@vitest/snapshot", "version": "2.1.9", "dependencies": {"pathe": "^1.1.2", "magic-string": "^0.30.12", "@vitest/pretty-format": "2.1.9"}, "devDependencies": {"@vitest/utils": "2.1.9", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "24260b93f798afb102e2dcbd7e61c6dfa118df91", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-2.1.9.tgz", "fileCount": 13, "integrity": "sha512-oBO82rEjsxLNJincVhLhaxxZdEtV0EFHMK5Kmx5sJ6H9L183dHECjiefOAdnqpIgT5eZwT04PoggUnW88vOBNQ==", "signatures": [{"sig": "MEUCIQDM7IAxUR6xYu5491Ga4pTcB89YR12eTAK4yIxx/ds5HQIgQ/bfAk4hOwrEBBZk4ZmBNGYR/74JUWC6WfvelXc1//I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@2.1.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 85267}, "funding": "https://opencollective.com/vitest"}, "3.0.5": {"name": "@vitest/snapshot", "version": "3.0.5", "dependencies": {"pathe": "^2.0.2", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.5"}, "devDependencies": {"@vitest/utils": "3.0.5", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "afd0ae472dc5893b0bb10e3e673ef649958663f4", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.5.tgz", "fileCount": 13, "integrity": "sha512-GJPZYcd7v8QNUJ7vRvLDmRwl+a1fGg4T/54lZXe+UOGy47F9yUfE18hRCtXL5aHN/AONu29NGzIXSVFh9K0feA==", "signatures": [{"sig": "MEYCIQCGyhgUG0IJGZ8rBj9qM94PdBnINd6C7cgjo7tcYF2XlgIhAKNETI8v7l4FAUgj+ud453+JIC3NMRPC424zpPIjUhVm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 86894}, "funding": "https://opencollective.com/vitest"}, "3.0.6": {"name": "@vitest/snapshot", "version": "3.0.6", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.6"}, "devDependencies": {"@vitest/utils": "3.0.6", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "e962319e487b2e8da7ad39322b5e0b39ea639d7a", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.6.tgz", "fileCount": 13, "integrity": "sha512-qKSmxNQwT60kNwwJHMVwavvZsMGXWmngD023OHSgn873pV0lylK7dwBTfYP7e4URy5NiBCHHiQGA9DHkYkqRqg==", "signatures": [{"sig": "MEYCIQD6J17BZDfxjr7FsJJT9sAf3MtMcA5hi/AKNkxhV8NC3gIhANh9cJ20RoW0SetStnocJ1azQU15obaV2P2+NZF3v4XX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 88931}, "funding": "https://opencollective.com/vitest"}, "3.0.7": {"name": "@vitest/snapshot", "version": "3.0.7", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.7"}, "devDependencies": {"@vitest/utils": "3.0.7", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "df34e3c5820bdd54bba8919291a182df5c6b8c6f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.7.tgz", "fileCount": 13, "integrity": "sha512-eqTUryJWQN0Rtf5yqCGTQWsCFOQe4eNz5Twsu21xYEcnFJtMU5XvmG0vgebhdLlrHQTSq5p8vWHJIeJQV8ovsA==", "signatures": [{"sig": "MEYCIQDGnYsRjSvYLuGu+p1yEaWC6x/z68NRxhU1e7wqE6MioAIhALr8NX6JQonwKsYqhr8HY/wq6tleF7mWuGhO5he2ek+h", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 88931}, "funding": "https://opencollective.com/vitest"}, "3.0.8": {"name": "@vitest/snapshot", "version": "3.0.8", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.8"}, "devDependencies": {"@vitest/utils": "3.0.8", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "b65d738c00ff052a323125ad7dfb001927049c78", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.8.tgz", "fileCount": 13, "integrity": "sha512-x8IlMGSEMugakInj44nUrLSILh/zy1f2/BgH0UeHpNyOocG18M9CWVIFBaXPt8TrqVZWmcPjwfG/ht5tnpba8A==", "signatures": [{"sig": "MEYCIQCqbdfJR7xZCMgUGNa6kKZOPkRq8jh2jacrVwDZHRBE/QIhAJRQjUWHysFzPt/YiXUf+qmLOO0BNX7MU3gYxX78QhXu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 90001}, "funding": "https://opencollective.com/vitest"}, "3.0.9": {"name": "@vitest/snapshot", "version": "3.0.9", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.0.9"}, "devDependencies": {"@vitest/utils": "3.0.9", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "2ab878b3590b2daef1798b645a9d9e72a0eb258d", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.0.9.tgz", "fileCount": 13, "integrity": "sha512-AiLUiuZ0FuA+/8i19mTYd+re5jqjEc2jZbgJ2up0VY0Ddyyxg/uUtBDpIFAy4uzKaQxOW8gMgBdAJJ2ydhu39A==", "signatures": [{"sig": "MEUCIQCNS6AaLX1Gnp6/RD13emQQG0lgVKd9adiUOF7w4jD/kAIgUW/FYhc73I2UjHsVPqQrelBKc78cQFIjSBIgiNjuqNw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.0.9", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89265}, "funding": "https://opencollective.com/vitest"}, "3.1.0-beta.1": {"name": "@vitest/snapshot", "version": "3.1.0-beta.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.0-beta.1"}, "devDependencies": {"@vitest/utils": "3.1.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "f050b18c76b224fb27dcdc7da033dd65c0daba7f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-9sGHyh+G4kNd5FAyLYKh/klIfq4huIwDDFUvYJty4scS60sAz2vBTUmlfQlSQQq1HyiktW1OPCO973fMOaKlqA==", "signatures": [{"sig": "MEUCIDC9futPQYbh2yAA29L7B14GTg2JnWdmfEjKdQqg2C7WAiEAjD4xze6dEp9Rew8Emr3R7O0BGvnq+IyQzdthbrIZ4N4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 89286}, "funding": "https://opencollective.com/vitest"}, "3.1.0-beta.2": {"name": "@vitest/snapshot", "version": "3.1.0-beta.2", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.0-beta.2"}, "devDependencies": {"@vitest/utils": "3.1.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "b4459119f5e6d9a0378ed9118c4798bb6b3b2247", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-HXK4uKd8j5ldZ0rXfrAOpnIwHiy060znuDmpp+sMde4OoQqhpYhUHZUmqbDnF2hQes+M9TwBh7OsH+k0Z9RVYw==", "signatures": [{"sig": "MEUCIQCBusYWp2ed8WFisCjygT41iGs3O3o3ao2tff3n1DdFpQIgE9BSn7O0ATjl9pH+XQkqKA+X7tNgl6372n7F1Q5u2Bs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87624}, "funding": "https://opencollective.com/vitest"}, "3.1.0": {"name": "@vitest/snapshot", "version": "3.1.0", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.0"}, "devDependencies": {"@vitest/utils": "3.1.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "65c8c879dc6307af2bbda5fb821bbe9e9765166c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.0.tgz", "fileCount": 13, "integrity": "sha512-H5xN6SMxLkNVvYvPaeN/FN4Y0e6ZgReaTVvHd1a7IKAUyKjF+Ttbb118PXxCXYTLQY45LKcMVNioG2TDL0votA==", "signatures": [{"sig": "MEQCIDihjfslbPFvV/eBitZTi/BnPpZdhoKtNioCLFnYfK8cAiAp5Ki86A9ZEXXvpi+bbke0P7qnVvTcvmTcYrk6FuOu8A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 87058}, "funding": "https://opencollective.com/vitest"}, "3.1.1": {"name": "@vitest/snapshot", "version": "3.1.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.1"}, "devDependencies": {"@vitest/utils": "3.1.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "42b6aa0d0e2b3b48b95a5c76efdcc66a44cb11f3", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.1.tgz", "fileCount": 13, "integrity": "sha512-bByMwaVWe/+1WDf9exFxWWgAixelSdiwo2p33tpqIlM14vW7PRV5ppayVXtfycqze4Qhtwag5sVhX400MLBOOw==", "signatures": [{"sig": "MEYCIQDYJLRmGaViGv/edWog8Hr2vD/D8zSQyCp1GUpUnrtt6gIhAO0ZOaLwY3gq32YD7gxb+n0YFK1RBoM1Ior8xZ37pCQb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87058}, "funding": "https://opencollective.com/vitest"}, "3.1.2": {"name": "@vitest/snapshot", "version": "3.1.2", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.2"}, "devDependencies": {"@vitest/utils": "3.1.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "46c52a417afbf1fe94fba0a5735cbedf9cfc60f6", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.2.tgz", "fileCount": 13, "integrity": "sha512-Q1qkpazSF/p4ApZg1vfZSQ5Yw6OCQxVMVrLjslbLFA1hMDrT2uxtqMaw8Tc/jy5DLka1sNs1Y7rBcftMiaSH/Q==", "signatures": [{"sig": "MEQCICkMryP/97KOI8nKPxh4xPaNlfQN2rDmASioJaXRSS7MAiB7ZLtdyJhAT42RIJXjqTTtgAgkWSNJe3PJLk+HL9CXqA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87056}, "funding": "https://opencollective.com/vitest"}, "3.1.3": {"name": "@vitest/snapshot", "version": "3.1.3", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.3"}, "devDependencies": {"@vitest/utils": "3.1.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "39a8f9f8c6ba732ffde59adeacf0a549bef11e76", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.3.tgz", "fileCount": 13, "integrity": "sha512-XVa5OPNTYUsyqG9skuUkFzAeFnEzDp8hQu7kZ0N25B1+6KjGm4hWLtURyBbsIAOekfWQ7Wuz/N/XXzgYO3deWQ==", "signatures": [{"sig": "MEUCIQDe5gvhBnA5eK0NIdMd2poOz76A1MZ47ctvIwdqTb8HaQIgZPi5yysp2tRB3qz474e9XvJV5muqbl3VhKM+MWEidsg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87056}, "funding": "https://opencollective.com/vitest"}, "3.2.0-beta.1": {"name": "@vitest/snapshot", "version": "3.2.0-beta.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.0-beta.1"}, "devDependencies": {"@vitest/utils": "3.2.0-beta.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "186b3de4f35850bf881045a0d010d3845659038b", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-VY9ngpOSnpOc4QGePhjHOGW+bqLxw6Fsi6Hzlbrq4ApufoBQtQXVQ8WrTcnPgLHS+pK2zVM5rPbs5sHu55uMbA==", "signatures": [{"sig": "MEUCIQDGs1zuk2Q7S3RDgKWYuB5faeCUTxenQ16T1QV9p7UwhQIgJAbd+mcaZvep0IixdqtwxHqI8BmG4JYwQe5JJGnoPL0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.0-beta.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87077}, "funding": "https://opencollective.com/vitest"}, "3.2.0-beta.2": {"name": "@vitest/snapshot", "version": "3.2.0-beta.2", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.0-beta.2"}, "devDependencies": {"@vitest/utils": "3.2.0-beta.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "90e6d33bb0e835c528d4b0556a7c2745193188e7", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.0-beta.2.tgz", "fileCount": 13, "integrity": "sha512-UT//cec0kpLqzm2D0BhnI4FjMryHIlmewpX8fH7bQrmcSmco8/H7NwSxOI1rfjWeerLoIpUKlKbcEEvW0GqK2w==", "signatures": [{"sig": "MEYCIQCWew3ogcQkNF8G1eJXCNoyn3D0hEVsushqMWzcgdgvmwIhAJX0jvfslh9KrWyYKmk29hrHvi193eSjJEy6yBvSbAwI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.0-beta.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87077}, "funding": "https://opencollective.com/vitest"}, "3.1.4": {"name": "@vitest/snapshot", "version": "3.1.4", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.1.4"}, "devDependencies": {"@vitest/utils": "3.1.4", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "7897d4960a3cf617fb0f17e182cc15c7e3e4ed3f", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.1.4.tgz", "fileCount": 13, "integrity": "sha512-JPHf68DvuO7vilmvwdPr9TS0SuuIzHvxeaCkxYcCD4jTk67XwL45ZhEHFKIuCm8CYstgI6LZ4XbwD6ANrwMpFg==", "signatures": [{"sig": "MEYCIQC5Oi400j16+wBQAR5orU8o43BDieF58do2yRM0S7xFFQIhAJZEt45U7ZB058SjfE5uFA9cV1IzZ6aqVlAtWfdSXhHm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 87056}, "funding": "https://opencollective.com/vitest"}, "3.2.0-beta.3": {"name": "@vitest/snapshot", "version": "3.2.0-beta.3", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.0-beta.3"}, "devDependencies": {"@vitest/utils": "3.2.0-beta.3", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "7163837a2f3862ddcb807e360fac6caab07d680c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.0-beta.3.tgz", "fileCount": 13, "integrity": "sha512-bLGgmpl9knKv1EJde5THe4eGqmZpb+Vv97Kq0t3lP8uYN1viID4lVsg1swSXtIAd0F1/anx4Uiu3Bp71hmFlow==", "signatures": [{"sig": "MEUCIG4jv25118wAcRAFO+2ibB4TyZOqaXlyTfhh6WmTGUBRAiEAl/L8UTW5OmgBYHghrp0ZLMDmnfmJiVv9IAVHXGfzr1c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.0-beta.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92655}, "funding": "https://opencollective.com/vitest"}, "3.2.0": {"name": "@vitest/snapshot", "version": "3.2.0", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.0"}, "devDependencies": {"@vitest/utils": "3.2.0", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "98ba12e3eb0ad39beffb9c44747b7c4d42f9e25e", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.0.tgz", "fileCount": 13, "integrity": "sha512-z7P/EneBRMe7hdvWhcHoXjhA6at0Q4ipcoZo6SqgxLyQQ8KSMMCmvw1cSt7FHib3ozt0wnRHc37ivuUMbxzG/A==", "signatures": [{"sig": "MEQCIGvz5qUIcNpvTvH/E7HU8o8fwydg+1nRlYuitaF0N+zwAiBfVUabwDmCzp1x2HmY/jxSHlbnJAIhSMH0F+JmbPlS/g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92620}, "funding": "https://opencollective.com/vitest"}, "3.2.1": {"name": "@vitest/snapshot", "version": "3.2.1", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.1"}, "devDependencies": {"@vitest/utils": "3.2.1", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "40a31ffe0304f93a7b6099194e9130e59b8b4a71", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.1.tgz", "fileCount": 13, "integrity": "sha512-5xko/ZpW2Yc65NVK9Gpfg2y4BFvcF+At7yRT5AHUpTg9JvZ4xZoyuRY4ASlmNcBZjMslV08VRLDrBOmUe2YX3g==", "signatures": [{"sig": "MEQCIEVVkPCsnLcl8LQXxHCRV99VrentAVsJk5oDRUUtWs+AAiAxtj2vqUSdD1u6mrQ6sg0dkbKw/r5QIR46dwGbWIBLrA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92620}, "funding": "https://opencollective.com/vitest"}, "3.2.2": {"name": "@vitest/snapshot", "version": "3.2.2", "dependencies": {"pathe": "^2.0.3", "magic-string": "^0.30.17", "@vitest/pretty-format": "3.2.2"}, "devDependencies": {"@vitest/utils": "3.2.2", "natural-compare": "^1.4.0", "@types/natural-compare": "^1.4.3"}, "dist": {"shasum": "9a97cf61d821ea7d421d14e0134c5469bce9e83c", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.2.tgz", "fileCount": 13, "integrity": "sha512-aMEI2XFlR1aNECbBs5C5IZopfi5Lb8QJZGGpzS8ZUHML5La5wCbrbhLOVSME68qwpT05ROEEOAZPRXFpxZV2wA==", "signatures": [{"sig": "MEUCIEKRJrLCYMJHROydPO8QmcGburB/2243WKB9MppZGdZHAiEAiFMb3GlYUhJCwBR82GEmlxWI2MFeurnbAa6KfK8sE0c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 92620}, "funding": "https://opencollective.com/vitest"}, "3.2.3": {"name": "@vitest/snapshot", "version": "3.2.3", "dependencies": {"magic-string": "^0.30.17", "pathe": "^2.0.3", "@vitest/pretty-format": "3.2.3"}, "devDependencies": {"@types/natural-compare": "^1.4.3", "natural-compare": "^1.4.0", "@vitest/utils": "3.2.3"}, "dist": {"integrity": "sha512-9gIVWx2+tysDqUmmM1L0hwadyumqssOL1r8KJipwLx5JVYyxvVRfxvMq7DaWbZZsCqZnu/dZedaZQh4iYTtneA==", "shasum": "786dc1939174e1ac6b674d6fd3259bd4ea35a804", "tarball": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.3.tgz", "fileCount": 13, "unpackedSize": 92620, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@vitest%2fsnapshot@3.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCU5eO7eWGG23YL01nrtPU1pQR85Fn4Jc84+SkK8+qevAIgaOXCW9mZ1qq6Q2TmL/6JujiGTOcLGSw0wg4IMZ7doJc="}]}, "funding": "https://opencollective.com/vitest"}}, "modified": "2025-06-09T11:32:40.255Z", "cachedAt": 1750172038601}