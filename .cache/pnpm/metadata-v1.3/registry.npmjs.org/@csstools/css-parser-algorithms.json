{"name": "@csstools/css-parser-algorithms", "dist-tags": {"latest": "3.0.5"}, "versions": {"1.0.0": {"name": "@csstools/css-parser-algorithms", "version": "1.0.0", "peerDependencies": {"@csstools/css-tokenizer": "^1.0.0"}, "dist": {"shasum": "63f0ecbb926abf92d6cd8d076663650ad340db8c", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-1.0.0.tgz", "fileCount": 16, "integrity": "sha512-lPphY34yfV15tEXiz/SYaU8hwqAhbAwqiTExv5tOfc7QZxT70VVYrsiPBaX1osdWZFowrDEAhHe4H3JnyzbjhA==", "signatures": [{"sig": "MEYCIQDqLuEJX5LdTFdtbNqLj6hxNoI+0KXk0KFjspm9o4vaqgIhAJWAgZyxDAA2ZTsdqruhM2t0nKNrTb2vGxKTiWrrfrkJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcg6KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIbA/+Kvso22Ep5494GXQ+GkEnOG/TWgBIOK+WHFzBKJvmKcIu2lgd\r\n3HWLjZ+/aurtZvv9jiJw/Ny36CFBKXI6cPB+cjCHwhE2HYkULig6UJOK6dV4\r\nEETX65DZ6GolCHPAhne6rL58xQ+T7OXRQL+ewWkKixUi415YCjKTQ652DKrV\r\nixw+ia9jgVjHdiS6wylHu9lii5XInj2iIqjAWdaK+7/D54cPpqSKGHRFCOlQ\r\ns/1cpItg8XtCVmNcoDkv86AD2s3tjEtdzClIwD+DFE57iguTdwWaxbsEbJT3\r\nKEPPY+u/K0cF4SMRw+lNlVPdedT63hnD27nf1Vziz1v+XCJD18alRE9HMrx3\r\nfIK659dXaeqqGq7DasoMAc0SFbFeYZn9VJUAwt5NZMHq/QzVVOCwyAseVZwI\r\nLUDcd/nHBKdVu19cQyGBklSLvLyc2q69UdBZZGd8Ca8XW/Mwrhe7kCoC3G4l\r\nZywPnuXzOPrPjT68qkLNR5dYen+cBYD6/IgaoWU1vvK47fVIsnsGuEX6lijC\r\nWgvprtiR7wyzVjdkhjCon0fUOTnIvvF9gVrkEk129AeVDeLBq5FUqFQ+D/dr\r\nOynZPNHL5cf4Sa48Ngdi9lM5dFIWq5Z5dpIhihV8FvvrglQk30PmEdjhJbuI\r\nnGYRwBB/EQV8ODSLRCmxOxEDid2c0IlUF6Y=\r\n=aygf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.0.0": {"name": "@csstools/css-parser-algorithms", "version": "2.0.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.0.0"}, "dist": {"shasum": "2f27a1f2aef94f5d009d3d83f08f1e095dff2b30", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.0.0.tgz", "fileCount": 15, "integrity": "sha512-RbukP8OjQvuH85veuzOq8abPjsvqvleZaQC6W0GJFGpwLUh8XmFMQjvtuIM9bQ589YFx4lwwAcSwN4nfcvxIEw==", "signatures": [{"sig": "MEQCIH80G7elAuceahGbbeZnazSrNb5wlRCaJ8stPcH95xTiAiAr49WSUA2PSIUQCHMjPYfsMwRYWAFVklFZvIOGE8E+Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyZHMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPBg/+KP2BWfZo0NKsd1P0r3+KYpu1DCLVu6N9me7btsTBa6tKkE+v\r\nTricYKSIZL6BsaMr9WhqKwppjgjZAEf+hYE5YwjX96q/K2KLb+i/qMqnDEy2\r\nU0LjYVyJFObnCMz6Gel54Y5jQ0Pygqa5Fi/WpP+VPGehTNgc0toWzufJuncY\r\nVKzU1IiqSWH3E1/XKr/S8M9ANiJhUtiXebZokzqemLzXtG7H+aCwv0PM1kzC\r\nYNCxYKMOVoukwlBJsH4h8njzNhcDA+oypZwGn3/DyP7vbWI0jpX+QtbGvyUd\r\nlO5pfW89pNN3qQoJmjt0EzwtSjqZgmxPVWvVOb1+oAr9fP/7efxqfdlmiS2c\r\nix+aP2kqrRkYosidAPz0BaHBR/shcMRgHXwje5tXk/wyGzhaZIjwHJzU+2b+\r\ndmjr/WxR2kVnXOHEI57k1xQ4a3bn+WekhPza09Yeq3nEK9NYrOommST1C+Lp\r\n1m7+tqzncmwGz6hwMV7oKD/2331co1g7X1K4RuHHmAGEzH9Y86PWoIgAbjyh\r\nlw6ZeQ+bmsi3KREG+58pMZLW/oczseLUce72//52FTBIQs0c9PhzrYC07GHu\r\nKEoSBWlZ/tP93WTMDQtNODzvaZ6LMdQ4fDKOslzb8gaogjnNpp882Ipd60vT\r\nK8RWK0mZ8yHvVdeVe8dwL+VGFP9p5pXNuA4=\r\n=qT0a\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.0.1": {"name": "@csstools/css-parser-algorithms", "version": "2.0.1", "peerDependencies": {"@csstools/css-tokenizer": "^2.0.0"}, "dist": {"shasum": "ff02629c7c95d1f4f8ea84d5ef1173461610535e", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.0.1.tgz", "fileCount": 15, "integrity": "sha512-B9/8PmOtU6nBiibJg0glnNktQDZ3rZnGn/7UmDfrm2vMtrdlXO3p7ErE95N0up80IRk9YEtB5jyj/TmQ1WH3dw==", "signatures": [{"sig": "MEYCIQD9q5w5xXQOyyt5fXefdz+ivUyqrn6VHXwOQ++XxXAN/wIhAP4sS2JmxkwYbLKVPkkvjZC17Z0KArSscnwQdRB7Ieul", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1M0BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqnow/+ORmlE9mdTYG/bZQxieH9GCPz3/tKn3zOA3oXcyicWLAtcVkR\r\n9XylTwMdGBpb/XYCbto/vQd2KO5hEoSzWeHf2TOaez8jrpm+dTDztdTv9Cm3\r\nlEJyvHub3MMDofNFjdLnw3t2xKpzaVf839pj2xcYjjq6zvE//B0sk489za9s\r\n+ybhb/zSuiJZHYB8/UhhyEtcI61gR/sD4GYo4Zm1u9ylj16qDaiSAbU8ya7i\r\npvRkh0xXFVu/zd4mgvgv5Dfd6FRpgQGcvLWuCT08HTOa5ktDYlYHMMHeNN5X\r\n34x3UQiiEDqur6kjR/j/osKaYMboQpJFPxsD4/XJbrFnMc4RDrxYSTV8IUFd\r\nd3b2w9mVM6hk0542mGS6Eui3MNAJ/6Kz9mbn0L/+C0Ogx9tZpc79fwMelp9+\r\nQhYFCYzWS9/lqUZQ+38aB6lhk/W6gSbSlST0Xbd+4MvL6GMfHyA5Dq0t0hpf\r\nBBy5VA0+H1uaLG7v4E5iETfrukvzIrloe5VGPHCjYIeKo58SVhJC0HniZ4vI\r\nOab2MfZMPqq4/oLuxcvqsLHQnYN+Xr2DlKz6FwOhTg2xQGamrOS1CfjWTCKD\r\nZDGdK3ObeyBrq+zHZzX1Y+HL+fQbHjjX5TLpQt4Us6d5sW9BP2C59fL2pDWm\r\n+CovCeYeqLRb0aMTwETIL6XH7yMaD9pqNCk=\r\n=B+Nq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.1.0": {"name": "@csstools/css-parser-algorithms", "version": "2.1.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.0.0"}, "dist": {"shasum": "c0a605b0218790faeb5911f240964891c6031501", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.1.0.tgz", "fileCount": 17, "integrity": "sha512-KP8TicdXpUyeB1NMlbHud/1l39xvLGvqNFWMpG4qC6H1zs9SadGUHe5SO92n/659sDW9aGDvm9AMru0DZkN1Bw==", "signatures": [{"sig": "MEYCIQDphmMeF0hKPQ6XLjcY5l5DiLuCDmUUYXb+2H42/GhLKAIhAKdFfDAuo4qTjgHr1jyUBOxsTnZZK/VVasOfKPt9XjuO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHqb7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqn4g//bF6mpSUY9UHBr8PaiHYYaW2n2CboO4jBZuxEISxMMY0T9oKx\r\nZf/B0ru6yE3fgVruiZtHudgQzngS23WWVy36bzWNYnoScIGKnWXHdJOg0rzz\r\nr1thcuOOUuaCL4LixUW39ZzDwkhketZ41bj+Xb0nE/TJfz35X7cZM2UHl3DN\r\nH7910SbWbtTXQEk6qWzxeSBSAliO+6ZscXEjA2O405OkyjkhNPt9E5F+DSUb\r\nAnxi3iY9Z/+3hyXKH2XgOEnbuVVwbevrhYRVZNaPxvlxzH1BS+VsQVPLjZ7D\r\nu9QGglYy75PscqvxZfxF1mgrkBMIFbx2GdnJ5wR78OW5+jRS5Wyx6f4LnXkH\r\nF46iaBdcKl58g7GVQQpsVAEUYAbAicYcsPM5raoAQY/pkpzADsc5y5axo9lk\r\nVyhbabgFYsxHyBD44T3JKN9QSU0n2b+ZZ8hEV9UhBWCcZv7HOjFfO9+A/gqv\r\nwMr0xaFgq5Xt4gkH+5mNDNnVg1n3wsXI7utH97eMInX8qa47gmHAxWf44mxO\r\nH3R9Hw9yCrCn9HdfZMqb1QAXU8Kaa5cIrm1osZ1Y84lm15cJH1RoioW0jswc\r\nuBp0L7zWADl5iDxV/W+0IfK6mavsHhSTajW/hVVlVOQcWpUa4UBB+ClhPBX1\r\nrqIFO4W5PdudOnhdrbOZDv62W2vhgmaN7Q8=\r\n=ElF4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.1.1": {"name": "@csstools/css-parser-algorithms", "version": "2.1.1", "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1"}, "dist": {"shasum": "7b62e6412a468a2d1096ed267edd1e4a7fd4a119", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.1.1.tgz", "fileCount": 17, "integrity": "sha512-viRnRh02AgO4mwIQb2xQNJju0i+Fh9roNgmbR5xEuG7J3TGgxjnE95HnBLgsFJOJOksvcfxOUCgODcft6Y07cA==", "signatures": [{"sig": "MEQCICk3lu82pP7VRmpdJ15U3+py0WhRiUhsWQ99hURwoDnDAiADT6fc5MPbFDxSoIRdIoeKlAOfUDZQnu7IDe2VjGd5Qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31154, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM+ywACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPeA//cvFOapZlkY0LvV3WQNyaV7DEVWJIfaevTZch/VOdPWT2aGM9\r\n3ZkhAWKR6QAQebJVqa3k9ViV79rCOJxePrzAsCc7lROevaElIJDyu70NCV0l\r\n9wMYbgmPq0Ysqe4uxRxp57gihrUmV3UqeYrEYo2AgYZCTeChV9Eswj2cKdYp\r\naIn5Ow0EMKoUfcNZ+trRKuiUwO8b4I2XYhbAdzn9jrhFPxH4A3iFUp2JFpq2\r\nBnxyZCLHga38G+dE19YgvlriDILaAxifO35TzjpkImJ3yPRQWQ1n8kmTx8hd\r\naPZvVuQfblK0kKm5A8ZSxIs3FOLDmcCi6kgF45SVFSOmhDb/R5bRR2Jg3fB7\r\nDkjyC6BKrpRKh8uMepJAc4e0flWhuMvGnalncssh6Da1CE2lymdf8bz6hD/Y\r\nYOzINZNe/Mx8LhK9WgaVXKxVf9RlfZu25Ku61MMHc+Wrp+PC0t2Xlfhy5NYA\r\nQE/0hiDXx9royTNcoKfoBraT1Ab21YDvUL1aE1UZbjle4KCsNM8Nm309li5a\r\n1PgK5JyPrY9Y23sFGiYijDlTOgs8k7Etyas8rl9oRepxbc87nFThynyH16Vb\r\n1dKnRheyuT4yVXR4WN9tTI0qQ4JHYKvhghqhkdcJFPLUyDXEZIx/2tMCAjHq\r\nVbT4Uc0f2i2trl6imobFafwyUmeLQHnuHOo=\r\n=szgt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.2.0": {"name": "@csstools/css-parser-algorithms", "version": "2.2.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1"}, "dist": {"shasum": "1268b07196d1118296443aeff41bca27d94b0981", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.2.0.tgz", "fileCount": 17, "integrity": "sha512-9BoQ/jSrPq4vv3b9jjLW+PNNv56KlDH5JMx5yASSNrCtvq70FCNZUjXRvbCeR9hYj9ZyhURtqpU/RFIgg6kiOw==", "signatures": [{"sig": "MEUCIQDCxZ+gAAv63OuordAEadk/rU6guHlxc5kWabjr8+gLfwIgHW7BzpIGbRpyUjLp6pVf4xUj5j4cXIn939dBalpqEHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31799}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.3.0": {"name": "@csstools/css-parser-algorithms", "version": "2.3.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1"}, "dist": {"shasum": "0cc3a656dc2d638370ecf6f98358973bfbd00141", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.3.0.tgz", "fileCount": 18, "integrity": "sha512-dTKSIHHWc0zPvcS5cqGP+/TPFUJB0ekJ9dGKvMAFoNuBFhDPBt9OMGNZiIA5vTiNdGHHBeScYPXIGBMnVOahsA==", "signatures": [{"sig": "MEYCIQDliBD2gWNQV2c4MOgx13FJgwCl4/GZ9PJA0s/kt2mUVAIhALvirEZQQ4XRuja9IZbVv4v8QD3J4LgVLoo8MInd+kcP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32885}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.3.1": {"name": "@csstools/css-parser-algorithms", "version": "2.3.1", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.0"}, "dist": {"shasum": "ec4fc764ba45d2bb7ee2774667e056aa95003f3a", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.3.1.tgz", "fileCount": 18, "integrity": "sha512-xrvsmVUtefWMWQsGgFffqWSK03pZ1vfDki4IVIIUxxDKnGBzqNgv0A7SB1oXtVNEkcVO8xi1ZrTL29HhSu5kGA==", "signatures": [{"sig": "MEUCIHoqwO24HSVo5o9JziD+DIzlr6L9E6v3AOn9XIv6x3FDAiEA72lf2NhR/LRzT66jSqHcEpeDFSAhfU6uNEZDHkzNFyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33146}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.3.2": {"name": "@csstools/css-parser-algorithms", "version": "2.3.2", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.1"}, "dist": {"shasum": "1e0d581dbf4518cb3e939c3b863cb7180c8cedad", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.3.2.tgz", "fileCount": 18, "integrity": "sha512-sLYGdAdEY2x7TSw9FtmdaTrh2wFtRJO5VMbBrA8tEqEod7GEggFmxTSK9XqExib3yMuYNcvcTdCZIP6ukdjAIA==", "signatures": [{"sig": "MEUCIQDXk4txuqaNO0/386AWRw5QKJbYNp4HVCco842hY7ZFpwIgAqvvqt+GuDgAUb2rOQ6PPjbb6Mq+ds7NvjLNwq6A8Pg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33412}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.4.0": {"name": "@csstools/css-parser-algorithms", "version": "2.4.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.2"}, "dist": {"shasum": "88c7b62b8e00c391b24c585f9db5a0b62ed665b0", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.4.0.tgz", "fileCount": 7, "integrity": "sha512-/PPLr2g5PAUCKAPEbfyk6/baZA+WJHQtUhPkoCQMpyRE8I0lXrG1QFRN8e5s3ZYxM8d/g5BZc6lH3s8Op7/VEg==", "signatures": [{"sig": "MEUCIQC56z13vyXFNZN+DA0w8Xl3o1bjU7+RrWZWKadOn1QFAgIgb4uWlu2YDc8EWbh5QzrTlRQxBvRsMK5Hq5RoKL8iuoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34588}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.5.0": {"name": "@csstools/css-parser-algorithms", "version": "2.5.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.3"}, "dist": {"shasum": "0c03cd5418a9f404a05ff2ffcb1b69d04e8ec532", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.5.0.tgz", "fileCount": 7, "integrity": "sha512-abypo6m9re3clXA00eu5syw+oaPHbJTPapu9C4pzNsJ4hdZDzushT50Zhu+iIYXgEe1CxnRMn7ngsbV+MLrlpQ==", "signatures": [{"sig": "MEUCIQDc3NMLA6DrpZ11b2aQfCGN4c8XtcE0UVQ6q7WUrUdatAIgRqW+6AZnzdm+oFq0Xti86Zda1SGI0ZfmozDkWhN3NAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45912}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.6.0": {"name": "@csstools/css-parser-algorithms", "version": "2.6.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.3"}, "dist": {"shasum": "b45d3c7cbdd4214261724c82f96e33c746fedd58", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.6.0.tgz", "fileCount": 7, "integrity": "sha512-YfEHq0eRH98ffb5/EsrrDspVWAuph6gDggAE74ZtjecsmyyWpW768hOyiONa8zwWGbIWYfa2Xp4tRTrpQQ00CQ==", "signatures": [{"sig": "MEUCIDUvfDukunUAAYJlPbZ1KTAEaSpsb+u0H8SBgnaskBtGAiEA3G4HfoqVW/R05lhO3Fh6gYqXPRCI5hnSDSxKoVrta6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45966}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.6.1": {"name": "@csstools/css-parser-algorithms", "version": "2.6.1", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.4"}, "dist": {"shasum": "c45440d1efa2954006748a01697072dae5881bcd", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.6.1.tgz", "fileCount": 7, "integrity": "sha512-ubEkAaTfVZa+WwGhs5jbo5Xfqpeaybr/RvWzvFxRs4jfq16wH8l8Ty/QEEpINxll4xhuGfdMbipRyz5QZh9+FA==", "signatures": [{"sig": "MEQCICzX2vjdONwKZ/pu1Ks3Z94Eo28dyoNvCfONCavJ/S6TAiA1ZBg87DaSAz+T0R5cCn6n6TQU2UO6k58lYzrHRj5vdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45522}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.6.2": {"name": "@csstools/css-parser-algorithms", "version": "2.6.2", "peerDependencies": {"@csstools/css-tokenizer": "^2.3.0"}, "dist": {"shasum": "e17cec08a2b62f109736d829fac731bfa911e51d", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.6.2.tgz", "fileCount": 7, "integrity": "sha512-f/K04/bX1nD3nKOOGfG+duxFeULE1k/qpfgZcQumRrW1I/sE4VqkJqszmZKhaDc04frQwNoKYowJc13i1xyyvw==", "signatures": [{"sig": "MEQCIDjT3xo6g44/INVDw5/AbWH7+UOjH1fBbxhrBQxwczwZAiBYSaPgGZ4s7v1fBq4fJMOmjGrcbE6wbeAI7+bs76D9xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45170}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.6.3": {"name": "@csstools/css-parser-algorithms", "version": "2.6.3", "peerDependencies": {"@csstools/css-tokenizer": "^2.3.1"}, "dist": {"shasum": "b5e7eb2bd2a42e968ef61484f1490a8a4148a8eb", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.6.3.tgz", "fileCount": 7, "integrity": "sha512-xI/tL2zxzEbESvnSxwFgwvy5HS00oCXxL4MLs6HUiDcYfwowsoQaABKxUElp1ARITrINzBnsECOc1q0eg2GOrA==", "signatures": [{"sig": "MEYCIQDf4CwRfAucsir3Wjc3Qg67xJDVPdoFPzlVLSha0o/bngIhALjTVyGctlARAVkyHhaPDLY79svitUJubvukxdJ0tR5w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45322}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.7.0": {"name": "@csstools/css-parser-algorithms", "version": "2.7.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.3.2"}, "dist": {"shasum": "ee41f252438b97045db2528c1a999e95f15610d8", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.7.0.tgz", "fileCount": 7, "integrity": "sha512-qvBMcOU/uWFCH/VO0MYe0AMs0BGMWAt6FTryMbFIKYtZtVnqTZtT8ktv5o718llkaGZWomJezJZjq3vJDHeJNQ==", "signatures": [{"sig": "MEQCIBLYpflj280ZiroZ70zkYa12PU9n+QlN5TdX3M3BJpVCAiB6tkOlPikOXKI8YPnOmTvFDFP85bggE/Nh0DsQEVsieg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45833}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.7.1": {"name": "@csstools/css-parser-algorithms", "version": "2.7.1", "peerDependencies": {"@csstools/css-tokenizer": "^2.4.1"}, "dist": {"shasum": "6d93a8f7d8aeb7cd9ed0868f946e46f021b6aa70", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.7.1.tgz", "fileCount": 7, "integrity": "sha512-2SJS42gxmACHgikc1WGesXLIT8d/q2l0UFM7TaEeIzdFCE/FPMtTiizcPGGJtlPo2xuQzY09OhrLTzRxqJqwGw==", "signatures": [{"sig": "MEYCIQDoim05B3S2zUR0mDY+Tkz3XAJLAlPh2iFzG89unRVdYgIhANKccrLCfZkUGyl8kiEQoGp4XAcWcGP4retNNknO1AvA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45834}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.0": {"name": "@csstools/css-parser-algorithms", "version": "3.0.0", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.0"}, "dist": {"shasum": "dd428c4569caea7185716fbba174202a4ba41fda", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-20hEErXV9GEx15qRbsJVzB91ryayx1F2duHPBrfZXQAHz/dJG0u/611URpr28+sFjm3EI7U17Pj9SVA9NSAGJA==", "signatures": [{"sig": "MEQCICDya5pB6TelJQWvKovHy0pvLmoJwDh+w8ptM2aH9Zc5AiACR/IYKxDgW32vE31BRPSULhVxnALbIw99RMSjpbWhVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45302}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.1": {"name": "@csstools/css-parser-algorithms", "version": "3.0.1", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.1"}, "dist": {"shasum": "f14ade63bae5f6025ac85c7d03fe47a7ca0e58af", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-lSquqZCHxDfuTg/Sk2hiS0mcSFCEBuj49JfzPHJogDBT0mGCyY5A1AQzBWngitrp7i1/HAZpIgzF/VjhOEIJIg==", "signatures": [{"sig": "MEUCIQCtnSRj9jWRoj7RT3HWKZvlUb0Z869wVKEri/Ux+fhPxwIgJZmxC8ISvlzzKFnhVzRrPYaK0MxqONyOLbtCJW8Jfzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45261}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.2": {"name": "@csstools/css-parser-algorithms", "version": "3.0.2", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.2"}, "dist": {"shasum": "be03c710a60b34f95ea62e332c9ca0c2674f6d5f", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-6tC/MnlEvs5suR4Ahef4YlBccJDHZuxGsAlxXmybWjZ5jPxlzLSMlRZ9mVHSRvlD+CmtE7+hJ+UQbfXrws/rUQ==", "signatures": [{"sig": "MEUCIQCk9VuG+lUeKuWWJG8FWBD7u+hQ5WTzFgLDyPNc+tVKcwIgWFDEYyi5xtVbxoprN91ELEIJGktcT2aBqkDRqNoLG88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45262}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.3": {"name": "@csstools/css-parser-algorithms", "version": "3.0.3", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.2"}, "dist": {"shasum": "f0bffb2faa0f054eda350c1abd25306f15465323", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.3.tgz", "fileCount": 7, "integrity": "sha512-15WQTALDyxAwSgAvLt7BksAssiSrNNhTv4zM7qX9U6R7FtpNskVVakzWQlYODlwPwXhGpKPmB10LM943pxMe7w==", "signatures": [{"sig": "MEUCIQClV6hrZyD6oOHSfCqNV1FfHSi0jw65TPwIgnX2EoSDLgIgXMNfJgUNR7rR23K5lxzeXo5e6k5Q/kwm08R5/h1Wblo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45128}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.4": {"name": "@csstools/css-parser-algorithms", "version": "3.0.4", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3"}, "dist": {"shasum": "74426e93bd1c4dcab3e441f5cc7ba4fb35d94356", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.4.tgz", "fileCount": 7, "integrity": "sha512-Up7rBoV77rv29d3uKHUIVubz1BTcgyUK72IvCQAbfbMv584xHcGKCKbWh7i8hPrRJ7qU4Y8IO3IY9m+iTB7P3A==", "signatures": [{"sig": "MEYCIQDQ0JwEzaORzT764f2F4DU47jupC1EQ5l2MEy9JAMx1rwIhAJPsQOpVFWd1HC+E07ot+g8criOsXRKY/gT1rLDQhKbR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45265}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.5": {"name": "@csstools/css-parser-algorithms", "version": "3.0.5", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.4"}, "dist": {"integrity": "sha512-DaDeUkXZKjdGhgYaHNJTV9pV7Y9B3b644jCLs9Upc3VeNGg6LWARAT6O+Q+/COo+2gg/bM5rhpMAtf70WqfBdQ==", "shasum": "5755370a9a29abaec5515b43c8b3f2cf9c2e3076", "tarball": "https://registry.npmjs.org/@csstools/css-parser-algorithms/-/css-parser-algorithms-3.0.5.tgz", "fileCount": 7, "unpackedSize": 45381, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCpaCBEtl/3omcnqC9WOyWiafdqY3k31V1D6+34TzVoIAIgbjOBUWzduyP1pOISHcMW7JmMRsW26uSjos8DhflZiyc="}]}, "engines": {"node": ">=18"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}]}}, "modified": "2025-05-27T10:46:45.806Z", "cachedAt": 1750172042011}