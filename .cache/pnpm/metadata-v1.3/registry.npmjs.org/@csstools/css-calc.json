{"name": "@csstools/css-calc", "dist-tags": {"latest": "2.1.4"}, "versions": {"1.0.0": {"name": "@csstools/css-calc", "version": "1.0.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.0.1", "@csstools/css-parser-algorithms": "^2.0.1"}, "dist": {"shasum": "f93e4cc9d80b504467aee1b5251bb2fd8be435a7", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.0.0.tgz", "fileCount": 63, "integrity": "sha512-Xw0b/Jr+vLGGYD8cxsGWPaY5n1GtVC6G4tcga+eZPXZzRjjZHorPwW739UgtXzL2Da1RLxNE73c0r/KvmizPsw==", "signatures": [{"sig": "MEYCIQC9i2+TEiXmRqt+27ZYY6hnnXe1AYygH5xUUcjb9GUCtQIhAPeL+tVSb72VYoN9e5EAeLGRb023KwC6i2s/hUvn5EDi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9O+IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq31w//Q6c41BtXML+dggHh1ZVavOENZg8Afcm1TwW6POisnAMcgRhi\r\nhBwrIEy8VFsESTxZOXJ3PwztlIZMqYQPPh8zVZyzWbQPZWTRSdeWSkkEnj76\r\nQM+zqG7nApVAM2bGLnTtumLRYfWYdl/UOiP+a01X4AbjJzqGcr/YtiNu60YT\r\npBk1qM/S6Zz+TD1f/J5QkqAl1JWGDstLw/iEZ8RU9l9PWyPwAbPuZNquaUk+\r\n/hto+W/xaxBUIj8/B1ixCoD3fwrZDzVhbLMt5Nq3A/bQKCwY11pHPYlfZQw2\r\nCssJxwNXASgJBu4mdgjuLniKckkGmN57f861yT6xQ6e88hUJ5zPqidCs2d5B\r\nF/WXtSpqnUBpFZa+09+WabVEdU+2NgSlRKzNUINcCTfKXCIg+BHuKeC8rv/c\r\n+9cmqlr36MbIDQ8SPttRidoUjKRnk1cG6QtFOeSKBe5M20vRESRMfhr0cAmP\r\nMuYCdEuN991MZWrfkRFgFTcNMshiFcNhi7PBgvN3fEqZlCVP3fwYgHeUCx3v\r\nIH6+F1ScmuqZzWewCCypwNXzQgHMxjqnSXtBHXO+SM50Aukh8MAY/ezaGw0m\r\ntOcVObN20F1p1X9xy9sox43PPo7/GE8MDN3BQu7qolhzrrbFSvWK0Gsz8iA8\r\nfyWKAwn2wRFCSYDfFWtcPpIpkBAEla4jt+o=\r\n=byev\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "1.0.1": {"name": "@csstools/css-calc", "version": "1.0.1", "peerDependencies": {"@csstools/css-tokenizer": "^2.0.1", "@csstools/css-parser-algorithms": "^2.0.1"}, "dist": {"shasum": "c478dbfb2c10e22741b261b2a64998960d69bfc7", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.0.1.tgz", "fileCount": 64, "integrity": "sha512-VBI8X0bmStfc85wWTa2bsbnlBQxgW4FmJ0Ts9ar9UqytE6kii3yg6GO+wpgzht2oK5Qlbpkm1Fy2kcqVmu6f3Q==", "signatures": [{"sig": "MEYCIQC/IhuiSmF1SBudY7jjzVZ3ZTtoGrDN2kdFQJN0wCEEuwIhAMhhQFCwIgjOCzlHcb1b+XR1W+cep2jdlgQtShnH5Vpq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHqg9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUIA/9GRCLduRMxblkq+RQGp+RnvOfW+wgoRsIAAzKD6WICluNQaIc\r\naQ9mOX3FO5CIPiKdwNB2ctREp+rWITelWj2L5VZ580R8Ww5sj/3Au5es/Pf0\r\nJXFGk8buoiAiMMF1AiIGe7Axq9nxpyZv1S/izTRHMkssQmGr2tewaWPqZR9n\r\noR2ms6UCly+KBqXBTY9r9TxW3D/Gd0HYjGnTrtMlrEEKngnLTPXeTcsZ0mRK\r\nnX5ulLCAuoZJkqOlD/m9ZD84Lvfpg6k8a/fehz9inxaLZ2RFGBDaF64p2J5n\r\nOEVln5DxSTfw9Ry/pKqJm3ghax2u4R0NyirxFaBFZyjKMZNrJSNRilRqoMVu\r\nwsUyzqoos08tIPbVSdtulGwFC1H0XFiLlGB3oZJT1H3Iod82/MskTO901gAm\r\naaC9CyMI9MA5gbEC+ZD5mAhWCsgS26zSqPILgSzDm4Z851uoyTDQYt1Da2Gh\r\nowQSXLdB2ZNdDS4Id8EdumDqOADWdMwnTyY6psJr1MnKBngKmpNscY1SQ4FB\r\n73D4nEHwOQiM9f/8wq24nP2dgOkvOCX+3Mwal119k4L2XeVm8P2ncoEeBs/B\r\n5Ewj5vKwradf+7g8u/GpDGZWLXtoMosCo6LVYyRKLpdgGxBVwJRjeiSzgn3k\r\nXAxPhLW6BxTiCllTnMU9jQLhfvEncxWrCq8=\r\n=YPXn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "1.1.0": {"name": "@csstools/css-calc", "version": "1.1.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.0.1", "@csstools/css-parser-algorithms": "^2.0.1"}, "dist": {"shasum": "1470b40c59669fb0acb04fde41a483fe11ef5249", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.0.tgz", "fileCount": 64, "integrity": "sha512-/d0vIpFLa3aam5vxbv+u8sKEoSVS6oJkJcPdrp54n4O1zkkp8Ob6mYSYCNmY+PgOu5/EE+L5mHJobLOQOGZqdw==", "signatures": [{"sig": "MEQCIFgLvG04vY0SeQXWlyyGzY32Ax+78On8YWeARWEX5HzEAiAUDzM94Q74YxeSglfr8kG7b+D+Tkpee6ATgXdLyH2ZUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM78wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGbw/7BapV7fIa8301CZUGv9+amgnlBERfw0PTfgI/QQIfkqAOjYbK\r\nsce2mlLSgrazQYec7WOgEJfNSQi1odcYLO+hqVxH1syB3PlyOjd+95JrvQqX\r\nBcbW6b371vIB++3SXYh7a0pUsfF6PqdBrOm/s2N6PLvfr4RAFOqG5hCeHOT9\r\n67tgMJ6YSGid+3SwHs9VxJbNaXRckK02pQWlpDSB2OYWvuylDgGDsnTxhFUt\r\nPzbNIUxwuuL7pW/ERvbDBjUW+vpv3U56W7EILbc37/I/Scel3sXHcRp7ksig\r\nk8mdmqst89jRXCiqnV6Zy9d+yXI9hFt3D0ELOVpfuclQhb8oaUTISYuXx4aw\r\nIKGP0W9tmk1gN2E853JNXZr+eKNjlfDhvBE6Tz1yIs47XRkv1hkQltgy4N7i\r\nD/nfgznH/2/a/oBKuQ5ZestI1xlLWg9yJjuo9lfPbKCYF6LSHqPa9nRt1adW\r\nyPgjO/+I1XVhruwLopHZLn0TQoR8HEqC3GflV5PM3GM63z0a51IE+Q2JeaV7\r\nuaPuXcXVgaITLf92uEEqz7GeflC4n1n06C2L32SMrron6tlOPUQy0nwrbHbR\r\nEPSuDURMy1tx4L5iv2q9t8icG/9M2duGpOYI2tVHWILebEHmxNf1U0QTJXDI\r\nAm+qM6N2GTf2uK3nOoN0VLfZfYjqniz4SkI=\r\n=xs3c\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "1.1.1": {"name": "@csstools/css-calc", "version": "1.1.1", "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1", "@csstools/css-parser-algorithms": "^2.1.1"}, "dist": {"shasum": "c622728b7f0c9aae70952623c2b0d3d114752987", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.1.tgz", "fileCount": 64, "integrity": "sha512-Nh+iLCtjlooTzuR0lpmB8I6hPX/VupcGQ3Z1U2+wgJJ4fa8+cWkub+lCsbZcYPzBGsZLEL8fQAg+Na5dwEFJxg==", "signatures": [{"sig": "MEQCIC8YGWipEENfyyOHtaoMYSnZSuPv4jabrKOB1MVJ2Zm9AiA3mfrSfqCMztscCXA/s5C/1P4+ysvrqSqeOHToXsB3yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM+zKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNiRAAmgF31OGb1XdKQfM5UJo5ibs14cF/PPxXix1ABOXTwVYG+rZD\r\nwQXSQzkUi3zZgc/mfcFE95JUBCIgwgPbMOVGIv7w9/sv9Iw0vwlfIlo8fAT4\r\nNIW50j2bSb5ADvYOx67cFe2hwDUXU4jIhqh/0Ma2t6KNbMl7rYkyLu3/VPHc\r\nTOE4heKid/PS7xrfPf+YljVRnm15j1RvXNZfPxKArO7wJZ4QjTOh5VSxNk2U\r\nEADp4o/4mBF6+RBHmzN6ikKl4qTDeDywOt1zpc4toSC3OfgHk56RIAdnAjOt\r\n8NOTrWEAotwGwMzqX8mCT8dV5pigntSePyFgqcHo1IuBgzzBoxG0sauCgG66\r\nbCm7T0PiUzhMwFLzZfIryMgOxxUuOllnTP8cF/kxBAbR+uybif1p3DNS4fpr\r\nfGaCd4qIbZ0thaM1UugEwgtgzZpF9TX7+y4vDii/RyjWV1XJIdLFCAv2IpJs\r\nqRirP4jxjko2i7J7n7LRj5rqcFpN7JoEhK15Faqgmm8IDQkwx+qWkOXrmsNs\r\np3VAkxoGbHqVtgnKBp9OXdMfwRnLDUZiHvA0IjMGbfDVfCBb3mbbfvoiHz8R\r\n0ZFn9L8w96j6KL8CIQ1VnLbL7Pold2oDEu0CPtquZ5cGarXhBLDVzoXGh6h+\r\n0ImqY7rfifviexNOJPr60PvP60c34np5Tho=\r\n=zZhi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "1.1.2": {"name": "@csstools/css-calc", "version": "1.1.2", "peerDependencies": {"@csstools/css-tokenizer": "^2.1.1", "@csstools/css-parser-algorithms": "^2.3.0"}, "dist": {"shasum": "bf2c376bbb9a43de4851a7efcde1818d18e0fe7d", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.2.tgz", "fileCount": 64, "integrity": "sha512-qzBPhzWz4tUNk2tM1fk6tOSGaWlrhmH66w6WyUDoB+2Pj7pxvu6mlvXVwOGODGJBIF158aPWPheVQgcoBTszkg==", "signatures": [{"sig": "MEYCIQD5B4jS/ZzcpRbPG81XoNQZAoURb7gQb57Gjc9W1TnkAAIhAJh895TE23lyxJDgldTTsGcnmimjHJUAifOTVc49YIzP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72317}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.1.3": {"name": "@csstools/css-calc", "version": "1.1.3", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.0", "@csstools/css-parser-algorithms": "^2.3.1"}, "dist": {"shasum": "75e07eec075f1f3df0ce25575dab3d63da2bd680", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.3.tgz", "fileCount": 64, "integrity": "sha512-7mJZ8gGRtSQfQKBQFi5N0Z+jzNC0q8bIkwojP1W0w+APzEqHu5wJoGVsvKxVnVklu9F8tW1PikbBRseYnAdv+g==", "signatures": [{"sig": "MEYCIQDvXKrqJuDVr9Ayv70KCnL3428oIn7Kq3sKsMEBZruq1wIhAOA/SX+BuCkRo1AHa20zUBZ69P/vnomcOG/QXuQ4aHbE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73040}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.1.4": {"name": "@csstools/css-calc", "version": "1.1.4", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.1", "@csstools/css-parser-algorithms": "^2.3.2"}, "dist": {"shasum": "70bf4c5b379cdc256d3936bf4a21e3a3454a3d68", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.4.tgz", "fileCount": 64, "integrity": "sha512-ZV1TSmToiNcQL1P3hfzlzZzA02mmVkVmXGaUDUqpYUG84PmLhVSZpKX+KfxAuOcK7de04UXSQPBrAvaya6iiGg==", "signatures": [{"sig": "MEYCIQCAvIKmvnTNH8Hz9KPDycFKNKSG9O1I66q8EpYVJIREHAIhAJPdlOQNSVQWOVPnC+HiIx4dcWaetxGW/Pv9MZAMZCng", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73562}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.1.5": {"name": "@csstools/css-calc", "version": "1.1.5", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.2", "@csstools/css-parser-algorithms": "^2.4.0"}, "dist": {"shasum": "cf5ac0b51a0533bd69be258b15ae243a7a47e3e1", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.5.tgz", "fileCount": 7, "integrity": "sha512-Uh<PERSON>5oSRAUtTHY3MyGahqn0ZzQOHVoPpfvUcOmYipAZ1rILAvCBoyiLSsa/clv1Xxct0SMKIq93KO5Bfl1cb6tQ==", "signatures": [{"sig": "MEYCIQD8QSyG0X0WbhucCkoGDiYpxcmhys7CgtQwXdXyaXUc7AIhAOKshEENsZZR/4LjnsqXyNczuR8PIN0WGMaCMdGmsnoA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60195}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.1.6": {"name": "@csstools/css-calc", "version": "1.1.6", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.3", "@csstools/css-parser-algorithms": "^2.5.0"}, "dist": {"shasum": "2d4e16725c3f981f7c6e469c306bcb1f490e1082", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.6.tgz", "fileCount": 7, "integrity": "sha512-YHPAuFg5iA4qZGzMzvrQwzkvJpesXXyIUyaONflQrjtHB+BcFFbgltJkIkb31dMGO4SE9iZFA4HYpdk7+hnYew==", "signatures": [{"sig": "MEUCIQDejJXLlipi2WkB52OsSpcRY5U4GF9Q+xnvKhLj696I8AIgb4mOQ4MEwlkH1EMc76hk1FgAVVrmBYYCSFnrpAyKtmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60157}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.1.7": {"name": "@csstools/css-calc", "version": "1.1.7", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.3", "@csstools/css-parser-algorithms": "^2.6.0"}, "dist": {"shasum": "89b5cde81ecb4686d9abd66b7eb54015cf39c442", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.1.7.tgz", "fileCount": 7, "integrity": "sha512-+7bUzB5I4cI97tKmBJA8ilTl/YRo6VAOdlrnd/4x2NyK60nvYurGKa5TZpE1zcgIrTC97iJRE0/V65feyFytuw==", "signatures": [{"sig": "MEQCIGI3oEVo3OphIKa0sWeO8jJB/71WbsGc7IZckeEOLklxAiAjn5PN1QcnogRlLAyeOdLYEZ+r8QNi61T2unu89xt1kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59924}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.2.0": {"name": "@csstools/css-calc", "version": "1.2.0", "peerDependencies": {"@csstools/css-tokenizer": "^2.2.4", "@csstools/css-parser-algorithms": "^2.6.1"}, "dist": {"shasum": "a45145a868e644c31c79baf74c8de64fd09b3415", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.0.tgz", "fileCount": 7, "integrity": "sha512-iQqIW5vDPqQdLx07/atCuNKDprhIWjB0b8XRhUyXZWBZYUG+9mNyFwyu30rypX84WLevVo25NYW2ipxR8WyseQ==", "signatures": [{"sig": "MEUCIGvESM2I9FoaYDNAkCQpFyTa/zX7qO5/8GK5WD8358RDAiEAxmlVwZDFUc7q5d+Can7mIsZ43McpQ7GYoYf3vNuukas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60749}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.2.1": {"name": "@csstools/css-calc", "version": "1.2.1", "peerDependencies": {"@csstools/css-tokenizer": "^2.3.0", "@csstools/css-parser-algorithms": "^2.6.2"}, "dist": {"shasum": "ad105be9960b9ea072198204f3862fb79eb541ce", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.1.tgz", "fileCount": 7, "integrity": "sha512-xNLOBhlXe3qE3REPtPOQVY6WV3XJoJ3RFRf83fLiFFWbOZd5AT7mqcIZB014I72KMn033DAahDXZWIyoxe5p8A==", "signatures": [{"sig": "MEUCIHVuUvYJezDz9L6EOvcMfN7ZJFKUAroAYDvchkZb5jh7AiEA6gOw4q0jjOmMCsBGo44D/C7HUTXjaGaCQFOU+NVT/aA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59285}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.2.2": {"name": "@csstools/css-calc", "version": "1.2.2", "peerDependencies": {"@csstools/css-tokenizer": "^2.3.1", "@csstools/css-parser-algorithms": "^2.6.3"}, "dist": {"shasum": "bcb856e63ecc16a7508f43e77ea43ac5daaf2833", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.2.tgz", "fileCount": 7, "integrity": "sha512-0owrl7AruDRKAxoSIW8XzJdz7GnuW3AOj4rYLfmXsoKIX2ZZzttzGXoiC8n8V08X7wIBlEWWVB4C8fAN18+I6Q==", "signatures": [{"sig": "MEUCIFcmpVVp52UrPhCAWVL94kdXn0eFCvRLU65l9gudGe6gAiEAqIYqEH8wY1pvE1KWiG4Jh1IWAYzuN1t++qvtLMb/Pf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59511}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.2.3": {"name": "@csstools/css-calc", "version": "1.2.3", "peerDependencies": {"@csstools/css-tokenizer": "^2.3.2", "@csstools/css-parser-algorithms": "^2.7.0"}, "dist": {"shasum": "4ea248c39d27b8e326dce3e82268249e2cbccaf5", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.3.tgz", "fileCount": 7, "integrity": "sha512-rlOh81K3CvtY969Od5b1h29YT6MpCHejMCURKrRrXFeCpz67HGaBNvBmWT5S7S+CKn+V7KJ+qxSmK8jNd/aZWA==", "signatures": [{"sig": "MEQCICZzNqhCEa78nYKIMuIy1HOMU7ggyzwU1eBql5WTJqPCAiAZuvr7h/eT26W9mfER6oBm+uxu94RevkyiSpr1FXNWmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59377}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "1.2.4": {"name": "@csstools/css-calc", "version": "1.2.4", "peerDependencies": {"@csstools/css-tokenizer": "^2.4.1", "@csstools/css-parser-algorithms": "^2.7.1"}, "dist": {"shasum": "9d9fb0dca33666cf97659f8f2c343ed0210e0e73", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-1.2.4.tgz", "fileCount": 7, "integrity": "sha512-tfOuvUQeo7Hz+FcuOd3LfXVp+342pnWUJ7D2y8NUpu1Ww6xnTbHLpz018/y6rtbHifJ3iIEf9ttxXd8KG7nL0Q==", "signatures": [{"sig": "MEQCIGIz61uE+oLaWLmE2C9wJ5MdBYs0Cs1C3n0zYvGxsOIOAiAc9PkgUebYZaLZGqDKIM4ar4B72kQocTfYiMkyyIo6xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59260}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.0.0": {"name": "@csstools/css-calc", "version": "2.0.0", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.0", "@csstools/css-parser-algorithms": "^3.0.0"}, "dist": {"shasum": "f75d4b2f17b38ffe56c151a80d4b97e8f8968cc5", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-fxPxNrEVGeej4F35Xt69Q7gPMKa7oEGNxeP1DpA01sWpTF3Yhgux/0slVX3jLHd7dhlszeQlNAUhpAorVxoHdQ==", "signatures": [{"sig": "MEUCIBakxJEq9MqiAsUpbw7bWX+bRs06DnfoVIahRCiuRhJ4AiEAnHejeTiw8sM0ax0DlKeN2iL8nOWgQSdTBxRPgBi1rxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58263}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.0.1": {"name": "@csstools/css-calc", "version": "2.0.1", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.1", "@csstools/css-parser-algorithms": "^3.0.1"}, "dist": {"shasum": "1675297b19f0933c729fdd7f4f5279b855ae724f", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.1.tgz", "fileCount": 7, "integrity": "sha512-e59V+sNp6e5m+9WnTUydA1DQO70WuKUdseflRpWmXxocF/h5wWGIxUjxfvLtajcmwstH0vm6l0reKMzcyI757Q==", "signatures": [{"sig": "MEUCIG7A+7AKEkUOSrN5zJPevbfo3Edp0GGytMDy/9nNBQtBAiEAv4y3d9UBdtmn0ozErFkEmTqVQC2gDE4Ft1eJlCg0UU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59630}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.0.2": {"name": "@csstools/css-calc", "version": "2.0.2", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.2", "@csstools/css-parser-algorithms": "^3.0.2"}, "dist": {"shasum": "20f55c1c5857999b9cb0acca38e4eaf1a38b53e4", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.2.tgz", "fileCount": 7, "integrity": "sha512-N70YZw+R6WDP9EEd5xAT3xd+SgZFZsllXR6kclq6U8e2thlakNpWCKhuOiWfCKU8HpeWOyL+2ArSX8uDszMytA==", "signatures": [{"sig": "MEUCICr8eNVrifVVXFjPs2p0NGEmBte/8VlcUtETCNSDDCqzAiEAz5pj5RViEPOusSFLSs+33T+CPbH908GvULnolMFD2Go=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59397}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.0.3": {"name": "@csstools/css-calc", "version": "2.0.3", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.2", "@csstools/css-parser-algorithms": "^3.0.3"}, "dist": {"shasum": "51d68e75b19c686a4aa916bbe647055f42687682", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.3.tgz", "fileCount": 7, "integrity": "sha512-UAhqOt43s8e4MfLAnIS1OmB/lDN32t03YObodmFyy60+1i6ZsT2rlwBEdajH6zDFS/TGogsvgMamV5GzZt2muA==", "signatures": [{"sig": "MEQCIDf8jt2xfHTHEadlLT87hCUlB+Hl/YxH2jP+E6OtBJZbAiAf8p9MorBdmbhq/uZBbBCnOSclNla8LtbHzQeF1imLIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59165}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.0.4": {"name": "@csstools/css-calc", "version": "2.0.4", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "dist": {"shasum": "c04be9b80a65dc70b82a6acd8ab3bcf3301827ad", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.0.4.tgz", "fileCount": 7, "integrity": "sha512-8/iCd8lH10gKNsq5detnbGWiFd6PXK2wB8wjE6fHNNhtqvshyMrIJgffwRcw6yl/gzGTH+N1i+KRhjqHxqYTmg==", "signatures": [{"sig": "MEYCIQDXLgLPNmWTcmPX7lkp9HyoWEzwJR7ca9NScIRFLBcEGwIhAIHbqU8yd4ICrfDnWwhFaz9n9jFbEG+6+AJ8BUf6cPwZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59397}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.1.0": {"name": "@csstools/css-calc", "version": "2.1.0", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "dist": {"shasum": "3f28b8f8f736b8f78abbc75eebd55c756207e773", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.0.tgz", "fileCount": 7, "integrity": "sha512-X69PmFOrjTZfN5ijxtI8hZ9kRADFSLrmmQ6hgDJ272Il049WGKpDY64KhrFm/7rbWve0z81QepawzjkKlqkNGw==", "signatures": [{"sig": "MEUCIQDIaqN2slxawRfmjUNWqlyiRD5Qjd0OALmCWsivnx/MhgIgXDtE8asWZa4ytBKs3NewjJeILvby5ixGbNUN+xPMC1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67522}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.1.1": {"name": "@csstools/css-calc", "version": "2.1.1", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "dist": {"shasum": "a7dbc66627f5cf458d42aed14bda0d3860562383", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.1.tgz", "fileCount": 7, "integrity": "sha512-rL7kaUnTkL9K+Cvo2pnCieqNpTKgQzy5f+N+5Iuko9HAoasP+xgprVh7KN/MaJVvVL1l0EzQq2MoqBHKSrDrag==", "signatures": [{"sig": "MEYCIQCWlxP3fKOaOlkUEwuJQueioJ1/24QXH3lB1A9CVx9NHwIhANlzc1YNDq8ntzZhqVm4RiIHXe/dA55mlX6KgNXqulQY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67658}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.1.2": {"name": "@csstools/css-calc", "version": "2.1.2", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "dist": {"shasum": "bffd55f002dab119b76d4023f95cd943e6c8c11e", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.2.tgz", "fileCount": 7, "integrity": "sha512-TklMyb3uBB28b5uQdxjReG4L80NxAqgrECqLZFQbyLekwwlcDDS8r3f07DKqeo8C4926Br0gf/ZDe17Zv4wIuw==", "signatures": [{"sig": "MEYCIQDdeMxYe/fv4iscvcZ/dr+R/eSV1Jf76nPA4wRgqvyX/gIhAJY66bjlC0NEkeGJpow+3+XCpfIF8uhDMkxx6HqE8W7H", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67883}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.1.3": {"name": "@csstools/css-calc", "version": "2.1.3", "peerDependencies": {"@csstools/css-tokenizer": "^3.0.3", "@csstools/css-parser-algorithms": "^3.0.4"}, "dist": {"shasum": "6f68affcb569a86b91965e8622d644be35a08423", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.3.tgz", "fileCount": 7, "integrity": "sha512-XBG3talrhid44BY1x3MHzUx/aTG8+x/Zi57M4aTKK9RFB4aLlF3TTSzfzn8nWVHWL3FgAXAxmupmDd6VWww+pw==", "signatures": [{"sig": "MEYCIQCCunXbKJ/vhz/DJnIHCdpbZKrusWtylm+N/lohSpGBTwIhALo8mrtNNwjta+YMXSYJBTgCinX98MXBMrqXYADckkSz", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 70683}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.1.4": {"name": "@csstools/css-calc", "version": "2.1.4", "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}, "dist": {"integrity": "sha512-3N8oaj+0juUw/1H3YwmDDJXCgTB1gKU6Hc/bB502u9zR0q2vd786XJH9QfrKIEgFlZmhZiq6epXl4rHqhzsIgQ==", "shasum": "8473f63e2fcd6e459838dd412401d5948f224c65", "tarball": "https://registry.npmjs.org/@csstools/css-calc/-/css-calc-2.1.4.tgz", "fileCount": 7, "unpackedSize": 71012, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDvhPy0nwpA33lpHyiREpwn9tAedgWa3g2RLmzkaZROQAIgHFqbHVR1KAIJ819dfJZ5CWGvWM11/aLgFxIz7dqTug0="}]}, "engines": {"node": ">=18"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}]}}, "modified": "2025-05-27T10:47:50.974Z", "cachedAt": 1750172042017}