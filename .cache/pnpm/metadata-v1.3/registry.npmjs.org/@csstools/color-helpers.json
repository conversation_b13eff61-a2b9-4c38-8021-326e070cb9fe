{"name": "@csstools/color-helpers", "dist-tags": {"latest": "5.0.2"}, "versions": {"1.0.0": {"name": "@csstools/color-helpers", "version": "1.0.0", "dist": {"shasum": "7097722a51da1e9e622345ca000261f1ae6e8f58", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-1.0.0.tgz", "fileCount": 77, "integrity": "sha512-tgqtiV8sU/VaWYjOB3O7PWs7HR/MmOLl2kTYRW2qSsTSEniJq7xmyAYFB1LPpXvvQcE5u2ih2dK9fyc8BnrAGQ==", "signatures": [{"sig": "MEUCIQDQdy22U9Eq1hXMI4+g5z92DtP+D3hnwNCDaRp26ozcrwIgCgjYHhpfbKvG4nkBvLmuaxgljCtOZJniUHppkk2KmH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj23dfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO+hAAo2M/WSutMafKdqWl7fVDYx3AXgvJNZHT/b1T+quSZGUmfU8w\r\nfKwuKLE9R29O+MNKTzIQSQFywQro1a5fz6j5Y5bx2ymyKxZg3uM5tyqPckRg\r\n4BwLI4tOofBG93aRXvlm5/ciRGwtOf2mI0YX528MksgceOoJmqvrgGosvEAv\r\nPSsvcFuvFNNtpiZbIbuV3uzNasjMFMjCPSJ0UKBCjVlB1vGBsMLykTlHlIJW\r\nRQaB3cDm+raNHekqsweeR6yFAisisxdaRENAyC4W6/L53aHVaPplgQjq85as\r\nGWIcYypRw8q/3Ffi7ILQ9d8YJMF0xBUEcmNL4R99k/X+s6w6Z8Yt0faknQmu\r\n0LXp3AsJboDZeoDWPb0p8wM69GaHfxvJ8Ms1iDMjL0TPHaNAjmS0uLRIrpxM\r\nghT0oi/vS/4SXcDEyc5alRomT7Ty+badXTWiH3j821xkLGC/lktB60PUNJ5F\r\nupixbzqJUQG36nw3s9jIP632EsGnEiWgKegal7FIlKaxoecriGioS8SxBep6\r\nCzMZtUYZVdzYeGGhth9li/4RiK14Cswz7NzAyDhmb54bvS047tBdRNg1qPYP\r\nGYc0J1FJsMs/xQ54CTT5MllfVL+hCXs4XYex7Ou+npkuRhfmUEhBK5O9+lZL\r\ns95QBYADC3gJ4lRDkMC9BYmTUnde8SWMSTw=\r\n=uhvV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.0.0": {"name": "@csstools/color-helpers", "version": "2.0.0", "dist": {"shasum": "4ac578cb00b4e853b94f2250267d85ba957c4fc9", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-2.0.0.tgz", "fileCount": 59, "integrity": "sha512-VcPjEnp07RNgz/D+oI2uIALg+IPCSl6mj0XhA3pl3F2bM2B95vgzatExmmzSg/X0zkh+R2v+jFY/J2pV/bnwpw==", "signatures": [{"sig": "MEUCIDm1+YucQL4WVEHjPp3sVlCbcqCOPeoxRZ+TRfsylRVnAiEAo739WtHcQ20vdQFBCqzAh2NNQ4RXe7XN9JYy2tOa8pI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkHqVJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGmQ//fOTEEuybVyjUctQUWl1ztgx1kB4HU+biqalRFgQ6sHCoSFnR\r\nFH1mh+Gu22P9YY8RuDK4QGOqXL+Lx1WgrIfPtYJ5hTDX1a218bN3u/pKzkFU\r\nEpuCoCwl86T5CgvlEYsEERX9GdVsFYvcrPvYNPVGmlwVtrijhldng9zzK0f4\r\neOt6UjXT1AicC0yEUM0quCt4vwI2Tb+SBbDDRWfumicVbkkugMhBtWqb1a5c\r\nKhNxctlagphiSuq+Nd+bADloIC3GKdoPOvx9IpK1/Qx9bDMhkQ1F7ocffxpA\r\nixYf4IH/Vn9U6mg8V87/n5fM5rsNkWUEOH1ykZU8dPxh35iSxvq6eO25MXJY\r\nM9aYpx9uZxhH9hbjThXbzzdaHtRflZHL0nqb6QKhRSyTuNPLRFqmOHCg8Ai0\r\npiMMuucvfHXP3wFnZk4sDE+JCk6zpl4V97e9oKmagogIvd5/T9n5Ivt5NXT2\r\nGIOMbHKHfHtuVehMjAN4CTpT0fe6RqsjMrZ3ye0lsNU4NjqqKK0+4kdOl2qG\r\n9i+o8oPfFbjWNtNWK4ObFJw/meyjGWi0R4TAYtTCyxgRzgF1p7dJ4/IvaYyz\r\n/Pt0gEV9ou9HlZL+HSK26puHFgxu09/91KOCdqcBDl1Fo6HQG57HswuFgErq\r\nHzKmBALOGJtWn1/cWd+vojDqodL4zbxP2/A=\r\n=gNq+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.1.0": {"name": "@csstools/color-helpers", "version": "2.1.0", "dist": {"shasum": "b27d8376e9e8a947878f10967481c22bf046976a", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-2.1.0.tgz", "fileCount": 60, "integrity": "sha512-OWkqBa7PDzZuJ3Ha7T5bxdSVfSCfTq6K1mbAhbO1MD+GSULGjrp45i5RudyJOedstSarN/3mdwu9upJE7gDXfw==", "signatures": [{"sig": "MEYCIQC6aZyGLsctQv8pANMWUxhqGkH9eS1rLMvucNNoVs7TkgIhAMOTB55YY6cxEWAYYsFkCncj96MtHzwOz8im4Y86Gae3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114601}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.0": {"name": "@csstools/color-helpers", "version": "3.0.0", "dist": {"shasum": "b64a9d86663b6d843b169f5da300f78c0242efc2", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-3.0.0.tgz", "fileCount": 60, "integrity": "sha512-rBODd1rY01QcenD34QxbQxLc1g+Uh7z1X/uzTHNQzJUnFCT9/EZYI7KWq+j0YfWMXJsRJ8lVkqBcB0R/qLr+yg==", "signatures": [{"sig": "MEUCIQC/Rx2bVbMHkBJOTix57v5LLmQRWYygMyeOXqDcRH5/8gIgJF79/ZhYMhclhK53nVm5oK/tsIlP+WEtP04A7XwSwpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108888}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.1": {"name": "@csstools/color-helpers", "version": "3.0.1", "dist": {"shasum": "16013825e8c730de164a5dad27c299abae2eee18", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-3.0.1.tgz", "fileCount": 60, "integrity": "sha512-Tsp6FcSPaPN/+4T7iBPxBVopJUs7bimnNx4yuWeGXkH084Vro/y8fmrGg1LGSWH8SU6YuH20fP5Rtqtb979jyw==", "signatures": [{"sig": "MEUCIQChTxVjhSavE18CnpkmUPp3wvCVQTbgGpsgGwub5Dj2pAIgbSSBhQ9oW5XbaXm55aPpEDLvGJJBROC8ScnniDoAzNM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108988}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.2": {"name": "@csstools/color-helpers", "version": "3.0.2", "dist": {"shasum": "6571d289af8bfcc3a8d75357b35e6d17a8ba6848", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-3.0.2.tgz", "fileCount": 60, "integrity": "sha512-NMVs/l7Y9eIKL5XjbCHEgGcG8LOUT2qVcRjX6EzkCdlvftHVKr2tHIPzHavfrULRZ5Q2gxrJ9f44dAlj6fX97Q==", "signatures": [{"sig": "MEQCIGTtA3l+yw6cKqx+ljvUK+4oBWwD/Y7Uelz8GR+dSf8IAiAQoW7UX0ywdZBYzRI9KXLv9avtgyh+1Oi39CFpKmbNzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109030}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "4.0.0": {"name": "@csstools/color-helpers", "version": "4.0.0", "dist": {"shasum": "a1d6ffcefe5c1d389cbcca15f46da3cdaf241443", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-wjyXB22/h2OvxAr3jldPB7R7kjTUEzopvjitS8jWtyd8fN6xJ8vy1HnHu0ZNfEkqpBJgQ76Q+sBDshWcMvTa/w==", "signatures": [{"sig": "MEQCIAYsGLA5Z3TNGqYAhEeCJBnhpxqWhHMMoPbM+90rpgs/AiBK/Jp07tzHfOXQNc6nZgFdB69ORN+usTygIWYoJRznUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70585}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "4.1.0": {"name": "@csstools/color-helpers", "version": "4.1.0", "dist": {"shasum": "801977ec22c8eb23f9627a4f602e48beaa963bc2", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-pWRKF6cDwget8HowIIf2MqEmqIca/cf8/jO4b3PRtUF5EfQXYMtBIKycXB4yXTCUmwLKOoRZAzh/hjnc7ywOIg==", "signatures": [{"sig": "MEUCIQD+F0ive55jo0Jec2TQovTu1xtxRfCIQFhPDqi6RniGHwIgDxCrAXcqmsfNW0Y891FytsQqje5fFkt/ph8EQaYtDW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72226}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "4.2.0": {"name": "@csstools/color-helpers", "version": "4.2.0", "dist": {"shasum": "e8629ca9dce03a3a309506e7892b7f862673cf85", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-4.2.0.tgz", "fileCount": 7, "integrity": "sha512-hJJrSBzbfGxUsaR6X4Bzd/FLx0F1ulKnR5ljY9AiXCtsR+H+zSWQDFWlKES1BRaVZTDHLpIIHS9K2o0h+JLlrg==", "signatures": [{"sig": "MEUCIEyFRT3FN7B67/6j8vScjV7QiHIvL6hz8DFRJ/2kpJqZAiEA1qzrPzj1JGhzsGeWRyO4c6nbEWrjbONzAXB9RYlt2dY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74039}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "4.2.1": {"name": "@csstools/color-helpers", "version": "4.2.1", "dist": {"shasum": "da573554220ccb59757f12de62bf70c6b15645d4", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-4.2.1.tgz", "fileCount": 7, "integrity": "sha512-CEypeeykO9AN7JWkr1OEOQb0HRzZlPWGwV0Ya6DuVgFdDi6g3ma/cPZ5ZPZM4AWQikDpq/0llnGGlIL+j8afzw==", "signatures": [{"sig": "MEQCIDLNr6NQ1wjFJ5ijQNpe4TsKJP6NDds6AersKwArtDOQAiBbSJeCDouKeXW58Lv/6S8tV+ZNFkK3Eu2gXtnRg0qQig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76191}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "5.0.0": {"name": "@csstools/color-helpers", "version": "5.0.0", "dist": {"shasum": "f6d31d00ed4467608974d0df7ea7fa46a2265758", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.0.tgz", "fileCount": 7, "integrity": "sha512-4NemgIXEFNi7mKVUt5vt9O06UFQfpIQSjIyRbrYsfb+BLxKecasENHyIzn//lrI+Za7bcDznBSjKLajgeKmJ9A==", "signatures": [{"sig": "MEUCIAGbeDMYxGYG0LaopXojksLXOuTtS8nildv6FMAu24SAAiEAmNbB7pDsLkb4ajOv/1Fzka8/i+bSm+gX1nY3Ag9pvkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79356}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "5.0.1": {"name": "@csstools/color-helpers", "version": "5.0.1", "dist": {"shasum": "829f1c76f5800b79c51c709e2f36821b728e0e10", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.1.tgz", "fileCount": 7, "integrity": "sha512-MKtmkA0BX87PKaO1NFRTFH+UnkgnmySQOvNxJubsadusqPEC2aJ9MOQiMceZJJ6oitUl/i0L6u0M1IrmAOmgBA==", "signatures": [{"sig": "MEUCIQCvVKl95+YmjPPZnY3iHpbxCP5Biupqo84AXHH1xS1mcgIgZpyTfXJl8OJtnYOQQFc6P/ZatXys8wmaqgvIlhQH2tQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79827}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "5.0.2": {"name": "@csstools/color-helpers", "version": "5.0.2", "dist": {"integrity": "sha512-JqWH1vsgdGcw2RR6VliXXdA0/59LttzlU8UlRT/iUUsEeWfYq8I+K0yhihEUTTHLRm1EXvpsCx3083EU15ecsA==", "shasum": "82592c9a7c2b83c293d9161894e2a6471feb97b8", "tarball": "https://registry.npmjs.org/@csstools/color-helpers/-/color-helpers-5.0.2.tgz", "fileCount": 7, "unpackedSize": 79946, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICC/s+gvTJaeAPt1DJt5wIQLpcLtzty0zdK3lyQkp/pYAiEAwQtUs/ba6C1H/aqkrNtMz50OdWV4BoVcvX98UbODc0E="}]}, "engines": {"node": ">=18"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}]}}, "modified": "2025-02-23T17:05:07.173Z", "cachedAt": 1750172042420}