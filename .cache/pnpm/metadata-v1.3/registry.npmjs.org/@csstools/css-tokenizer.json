{"name": "@csstools/css-tokenizer", "dist-tags": {"latest": "3.0.4"}, "versions": {"1.0.0": {"name": "@csstools/css-tokenizer", "version": "1.0.0", "dist": {"shasum": "161c0c9b632952ee8c2f0a62eb479d736a5627ff", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-1.0.0.tgz", "fileCount": 37, "integrity": "sha512-xdFjdQ+zqqkOsmee+kYRieZD9Cqh4hr01YBQ2/8NtTkMMxbtRX18MC50LX6cMrtaLryqmIdZHN9e16/l0QqnQw==", "signatures": [{"sig": "MEQCID5ZiIWg74fOMC/2BXDOt/mGuAScaO1ti1Fyo0QqP5nXAiBmJN48SlmH39WIyrnUdH/MjU/SPrNSuZ4CPSVIhp4eew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcg4wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7tg//eyX3YYAPTgLq5WBDI+LUIykogvD+au+ZeWnRzOnYdbf4YAXD\r\nWiJs4bPZJAOFbl1Z94CkjnnRGfgJpbPIJ+gPBet1llpOsyQaKiV58NMdDM/8\r\nB2lXfYSMSXfTJGXGYzfxry/mVDTqIBSsxFDrT2isCy7mp2NBqwoaGhd8I+B+\r\nxv4mV1HoigCqSxNTVj9rL6EsGF31CYDWFGBXeTrLU89HP2I6qr0qtWUUVMCP\r\nOirkrxO/DTqFaayoyySBt1+jWBdlpgJVv/g9LK6gQT5MiudJhfNkOBIaXYFz\r\nwdfjzWEfpfTH5cVzHxYRIov0bTggK1P/iadht9QqdFI4aG3hppWPOzNcQSId\r\nU38dnzyzxvkhtzshmFVUV+SN9NXo4e2Uqiy4Kv3kjy6oLqObBCe9fgkQhW/1\r\n0Eepb8VipVo3R29SuyxkOJ9HQ6qedYf1zYJcMy5L6aJ3O4UakgoZjhlfGwmn\r\n8HWzr0hfQJBgMf1k7RwejaNECp86eIghC7CF6kgLkUYpatw7NBhxU3Psh30f\r\nn0m2/a6FkhOuEUQdwCgIepRPfh8UljCa9pyInRJtuv+zxUR9UBeGX/TwXf3Y\r\nunaMQvU2fUP6Zj+wnW6/lUi8O39fhByJzpnj8PJhdb2/Xe/thI0QkWnxxDwB\r\nNJHKUd4fGyEOMFa4Mf4lu728lvzy7vc/s1w=\r\n=6wM1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.0.0": {"name": "@csstools/css-tokenizer", "version": "2.0.0", "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "dist": {"shasum": "fa2a7e8f4ed965e73ba30ee80c00fa64980fd11e", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.0.0.tgz", "fileCount": 36, "integrity": "sha512-IB6EFP0Hc/YEz1sJVD47oFqJP6TXMB+OW1jXSYnOk5g+6wpk2/zkuBa0gm5edIMM9nVUZ3hF0xCBnyFbK5OIyg==", "signatures": [{"sig": "MEUCIEzDOMJDn7xV5jExdVOTtSrdubABZIFmKMsU7m5WF4CjAiEA6hSmRx/rNJUtEadKp112GGbD2B6pF2JMfXnrXxMX8tQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyY/9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKbhAAlC6tQsL1LSjKkhqPqz6dmQXXth74fhp7Lqvvul2FzE5f1zEF\r\neVjYi66vh/jpxtPUSaH77XwDyXWaaHHwFpfFZ3GBa+bLKG3YCR/xYo2xTDsf\r\nfgTeagPQVCeEyIZ8nY56+zZ/73ss/ONQTTs6Ep3XSQ/N6iVSl9/7jQ8TBhxy\r\nIeqxN9NtPNpUxVNOvOHKWHXenPAqfm7n/hJU44yhJE1OrsGuXTVLGir8Y3J3\r\ncCsB0pj30b8DemKCa+Df3elhxaAi6231rxWaRHNLTuL0sscSPyaD+t5dTMeM\r\n6uVYBEaRQ6CNChkbz1HfZ8AlfmEps7opw+Z3pY/WrJqoY+Sy81nwUDzeAlrA\r\nnL7IXDTUvbGcJ3MlfTJZeMJwCoZkYeKiyTZ89CUmKJ3CEsVSCeAmadVl6/He\r\nUMJeGEdh6ozd7mc0oOp5vmY9CNqOGSPJVr1kvBy941vx7bu0eR2m8K2imweO\r\nio19daKXmp4Ob7QIfMku1BbSU4HKYi4/TP68cGhQ2R5IoJsNiLbsdtwFj/dS\r\n/j3uh2ZxVdP64EaPQOqDeOCgTHSok3ssKYWVatZpCwhh+2L5lKkwpGk+/Q8Y\r\nSmBNipCFj4iXLrCMAlqMasFQST271L4+MCvj0ZBqSOJDQdw7jz2knT0vTHl2\r\nFTglhhmNp0Zn60C3n9U+Jl6eB/AgdtJVLHA=\r\n=SFgR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.0.1": {"name": "@csstools/css-tokenizer", "version": "2.0.1", "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "dist": {"shasum": "cb1e11752db57e69d9aa0e84c3105a25845d4055", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.0.1.tgz", "fileCount": 36, "integrity": "sha512-sYD3H7ReR88S/4+V5VbKiBEUJF4FqvG+8aNJkxqoPAnbhFziDG22IDZc4+h+xA63SfgM+h15lq5OnLeCxQ9nPA==", "signatures": [{"sig": "MEUCIH4ugb7TJvpBF0Kc0F18VHsHjEyUaEAqmkcFkV1xHKQHAiEAykfRgoDDPhn9ivLXya4BiUzZ93j5Q2uNtYgXY2bAYss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1MzxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMvA//V1lI2XnSrj5EZm/AM/zD3h/orvOKPQEeAlWFiNyvbvLquIjm\r\nV7/QiWOBOqBEw5ZxoGlUik5EyOBhSnUL7WrLhM/hCbgmqwcQl96wyJZLCC+n\r\nGKkguDPZsbHd3Bwq2Z/Tl1ow+YddwHAWrEvQ+1i5FJz8IIfACuWROle+n+Cv\r\nbEMR0ItlEi/9cpx7osv5kqdalNyZLIRW3Tb9lehXkW0b+MM1iB9lN0fafda+\r\nekFS0iFpmNSVbHPMilrdii996RjDipOrJGOc2mEfLS0ObD72oaWOflohdwQa\r\nz/34xMny61bhIbL/++1vmN3atqIJgeoCzLcTZ32H501gIsCQMDWMUhWGYyny\r\ndtpq8D0xqIgz4PeWLspb5mawbeU7y62Aar9CnJM+5p5BTuOOXrwnEIdZEwcO\r\n2R7w9iwhGgDzJ2v5JBMS3VOZQAedbmHdtf8uXKSmZBZwUrCQOeXxWJc5IrW5\r\naS4Dnbjq7jOKqn2nmSybRw1kc84HqYwF9huz+J5rCfn0XggzK9qZsWEmj5gK\r\ns8y8QHzqWtKVmN+mHQygonsYGJuBRoRhXMkWyhXDxj7OnE+nh87CliX8Qyeu\r\nwLYGTZPlqND0LwcoYu/VkXLZJkjY7b+WeKoyFxf2eg7tI5ZVuWHK+BPDlThn\r\nofEJxqgkadR4z9WcOvawiOkIp29P9jTgcbE=\r\n=y7Xm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.0.2": {"name": "@csstools/css-tokenizer", "version": "2.0.2", "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "dist": {"shasum": "3635560ffc8f1994295d7ce3482e14f956d3f9e1", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.0.2.tgz", "fileCount": 36, "integrity": "sha512-prUTipz0NZH7Lc5wyBUy93NFy3QYDMVEQgSeZzNdpMbKRd6V2bgRFyJ+O0S0Dw0MXWuE/H9WXlJk3kzMZRHZ/g==", "signatures": [{"sig": "MEUCIQDLSLGKKxvAiN2bEbNcfYlVS0U/HQbAo33RYK6AxEl9fwIgbF4DfOwHx05XixBpS1ioBh7bmgA/7TR57eDddlbxlIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58479, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6kgMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/lQ/+NjSriE+YuDYWucqHA8oUmAnpDEbFw34cFPGmvis5pP/7RYPY\r\nB3MsAXMtmG3nXNjfbPWpYVdJpp4iEQG87fnl0GEaxGRO8R5Ov9ey536fOd7C\r\n98fwtn63NVmjYDZ25l60/C8vigtKOXSmRhz4to5DjvwBhvVDZZcYQILAwUnk\r\n58a+1ytKrNeaBBJ2JTzD01D1Vinud/LEebDNIDPYmFMnf/Sciat9aZMzXSmU\r\ncWC6CVYLx8ed4NdpwxyseE1Rv9KujKIE8MdweAgwlA2XVmt9iQzW9CI4nx72\r\n0gidFZqZ4gM7qvGWFP1409UqZXHiXqD9AHSlIpli02BCp8uFPEo0jg9h5J5e\r\n1zt9TO8myUHdamat2/GY09fJFJb9Hp4r8KBtJhzKnGlT8aQziEaLmcBdpK4X\r\nu389LSHSeK/p6iZdzHjUwgJRl8ppFiUigZ+ZlEidDAHx4UOewLV6dC7a8jk8\r\naHlVeYcc2MffWN3UH3JwcqrCYCniG5qxY29oIpsL0nS0G+ewdBUpaCZ44SpP\r\neiX3BAtMCo4POS0b6ijZ6aDm710A4bQnBnGCa3vTDxCNrFJlrYbIaDcDsBAP\r\ntVNJWsPuAULRtvPfT6+/tQrNHL7AvMscjgSpo385PWtijk2jC2EuMY8hUorQ\r\nb2j+gtLDdUB/GDWjuZHn/GwyyCXk5flukoY=\r\n=q5z8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.1.0": {"name": "@csstools/css-tokenizer", "version": "2.1.0", "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "dist": {"shasum": "fee4de3d444db3ce9007f3af6474af8ba3e4b930", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.1.0.tgz", "fileCount": 36, "integrity": "sha512-dtqFyoJBHUxGi9zPZdpCKP1xk8tq6KPHJ/NY4qWXiYo6IcSGwzk3L8x2XzZbbyOyBs9xQARoGveU2AsgLj6D2A==", "signatures": [{"sig": "MEQCICzQs9DPxZlq2IMNX/Pgvd7EyNq3Qzi9wQ0UpqX5Xkx/AiBxju0Q1YWChMd5B/SJlQ1yjGNF2fIkNNdSErTuq7721w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9K5AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZNQ//SUaeDSmR3Mq0JG1D9qsqJucnIksfsDvak9po+ky4ATeirCbu\r\nIMPfINQbKu+eBSf5A1x/+n+/LvWvOBxH5Ok2vNzsEoJJtVjfFop6PhTp7SK9\r\nuYofU9UOrK1dIb87zDpJRL6Nu66tNZ6sVXZEsFJjBi0SI2c6iLL1as63CC4/\r\n2DU9lgydHUsDXUk5B7MgfBi5GVrWM3zZsNXVOv6aFAVXVGnpFl73UrgwKlff\r\n/u8gYCpSGazLoWmePdqQJDmLVF74kCzeFz8YprwNTdBDLKfLCu1bgUKd8cxK\r\nE6ijHayYyi0755i26zKR5mHhfN7iR9PvY5NxgFZWyQy/jjzOVX/0aSciXvwO\r\n+WrOUUuJ4GsMivqBAdc3/3H/CYz9xYGcRIaP+GtdpFPi4pP+hnCrzl+3Zizb\r\nm6IJwbBmL3pRwz4A9eA8l/3NKE+mKi6lrswyTm8E4o8WJ/+Ed7o0PIBB6iaL\r\npXI675JsrkUjp/jdG22vm67H8eQDJ9z7nsxX0IF0+RxuWhYdj83Q7Sv9dud1\r\nm3LVOyjtLxhWk6ecMrffrNgjiDHUhbbzQ3gKLw+MXpsOFUTXMNihbKWFvyd6\r\nhPnHWFzm6012Q7L8PVVNnb8KzRziEEB3ThSjU0sERC83SM4jhFfxxDiQ7/S7\r\nsfYluxfqC8bN+yLws5zQt14/xAmFbYafrmA=\r\n=kVSN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.1.1": {"name": "@csstools/css-tokenizer", "version": "2.1.1", "devDependencies": {"postcss-parser-tests": "^8.5.1", "@rmenke/css-tokenizer-tests": "^1.0.9"}, "dist": {"shasum": "07ae11a0a06365d7ec686549db7b729bc036528e", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.1.1.tgz", "fileCount": 36, "integrity": "sha512-GbrTj2Z8MCTUv+52GE0RbFGM527xuXZ0Xa5g0Z+YN573uveS4G0qi6WNOMyz3yrFM/jaILTTwJ0+umx81EzqfA==", "signatures": [{"sig": "MEUCIQCKmjpKpzyiqktnctSDBD3N2bRlOMbXDrSG6bXq5Npp+wIgM3szBmfNYAk1cCtqjW2GUlsxTDVeyMdA8MLCubSgAo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59024, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkM+liACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodAg/+JccYYWHKEd3GXr121iC4zOFhXvn0PnD6cw8EB5oDZMcj0C56\r\ns2YAlE6AJmrKe05jgE9ghjVS/ERf0+932+RevLYbnelKbnzzhAUqi0DnECsC\r\na2n7M0Pf9Y9YU1wBQQ6COROAbpG4ccfA7YFYPBXRYt1EEytStIQn+SK6HNQY\r\n7QwbqxjZnsq1+3MvRfCKC1JYBjx0f8U2ucIikPnw9yKmG7ptVIDO3WxDInRe\r\nIuLsaWLvC0h+rIzceFYzfl61Cd/nkFJY2eqi0m/Ysg+ImumqNVqU8+0TTe+p\r\n17Kf10UZONvkiyemDFena9/4xiHOhHKH9yQm6qPSv/V7YJAFFV7na3YuAk9E\r\nr/ICJTW7k0qTU4BrGRGD0HQ0uxYte0M0BNA89CAtlITVCd56z/uuGaFpGoKW\r\nw9ubsZcZjh86FZygNjWhk13Yt6NDuYHxGdnnPekgA0vYWT81S3bEl0kcqqkA\r\nVJ83F0W5lILV4qPt0VlkcsmyNlDkyPJtJLS/ARDVMwEYIaLqfjk7doWtLkvM\r\nnMVjip6XVtWN7EIddYlKBF5AwxZICwflsDnwDetYkFMp+e90/chICO+EoGf8\r\n7jnz7ZDxdGZiJBY9t/mYDz4JI/K7S8fBMU+DwaiEqJWT59FaCuk4vl8trDVz\r\nnDAMtg0CMj7iHmFWHWYmcuHw1O5xmxXxsKY=\r\n=tXxq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"url": "https://opencollective.com/csstools", "type": "opencollective"}}, "2.2.0": {"name": "@csstools/css-tokenizer", "version": "2.2.0", "devDependencies": {"postcss-parser-tests": "^8.6.0", "@rmenke/css-tokenizer-tests": "^1.1.1"}, "dist": {"shasum": "9d70e6dcbe94e44c7400a2929928db35c4de32b5", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.0.tgz", "fileCount": 38, "integrity": "sha512-wErmsWCbsmig8sQKkM6pFhr/oPha1bHfvxsUY5CYSQxwyhA9Ulrs8EqCgClhg4Tgg2XapVstGqSVcz0xOYizZA==", "signatures": [{"sig": "MEUCIG2An+y9urFv8ZpZSizSYjHs3TN3aosLmOke5U1rcuwOAiEA5rn0LeV6AsdIgdG0jfx2+pky1jKaE15PqonX2au2R9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65075}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.2.1": {"name": "@csstools/css-tokenizer", "version": "2.2.1", "devDependencies": {"postcss-parser-tests": "^8.8.0", "@rmenke/css-tokenizer-tests": "^1.1.1"}, "dist": {"shasum": "9dc431c9a5f61087af626e41ac2a79cce7bb253d", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.1.tgz", "fileCount": 38, "integrity": "sha512-Zmsf2f/CaEPWEVgw29odOj+WEVoiJy9s9NOv5GgNY9mZ1CZ7394By6wONrONrTsnNDv6F9hR02nvFihrGVGHBg==", "signatures": [{"sig": "MEUCIQC3KOGqstk9Tr0UOUs16ly3m74IFxVf7yA54gotOEzLvwIgL4j+EAnz23L6v9K5ubE2jAS4goQXj+VKSlgKbyLrttA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64840}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.2.2": {"name": "@csstools/css-tokenizer", "version": "2.2.2", "dist": {"shasum": "bcd85cef4468c356833b21e96d38b940c9760605", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.2.tgz", "fileCount": 7, "integrity": "sha512-wCDUe/MAw7npAHFLyW3QjSyLA66S5QFaV1jIXlNQvdJ8RzXDSgALa49eWcUO6P55ARQaz0TsDdAgdRgkXFYY8g==", "signatures": [{"sig": "MEUCIQCQ3OjKCL2kGzSHoVJtR1WoHGr20pCkgxzsW3eBNCBqIQIgYvQszjYTWcvHNpO7F8XWtbyQpH9dT3AXVCcaPYidNMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54910}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.2.3": {"name": "@csstools/css-tokenizer", "version": "2.2.3", "dist": {"shasum": "b099d543ea57b64f495915a095ead583866c50c6", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.3.tgz", "fileCount": 7, "integrity": "sha512-pp//EvZ9dUmGuGtG1p+n17gTHEOqu9jO+FiCUjNN3BDmyhdA2Jq9QsVeR7K8/2QCK17HSsioPlTW9ZkzoWb3Lg==", "signatures": [{"sig": "MEQCIEonlh71iVUO9wgh0rgnnNvDpXWqcEcJkaYCyswyLcBvAiAwIhow1kGw55Y2mwyMk6/3PNMpPT7EgijrYQxj3zGaig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61637}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.2.4": {"name": "@csstools/css-tokenizer", "version": "2.2.4", "dist": {"shasum": "a4b8718ed7fcd2dcd555de16b31ca59ad4b96a06", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.2.4.tgz", "fileCount": 7, "integrity": "sha512-PuWRAewQLbDhGeTvFuq2oClaSCKPIBmHyIobCV39JHRYN0byDcUWJl5baPeNUcqrjtdMNqFooE0FGl31I3JOqw==", "signatures": [{"sig": "MEUCIH55RHPvLGudR8s3RkuSxx/Vvu2BB0XxOa/tnRaERhmhAiEAqcMUHVfd1i4vZzICFbi3zRkOBCWkafQJ+NTzPGIw+rE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61659}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.3.0": {"name": "@csstools/css-tokenizer", "version": "2.3.0", "dist": {"shasum": "8f08b190fa696ed038bb3652510926ee7c6233d1", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.3.0.tgz", "fileCount": 7, "integrity": "sha512-v1WxYzvjjv5XwrHhSV/xRQFrToAX5FmRcYCAMzRD+ZTdG7962mf3JJXju7vPodWcuk2fJTmL+xbviMG8IxccHQ==", "signatures": [{"sig": "MEQCIAFTBuIo5fTmLo72Q+/w4j3GinXA5jYZ5d7TQrsibiYZAiBvv7XkPv6FCC2L6wO7dCNG0PZNIT31tkCzdHXfONJXYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71041}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.3.1": {"name": "@csstools/css-tokenizer", "version": "2.3.1", "dist": {"shasum": "3d47e101ad48d815a4bdce8159fb5764f087f17a", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.3.1.tgz", "fileCount": 7, "integrity": "sha512-iMNHTyxLbBlWIfGtabT157LH9DUx9X8+Y3oymFEuMj8HNc+rpE3dPFGFgHjpKfjeFDjLjYIAIhXPGvS2lKxL9g==", "signatures": [{"sig": "MEUCIQDu9uBcwvScyXsKxGBrEQcsN7Zwvs+9q9TzRVCZwEotKwIgCk23SyqRHwTdLTm5u5zMoaTge5W1puDRljDQr9KR68I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71103}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.3.2": {"name": "@csstools/css-tokenizer", "version": "2.3.2", "dist": {"shasum": "4050fc549921b26212d9fd51afca2231b40e867b", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.3.2.tgz", "fileCount": 7, "integrity": "sha512-0xYOf4pQpAaE6Sm2Q0x3p25oRukzWQ/O8hWVvhIt9Iv98/uu053u2CGm/g3kJ+P0vOYTAYzoU8Evq2pg9ZPXtw==", "signatures": [{"sig": "MEUCIQCP3l3p3wtcqXYyyvKRhluOsEPcG05mYHUUeJd6k+T17gIgHjPnPPAQ0LqkbHVRLI8+Av/y2dy1h1s1KLKOafVKor8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71104}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.3.3": {"name": "@csstools/css-tokenizer", "version": "2.3.3", "dist": {"shasum": "628a9dd388c9688fb81d4c2dd25b62b776109a60", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.3.3.tgz", "fileCount": 7, "integrity": "sha512-fTaF0vRcXVJ4cmwg8nHofydDjitKMDBzC8cCu+O/Lg13C4PdkC15GVjGpbmWauOOnhomVSTg5I5LpLJFJE2Hfw==", "signatures": [{"sig": "MEUCIFLpS/X3IXxFTOYkc9NmvRg55G/ikVWqnohm1XZHhzeLAiEA6OofWDeSaK/r+8C1BueZBdHwzmtIMy3FbWQlw0s4AIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71304}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.4.0": {"name": "@csstools/css-tokenizer", "version": "2.4.0", "dist": {"shasum": "ab65a0ac9256ef5214461389d92dbcb183134ceb", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.4.0.tgz", "fileCount": 7, "integrity": "sha512-E3uoN77rNPJNuKsOJlPRs9Uy5Qv+gOB1E5xUOhLJtGktZ7+PaVFI6JMAM0Jd1VxJMOX5Y1EWfYRMK0U7t4w/NQ==", "signatures": [{"sig": "MEUCIDnKIGnI3aVKG+V/nrh4ukGMx/uTXY+VEhEd8kd/HwCyAiEA+cmcSORNgbVEhhFns6XO2LD8khAiXaRgfJAFwueS9/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74094}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "2.4.1": {"name": "@csstools/css-tokenizer", "version": "2.4.1", "dist": {"shasum": "1d8b2e200197cf5f35ceb07ca2dade31f3a00ae8", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-2.4.1.tgz", "fileCount": 7, "integrity": "sha512-eQ9DIktFJBhGjioABJRtUucoWR2mwllurfnM8LuNGAqX3ViZXaUchqk+1s7jjtkFiT9ySdACsFEA3etErkALUg==", "signatures": [{"sig": "MEQCIG/Th+ji9H3IXcB2qeTwt4qbGb+UK6F/QB5au127V3GyAiBomeddKznIlsvnBHSXZMHdExdr97QqeACWzGyVFi9KPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74071}, "engines": {"node": "^14 || ^16 || >=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.0": {"name": "@csstools/css-tokenizer", "version": "3.0.0", "dist": {"shasum": "9a8a1ea564dd92b8b567e0507c29f74252e01e04", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-efZvfJyYrqH9hPCKtOBywlTsCXnEzAI9sLHFzUsDpBb+1bQ+bxJnwL9V2bRKv9w4cpIp75yxGeZRaVKoMQnsEg==", "signatures": [{"sig": "MEUCIQDrxfHpeLUv+tnA/UVGndcWOdATpkmUZGpDSyyqBQ6AtQIgU46lewp64+WURY6Vf3En2fW3mYQeXnoOcBNhEgUn+E4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72177}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.1": {"name": "@csstools/css-tokenizer", "version": "3.0.1", "dist": {"shasum": "9dd9b10084f3011290f96789598091e5bcb3c29a", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.1.tgz", "fileCount": 7, "integrity": "sha512-UBqaiu7kU0lfvaP982/o3khfXccVlHPWp0/vwwiIgDF0GmqqqxoiXC/6FCjlS9u92f7CoEz6nXKQnrn1kIAkOw==", "signatures": [{"sig": "MEYCIQC2eET5oswzC5lH0Augt1c2AcK68ee8ZdtKhVSRP5EhMgIhAOTWk3DLrK6KigGFY1fhFJW9P++67hvVRu8NjT6/T4Tv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72132}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.2": {"name": "@csstools/css-tokenizer", "version": "3.0.2", "dist": {"shasum": "1c1d7298f6a7b3db94afe53d949b9a7d6a8ebc57", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.2.tgz", "fileCount": 7, "integrity": "sha512-IuTRcD53WHsXPCZ6W7ubfGqReTJ9Ra0yRRFmXYP/Re8hFYYfoIYIK4080X5luslVLWimhIeFq0hj09urVMQzTw==", "signatures": [{"sig": "MEUCIFtQW4tVUyPBK5eWDxh7QnNCVPRSzr8QUX2oTf1AZ9e8AiEAvsRDNFih8rHKvnpB9WO/iwfLrgDK2LtMORjftujk0ns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71974}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.3": {"name": "@csstools/css-tokenizer", "version": "3.0.3", "dist": {"shasum": "a5502c8539265fecbd873c1e395a890339f119c2", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.3.tgz", "fileCount": 7, "integrity": "sha512-UJnjoFsmxfKUdNYdWgOB0mWUypuLvAfQPH1+pyvRJs6euowbFkFC6P13w1l8mJyi3vxYMxc9kld5jZEGRQs6bw==", "signatures": [{"sig": "MEYCIQDiHUUYhin6YGV2OuoP1yZGIX9slx9yQsF5n6RaUH5UPgIhAKr/fPXefbTT5dVb5J7PhwALErPKEHjqy71rKKXAEAwg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72786}, "engines": {"node": ">=18"}, "funding": [{"url": "https://github.com/sponsors/csstools", "type": "github"}, {"url": "https://opencollective.com/csstools", "type": "opencollective"}]}, "3.0.4": {"name": "@csstools/css-tokenizer", "version": "3.0.4", "dist": {"integrity": "sha512-Vd/9EVDiu6PPJt9yAh6roZP6El1xHrdvIVGjyBsHR0RYwNHgL7FJPyIIW4fANJNG6FtyZfvlRPpFI4ZM/lubvw==", "shasum": "333fedabc3fd1a8e5d0100013731cf19e6a8c5d3", "tarball": "https://registry.npmjs.org/@csstools/css-tokenizer/-/css-tokenizer-3.0.4.tgz", "fileCount": 7, "unpackedSize": 72983, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFkpVTh3FbkNTVDT3KwRK60S4gYKgJRVFZrh55pPCtXkAiBCxpLqbeT3U51ScJsjJ0WlfYkvYEkgk+PU0peV97oDTw=="}]}, "engines": {"node": ">=18"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}]}}, "modified": "2025-05-27T10:45:30.124Z", "cachedAt": 1750172042045}